/*스타일일몰처리
.product-order-btn {width: 102px;height: 35px;font-weight: 600;font-size: 14px;color: #FFFFFF;border: none;border-radius: 5px;background: #383838}
#product-list-container tbody td.product-manage-utility .modifyAndCopy button {width: 60px;line-height: 30px;height: 30px;color: #333333;border-radius: 5px;background-color: #E3E3E3;border: solid 1px #B7B7B7;}
#product-list-container tbody td.product-manage-utility .priceAndDelete button {width: 60px;line-height: 30px;height: 30px;color: #FFFFFF;border-radius: 5px;}
#product-list-container tbody td.product-manage-utility .priceAndDelete button:first-child {background-color: #0062D4;}
#product-list-container tbody td.product-manage-utility .priceAndDelete button:last-child {background-color: #383838;}
*/


button {
    font-family: unset;
    font-size: unset;
    line-height: unset;
    border: none;
}

img {
    vertical-align: unset;
}

p {
    margin: 0;
    padding: 0;
}

select {
    -o-appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

select {
    width: calc(100% - 74px);
    margin-left: 10px;
    background: #FFFFFF url('/images/icon/full-arrow-down.svg') no-repeat 98% 50%;
    padding: 7px 0 7px 5px;
    font: 400 15px Pretendard;
    color: #444444;
    border: 1px solid #CCCCCC;
}

th {
    font-weight: 600;
    font-size: 15px;
    color: #222222;
}

td {
    height: 105px;
    font-weight: 400;
    font-size: 15px;
    color: #444444;
    background: #FFFFFF;
}

.product-page-title {
    font-weight: 600;
    font-size: 24px;
    color: #000000;
}

.product-add-btn-box {
    width: 100%;
    margin-bottom: 15px;
    text-align: end;
}



.number-w {
    width: 50px;
}

.category-w {
    width: 50px;
}

.image-w {
    width: 100px;
}

.title-w {
    width: 180px;
}

.price-w {
    width: 180px;
}

.yn-w {
    width: 120px;
}

.period-w {
    width: 100px;
}

.option-w {
    width: 150px;
}

.product-modify-btn, .product-copy-btn {
    width: 60px;
    height: 30px;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 14px;
    color: #333333;
    border: 1px solid #B7B7B7;
    border-radius: 5px;
}

.product-price-btn {
    width: 60px;
    height: 30px;
    margin-right: 7px;
    font-weight: 600;
    font-size: 14px;
    color: #FFFFFF;
    border: 1px solid #0062D4;
    border-radius: 5px;
    background: #0062D4;
}

.product-delete-btn {
    width: 60px;
    height: 30px;
    font-weight: 600;
    font-size: 14px;
    color: #FFFFFF;
    border-radius: 5px;
    background: #383838;
}

.title-div {
    padding-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.title-div h3 {
    font-size: 20px;
}

.quad-div {
    margin-right: 4px;
    display: inline-block;
    width: calc(25% - 10px)
}

.tri-div {
    display: inline-block;
    width: calc(33.333% - 5px);
}

.row-div {
}

.row-div .dotted {
    margin: 30px 0px;
    border: dashed 1px #CCCCCC;
}

.row-div-double {
    margin-bottom: 20px
}

.row-div h5 {
    display: inline-block;
    font-size: 15px;
}

.full-div {
    width: calc(100% - 10px)
}

.full-div .form-control {
    width: calc(100% - 26px)
}

.full-div h5 {
    font-size: 15px;
}

.tri-div-double {
    display: inline-block;
    width: calc(75% - 5px);
}

.tri-div-double h5 {
    font-size: 15px;
    font-weight: 600;
    display: inline-block;
}

.tri-div-double .form-control {
    display: inline-block;
    width: calc(100% - 105px);
    margin-left: 15px;
    height: 34px;
    line-height: 34px;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    box-shadow: inset 0 0 0 rgba(0, 0, 0, 0);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.half-div {
    display: inline-block;
    width: calc(50% - 10px)
}

.half-div .form-control {
    display: inline-block;
    width: calc(100% - 100px);
    height: calc(2.25rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    box-shadow: inset 0 0 0 rgba(0, 0, 0, 0);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.half-div h5 {
    font-size: 15px;
    font-weight: 600;
    display: inline-block;
}

.tri-div .form-select,
.quad-div .form-select {
    outline: none;
    width: calc(100% - 100px);
    margin-left: 10px;
    padding: 7px 0 7px 5px;
    font: 400 15px Pretendard;
    color: #444444;
    border: 1px solid #CCCCCC;
}

.search-box {
    width: 100%;
    /*margin-top:15px;*/
    margin-bottom: 30px;
    background: #FFFFFF;
    box-shadow: 2px 2px 6px 0 rgba(0, 0, 0, 0.25);
    padding: 30px 25px;
}

.search-input-box {
    display: inline-block;
    width: 89%;
    border: solid 1px #CCCCCC;
    margin-left: 10px;
    position: relative;

}

.search-input-box input {
    width: 100%;
}

.product-isDisplay .switch-box {
    /*display:inline-block;*/
    top: -5px;
    width: 100%;
    height: 33px;
    line-height: 33px;
    border: solid 1px #CCCCCC;
    cursor: pointer;
    border-radius: 5px;
    margin: auto;
    background-color: #FFFFFF;
}

.switch-box {
    position: relative;
}

.switch-box > ul.status {
    padding-left: 0;
    list-style: none;
}

.switch-box ul li.hide {
    display: block;
    padding-left: 0;
    list-style: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    white-space: nowrap;
    color: #AAAAAA;
}

.switch-box ul li.show {
    display: block;
    padding-left: 0;
    list-style: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    white-space: nowrap;
}

.toggle + div.switch-box .toggleSwitch {
    display: block;
    position: relative;
    background-color: #FFFFFF;
    width: 100%;
}

.toggle + div.switch-box .toggleButton {
    position: absolute;
    top: 2px;
    left: 6px;
    width: 4px;
    height: 27px;
    border-radius: 3px;
    background-color: #CCCCCC;
}

.toggle:checked + div.switch-box {
    background-color: #17E;
    transition: all ease-in .3s;
}

.toggle:checked + div.switch-box .toggleSwitch {
}

.toggle:checked + div.switch-box .toggleButton {
    left: 95%;
    background-color: #FFFFFF;
    transition: .3s;
}

.toggle:checked + div.switch-box .status li.show {
    color: #FFFFFF;
}

.search-input-box .search-input-text {
    border: none;
    /* height: 50px; */
    padding: 5px 0px 5px 20px;
    width: 100%;
    font-size: 16px;
    outline: none;
}

.product-sort-item {
    position: relative;
    display: flex;
    align-items: center;
    height: 50px;
    margin-bottom: 15px;
    background: #FFFFFF;
    opacity: 0.5
}

.product-sort-item.active {
    opacity: 1;
    filter: drop-shadow(2px 2px 2px rgba(0, 0, 0, 0.3));
}

.move-box {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 0 20px 0 20px;
    word-break: keep-all;
}

.move-image {
    width: 100%;
    height: 100%;
    object-fit: cover
}

.move-image-box {
    width: 84px;
    height: 100%;
}

.hamburger-image {
    width: 23px;
    height: 14px;
}

.hamburger-image-box {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 63px;
    height: 100%;
}

/*#product-search-button {*/
/*    width: 70px;*/
/*    height: 35px;*/
/*    background-color: transparent;*/
/*    border: solid 1px #1177EE;*/
/*    position: absolute;*/
/*    top: 0;*/
/*    right: 0;*/
/*}*/

/*#product-search-button span {*/
/*    color: #0a58ca;*/
/*}*/

#product-list-container {
    width: 100%;
}

#product-list-container thead {
    border-top: solid 2px #333333;
    background-color: #EDEEF3;
    height: 45px;
    line-height: 45px;
}

#product-list-container tbody {
    border-bottom: solid 1px #333333;

}

#product-list-container thead tr {
    border-bottom: solid 1px #CCC;
}

#product-list-container thead tr th {
    text-align: center;
}

#product-list-container tbody tr {
    background-color: #FFFFFF;
    border-bottom: solid 1px #CCC;
}

#product-list-container tbody tr td {
    height: 140px;
}

#product-list-container tbody tr td.product-thumbnail {
    max-width: 120px;
    height: fit-content;
    overflow: hidden;
}

#product-list-container tbody tr td.product-thumbnail img {
    width: 100%;
    height: 100%;
    aspect-ratio: 4 / 3;
    margin: 5px 0;
}

#product-list-container tbody tr:last-child {
    border-style: none;
}

#product-list-container tbody tr.status-ready td {
    background-color: #E6E6E6 !important;
}

#product-list-container tbody td {
    height: 100%;
    text-align: center;
}

#product-list-container tbody td.product-manage-utility .modifyAndCopy,
#product-list-container tbody td.product-manage-utility .priceAndDelete {
    height: unset;
    /*line-height:60px;*/
    line-height: initial;
}

#product-list-container tbody td.product-manage-utility .modifyAndCopy {
    margin-bottom: 5px;
}





#product-list-container tbody td.empty-list {
    height: 350px;
    line-height: 350px;
    text-align: center;
}

/* switch */

