package kr.co.wayplus.travel.web.front;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import kr.co.wayplus.travel.builder.ExcelDataBuilder;
import kr.co.wayplus.travel.model.excel.ExcelData;
import kr.co.wayplus.travel.model.excel.ExcelMetadata;
import kr.co.wayplus.travel.model.excel.HeaderInfo;
import kr.co.wayplus.travel.service.front.ExternalApiService;
import kr.co.wayplus.travel.service.recommand.RecommandationManageService;
import kr.co.wayplus.travel.util.ExcelUtil;

/**
 * 외부 사이트 API 연계 처리
 */
@RequestMapping("/eapi")
@RestController
public class ExternalApiController {

	private final Logger logger = LoggerFactory.getLogger(ExternalApiController.class);
	private final ExternalApiService externalApiService;
	private final RecommandationManageService rcmdService;

	@Autowired
	public ExternalApiController(
		ExternalApiService externalApiService
		 ,RecommandationManageService rcmdService) {
		this.externalApiService = externalApiService;
		this.rcmdService = rcmdService;
	}

	@GetMapping("/recommand")
	@ResponseBody
	public HashMap<String, Object> recommand_ajax(HttpServletRequest request){
		HashMap<String, Object> result = new HashMap<>();

		return rcmdService.updateRecommendations();
	}

	@GetMapping("/download")
	public ResponseEntity<byte[]> downloadDepartmentStats(@RequestParam String startDate,
			@RequestParam String endDate) {
		try {
			// 1. Excel 메타데이터 설정
			ExcelMetadata metadata = createDepartmentMetadata();

			// 2. 데이터 생성
			List<ExcelData> excelDataList = createSampleDepartmentData();

			// 3. Excel 생성
			byte[] excelFile = generateExcel(metadata, excelDataList);

			// 4. 응답 헤더 설정
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
			headers.setContentDisposition(ContentDisposition.builder("attachment")
					.filename("department_stats.xlsx", StandardCharsets.UTF_8).build());

			return new ResponseEntity<>(excelFile, headers, HttpStatus.OK);
		} catch (Exception e) {
			logger.error("Failed to generate excel file", e);
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

    private ExcelMetadata createDepartmentMetadata() {
        return ExcelMetadata.builder()
            .sheetName("부서별 통계")
            .addHeaderRow(Arrays.asList(
                new HeaderInfo("부서정보", 1, 2),
                new HeaderInfo("인원현황", 1, 3),
                new HeaderInfo("업무실적", 1, 3)
            ))
            .addHeaderRow(Arrays.asList(
                new HeaderInfo("부서명", 1, 1),
                new HeaderInfo("팀수", 1, 1),
                new HeaderInfo("정규직", 1, 1),
                new HeaderInfo("계약직", 1, 1),
                new HeaderInfo("총원", 1, 1),
                new HeaderInfo("처리건수", 1, 1),
                new HeaderInfo("완료건수", 1, 1),
                new HeaderInfo("처리율", 1, 1)
            ))
            .addColumnMapping("teamCount", "팀수")
            .addColumnMapping("regularCount", "정규직")
            .addColumnMapping("contractCount", "계약직")
            .addColumnMapping("totalCount", "총원")
            .addColumnMapping("totalCase", "처리건수")
            .addColumnMapping("completedCase", "완료건수")
            .addColumnMapping("completionRate", "처리율")
            .addColumnMapping("deptName", "부서명")
            .startRow(0)
            .startCol(0)
            .build();
    }

    private List<ExcelData> createSampleDepartmentData() {
        List<ExcelData> dataList = new ArrayList<>();

        // 개발본부 데이터
        dataList.add(ExcelDataBuilder.create()
            .id("dev")
            .value("deptName", "개발본부")
            .value("teamCount", 3)
            .value("regularCount", 45)
            .value("contractCount", 15)
            .value("totalCount", 60)
            .value("totalCase", 120)
            .value("completedCase", 100)
            .value("completionRate", 83.3)
            .format("completionRate", "#0.0%")
            .build());

        // 영업본부 데이터
        dataList.add(ExcelDataBuilder.create()
            .id("sales")
            .value("deptName", "영업본부")
            .value("teamCount", 4)
            .value("regularCount", 50)
            .value("contractCount", 10)
            .value("totalCount", 60)
            .value("totalCase", 200)
            .value("completedCase", 180)
            .value("completionRate", 90.0)
            .format("completionRate", "#0.0%")
            .build());

        // ... 추가 데이터

        return dataList;
    }

    private byte[] generateExcel(ExcelMetadata metadata, List<ExcelData> dataList) throws IOException {
        ExcelUtil excelUtil = new ExcelUtil();

        // 헤더 생성
        excelUtil.createHeader(metadata.getHeaders());

//        // 데이터 변환 및 추가
//        List<Map<String, Object>> excelData = dataList.stream()
//            .map(data -> data.getData())
//            .collect(Collectors.toList());

        excelUtil.addGridData(dataList, metadata.getHeaders().size());

        return excelUtil.generateExcelFile();
    }
}