package kr.co.wayplus.travel.web.front;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.UUID;

import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import jakarta.servlet.ServletRequest;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.InquiryContent;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.MainBannerImage;
import kr.co.wayplus.travel.model.MenuUser;
import kr.co.wayplus.travel.model.PagingDTO;
import kr.co.wayplus.travel.model.Reservation;
import kr.co.wayplus.travel.model.UserCustomerPayment;
import kr.co.wayplus.travel.service.front.PageService;

@Controller
@RequestMapping("/crm")
public class CrmController  extends BaseController {

    @Value("${cookie-set.domain}")
    private String cookieDomain;
    @Value("${cookie-set.prefix}")
    private String cookieName;

    @Value("${upload.file.path}")
	String externalImageUploadPath;

	final String addPath = "images/";

	@Value("${key.crypto.encrypt}")
    private String encrypt;
    @Value("${key.crypto.iv}")
    private String iv;

    @Value("${upload.file.max-size}")
    int maxFileSize;

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final PageService pageService;

    @Autowired
    public CrmController(PageService pageService) {
    	this.pageService = pageService;
    }

    @PostMapping(value="/{url}/{id}/check")
    @ResponseBody
    public HashMap<String, Object> crm_view(
    		HttpServletRequest request,
    		@RequestParam(value="customerName") String customerName,
    		@RequestParam(value="customerPass") String customerPass,
    		@PathVariable String url,
    		@PathVariable String id){
    	HashMap<String, Object> retrunMap = new HashMap<String, Object>();
    	try {
	        if( url.equals("inquiry") ) {
	        	HashMap<String, Object> paramMap = new HashMap<>();
	        	paramMap.put("id", id);

		        InquiryContent content = pageService.selectOneInquiryContent(paramMap);

		        if( customerName.equals( content.getCustomerName()) ||
		        	customerPass.equals( content.getCustomerPass()) )  {
		        	retrunMap.put("secretPass", true);

		        	HttpSession session = request.getSession();
		        	session.setAttribute( "secretPass"+id , true );
		        	session.setMaxInactiveInterval(1800);
		        }
	        } else if( url.equals("reservation") ) {
	        }

	        retrunMap.put("result", "success");
	        retrunMap.put("message", "처리가 완료 되었습니다.");
	    } catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			retrunMap.put("info", e.getMessage());
			logger.error(e.getCause().getMessage());
		}
        return retrunMap;
    }

    @PostMapping(value="/{url}/form")
    public ModelAndView crm_form(
    		@PathVariable String url,
    		@RequestParam(value="mode", defaultValue="I") String mode,
    		@RequestParam(value="id", defaultValue="0") String id,
    		@Param(value="onlyCategory")  String onlyCategory
    		) {
    	ModelAndView mav = new ModelAndView();

    	HashMap<String, Object> paramMenu = new HashMap<>();
    	String menuUrl = "/"+url;;
    	paramMenu.put("menuType", "crm");
        paramMenu.put("menuUrl", menuUrl);
        MenuUser menu = pageService.selectOneMenuUser( paramMenu ); // -> menu_id=>upper_menu_id
        mav.addObject("menu", menu);

        if( url.equals("inquiry") ) {
        	HashMap<String, Object> paramCategory = new HashMap<>();
        	paramCategory.put("useYn","Y");
            paramCategory.put("deleteYn","N");
            paramCategory.put("groupYn","N");
            paramCategory.put("sort","orderNum");
            paramCategory.put("sortOrder","ASC");
            if( onlyCategory != null )
            	paramCategory.put("id",onlyCategory);

            mav.addObject("categorys",  pageService.selectListInquiryCategory( paramCategory ));

	        if(mode.equals("I")) {
	        	mav.addObject("content",  new InquiryContent());
	        } else {
	        	HashMap<String, Object> paramMap = new HashMap<>();
	        	paramMap.put("id", id);
	        	mav.addObject("content",  pageService.selectOneInquiryContent(paramMap));
	        }
        } else {

        }

        mav.addObject("menuUrl", menuUrl);
        mav.addObject("mode", mode);

//        HashMap<String, Object> paramMap = new HashMap<>();
//    	paramMap.put("content_id", id);
//    	mav.addObject("listAttach",  boardService.selectListBoardAttachFile(paramMap));

    	mav.setViewName("/front/crm"+menuUrl+"/form");
		return mav;
	}

	@PostMapping("/{url}/save")
	@ResponseBody
	public HashMap<String, Object> dnymic_save_ajax(
			@PathVariable String url,
			@RequestParam(value="mode", defaultValue="I") String mode,
			InquiryContent bc,
		ServletRequest request
	){
		HashMap<String, Object> retrunMap = new HashMap<>();

		try {
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			String userEmail = "";

			if(_user instanceof LoginUser) {
				LoginUser user = (LoginUser)_user;

				if(bc.getCreateId() == null) {
					userEmail = user.getUserEmail();
				} else {
					userEmail = bc.getCreateId();
				}
			} else {
				userEmail = bc.getCustomerEmail();
			}

			if(mode.equals("I")) {
				bc.setCreateId( userEmail );
				pageService.insertInquiryContent(bc);
				pageService.sendCustomerQnaSms();
			} else {
				bc.setLastUpdateId( userEmail);
				pageService.updateInquiryContent(bc);
			}

			retrunMap.put("result", "success");
			retrunMap.put("message", "처리가 완료 되었습니다.");
/*
			} else {
				retrunMap.put("result", "fail");
				retrunMap.put("message", "로그인 문제가 발생되었습니다.");
			}
*/
		} catch (Exception e) {
			e.printStackTrace();
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			retrunMap.put("info", e.getMessage());
			logger.error(e.getCause().getMessage());
		} 
		return retrunMap;
	}

	@PostMapping("/{url}/cancel")
	@ResponseBody
	public HashMap<String, Object> dnymic_cancel_ajax(
			@PathVariable String url,
			InquiryContent bc,
			ServletRequest request,
			BindingResult bindingResult
	){
		HashMap<String, Object> retrunMap = new HashMap<>();

		try {
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			String userEmail = "";

			if(_user instanceof LoginUser) {
				LoginUser user = (LoginUser)_user;

				if(bc.getCreateId() == null) {
					userEmail = user.getUserEmail();
				} else {
					userEmail = bc.getCreateId();
				}
			} else {
				userEmail = bc.getCustomerEmail();
			}

			bc.setLastUpdateId( userEmail);
			bc.setCancelYn("Y");
			pageService.updateInquiryContent(bc);

			retrunMap.put("result", "success");
			retrunMap.put("message", "처리가 완료 되었습니다.");

		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			retrunMap.put("info", e.getMessage());
			logger.error(e.getCause().getMessage());
		}
		return retrunMap;
	}

	@PostMapping("/reservation/save")
    @ResponseBody
    public HashMap<String, Object> inquiry_save_ajax(
    		HttpSession session,
    		@RequestParam(value="mode", defaultValue="I") String mode,
    		@RequestParam(value="isGuest", defaultValue="false") boolean isGuest,
    		Reservation data,
    		//Multipart
    		HttpServletRequest request
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser user = (LoginUser)_user;
        		HashMap<String, Object> paramMap = new HashMap<>();

        		String thumbnailUrl = "";

	    		if(mode.equals("I")) {
	    			data.setCreateId(data.getUserEmail());
	    			pageService.insertReservation(data);
	    		} else {
	    			data.setLastUpdateId(data.getUserEmail());
	    			pageService.updateReservation(data);
	    		}
	    		retrunMap.put("result", "success");
	    		retrunMap.put("message", "처리가 완료 되었습니다.");
        	} else {
        		retrunMap.put("result", "fail");
	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
        	}
		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			retrunMap.put("info", e.getMessage());
			logger.error(e.getCause().getMessage());
		}
        return retrunMap;
    }
}
