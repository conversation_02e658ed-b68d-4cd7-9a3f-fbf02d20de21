<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.admin.AdminMapper">
    <select id="selectAdministratorUserListCount" resultType="Integer">
        SELECT count(*) FROM user
        WHERE user_role IN ('STAFF', 'ADMIN', 'MANAGER')
    </select>

    <select id="selectAdministratorUserList" parameterType="HashMap" resultType="LoginUser">
        SELECT user_email, user_token_id, user_name,
        user_role, user_mobile
        FROM user
        WHERE user_role IN ('STAFF', 'ADMIN', 'MANAGER')
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
            LIMIT #{itemStartPosition}, #{pagePerSize}
        </if>
    </select>

    <select id="selectAdministratorInfo" parameterType="String" resultType="LoginUser">
        SELECT user_email, user_password, user_join_type,
               user_name, user_auth_id, user_nationality, user_birthday,
               user_gender, user_mobile, user_tel,
               user_addr_zipcode, user_addr_extra,
               user_addr_jibun, user_addr_road, user_addr_detail,
               user_role, user_auth_id, user_class_name, user_class_code,
               user_group_code, user_token_id,
               user_grade_id, user_grade, user_grade_start, user_grade_end,
               user_membership_id, user_membership_grade, user_membership_start, user_membership_end,
               user_verified_email, user_verified_mobile,
               user_ci, user_di, user_di_corp,
               user_join_date, user_modify_date, last_login_date,
               last_password_date, last_login_fail_count, account_status,
               naver_token, naver_email, naver_join_date,
               kakao_token, kakao_email, kakao_join_date,
               google_token, google_email, google_join_date,
               facebook_token, facebook_email, facebook_join_date,
               secondary_email, privacy_retention_days, mailing_yn
        FROM user
        WHERE user_email = #{value}
    </select>

    <insert id="insertAdministrator" parameterType="LoginUser" useGeneratedKeys="true" keyProperty="userEmail">
        INSERT INTO user
            SET user_email = #{userEmail}
            , user_password = #{userPassword}
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userJoinType)">, user_join_type = #{userJoinType}</if>
            , user_name = #{userName}
            , user_mobile = #{userMobile}
            , user_tel = #{userTel}
            , user_addr_zipcode = #{userAddrZipcode}
            , user_addr_extra = #{userAddrExtra}
            , user_addr_jibun = #{userAddrJibun}
            , user_addr_road = #{userAddrRoad}
            , user_addr_detail = #{userAddrDetail}
            , user_nationality = #{userNationality}
            , user_birthday = #{userBirthday}
            , user_gender = #{userGender}
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userRole)">, user_role = #{userRole}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userAuthId)">, user_auth_id = #{userAuthId}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isEmpty(userAuthId)">, user_auth_id = null</if>
            , user_class_name = #{userClassName}
            , user_class_code = #{userClassCode}
            , user_group_code = #{userGroupCode}
            , user_token_id = #{userTokenId}
            , user_grade = #{userGrade}
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userVerifiedEmail)">, user_verified_email = #{userVerifiedEmail}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userVerifiedMobile)">, user_verified_mobile = #{userVerifiedMobile}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userCi)">, user_ci = #{userCi}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userDi)">, user_di = #{userDi}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userDiCorp)">, user_di_corp = #{userDiCorp}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverToken)">, naver_token = #{naverToken}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverEmail)">, naver_email = #{naverEmail}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverToken)">, naver_join_date = now()</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoToken)">, kakao_token = #{kakaoToken}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoEmail)">, kakao_email = #{kakaoEmail}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoToken)">, kakao_join_date = now()</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleToken)">, google_token = #{googleToken}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleEmail)">, google_email = #{googleEmail}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleToken)">, google_join_date = now()</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookToken)">, facebook_token = #{facebookToken}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookEmail)">, facebook_email = #{facebookEmail}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookToken)">, facebook_join_date = now()</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secondaryEmail)">, secondary_email = #{secondary_email}</if>
            <if test="privacyRetentionDays > 0">, privacy_retention_days = #{privacyRetentionDays}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mailingYn)">, mailing_yn = #{mailingYn}</if>
    </insert>

    <update id="updateAdministrator" parameterType="LoginUser">
        UPDATE user
           SET user_modify_date = now()
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPassword)">, user_password = #{userPassword}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userJoinType)">, user_join_type = #{userJoinType}</if>
               , user_name = #{userName}
               , user_mobile = #{userMobile}
               , user_tel = #{userTel}
               , user_addr_zipcode = #{userAddrZipcode}
               , user_addr_extra = #{userAddrExtra}
               , user_addr_jibun = #{userAddrJibun}
               , user_addr_road = #{userAddrRoad}
               , user_addr_detail = #{userAddrDetail}
               , user_nationality = #{userNationality}
               , user_birthday = #{userBirthday}
               , user_gender = #{userGender}
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userRole)">, user_role = #{userRole}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userAuthId)">, user_auth_id = #{userAuthId}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isEmpty(userAuthId)">, user_auth_id = null</if>
               , user_class_name = #{userClassName}
               , user_class_code = #{userClassCode}
               , user_group_code = #{userGroupCode}
               , user_grade = #{userGrade}
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userVerifiedEmail)">, user_verified_email = #{userVerifiedEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userVerifiedMobile)">, user_verified_mobile = #{userVerifiedMobile}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userCi)">, user_ci = #{userCi}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userDi)">, user_di = #{userDi}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userDiCorp)">, user_di_corp = #{userDiCorp}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverToken)">, naver_token = #{naverToken}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverEmail)">, naver_email = #{naverEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverToken)">, naver_join_date = now()</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoToken)">, kakao_token = #{kakaoToken}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoEmail)">, kakao_email = #{kakaoEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoToken)">, kakao_join_date = now()</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleToken)">, google_token = #{googleToken}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleEmail)">, google_email = #{googleEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleToken)">, google_join_date = now()</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookToken)">, facebook_token = #{facebookToken}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookEmail)">, facebook_email = #{facebookEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookToken)">, facebook_join_date = now()</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secondaryEmail)">, secondary_email = #{secondary_email}</if>
               <if test="privacyRetentionDays > 0">, privacy_retention_days = #{privacyRetentionDays}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mailingYn)">, mailing_yn = #{mailingYn}</if>
         WHERE user_email = #{userEmail} AND user_token_id = #{userTokenId}
    </update>

    <delete id="deleteAdministrator" parameterType="LoginUser">
        DELETE FROM user
         WHERE user_email = #{userEmail} AND user_token_id = #{userTokenId}
    </delete>

    <insert id="insertWithdrawalAdministrator" parameterType="LoginUser">
        INSERT INTO user_stored_withdrawal
        (
            user_email, user_password, user_join_type,
            user_name, user_nationality, user_birthday, user_gender,
            user_mobile, user_tel,
            user_addr_zipcode, user_addr_extra,
            user_addr_jibun, user_addr_road, user_addr_detail,
            user_role, user_class_name, user_class_code,
            user_group_code, user_token_id,
            user_grade_id, user_grade, user_grade_start, user_grade_end,
            user_membership_id, user_membership_grade, user_membership_start, user_membership_end,
            user_verified_email, user_verified_mobile,
            user_ci, user_di, user_di_corp,
            user_join_date, user_modify_date, last_login_date,
            last_password_date, last_login_fail_count, account_status,
            naver_token, naver_email, naver_join_date,
            kakao_token, kakao_email, kakao_join_date,
            google_token, google_email, google_join_date,
            facebook_token, facebook_email, facebook_join_date,
            secondary_email, privacy_retention_days, mailing_yn,
            delete_date, delete_id
        )

        SELECT user_email, user_password, user_join_type,
               user_name, user_nationality, user_birthday, user_gender,
               user_mobile, user_tel,
               user_addr_zipcode, user_addr_extra,
               user_addr_jibun, user_addr_road, user_addr_detail,
               user_role, user_class_name, user_class_code,
               user_group_code, user_token_id,
               user_grade_id, user_grade, user_grade_start, user_grade_end,
               user_membership_id, user_membership_grade, user_membership_start, user_membership_end,
               user_verified_email, user_verified_mobile,
               user_ci, user_di, user_di_corp,
               user_join_date, user_modify_date, last_login_date,
               last_password_date, last_login_fail_count, account_status,
               naver_token, naver_email, naver_join_date,
               kakao_token, kakao_email, kakao_join_date,
               google_token, google_email, google_join_date,
               facebook_token, facebook_email, facebook_join_date,
               secondary_email, privacy_retention_days, mailing_yn,
               now(), #{operator}
          FROM user
         WHERE user_email = #{userEmail} AND user_token_id = #{userTokenId}
    </insert>

<!--################################### manageMenuAuth ###################################-->
	<select id="selectCountManageMenuAuth" parameterType="HashMap" resultType="Integer">
		SELECT count(auth_id)
		  FROM manage_menu_auth a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authId)" >	and auth_id=#{authId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authName)" >	and auth_name=#{authName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>
	</select>

	<select id="selectListManageMenuAuth" parameterType="HashMap" resultType="ManageMenuAuth">
		select *
		  from (
			SELECT @rownum:=@rownum+1 AS rownum
				, a.auth_id
				, a.auth_name
				, a.use_yn
				, a.delete_yn
				, a.create_id
				, a.create_date
				, a.last_update_id
				, a.last_update_date
				, a.delete_id
				, a.delete_date
			  FROM manage_menu_auth a
			  join (SELECT @rownum:= 0) rnum
			<where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authId)" >	and auth_id=#{authId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authName)" >	and auth_name=#{authName}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
			</where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort=='authId'" >	a.auth_id	</when>
					<when test="sort=='authName'" >	a.auth_name	</when>
					<when test="sort=='useYn'" >	a.use_yn	</when>
					<when test="sort=='deleteYn'" >	a.delete_yn	</when>
					<when test="sort=='createId'" >	a.create_id	</when>
					<when test="sort=='createDate'" >	a.create_date	</when>
					<when test="sort=='lastUpdateId'" >	a.last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	a.last_update_date	</when>
					<when test="sort=='deleteId'" >	a.delete_id	</when>
					<when test="sort=='deleteDate'" >	a.delete_date	</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
					<when test="sort=='item.authId'" >	a.auth_id	</when>
					<when test="sort=='item.authName'" >	a.auth_name	</when>
					<when test="sort=='item.useYn'" >	a.use_yn	</when>
					<when test="sort=='item.deleteYn'" >	a.delete_yn	</when>
					<when test="sort=='item.createId'" >	a.create_id	</when>
					<when test="sort=='item.createDate'" >	a.create_date	</when>
					<when test="sort=='item.lastUpdateId'" >	a.last_update_id	</when>
					<when test="sort=='item.lastUpdateDate'" >	a.last_update_date	</when>
					<when test="sort=='item.deleteId'" >	a.delete_id	</when>
					<when test="sort=='item.deleteDate'" >	a.delete_date	</when>
		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if> ) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneManageMenuAuth" parameterType="HashMap" resultType="ManageMenuAuth">
		SELECT a.auth_id
			, a.auth_name
			, a.use_yn
			, a.delete_yn
			, a.create_id
			, a.create_date
			, a.last_update_id
			, a.last_update_date
			, a.delete_id
			, a.delete_date
		  FROM manage_menu_auth b
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authId)" >	and auth_id=#{authId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authName)" >	and auth_name=#{authName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>
	</select>

	<insert id="insertManageMenuAuth" parameterType="ManageMenuAuth" useGeneratedKeys="true" keyProperty="authId">
        INSERT INTO manage_menu_auth
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authId)" >	auth_id=#{authId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authName)" >	auth_name=#{authName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	delete_yn=#{deleteYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},last_update_id=#{createId},	</if>
			create_date=now(),last_update_date=now()
        </set>
    </insert>

	<update id="updateManageMenuAuth" parameterType="ManageMenuAuth">
        Update manage_menu_auth
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authName)" >	auth_name=#{authName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	delete_yn=#{deleteYn},	</if>
			last_update_date=now()
        </set>
        <where>
        	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authId)" >	and auth_id=#{authId}	</if>
        </where>
    </update>

    <update id="restoreManageMenuAuth" parameterType="ManageMenuAuth">
        UPDATE manage_menu_auth
           SET
           <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
           last_update_date=now(),
           delete_yn='N',
           delete_id=null,
           delete_date=null
        WHERE auth_id=#{authId}
    </update>

    <update id="deleteManageMenuAuth" parameterType="ManageMenuAuth">
        Update manage_menu_auth
        <set>
			delete_yn='Y',
			delete_id=#{deleteId},
			delete_date=now()
        </set>
        <where>
        	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authId)" >	and auth_id=#{authId}	</if>
        </where>
    </update>


	<select id="selectCountSmsRecivedUser" parameterType="HashMap" resultType="Integer">
		SELECT count(create_date) FROM sms_recived_user
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
		</where>
	</select>

    <select id="selectListSmsRecivedUser" parameterType="HashMap" resultType="SmsRecivedUser">
		SELECT @rownum:=@rownum+1 AS rownum,
			   a.user_email, u.user_mobile, use_yn, qna_yn, create_id, create_date, last_update_id, last_update_date
		  FROM sms_recived_user a
		  left join `user` u on a.user_email = u.user_email
		  join (SELECT @rownum:= 0) rnum
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(qnaYn)" >	and qna_yn=#{qnaYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(specialYn)" >	and special_yn=#{specialYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(financeYn)" >	and finance_yn=#{financeYn}	</if>
		</where>
	</select>

	<select id="selectOneSmsRecivedUser" parameterType="HashMap" resultType="SmsRecivedUser">
		SELECT a.user_email, u.user_mobile, use_yn, qna_yn, special_yn, finance_yn, create_id, create_date, last_update_id, last_update_date
		  FROM sms_recived_user
		  left join `user` u on a.user_email = u.user_email
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(qnaYn)" >	and qna_yn=#{qnaYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(specialYn)" >	and special_yn=#{specialYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(financeYn)" >	and finance_yn=#{financeYn}	</if>
		 </where>
	</select>

	<insert id="insertSmsRecivedUser" parameterType="SmsRecivedUser" useGeneratedKeys="true" keyProperty="menuId">
		INSERT INTO sms_recived_user
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(qnaYn)" >	qna_yn=#{qnaYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(specialYn)" >	special_yn=#{specialYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(financeYn)" >	finance_yn=#{financeYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId}, last_update_id=#{createId},	</if>
			create_date = now(),last_update_date = now()
		</set>
	</insert>

	<update id="updateSmsRecivedUser" parameterType="SmsRecivedUser">
		UPDATE sms_recived_user
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(qnaYn)" >	qna_yn=#{qnaYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(specialYn)" >	special_yn=#{specialYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(financeYn)" >	finance_yn=#{financeYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
		</where>
	</update>


	<delete id="deleteSmsRecivedUser" parameterType="HashMap">
        DELETE FROM sms_recived_user
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
		</where>
    </delete>




    <!--################################### manageMenuConnectAuth ###################################-->
	<select id="selectCountManageMenuConnectAuth" parameterType="HashMap" resultType="Integer">
		SELECT count(b.id)
		  FROM manage_menu_connect_auth a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authId)" >	and auth_id=#{authId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
		</where>
	</select>

	<select id="selectListManageMenuConnectAuth" parameterType="HashMap" resultType="ManageMenuConnectAuth">
		select *
		  from (
			SELECT @rownum:=@rownum+1 AS rownum
				, a.menu_id
				, a.auth_id
				, a.create_id
				, a.create_date
			  FROM manage_menu_connect_auth a
			  join (SELECT @rownum:= 0) rnum
			<where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authId)" >	and auth_id=#{authId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			</where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort=='menuId'" >	menu_id	</when>
					<when test="sort=='authId'" >	auth_id	</when>
					<when test="sort=='createId'" >	create_id	</when>
					<when test="sort=='createDate'" >	create_date	</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort=='menuId'" >	menu_id	</when>
					<when test="item.sort=='authId'" >	auth_id	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if> ) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneManageMenuConnectAuth" parameterType="HashMap" resultType="ManageMenuConnectAuth">
		SELECT a.menu_id
				, a.auth_id
				, a.create_id
				, a.create_date
		  FROM manage_menu_connect_auth a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authId)" >	and auth_id=#{authId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
		</where>
	</select>

	<insert id="insertManageMenuConnectAuth" parameterType="ManageMenuConnectAuth" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO manage_menu_connect_auth
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	menu_id=#{menuId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authId)" >	auth_id=#{authId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId}	</if>
			create_date=now()
        </set>
    </insert>

    <delete id="deleteManageMenuConnectAuth" parameterType="ManageMenuConnectAuth">
        delete from manage_menu_connect_auth
        <where>
        	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authId)" >	and auth_id=#{authId}	</if>
        </where>
    </delete>

</mapper>