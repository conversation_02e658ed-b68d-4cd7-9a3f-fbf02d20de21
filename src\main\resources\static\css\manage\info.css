/*
th { border-bottom: none !important; }
tr { background: #FFFFFF;}
td { height: 57px; font-weight: 400; font-size: 15px; color: #444444; }
button { background: none; border: none; }
p { margin: 0; padding: 0 }
select { -o-appearance: none; -webkit-appearance: none; -moz-appearance: none; appearance: none;}
label { margin-bottom: 0}
*/
.product-option-add-btn-box { width: 100%; text-align: end; }
.product-option-modify-btn {width: 60px; height: 30px; margin-right: 7px; font-weight: 500; font-size: 14px;color: #333333; border: 1px solid #B7B7B7;  border-radius: 5px; background: #EEEEEE;}
.product-option-delete-btn { width: 60px; height: 30px; font-weight: 500; font-size: 14px; color: #FFFFFF; border-radius: 5px; background: #383838; }
.w-80 { width: 80px; }
.product-option-add-modal-inner-box { width: 500px; height: 375px; background: #FFFFFF; }
.product-option-add-modal-top { display: flex; justify-content: space-between; align-items: center; height: 65px; padding: 0 30px; border-bottom: 1px solid #222222; }
.product-option-add-text { align-items: center; display: flex; min-width: 60px; margin-right: 11px; font-weight: 600; font-size: 20px; color: #222222; }
.category-text-main-box { padding: 30px; background: #F9F9F9; }
.product-option-category-text { min-width: 73px; font-weight: 600; font-size: 15px; color: #222222 }
.product-option-category-text-box { display: flex; align-items: center; margin-bottom: 15px; }
.btn-group-box { display: flex; justify-content: center; margin-top: 30px; }
.option-save-btn { width: 75px; height: 35px; margin-right: 10px; font: 600 14px Pretendard; border-radius: 5px; color: #FFFFFF; background-color: #0062D4 }
.option-close-btn { width: 75px; height: 35px; font: 600 14px Pretendard; border-radius: 5px; color: #FFFFFF; background-color: #383838 }
.option-delete-btn { width: 75px; height: 35px; margin-right: 10px; font: 600 14px Pretendard; border-radius: 5px; color: #FFFFFF; background-color: #D4445C }
.product-option-category-select {position: relative; width: 100%; height: 35px; padding: 8px 12px; font: 400 15px Pretendard; color: #444444;border: 1px solid #CCCCCC; background: #FFFFFF url('/images/icon/full-arrow-down.svg') no-repeat 98% 50%;}
.product-option-category-input {position: relative; width: 100%; height: 35px; padding: 8px 12px; font: 400 15px Pretendard; color: #444444;border: 1px solid #CCCCCC;}
.product-option-add-modal-box {display: none; justify-content: center; align-items: center;position: fixed; width: 100%;height: 100%; background-color: #0000009A; top: 0; z-index: 1038;}
.product-option-add-modal { width: 500px; height: 375px; background: #FFFFFF; }
.order-modify-btn { width: 102px; height: 35px; font-weight: 600; font-size: 14px; color: #FFFFFF; border: none; border-radius: 5px; background: #383838 }
.product-option-change-box { padding: 21px 30px; }
.product-option-order-change-modal { width: 437px; height: 594px;background: #FFFFFF; }
.product-option-order-change-modal-scroll-inner-box { height: 331px; overflow-y: scroll; }
.product-option-order-change-modal-inner-box { height: 375px; background: #FFFFFF; }
.product-option-order-change-modal-scroll-box { height: 370px; padding: 20px 0 30px 30px; padding-right: 20px; background-color: #F9F9F9; }
.move-box-text { font-weight: 600; font-size: 15px; color: #222222; text-align: center; }
.option-sort-item {position: relative; display: flex; justify-content: center; align-items: center; width: 343px; height: 60px;margin-bottom: 15px; background: #FFFFFF; filter: drop-shadow(2px 2px 2px rgba(0, 0, 0, 0.3)); opacity: 0.5}
.option-sort-item.active { opacity: 1; }
.product-option-change-btn-group-box { display: flex; justify-content: center; margin-top: 20px;}

.product-sales-date-modal { width: 1100px; height: 800px;background: #FFFFFF; }
.product-sales-date-modal-scroll-inner-box { width: 1100px; height: 800px; overflow-y: scroll; }
.product-sales-date-modal-inner-box { padding-bottom: 20px; background: #FFFFFF; }
.product-sales-date-modal-scroll-box { height: 370px; padding: 20px 0 30px 30px; padding-right: 20px; background-color: #F9F9F9; }

.product-rest-date-modal { width: max(1100px, 90vw); height: 800px;background: #FFFFFF; }
.product-rest-date-modal-inner-box { padding-bottom: 20px; background: #FFFFFF; }
.product-rest-date-modal-scroll-inner-box { width: max(1100px, 90vw); height: 800px; overflow-y: scroll; }
.product-rest-date-modal-scroll-box { height: 370px; padding: 20px 0 30px 30px; padding-right: 20px; background-color: #F9F9F9; }

/*
.period-table th.today,.period-table td.today { border-left: 2px solid var(--way-color-red); border-right: 2px solid var(--way-color-red); }
.period-table td.today{text-align: center;}
.period-table tr:nth-last-child td.today { border-bottom: 2px solid var(--way-color-red); }
*/

.period-table th.today { background-color: var(--way-color-red); }
.period-table td:nth-child(n+4){background-color: rgba(255, 0, 0, 0.1);}
.period-table td[data-sale="true"] {background-color: rgba(0, 255, 0, 0.1);}
.period-table td.rest-date { background-color: var(--way-color-lightgray);}
.period-table td.rest-date div { color: var(--way-color-red);display: flex;flex-direction: column;justify-content: center;align-items: center;}
