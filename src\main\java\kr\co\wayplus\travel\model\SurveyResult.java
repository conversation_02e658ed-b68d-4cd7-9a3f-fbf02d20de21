package kr.co.wayplus.travel.model;

import java.util.ArrayList;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@Data
public class SurveyResult extends CommonDataSet {
	private Integer id;	//고유 번호
	private String userEmail;	//사용자 아이디
	private String upperId;	//사용자분석 버전
	private String resultRecord;	//사용자분석 전체 응답
	private String analyticsShareUrl;	//외부 공유 URL
	private String recommandMenuIds;	//추천메뉴ID들

	//vurtial
	private int allcnt;
	private int yescnt;
}
