package kr.co.wayplus.travel.util;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.Base64.Decoder;

import javax.imageio.ImageIO;

import org.apache.poi.hssf.usermodel.HSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

public class ExcelUtil {
	public static final String BASE64_PNG_PRE_FIX = "data:image/png;base64,";
	   /**
     * Save the picture in excel
     * @param dataChart picture BASE64 format encoding
     * @param patriarch Excel-sheet canvas
     * @param wb Excel workbook
     * @param col1 specifies the starting cell row index
     * @param row1 specifies the starting cell column index
     * @param col2 specifies the ending cell row index
     * @param row2 specifies the ending cell column index
     * @throws Exception
     */
    @SuppressWarnings ( "Restriction" )
    public static void createPictureInExcel ( String DataChart , XSSFDrawing Patriarch , XSSFWorkbook WB , Short col1 ,  int ROW1 ,  Short col2 ,  int ROW2 )  throws Exception {
        //for BASE64 encoded byte array into
    	if(DataChart.startsWith(ExcelUtil.BASE64_PNG_PRE_FIX))
    		DataChart = DataChart.replaceAll(ExcelUtil.BASE64_PNG_PRE_FIX, "");

        Decoder base64Decoder = Base64.getDecoder();

        ByteArrayOutputStream dataChartoutStream = new  ByteArrayOutputStream ( ) ;
        //Use dataChartStringin as the input stream, read the picture and store it in the image
        ByteArrayInputStream dataChartin =  new ByteArrayInputStream(base64Decoder.decode(DataChart));
        BufferedImage dataChartbufferImg = ImageIO.read ( dataChartin ) ;
        //Use HSSFPatriarch to save the picture Write to EXCEL
        ImageIO.write( dataChartbufferImg ,  "png" , dataChartoutStream ) ;
        /*
         * Specify the location and size of the drawing area
         * HSSFClientAnchor(int dx1, int dy1, int dx2, int dy2, short col1, int row1, short col2, int row2)
         * Parameter Description:
         * dx1 dy1 The x and y coordinates in the starting cell.
         * dx2 dy2 The x and y coordinates in the ending cell.
         * col1,row1 specify the starting cell, and the subscript starts from 0.
         * col2,row2 specify the ending cell, and the subscript starts from 0.
         */
        XSSFClientAnchor anchorCostStr =  new XSSFClientAnchor ( 0 ,  0 ,  0 ,  0 , col1 , ROW1 , col2 , ROW2 ) ;
        //draw
        Patriarch.createPicture( anchorCostStr , WB.addPicture(dataChartoutStream.toByteArray() , XSSFWorkbook.PICTURE_TYPE_PNG ) ) ;
    }
}
