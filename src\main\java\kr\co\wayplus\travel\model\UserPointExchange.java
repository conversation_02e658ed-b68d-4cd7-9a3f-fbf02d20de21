package kr.co.wayplus.travel.model;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UserPointExchange extends CommonDataSet {
    private int rownum;
    private int id;
    private int userPointUsedLogId;
    private String userEmail;
    private String exchangeDate;
    private String exchangeCode;
    private String exchangeType;
    private int exchangePoint;
    private String pointExchangeReason;
    private int pointExchangeCount;
    private String pointExchangeConfirmYn;
    private String pointExchangeConfirmNote;
    private String createId;
    private String createDate;
    private String cancelCode;
    private String cancelId;
    private String cancelDate;

    private String userName;
}
