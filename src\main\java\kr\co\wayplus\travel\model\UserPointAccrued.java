package kr.co.wayplus.travel.model;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UserPointAccrued {
    private int id;
    private int userPointExchangeLogId;
    private Integer boardContentsId;
    private Integer boardCategoryId;
    private Integer boardCommentId;
    private Integer userFavoriteId;
    private Integer productCommentId;
    private String productSerial;
    private String paymentMoid;
    private String userEmail;
    private String accruedDate;
    private String accruedCode;
    private String accruedReason;
    private String accruedType;
    private String expireDate;
    private int pointAccrued;
    private int pointRemain;
    private String createId;
    private String createDate;
    private String cancelCode;
    private String cancelId;
    private String cancelDate;

    private String userName;
    private String duplicatePeriod;
}
