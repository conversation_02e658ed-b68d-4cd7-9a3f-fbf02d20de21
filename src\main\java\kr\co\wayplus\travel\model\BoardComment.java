package kr.co.wayplus.travel.model;

import java.util.List;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BoardComment extends CommonDataSet {
    private int id;
    private int boardId;
    private int commentId;
    private int contentId;
    private int tabIndex;
    private int upperId;
    private String note;
    private int warningId;
    private String blindYn;
    private String blindId;
    private String blindDate;
    private String applyCode;
    private String secretYn;
    private String createName;

    //청풍용
    private Integer menuId;
    private String userEmail;
    private String userName;
    private String userRole;
    private String expireDate;
    private String favoriteCalcType;
    private String userFavorite;
    private String userFavoriteId;
    private List<String> mentionsParam;
    private String mentions;

    public BoardComment(){
        this.id = 0;
        this.boardId = 0;
        this.contentId = 0;
        this.tabIndex = 0;
        this.upperId = 0;
        this.warningId = 0;
    }
}
