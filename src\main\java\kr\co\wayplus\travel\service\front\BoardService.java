package kr.co.wayplus.travel.service.front;

import kr.co.wayplus.travel.mapper.front.BoardMapper;
import kr.co.wayplus.travel.model.*;
import kr.co.wayplus.travel.service.manage.BadgeManageService;
import kr.co.wayplus.travel.service.user.UserPointService;
import kr.co.wayplus.travel.util.FileInfoUtil;
import kr.co.wayplus.travel.util.ImageUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import jakarta.servlet.http.HttpServletRequest;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
public class BoardService {

	@Value("${upload.file.path}")
	String externalImageUploadPath;

	final String addPath = "images/";
	final String addThumbnail = "thumb/";

	private final MenuService menuService;
	private final UserPointService userPointService;
	private final BadgeManageService badgeService;

    private final BoardMapper mapper;

	private final ImageUtil imageUtil;

    @Autowired
    public BoardService(BoardMapper mapper, UserPointService userPointService, MenuService menuService, BadgeManageService badgeService, ImageUtil imageUtil) {
        this.mapper = mapper;
		this.userPointService = userPointService;
		this.menuService = menuService;
		this.badgeService = badgeService;
		this.imageUtil = imageUtil;
    }

//	<!--################################### BoardContents ###################################-->
	public int selectCountBoardContents(HashMap<String, Object> paramMap) {
		return mapper.selectCountBoardContents(paramMap);
	}
	public int selectCountBoardContents(BoardContents bc) {
		return mapper.selectCountBoardContents(bc);
	}
	public ArrayList<BoardContents> selectListBoardContents(HashMap<String, Object> paramMap) {
		return mapper.selectListBoardContents(paramMap);
	}

	public int isMissionAlreadyCheck(HashMap<String, Object> paramMap) {
		return mapper.isMissionAlreadyCheck(paramMap);
	}
//	public ArrayList<BoardContents> selectListBoardContents(BoardContents bc) {
//		return mapperWayplusManage.selectListBoardContents(bc);
//	}
	public BoardContents selectOneBoardContents(HashMap<String, Object> paramMap) {
		return mapper.selectOneBoardContents(paramMap);
	}

	public void saveBoardContents(BoardContents bc) throws SQLException {
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("id", bc.getId());

		if( this.selectCountBoardContents(paramMap) == 0) {
			this.insertBoardContents(bc);
		} else {
			mapper.updateBoardContents(bc);
		}

	}

	@Transactional
	public void insertBoardContents(BoardContents bc) throws SQLException {
		mapper.insertBoardContents(bc);

		if ( bc.getMenuId() != null && bc.getMenuId() != 0 ) {
			this.insertBoardAddPoint(bc);
		}

		// 게시글 몇회 등록했었는지 체크.
		HashMap<String, Object> paramMap = new HashMap<>();

		//Caution: 미션 보드 하드코딩됨
		if(bc.getBoardId() == 7){
			paramMap.put("boardType", "mission");
			paramMap.put("automationType", "mission");
			paramMap.put("badgeId", bc.getUserBadgeSetId());
			paramMap.put("badgeType", "normal");
		} else if(bc.getBoardId() == 3){
			paramMap.put("boardType", "review");
			paramMap.put("automationType", "review");
		}
		else{
			paramMap.put("boardType", "board");
			paramMap.put("automationType", "board");
		}

		paramMap.put("boardId", bc.getBoardId());
		paramMap.put("userEmail", bc.getCreateId());
		paramMap.put("deleteYn", "N");
		int userWroteBoardContentsCount = mapper.selectCountWroteBoardContents(paramMap);

		ArrayList<BadgeContents> boardBadgeList = badgeService.selectBadgeListByAutomationType(paramMap);
		for(BadgeContents badge: boardBadgeList){
			if(userWroteBoardContentsCount >= badge.getBadgeAutomationCount()){
				badgeService.badgeAcquire(bc.getCreateId(), bc.getCreateId(), badge);
			}
		}

	}

	public void updateBoardContents(BoardContents bc) throws SQLException {
		mapper.updateBoardContents(bc);
	}
	public void restoreBoardContents(BoardContents bc) throws SQLException {
		mapper.restoreBoardContents(bc);
	}
	@Transactional
	public void deleteBoardContents(BoardContents bc) throws SQLException {
		mapper.deleteBoardContents(bc);

		//포인트 감소 부분
        HashMap<String, Object> pointParam = new HashMap<>();
		//포인트 추가된 경우가 board 의 카테고리인경우
		pointParam.put("boardId", bc.getBoardId());
		ArrayList<BoardCategory> boardCategoryList = mapper.selectListBoardCategory(pointParam);
		BoardCategory boardCategory = null;
		for (BoardCategory bCategory : boardCategoryList) {
			if ( bc.getCategoryId() == bCategory.getId() && bCategory.getPointUseYn() != null && bCategory.getPointUseYn().equals("Y") ) {
				boardCategory = bCategory;
				break;
			}
		}
		pointParam.clear();
		pointParam.put("boardContentsId", bc.getId());
		if ( boardCategory != null && boardCategory.getId() != 0 ) {
			pointParam.put("boardCategoryId", boardCategory.getId());
		}

        UserPointAccrued pointAccrued = userPointService.getUserPointAccruedById(pointParam);
		if (pointAccrued != null) {
			pointAccrued.setCancelCode("게시글 삭제");
			pointAccrued.setCancelId(bc.getCreateId());

			pointParam.put("accruedId", pointAccrued.getId());
			pointParam.put("userEmail", bc.getCreateId());
			UserPointRecord userPointRecord = userPointService.getOneUserPointRecord(pointParam);

			userPointService.cancelUserPoint(userPointRecord.getId(), pointAccrued);
		}

	}

//	<!--################################### BoardComment ###################################-->
	public void updateBoardContent_CommentCount(BoardComment data) throws SQLException {
		HashMap<String, Object> param = new HashMap<>();
		param.put("contentId", data.getContentId());

		mapper.updateBoardContent_CommentCount(param);
	}

	public BoardComment selectOneBoardComment(HashMap<String, Object> paramMap) {
		return mapper.selectOneBoardComment(paramMap);
	}

	public int selectCountBoardComment(HashMap<String, Object> param) {
		return mapper.selectCountBoardComment(param);
	}

	public ArrayList<BoardComment> selectListBoardComment(HashMap<String, Object> param) {
		return mapper.selectListBoardComment(param);
	}

	@Transactional
	public void insertBoardComment(BoardComment data) throws SQLException {
		mapper.insertBoardComment(data);

		this.insertBoardCommentAddPoint(data);
	}
	public void updateBoardComment(BoardComment data) throws SQLException {
		mapper.updateBoardComment(data);

		BoardContents bc = new BoardContents();
		bc.setId(data.getBoardId());
		bc.setBoardCommentFavoriteType("plus");
		mapper.updateBoardContents(bc);
	}

//	<!--################################### BoardSetting ###################################-->
	public BoardSetting selectOneBoardSetting(HashMap<String, Object> paramMap) {
		return mapper.selectOneBoardSetting(paramMap);
	}

	public BoardSetting selectOneBoardSettingByMenuId( Long menuId ) {
		HashMap<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("menuId", menuId );
		return mapper.selectOneBoardSetting(paramMap);
	}
//	<!--################################### BoardCategory ###################################-->
	public int selectCountBoardCategory(HashMap<String, Object> paramMap)  {
		return mapper.selectCountBoardCategory(paramMap);
	}
	public ArrayList<BoardCategory> selectListBoardCategory(HashMap<String, Object> paramMap) {
		return mapper.selectListBoardCategory(paramMap);
	}
	public BoardCategory selectOneBoardCategory(HashMap<String, Object> paramMap) {
		return mapper.selectOneBoardCategory(paramMap);
	}

    public void updateBoardCategoryMissionCompleteCount(HashMap<String, Object> param) {
        mapper.updateBoardCategoryMissionCompleteCount(param);
    }



//	<!--################################### BoardAttachFile ###################################-->제휴사
	public ArrayList<BoardAttachFile> selectListBoardAttachFile(HashMap<String, Object> paramMap) {
		return mapper.selectListBoardAttachFile(paramMap);
	}
	public BoardAttachFile selectOneBoardAttachFile(HashMap<String, Object> paramMap) {
		return mapper.selectOneBoardAttachFile(paramMap);
	}
	public void insertBoardAttachFile(BoardAttachFile baf) {
		mapper.insertBoardAttachFile(baf);
	}
	public void deleteBoardAttachFile(BoardAttachFile baf) {
		mapper.deleteBoardAttachFile(baf);
	}

	public ArrayList<BoardCategory> selectMostMissionMonth(HashMap<String, Object> param) {
        return mapper.selectMostMissionMonth(param);
    }

    public ArrayList<BoardContents> selectMostMissionUser(HashMap<String, Object> param) {
        return mapper.selectMostMissionUser(param);
    }

	public int selectCountWroteBoardContents(HashMap<String, Object> wroteBoardParam) {
        return mapper.selectCountWroteBoardContents(wroteBoardParam);
    }
	public ArrayList<BoardContents> selectListWroteBoardContents(HashMap<String, Object> wroteBoardParam) {
        return mapper.selectListWroteBoardContents(wroteBoardParam);
    }

	//	<!--################################### menuConnectBoard ###################################-->
	public MenuConnectBoard selectOneMenuConnectBoard(HashMap<String, Object> paramMap) {
		return mapper.selectOneMenuConnectBoard(paramMap);
	}

	//포인트 추가 서비스
	private void processBoardPointAdd(Integer id, Integer menuId, Integer categoryId, String userRole, String userEmail
								, String userName, String expireDate, String createId, String pointAddType) throws SQLException {
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("menuId", menuId);
		MenuUser menu = menuService.selectOneMenu(paramMap);
		UserPointSet userPointSet = new UserPointSet();
		UserPointAccrued pointAccrued = new UserPointAccrued();

		if (menu != null && menu.getPointUseYn() != null && menu.getPointUseYn().equals("Y")
			&& userRole.toUpperCase().equals("USER")) {
			switch (pointAddType) {
				case "board":
					MenuConnectBoard menuConnectBoard = mapper.selectOneMenuConnectBoard(paramMap);
					paramMap.put("boardId", menuConnectBoard.getBoardId());
					//포인트사용하는 카테고리가 있을 경우 확인. 우선순위 포인트
					ArrayList<BoardCategory> boardCategoryList = mapper.selectListBoardCategory(paramMap);
					BoardCategory boardCategory = null;
					for (BoardCategory bc : boardCategoryList) {
						if ( bc.getId() == categoryId ) {
							boardCategory = bc;
							break;
						}
					}

					if ( boardCategory != null && boardCategory.getPointUseYn() != null
						&& boardCategory.getPointUseYn().equals("Y")
						&& boardCategory.getUserPointSetId() != 0 ) {
						userPointSet = userPointService.getUserPointSettingById(String.valueOf(boardCategory.getUserPointSetId()));
						pointAccrued.setBoardContentsId(id);
						pointAccrued.setBoardCategoryId(boardCategory.getId());
					} else if (menu.getBoardPointId() != null
						&& menu.getBoardPointId() != 0 ) {
						userPointSet = userPointService.getUserPointSettingById(String.valueOf(menu.getBoardPointId()));
						pointAccrued.setBoardContentsId(id);
					}
					else { return; }
					break;
				case "comment":
					if (menu.getCommentPointId() != null
						&& menu.getCommentPointId() != 0 ) {
						userPointSet = userPointService.getUserPointSettingById(String.valueOf(menu.getCommentPointId()));
						pointAccrued.setBoardCommentId(id);
					}
					else { return; }
					break;
				case "userFavorite":
					if (menu.getUserFavoritePointId() != null
						&& menu.getUserFavoritePointId() != 0 ) {
						userPointSet = userPointService.getUserPointSettingById(String.valueOf(menu.getUserFavoritePointId()));
						pointAccrued.setUserFavoriteId(id);
					}
					else { return; }
					break;
			}

			if (userPointSet == null || userPointSet.getDeleteYn().equals("Y")) { return; }

			pointAccrued.setUserEmail(userEmail);
			pointAccrued.setUserName(userName);
			pointAccrued.setAccruedType(pointAddType);
			pointAccrued.setAccruedReason(userPointSet.getName());
			pointAccrued.setAccruedCode(userPointSet.getAccruedCode());
			pointAccrued.setPointAccrued(userPointSet.getAccruedPoint());
			pointAccrued.setExpireDate(userPointSet.getExpireDate());
			pointAccrued.setCreateId(createId);

			userPointService.createUserPoint(pointAccrued);
		}
	}
	public void insertBoardAddPoint(BoardContents bc) throws SQLException {
		processBoardPointAdd(bc.getId(),bc.getMenuId(), bc.getCategoryId(), bc.getUserRole(), bc.getCreateId(), bc.getUserName(), bc.getExpireDate(), bc.getCreateId(), "board");
	}
	public void insertBoardCommentAddPoint(BoardComment comment) throws SQLException {
		processBoardPointAdd(comment.getId(), comment.getMenuId(), 0, comment.getUserRole(), comment.getCreateId(), comment.getUserName(), comment.getExpireDate(), comment.getCreateId(), "comment");
	}

    public ArrayList<BoardContents> selectListGreethingContents(HashMap<String, Object> param) {
        return mapper.selectListGreethingContents(param);
    }

    public Object selectListBoardProjectCategory(HashMap<String, Object> paramMap) {
        return mapper.selectListBoardProjectCategory(paramMap);
    }

    public BoardSetting selectOneBoardSettingTypeCode(HashMap<String, Object> wroteReviewViewParam) {
        return mapper.selectOneBoardSettingTypeCode(wroteReviewViewParam);
    }

    public ArrayList<BoardSetting> selectListBoardSettingTypeCode(HashMap<String, Object> wroteBoardViewParam) {
        return mapper.selectListBoardSettingTypeCode(wroteBoardViewParam);
    }

	public int selectTotalCommentCount(HashMap<String, Object> paramMap) {
        return mapper.selectTotalCommentCount(paramMap);
    }

    public List<Map<String, Object>> selectListTotalComment(HashMap<String, Object> paramMap) {
        return mapper.selectListTotalComment(paramMap);
    }

	public void updateBoardComments(HashMap<String, Object> paramMap) {
		mapper.updateBoardComments(paramMap);
	}

	public void deleteBoardComments(HashMap<String, Object> paramMap) {
		mapper.deleteBoardComments(paramMap);
	}

	public List<Map<String, Object>> selectCommentReply(HashMap<String, Object> paramMap) {
		return mapper.selectCommentReply(paramMap);
	}

	public Map<String, Object> selectOneCommentReply(HashMap<String, Object> paramMap) {
		return mapper.selectOneCommentReply(paramMap);
	}

	public int selectCommentReplyCount(HashMap<String, Object> paramMap) {
		return mapper.selectCommentReplyCount(paramMap);
	}

	public List<String> selectOneCommentMentions(HashMap<String, Object> paramMap) {
		return mapper.selectOneCommentMentions(paramMap);
	}

	@Transactional
	public void insertBoardContentsService(BoardContents bc, String mode, HttpServletRequest request) throws SQLException, IllegalStateException, IOException {
		HashMap<String, Object> paramMap = new HashMap<>();
		
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			String userEmail = "";
    		String thumbnailUrl = "";
			String newThumbnail = "";

    		if(_user instanceof LoginUser) {
				LoginUser user = (LoginUser)_user;
				if(bc.getCreateId() == null) {
					userEmail = user.getUserEmail();
				} else {
					userEmail = bc.getCreateId();
				}
				bc.setUserRole(user.getUserRole());
				bc.setUserName(user.getUserName());
			} else {
				userEmail = "guest_"+ bc.getGuestName()+"_"+ bc.getGuestPass();
			}

    		if(mode.equals("I")) {
    			bc.setCreateId( userEmail );

    			if(bc.getBoardId() == 7 && bc.getMissionCheckYn().equals("N") ) {
					

    				if( bc.getUserPointSetId() != 0  ) {
    					UserPointSet up = userPointService.getUserPointSettingById( bc.getUserPointSetId().toString() );
    					userPointService.createUserPoint(up, bc.getCreateId() , userEmail, bc.getId() );
    				}

    				if( bc.getUserBadgeSetId() != 0  ) {
    					BadgeAcquireHistory badgeAcquireHistory = new BadgeAcquireHistory();

    					HashMap<String, Object> param = new HashMap<>();
    					param.put("userEmail", bc.getCreateId());
    					param.put("badgeId", bc.getUserBadgeSetId());
    					if(badgeService.selectCountBadgeAcquireHistory(param) == 0){
    						badgeAcquireHistory.setUserEmail( bc.getCreateId() );
    						badgeAcquireHistory.setBadgeId( bc.getUserBadgeSetId() );
    						badgeAcquireHistory.setCreateId( userEmail );

							HashMap<String, Object> badgeParam = new HashMap<>();
							badgeParam.put("badgeId", bc.getUserBadgeSetId());
							badgeParam.put("badgeType", "normal");
							//뱃지 badge_automation_count체크
							BadgeContents badge = badgeService.selectOneBadgeContents(badgeParam);
							int badgeAutomationCount = 0;
							if (badge != null) {
								badgeAutomationCount = badge.getBadgeAutomationCount();
							}
							//badge_automation_count -1 과 지금 등록한 미션 게시글의 개수 비교
//							BoardContents missionBoard = new BoardContents();
//							missionBoard.setMissionCheckYn("YN");
//							missionBoard.setUserEmail(getBaseUserEmail());

							HashMap<String, Object> paramBc = new HashMap<>();
							paramBc.put("missionCheckYn", "YN");
							paramBc.put("userEmail", userEmail);

							//같을때만 배지부여.
							int wroteBcCount = 	this.selectCountBoardContents(paramBc);
							if (wroteBcCount == badgeAutomationCount -1) { //미션지는 이제 등록될것이기 때문에 미리 -1 시킴.
								badgeService.writeBadgeAcquireHistory(badgeAcquireHistory);
							}
    					}
    				}
    			}

				//숙소 이용후기 포인트 적립
				// if (bc.getBoardType() != null && bc.getBoardType().equals("review")) {
				// 	String currentUserEmail = userEmail;
				// 	if (currentUserEmail == null) {
				// 		// 사용자 이메일이 null인 경우 예외를 발생시켜 포인트 적립을 막습니다.
				// 		throw new IllegalStateException("User email for review point accrual is null.");
				// 	}
				// 	UserPointAccrued pointAccrued = new UserPointAccrued();
				// 	pointAccrued.setAccruedType("review");
				// 	pointAccrued.setAccruedReason("숙소 이용후기 작성");
				// 	pointAccrued.setExpireDate(null);
				// 	pointAccrued.setPointAccrued(5000);
				// 	pointAccrued.setPointRemain(5000);
				// 	pointAccrued.setUserEmail(currentUserEmail);
				// 	pointAccrued.setCreateId("admin");

				// 	userPointService.createUserPoint(pointAccrued);
				// }

    			this.insertBoardContents(bc);
    			/*
				if (bc.getMissionType() != null) {
					HashMap<String, Object> param = new HashMap<>();
					param.put("categoryId", bc.getCategoryId());
					param.put("boardId", bc.getBoardId());
					boardService.updateBoardCategoryMissionCompleteCount(param);
				}
				*/
    		} else {
    			bc.setLastUpdateId( userEmail);
    			this.updateBoardContents(bc);
    		}


    		List<MultipartFile> multipartFiles = null;

    		Object obj = request.getParameterValues("deletes");

    		if(request.getParameterValues("deletes") != null) {
    			String[] deletes = request.getParameterValues("deletes");

    			for (String strDeletes : deletes) {
    				paramMap.clear();
    				paramMap.put("fileId", strDeletes);

    				BoardAttachFile _old = this.selectOneBoardAttachFile( paramMap );
    				if(_old != null) {
    					FileInfoUtil.deleteImageFile_real(_old);
    					this.deleteBoardAttachFile(_old);
    				}
				}
    		}

			// MultipartHttpServletRequest인 경우에만 파일 처리 로직 실행
			if (request instanceof MultipartHttpServletRequest) {
				MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
				// 파일 처리 로직
				if(multipartRequest.getFile("attachs") != null) {
					multipartFiles = multipartRequest.getFiles("attachs");


					MultipartFile multipartFilesThumbnail = multipartRequest.getFile("thumbnail");

					if (multipartFiles.size() > 0) {
						File file = new File(externalImageUploadPath + addPath);
						if (!file.exists()) file.mkdirs();

						for (MultipartFile multipartFile : multipartFiles) {
							String uploadName = UUID.randomUUID().toString();
							multipartFile.transferTo(new File(externalImageUploadPath+ addPath + uploadName));

							BoardAttachFile baf = new BoardAttachFile();
							baf.setBoardId(bc.getBoardId());
							baf.setContentId(bc.getId());
							baf.setServiceType("board");
							baf.setUploadPath(externalImageUploadPath+ addPath);
							baf.setUploadFilename(uploadName);
							baf.setOriginFilename(multipartFile.getOriginalFilename());
							baf.setFileSize((int) multipartFile.getSize());
							baf.setFileMimetype(multipartFile.getContentType());
							if (multipartFile.getOriginalFilename().contains(".")) {
								baf.setFileExtension(multipartFile.getOriginalFilename().substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
							}

							paramMap.clear();
							paramMap.put("contentId", bc.getId());

							if (userEmail != null) {
								baf.setUploadId( userEmail );
							}

							this.insertBoardAttachFile(baf);

							if(multipartFilesThumbnail != null) {
								if(multipartFile.getOriginalFilename().equals( multipartFilesThumbnail.getOriginalFilename() )) {
									thumbnailUrl = "/upload/"+ addPath +uploadName;
								}
							}

							if( multipartFilesThumbnail == null && multipartRequest.getParameter("newThumbnail") != null) {
								if( multipartFile.getOriginalFilename().equals(multipartRequest.getParameter("newThumbnail")) ) {
									newThumbnail = "/upload/"+ addPath + addThumbnail + uploadName;
									imageUtil.resizeImage(externalImageUploadPath+ addPath + uploadName, externalImageUploadPath+ addPath+ addThumbnail + uploadName);
									
									//newThumbnail = "/upload/"+ addPath +  uploadName;
									//ImageUtil.resizeImage(multipartFile,"/upload/"+ addPath + addThumbnail,uploadName, 300,300 );
								}
							}
						}
					}
				}

				boolean isThum = false;

				if(request.getParameter("thumbnail") != null ) {
					isThum = true;
					thumbnailUrl = request.getParameter("thumbnail");
				} else if( thumbnailUrl.length() != 0 ) {
					isThum = true;
				}

				if(isThum) {
					bc.setThumbnailUrl(thumbnailUrl);
					this.updateBoardContents(bc);
				}

				if(request.getParameter("thumbnail") == null && request.getParameter("newThumbnail") != null ) {
					bc.setThumbnailUrl(newThumbnail);
					this.updateBoardContents(bc);
				}
			}
	}
}
