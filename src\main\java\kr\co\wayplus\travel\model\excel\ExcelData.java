package kr.co.wayplus.travel.model.excel;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import kr.co.wayplus.travel.builder.ExcelDataBuilder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ExcelData {
    private String id;                // 데이터 식별자
    private Map<String, Object> data; // 실제 데이터 값
    private Map<String, String> format; // 데이터 포맷 정보
    private Map<String, ExcelDataBuilder.CellStyle> styles;

    public ExcelData() {
        this.data = new LinkedHashMap<>();
        this.format = new HashMap<>();
        this.styles = new HashMap<>();
    }

    public void addValue(String key, Object value) {
        this.data.put(key, value);
    }

    public void addFormat(String key, String format) {
        this.format.put(key, format);
    }

    public Object getValue(String key) {
        return data.get(key);
    }

    public String getFormat(String key) {
        return format.get(key);
    }

    public void setStyles(Map<String, ExcelDataBuilder.CellStyle> styles) {
        this.styles = styles;
    }

    public Map<String, ExcelDataBuilder.CellStyle> getStyles() {
        return styles;
    }
}