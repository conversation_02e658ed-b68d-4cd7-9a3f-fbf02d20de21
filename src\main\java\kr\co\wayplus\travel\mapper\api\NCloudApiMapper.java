package kr.co.wayplus.travel.mapper.api;

import java.util.ArrayList;
import java.util.HashMap;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import kr.co.wayplus.travel.model.AlertMessageLog;
import kr.co.wayplus.travel.model.LogMessageSend;

@Mapper
@Repository
public interface NCloudApiMapper {

	int selectCountLogMessageSend(HashMap<String, Object> paramMap);

	ArrayList<AlertMessageLog> selectListLogMessageSend(HashMap<String, Object> paramMap);

	AlertMessageLog selectOneLogMessageSend(HashMap<String, Object> paramMap);

    void insertSmsSendRequestLog(LogMessageSend smsMessageContainer);

    void updateSmsSendRequestLogResult(LogMessageSend log);
/*
    void insertAuthenticationMessage(MessageAuthentication authentication);

    void updateAuthenticationMessageExpired(MessageAuthentication authentication);

    void updateAuthenticationMessageSendResult(MessageAuthentication authentication);

    MessageAuthentication selectAuthenticationMessageNumber(HashMap<String, Object> param);

    void updateAuthenticationMessageAuthResult(HashMap<String, Object> param);
*/



}
