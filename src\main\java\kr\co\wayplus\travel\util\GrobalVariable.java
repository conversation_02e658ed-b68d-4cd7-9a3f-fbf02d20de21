//package kr.co.wayplus.travel.util;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
//import kr.co.wayplus.travel.model.MenuUser;
//import lombok.Getter;
//import lombok.Setter;
//
//
//@Component
//public class GrobalVariable {
//    private final Logger logger = LoggerFactory.getLogger(getClass());
//
//    private static ArrayList<MenuUser> gbMenuUserTreeTop;
//    private static HashMap<String,MenuUser> gbMenuUserMapByMenuId;
//    private static HashMap<String,MenuUser> gbMenuUserMapByFullMenuUrl;
//
//    public GrobalVariable(){
//    }
//
//	public static ArrayList<MenuUser> getGbMenuUserTreeTop() {
//		return gbMenuUserTreeTop;
//	}
//
//	public static void setGbMenuUserTreeTop(ArrayList<MenuUser> gbMenuUserTreeTop) {
//		GrobalVariable.gbMenuUserTreeTop = gbMenuUserTreeTop;
//	}
//
//	public static HashMap<String,MenuUser> getGbMenuUserMapByMenuId() {
//		return gbMenuUserMapByMenuId;
//	}
//
//	public static void setGbMenuUserMapByMenuId(HashMap<String,MenuUser> gbMenuUserMap) {
//		GrobalVariable.gbMenuUserMapByMenuId = gbMenuUserMap;
//	}
//
//	public static HashMap<String,MenuUser> getGbMenuUserMapByFullMenuUrl() {
//		return gbMenuUserMapByFullMenuUrl;
//	}
//
//	public static void setGbMenuUserMapByFullMenuUrl(HashMap<String,MenuUser> gbMenuUserMapByFullMenuUrl) {
//		GrobalVariable.gbMenuUserMapByFullMenuUrl = gbMenuUserMapByFullMenuUrl;
//	}
//
//	public static MenuUser getMenuUserByFullMenuUrl( String strFullMenuUrl ) {
//		MenuUser menu = gbMenuUserMapByFullMenuUrl.get( strFullMenuUrl );
//		System.out.println( menu );
//
//		return menu;
//	}
//
//}
