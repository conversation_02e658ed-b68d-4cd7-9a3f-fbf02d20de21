<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.manage.IslandlifeManageMapper">
	<!--################################### island_life ###################################-->
	<select id="selectCountIslandlife" parameterType="HashMap" resultType="Integer">
		SELECT count(id) FROM island_life
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and title=#{title}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	and content=#{content}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		 </where>
	</select>

	<select id="selectListIslandlife" parameterType="HashMap" resultType="Islandlife">
		SELECT *
		  FROM(
			SELECT @rownum:=@rownum+1 AS rownum,
				   id, title, sub_title, content, islandlife_thumbnail,
				   create_id, create_date, last_update_id, last_update_date, use_yn, delete_yn, delete_id, delete_date,
				   select_color
			  FROM island_life a
			  join (SELECT @rownum:= 0) rnum
			<where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and title=#{title}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subTitle)" >	sub_title=#{subTitle},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	and content=#{content}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
			</where>

			 <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
				ORDER BY
				<choose>
					<when test="sort=='id'" >	id	</when>
					<when test="sort=='title'" >	title	</when>
					<when test="sort=='subTitle'" >	sub_title	</when>
					<when test="sort=='content'" >	content	</when>
					<when test="sort=='createId'" >	create_id	</when>
					<when test="sort=='createDate'" >	create_date	</when>
					<when test="sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="sort=='useYn'" >	use_yn	</when>
					<when test="sort=='deleteYn'" >	delete_yn	</when>
					<when test="sort=='deleteId'" >	delete_id	</when>
					<when test="sort=='deleteDate'" >	delete_date	</when>

					<otherwise>rownum</otherwise>
				</choose>
				<choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
				ORDER BY <foreach item="item" index="index" collection="listSort" separator=",">
				<choose>
					<when test="item.sort=='id'" >	id	</when>
					<when test="item.sort=='title'" >	title	</when>
					<when test="item.sort=='subTitle'" >	sub_title	</when>
					<when test="item.sort=='content'" >	content	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="item.sort=='useYn'" >	use_yn	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='deleteId'" >	delete_id	</when>
					<when test="item.sort=='deleteDate'" >	delete_date	</when>
				</choose>
				<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>

			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
		 LIMIT #{itemStartPosition}, #{pagePerSize}
		 </if>
	</select>

	<select id="selectOneIslandlife" parameterType="HashMap" resultType="Islandlife">
		SELECT id, title, content, islandlife_thumbnail,
			   create_id, create_date, last_update_id, last_update_date, use_yn, delete_yn, delete_id, delete_date
		  FROM island_life a
		 <where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and title=#{title}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subTitle)" >	sub_title=#{subTitle},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	and content=#{content}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		 </where>
	</select>

	<insert id="insertIslandlife" parameterType="Islandlife" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO island_life
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	title=#{title},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subTitle)" >	sub_title=#{subTitle},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	content=#{content},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(selectColor)" >	select_color=#{selectColor},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(islandlifeThumbnail)" >	islandlife_thumbnail=#{islandlifeThumbnail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	delete_yn=#{deleteYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			create_date = now(),last_update_date = now()
		</set>
	</insert>

	<update id="updateIslandlife" parameterType="Islandlife">
		UPDATE island_life
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	title=#{title},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subTitle)" >	sub_title=#{subTitle},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	content=#{content},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(selectColor)" >	select_color=#{selectColor},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(islandlifeThumbnail)" >	islandlife_thumbnail=#{islandlifeThumbnail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	delete_yn=#{deleteYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			create_date = now(),last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
		</where>
	</update>

	<update id="deleteIslandlife" parameterType="Islandlife">
		UPDATE island_life
		<set>
			delete_yn='Y',last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
		</where>
	</update>

<!--################################### ContentsAttachFile ###################################-->
	<select id="selectCountIslandlifeAttachFile" parameterType="HashMap" resultType="Integer">
		SELECT count(id)
		  FROM island_life_attach_file
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType) and
					  @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey) ">
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType=='userEmail'" >and title LIKE CONCAT('%', #{searchKey}, '%')</if>
			</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(islandlifeId)" >	and islandlife_id=#{islandlifeId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>
			and delete_yn = 'N'
		</where>
	</select>

	<select id="selectListIslandlifeAttachFile" parameterType="HashMap" resultType="IslandlifeAttachFile">
		select *
		  from (
			SELECT @rownum:=@rownum+1 AS rownum,
			       file_id, islandlife_id, service_type, upload_path, upload_filename, file_extension, file_size, file_mimetype, origin_filename, upload_id, upload_date
			  FROM island_life_attach_file
			  join (SELECT @rownum:= 0) rnum
			<where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType) and
						  @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey) ">
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType=='userEmail'" >and title LIKE CONCAT('%', #{searchKey}, '%')</if>
				</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(islandlifeId)" >	and islandlife_id=#{islandlifeId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>

			</where> ) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneIslandlifeAttachFile" parameterType="HashMap" resultType="IslandlifeAttachFile">
		SELECT file_id, islandlife_id, service_type, upload_path, upload_filename, file_extension, file_size, file_mimetype, origin_filename, upload_id, upload_date
		  FROM island_life_attach_file
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(islandlifeId)" >	and islandlife_id=#{islandlifeId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>
		</where>
	</select>

    <insert id="insertIslandlifeAttachFile" parameterType="IslandlifeAttachFile" useGeneratedKeys="true" keyProperty="fileId">
        INSERT INTO island_life_attach_file
               (
	                islandlife_id, service_type,
	                upload_path, upload_filename,
	                file_extension, file_size, file_mimetype,
	                origin_filename,
	                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	upload_id,	</if>
	                 upload_date
                )
        VALUES (
                   #{islandlifeId}, #{serviceType},
                   #{uploadPath}, #{uploadFilename},
                   #{fileExtension}, #{fileSize}, #{fileMimetype},
                   #{originFilename},
                   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	#{uploadId},	</if>
                    now()
               )
    </insert>

    <delete id="deleteIslandlifeAttachFile" parameterType="IslandlifeAttachFile">
		delete from island_life_attach_file
		 WHERE file_id = #{fileId}
	</delete>

</mapper>