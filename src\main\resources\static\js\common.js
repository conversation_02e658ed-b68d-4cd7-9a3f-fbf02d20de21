var separator= ' ~ ';
var _daterangepickerOptionSingleOnlyDate = {
		minYear: 1999,
		maxDate:moment().add(-1, 'hour'),
		autoUpdateInput: true,
		singleDatePicker:true,
		showDropdowns: true,
		showCustomRangeLabel:false,
		timePicker: false,
		timePicker24Hour: false,
		timePickerMinutes:false,
		timePickerSeconds:false,
		drops:"auto",
		locale: {
			format: 'YYYY-MM-DD',
			applyLabel: "적용",
			cancelLabel: "취소",
			daysOfWeek: ["일", "월", "화", "수", "목", "금", "토"],
			monthNames: ["1월", "2월", "3월", "4월", "5월", "6월", "7월", "8월", "9월", "10월", "11월", "12월"],
			separator:separator
		},
		ranges:null
	};
var _daterangepickerOptionSingleDateTime = {
		minYear: 1999,
		maxDate:moment().add(-1, 'hour'),
		autoUpdateInput: true,
		singleDatePicker:true,
		showDropdowns: true,
		showCustomRangeLabel:false,
		timePicker: true,
		timePicker24Hour: true,
		timePickerMinutes:true,
		timePickerSeconds:true,
		drops:"auto",
		locale: {
			format: 'YYYY-MM-DD HH:mm:ss',
			applyLabel: "적용",
			cancelLabel: "취소",
			daysOfWeek: ["일", "월", "화", "수", "목", "금", "토"],
			monthNames: ["1월", "2월", "3월", "4월", "5월", "6월", "7월", "8월", "9월", "10월", "11월", "12월"],
			separator:separator
		},
		ranges:null
	};
var _daterangepickerOptionRangeOnlyDate = {
		minYear: 1999,
		maxDate:moment().add(-1, 'hour'),
		autoUpdateInput: true,
		singleDatePicker:false,
		showDropdowns: true,
		showCustomRangeLabel:false,
		timePicker: false,
		timePicker24Hour: true,
		timePickerMinutes:false,
		timePickerSeconds:false,
		drops:"auto",
		locale: {
			format: 'YYYY-MM-DD',
			applyLabel: "적용",
			cancelLabel: "취소",
			daysOfWeek: ["일", "월", "화", "수", "목", "금", "토"],
			monthNames: ["1월", "2월", "3월", "4월", "5월", "6월", "7월", "8월", "9월", "10월", "11월", "12월"],
			separator:separator
		},
		ranges:null
	};
var _daterangepickerOptionRangeDateTime = {
		minYear: 1999,
		maxDate:moment().add(-1, 'hour'),
		autoUpdateInput: true,
		singleDatePicker:false,
		showDropdowns: true,
		showCustomRangeLabel:false,
		timePicker: true,
		timePicker24Hour: true,
		timePickerMinutes:true,
		timePickerSeconds:true,
		drops:"auto",
		locale: {
			format: 'YYYY-MM-DD HH:mm:ss',
			applyLabel: "적용",
			cancelLabel: "취소",
			daysOfWeek: ["일", "월", "화", "수", "목", "금", "토"],
			monthNames: ["1월", "2월", "3월", "4월", "5월", "6월", "7월", "8월", "9월", "10월", "11월", "12월"],
			separator:separator
		},
		ranges:null
	};

var _timeoption = {timeFormat: 'H:mm',interval: 5,defaultTime: '09',startTime: '09:00',dynamic: true,dropdown: true,scrollbar: true};

/*
 * 경로(servletpath)를 얻어온다.
 * ex)
 * http://localhost:8080/board/notice.cs?action=view&articeId=1
 * 결과 : /board/notice.cs
 */
function getServletPath() {
	var servletPath = document.URL;
	servletPath = servletPath.substr(servletPath.indexOf("/", servletPath.indexOf("//")+2));
	if(servletPath.indexOf("?") != -1) {
		servletPath = servletPath.substr(0, servletPath.indexOf("?"));
	}
	servletPath = servletPath.replace("#;","");
	return servletPath;
}

/*
 * 경로(servletpath)+쿼리스트링을 얻어온다.
 * ex)
 * http://localhost:8080/board/notice.cs?action=view&articeId=1
 * 결과 : /board/notice.cs?action=view&articeId=1
 */

function getServletReferrer() {
	var servletPath = document.referrer;
	servletPath = servletPath.substr(servletPath.indexOf("/", servletPath.indexOf("//")+2));
	if(servletPath.indexOf("?") != -1) {
		servletPath = servletPath.substr(0, servletPath.indexOf("?"));
	}
	servletPath = servletPath.replace("#;","");
	return servletPath;
}

function getServletPathQueryString() {
	var servletPath = document.URL;
	servletPath = servletPath.substr(servletPath.indexOf("/", servletPath.indexOf("//")+2));
	servletPath = servletPath.replace("#;","");
	return servletPath;
}

function getSearchCondition (form) {
	if (window.sessionStorage) {
		var query = "";
		var element;
		for(var i=0; i<form.length; i++){
			element = form.elements [form[i].name];
			if(element == null || element.value == '') continue;

			query += form[i].name + "=" + element.value + "&";
		}
		sessionStorage.setItem("searchCondition", query);
	}
}

function setSearchCondition (form) {
	if (window.sessionStorage) {
		var searchCondition = null;
		var searchCondition = sessionStorage.getItem("searchCondition");
		if(searchCondition != null || searchCondition == "object"){
//			console.log(searchCondition);
			var searchConditionList = searchCondition.split("&");
			for(idx in searchConditionList) {
				var val = searchConditionList[idx];
				var valArray = val.split("=");
				if(valArray[0] != '') {
					var element = form.elements[valArray[0]];
					element.value = valArray[1];
				}
			}
		}
	}
	sessionStorage.setItem("searchCondition", "");
}

//지정된 폼의 req class 항목들의 값 입력 여부 확인
function checkForm(formId){
	var result = true;
	$("#" + formId).find(".req:not([disabled='disabled']):not([disabled]):not(label):not(.disabled)").each(function(){
		var tagName = $(this).prop('tagName');
		var dataName = $(this).data("name");

		if(dataName === undefined){
			console.error( `#${$(this).attr('id')} 'data-name' 누락!!!` );
		}

		console.info( `${tagName} ${dataName}` );

		if(tagName =='INPUT' && isEmptyString($(this).val())){ //정규식 체크하기
			alert(dataName+" 입력하세요!");
			result = false;
			$(this).addClass('is-invalid').focus();
			return false;
		} else if(tagName =='SELECT' && $('option:selected',$(this)).index() === 0){
			alert(dataName+" 선택하세요!");
			result = false;
			$(this).addClass('is-invalid').focus();
			return false;
		} else if(tagName =='TEXTAREA' && isEmptyString($(this).val())){
			alert(dataName+"을 입력하세요!");
			result = false;
			$(this).addClass('is-invalid').focus();
			return false;
		}
	});

	$("#" + formId).find("input.req:invalid").each(function(){
		alert($(this).data("name")+" 확인하십시오!");
		result = false;
		$(this).focus();
		console.log('case 2', result);
		return false;
	});


	return result;
}

function replaceNewline(str){
	return str.replaceAll(/(?:\r\n|\r|\n)/g, '<br />');
}

// 빈문자열 검사
function isEmptyString (value){
	return value == null || (/^\s*$/).test (value) ;
}

// 아이디를 사용한 빈문자열 검사
function isEmptyStringId (id) {
	return isEmptyString ($("#" + id).val ());
}

//Thread.sleep
function sleep(num){	//[1/1000초]
	var now = new Date();
	var stop = now.getTime() + num;
	while(true){
		now = new Date();
		if(now.getTime() > stop)return;
	}
}

//시작일과 종료일이 오늘안에 존재하는지 확인
function checkTodayIn(startDtm, endDtm) {
	var today = new Date();

	var startDate = new Date(startDtm);
	var endDate = new Date(endDtm);

	if (today.getTime() > startDate.getTime() && today.getTime() < endDate.getTime()) {
		return true;
	}else {
		return false;
	}
}

//쿠키정보를 조회한다.
function getCookie( name ) {
	var nameOfCookie = name + "=";
	var x = 0;
	while ( x <= document.cookie.length ) {
		var y = (x+nameOfCookie.length);
		if ( document.cookie.substring( x, y ) == nameOfCookie ) {
			if ( (endOfCookie=document.cookie.indexOf( ";", y )) == -1 )
				endOfCookie = document.cookie.length;

			return unescape( document.cookie.substring( y, endOfCookie ) );
		}
		x = document.cookie.indexOf( " ", x ) + 1;
		if ( x == 0 )
		break;
	}
	return "";
}

//쿠키정보를 저장한다.
function setCookie( name, value, expiredays ) {
	var todayDate = new Date();
	todayDate.setDate( todayDate.getDate() + expiredays );
	document.cookie = name + "=" + escape( value ) + "; path=/; expires=" + todayDate.toGMTString() + ";"
}

Date.prototype.format = function(f) {
    if (!this.valueOf()) return " ";

    var weekName = ["일요일", "월요일", "화요일", "수요일", "목요일", "금요일", "토요일"];
    var d = this;

    return f.replace(/(yyyy|yy|MM|dd|E|hh|mm|ss|a\/p)/gi, function($1) {
        switch ($1) {
            case "yyyy": return d.getFullYear();
            case "yy": return (d.getFullYear() % 1000).zf(2);
            case "MM": return (d.getMonth() + 1).zf(2);
            case "dd": return d.getDate().zf(2);
            case "E": return weekName[d.getDay()];
            case "HH": return d.getHours().zf(2);
            case "hh": return ((h = d.getHours() % 12) ? h : 12).zf(2);
            case "mm": return d.getMinutes().zf(2);
            case "ss": return d.getSeconds().zf(2);
            case "a/p": return d.getHours() < 12 ? "오전" : "오후";
            default: return $1;
        }
    });
};
String.prototype.string = function(len){var s = '', i = 0; while (i++ < len) { s += this; } return s;};
String.prototype.zf = function(len){return "0".string(len - this.length) + this;};
Number.prototype.zf = function(len){return this.toString().zf(len);};

/**
 * 3자리마다 콤마(,)를 찍는다.
 * @param x
 * @returns
 */
function numberWithCommas(x) {
	if( x != undefined ){
		return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
	} else {
		return 0;
	}

}
function numberWithUncomma(x) {
    return String(x).replace(/[^\d]+/g, '');
}
function inputNumberFormat(obj, callback=undefined) {
    obj.value = numberWithCommas(numberWithUncomma(obj.value));
	if(callback !== undefined){
		//console.log(obj, obj.value, callback);
		callback(obj);
	}

}

/**
 * 좌측문자열채우기
 * @params
 *  - str : 원 문자열
 *  - padLen : 최대 채우고자 하는 길이
 *  - padStr : 채우고자하는 문자(char)
 */
function lpad(str, padLen, padStr) {
    if (padStr.length > padLen) {
        console.log("오류 : 채우고자 하는 문자열이 요청 길이보다 큽니다");
        return str;
    }
    str += ""; // 문자로
    padStr += ""; // 문자로
    while (str.length < padLen)
        str = padStr + str;
    str = str.length >= padLen ? str.substring(0, padLen) : str;
    return str;
}

/**
 * 우측문자열채우기
 * @params
 *  - str : 원 문자열
 *  - padLen : 최대 채우고자 하는 길이
 *  - padStr : 채우고자하는 문자(char)
 */
function rpad(str, padLen, padStr) {
    if (padStr.length > padLen) {
        console.log("오류 : 채우고자 하는 문자열이 요청 길이보다 큽니다");
        return str + "";
    }
    str += ""; // 문자로
    padStr += ""; // 문자로
    while (str.length < padLen)
        str += padStr;
    str = str.length >= padLen ? str.substring(0, padLen) : str;
    return str;
}

//전화번호 포맷팅
function checkTelNum(telNum){

	if(telNum != null){
		return telNum.replace(/(^02.{0}|^01.{1}|[0-9]{3})([0-9]+)([0-9]{4})/,"$1-$2-$3");
	}else{
		return "";
	}
}

function searchReset(){
	var targetClass = 'search-body';
	var strParams = "";

	$('input, select','.'+targetClass).each(function(index, element){
//		console.log(index, element);
		var el = $(element);

		var target = (typeof el.attr('id') != 'undefined'? el.attr('id') : el.attr('name'));
		var attr = (typeof el.attr('id') == 'undefined'? 'n' : 'i');
		var type = "";
		var data = el.val();
		var gubn = "";

		if(el.is('input')){
			type = 'ip';
			$(this).val("");
		} else if(el.is('select')) {
			type = 'sl';
			$('option',this).eq(0).prop('selected',true);
		}

//		if(data != ""){
//			if(strParams.length >= 1) gubn="¿";
//			strParams += gubn+attr+'ˇ'+type+'ˇ'+target +'ˇ'+data;
//		}
	});
	return strParams;
}

let searchParams;
let parmas;

$(document).ready(function() {
//	console.log('initParam');
	initParam();
    numberFormat.init();
});

const numberFormat = {
   // 숫자에 천단위 콤마 추가 (소수점 처리 포함)
   formatNumber: function(value) {
       if (value === '' || value === null || value === undefined) return '';
       // 0인 경우 '0' 반환
       if (value === 0 || value === '0') return '0';

       // 입력값에서 숫자, 소수점만 남기고 제거
       const cleanValue = value.toString().replace(/[^\d.]/g, '');
       // 소수점이 있는 경우 처리
       const parts = cleanValue.split('.');
       // 정수부분 천단위 콤마 처리
       parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
       // 소수점 이하 처리 (소수점이 있는 경우에만)
       if (parts.length > 1) {
           // 소수점 이하는 콤마 없이 그대로 합치기
           return parts[0] + '.' + parts[1];
       }
       return parts[0];
   },

   // jQuery 플러그인으로 등록
   init: function() {
       $.fn.formatNumber = function(options) {
           // 기본 옵션 설정
           const settings = $.extend({
               maxDecimal: 2,    // 소수점 이하 최대 자릿수
               allowDecimal: true, // 소수점 입력 허용 여부
               maxValue: 0,      // 최대값 0인 경우 무제한
               minValue: 0,      // 최소 입력 가능한 값
               allowEmpty: true  // 공백 입력 허용 여부
           }, options);

           return this.each(function() {
               const $input = $(this);

               // input 이벤트 바인딩
               $input.on('input', function() {
                   let value = $(this).val();

                   // 공백 처리
                   if (value === '') {
                       if (settings.allowEmpty) {
                           $(this).val('');
                           return;
                       } else {
                           value = '0';
                       }
                   }

                   if (settings.allowDecimal) {
                       // 소수점이 있는 경우 처리
                       const parts = value.split('.');

                       // 소수점 이하 자릿수 제한
                       if (parts.length > 1) {
                           parts[1] = parts[1].slice(0, settings.maxDecimal);
                           value = parts.join('.');
                       }
                   } else {
                       // 소수점 허용하지 않는 경우, 소수점 제거
                       value = value.replace(/\./g, '');
                   }

                   // 숫자값으로 변환 (콤마 제거 후)
                   let numValue = parseFloat(value.replace(/,/g, ''));

                   // 값이 NaN인 경우 공백으로 처리
                   if (isNaN(numValue)) {
                       if (settings.allowEmpty) {
                           $(this).val('');
                           return;
                       } else {
                           numValue = 0;
                       }
                   }

                   // 최대값 제한 (maxValue가 0보다 큰 경우에만 적용)
                   if(settings.maxValue > 0) {
                       if (numValue > settings.maxValue) {
                           numValue = settings.maxValue;
                       }
                   }

                   // 최소값 제한
                   if (numValue < settings.minValue) {
                       numValue = settings.minValue;
                   }

                   // 포맷팅된 값을 다시 설정
                   $(this).val(numberFormat.formatNumber(numValue));
               });

               // 초기값이 있는 경우 포맷팅
               if ($input.val()) {
                   $input.val(numberFormat.formatNumber($input.val()));
               }
           });
       };
   }
};


function initPreList(){
	$(document).bind('mousedown', function(e){
		if( e.button == 3 ){
			if( confirm( '목록으로 이동하시겠습니까?' ) ){
				preList();
			}
		}
	});
}

function initParam(){
	searchParams = new URLSearchParams(window.location.search);
	if(searchParams.has('params')){
		parmas = searchParams.get('params');
		//console.log('initParam', parmas);
    	setParams( parmas );
	}
}

function getParams(){
	var targetid = 'search-body';
	var strParams = "";

	$('input, select','.'+targetid).each(function(index, element){
//		console.log(index, element);
		var el = $(element);

		var target = (typeof el.attr('id') != 'undefined'? el.attr('id') : el.attr('name'));
		var attr = (typeof el.attr('id') == 'undefined'? 'n' : 'i');
		var type = "";
		var data = el.val();
		var gubn = "";

		if(el.is('input')){
			type = 'ip';
		} else if(el.is('select')) {
			type = 'sl';
		}

		if(data != ""){
			if(strParams.length >= 1) gubn="¿";
			strParams += gubn+attr+'ˇ'+type+'ˇ'+target +'ˇ'+data;
		}
	});
	return strParams;
}

function setParams(params){
	if(params.length >= 1){
		var array1 = params.split('¿');

		for(var i=0;i<array1.length;i++){
			var array2 = array1[i].split('ˇ');

			//console.log('setParams', array2);

			if(array2[0] == 'i'){
				$('#'+array2[2]).val(array2[3]);
			} else {
				console.log(array2);
				if(array2[1] == 'ip'){
					$('input[name='+array2[2]+']').val(array2[3]);
				} else if(array2[1] == 'sl'){
					$('select[name='+array2[2]+']').val(array2[3]).prop('selected',true);
				} else if(array2[1] == 'bt'){
					$(`button[${array2[2]}='${array2[3]}']`).trigger('click');
				}
			}
		}
	}
}

function convertParams(params){
	if(params.length >= 1){
		var array1 = params.split('¿');
		var urlParam = "";

		for(var i=0;i<array1.length;i++){
			var array2 = array1[i].split('ˇ');

			//console.log( array2[2], array2[3] );
			if( i > 0) urlParam += "&";

			urlParam += `${array2[2]}=${array2[3]}`;

			/*
			if(array2[0] == 'i'){
				//$('#'+array2[2]).val(array2[3]);
			} else {
				console.log(array2);
				if(array2[1] == 'ip'){
					//$('input[name='+array2[2]+']').val(array2[3]);
				} else if(array2[1] == 'sl'){
					//$('select[name='+array2[2]+']').val(array2[3]).prop('selected',true);
				} else if(array2[1] == 'bt'){
					//$(`button[${array2[2]}='${array2[3]}']`).trigger('click');
				}
			}
			*/
		}
		return urlParam;
	}
}

//두날짜 차이를 일로 받아온다.
function getGapDt(dt1, dt2) {
	var stDt = new Date(dt1).format("yyyy-MM-dd");
	var edDt = new Date(dt2).format("yyyy-MM-dd");
	return (new Date(edDt).getTime() - new Date(stDt).getTime())/ 1000 / 60 / 60 / 24;
}


//$(".content input").keypress(function(event) {
//	if (event.which == 13) {
//		event.preventDefault();
//		datatable.ajax.reload();
//	}
//});

function objectifyForm(formArray) {//serialize data function
	console.log(formArray);
	var returnArray = {};
	for (var i = 0; i < formArray.length; i++){

		returnArray[formArray[i]['name']] = formArray[i]['value'];
		console.log(formArray[i]['name']);
	}
	return returnArray;
}

function fnFormSerialize(obj, isStr = true) {
    var formData = new FormData(obj);
    var jsonObject = {};

	$('input[type=checkbox]:not(:checked)', obj).each(function(i,e){
		formData.set( $(e).attr('name'), 'N');
	});

    formData.forEach(function(value, key) {
        if (jsonObject[key] === undefined && key != "images") {
            jsonObject[key] = value;
		} else if(key =="images"){
			if (jsonObject[key] === undefined){
				jsonObject[key] = [value];
			} else {
				if (Array.isArray(jsonObject[key])) {
					jsonObject[key].push(value);
				} else {
					jsonObject[key] = [jsonObject[key]];
				}
			}
		}
    });

    if( isStr){
    	var jsonString = JSON.stringify(jsonObject);
    	return jsonString;
	} else {
    	return jsonObject;
	}
}

/*
$.ajaxSetup({
	beforeSend: function(xhr) {
		xhr.setRequestHeader("AJAX", true);
	},
	error: function(xhr, status, err) {
		console.log("ajaxSetup : " + xhr.status);
		var servletPath = document.URL;
		servletPath = servletPath.substr(servletPath.indexOf("/", servletPath.indexOf("//")+2));
		if(servletPath.indexOf("?") != -1) {
			servletPath = servletPath.substr(0, servletPath.indexOf("?"));
		}
		servletPath = servletPath.substr(0,7);

		if(xhr.status == 401) {
			if("/mngr/" == servletPath){
				alert("인증에 실패 했습니다. 로그인 페이지로 이동합니다.");
				location.href = '/login/loginForm.do';
			}else{
				alert("인증에 실패 했습니다. 로그인 페이지로 이동합니다.");
				location.href = '/login/loginForm.do';
			}

//			location.href = ""
		}else if (xhr.status == 403) {
			if("/mngr/" == servletPath){
				alert("세션이 만료가 되었습니다. 로그인 페이지로 이동합니다.");
				location.href = '/login/loginForm.do';
			}else{
				alert("세션이 만료가 되었습니다. 로그인 페이지로 이동합니다.");
				location.href = '/login/loginForm.do';
			}
		}
	}
});
*/
(function($){
    $.fn.serializeObject = function(){

        var self = this,
            json = {},
            push_counters = {},
            patterns = {
                "validate": /^[a-zA-Z][a-zA-Z0-9_]*(?:\[(?:\d*|[a-zA-Z0-9_]+)\])*$/,
                "key":      /[a-zA-Z0-9_]+|(?=\[\])/g,
                "push":     /^$/,
                "fixed":    /^\d+$/,
                "named":    /^[a-zA-Z0-9_]+$/
            };


        this.build = function(base, key, value){
            base[key] = value;
            return base;
        };

        this.push_counter = function(key){
            if(push_counters[key] === undefined){
                push_counters[key] = 0;
            }
            return push_counters[key]++;
        };

        $.each($(this).serializeArray(), function(){

            // skip invalid keys
            if(!patterns.validate.test(this.name)){
                return;
            }

            var k,
                keys = this.name.match(patterns.key),
                merge = this.value,
                reverse_key = this.name;

            while((k = keys.pop()) !== undefined){

                // adjust reverse_key
                reverse_key = reverse_key.replace(new RegExp("\\[" + k + "\\]$"), '');

                // push
                if(k.match(patterns.push)){
                    merge = self.build([], self.push_counter(reverse_key), merge);
                }

                // fixed
                else if(k.match(patterns.fixed)){
                    merge = self.build([], k, merge);
                }

                // named
                else if(k.match(patterns.named)){
                    merge = self.build({}, k, merge);
                }
            }

            json = $.extend(true, json, merge);
        });

        return json;
    };
})(jQuery);

//pc, mobile 구분(가이드를 위한 샘플 함수입니다.)
function checkPlatform(ua) {
	if(ua === undefined) {
		ua = window.navigator.userAgent;
	}

	ua = ua.toLowerCase();
	var platform = {};
	var matched = {};
	var userPlatform = "pc";
	var platform_match = /(ipad)/.exec(ua) || /(ipod)/.exec(ua)
		|| /(windows phone)/.exec(ua) || /(iphone)/.exec(ua)
		|| /(kindle)/.exec(ua) || /(silk)/.exec(ua) || /(android)/.exec(ua)
		|| /(win)/.exec(ua) || /(mac)/.exec(ua) || /(linux)/.exec(ua)
		|| /(cros)/.exec(ua) || /(playbook)/.exec(ua)
		|| /(bb)/.exec(ua) || /(blackberry)/.exec(ua)
		|| [];

	matched.platform = platform_match[0] || "";

	if(matched.platform) {
		platform[matched.platform] = true;
	}

	if(platform.android || platform.bb || platform.blackberry
			|| platform.ipad || platform.iphone
			|| platform.ipod || platform.kindle
			|| platform.playbook || platform.silk
			|| platform["windows phone"]) {
		userPlatform = "mobile";
	}

	if(platform.cros || platform.mac || platform.linux || platform.win) {
		userPlatform = "pc";
	}

	return userPlatform;
}

function platformCheck(){
	if(checkPlatform(window.navigator.userAgent) == "mobile"){//모바일 결제창 진입
		return 'M';
	}else{//PC 결제창 진입
		return 'P';
	}
}

function formValidCheck(item, type, classname, regexType){
	if($(item) === undefined) return false;
	let result = true;
	switch (type){
		case 'input':
		case 'textarea':
			if($(item).val() === undefined || $(item).val() === null || $(item).val().trim() === ''){
				result = false;
			}else {
				if(regexType !== null && regexType.trim() !== ''){
					switch (regexType){
						case 'email':
							if($(item).val().match( /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/ ) === null) {
								result = false;
							}
							break;
						case 'mobile':
							if($(item).val().match( /^0[0-9]{2}-?[0-9]{3,4}-?[0-9]{4}$/ ) === null) {
								result = false;
							}
							break;
						case 'tel':
							if($(item).val().match( /^0[0-9]{1,2}-?[0-9]{3,4}-?[0-9]{4}$/ ) === null) {
								result = false;
							}
							break;
						case 'count':
							if($(item).val().match( /^[0-9]+$/ ) === null) {
								result = false;
							}
							break;
						case 'medium-pass':
							if($(item).val().match(/^.*(?=^.{8,16}$)(?=.*\d)(?=.*[a-zA-Z])(?=.*[!@#$%^&+=]).*$/) === null) {
								result = false;
							}
							break;
						case 'strong-pass':
							if($(item).val().match(/^.*(?=^.{8,30}$)(?=.*\d)(?=.*[a-zA-Z])(?=.*[!@#$%^&+=]).*$/) === null) {
								result = false;
							}
							break;
						case 'hex-color':
							if($(item).val().match(/^#[0-9a-fA-F]{6}$/) === null) {
								result = false;
							}
							break;
						case 'url':
							if($(item).val().match(/^(\/[a-zA-Z0-9\-]+)(\/[a-zA-Z0-9\-]+)?$/g) === null) {
								// /[^\"'\s()]+/i
								result = false;
							}
							break;
						case 'yyyy':
							if($(item).val().match( /^(19|20)[0-9]{2}$/ ) === null) {
								result = false;
							}
							break;
						case 'age':
							if($(item).val().match( /^[0-9]{1,2}$/ ) === null) {
								result = false;
							}
							break;
						case 'text':
						default: break;
					}
				}
			}
			break;
		case 'select':
		default:
			if($(item).val() === undefined || $(item).val() === null){
				result = false;
			}
			break;
	}

	if(result){
		$(item).removeClass(classname);
	}else{
		$(item).addClass(classname);
		$(item).focus();
	}

	return result;
}

loadMakeUpHtmlInput = function(type, upperCode, value, name, classappend, labelYn){
	loadMakeUpHtmlInput(type, upperCode, value, name, classappend, labelYn);
}

loadMakeUpHtmlInput = function(type, upperCode, value, name, classappend, labelYn, labelClass=undefined){
	let _htmlData = "";
	var divYn=undefined, divClass=undefined;
	if( type !== undefined ){
		let list = loadCodeList(upperCode);
//		console.log(list);

		if(type == 'radio' || type == 'check'){
			for( idx in list){
				var data = list[idx];
				var itemId = name+"-"+data.code; /*라디오 ID값 중복처리 안되게*/
				var inputType = (type == 'radio' ? 'radio' : 'checkbox');

				if(divYn){
					                            _htmlData += `<div `;

					if(name != undefined)       _htmlData += `class='${divClass}'>`;
				}
				if(labelYn){
					                            _htmlData += `<label `;
					if(name != undefined)       _htmlData += `class='${labelClass}' `;
					                            _htmlData += `for='${itemId}'>`;
				}
				_htmlData += `<input type='${inputType}' `;
				if(name != undefined)           _htmlData += `name='${name}' id='${itemId}' `;
				if(classappend != undefined)    _htmlData += `class='${classappend}' `;
				                                _htmlData += `value='${data.code}' data-upperCode='${data.upperCode}' `;
				if(data.codeAcronym !== undefined)
				                                _htmlData += `data-acronym='${data.codeAcronym}' `;
				if(data.code === value)         _htmlData += `checked='checked' `;

				_htmlData += `>`;
				                                _htmlData += data.name;
				if(labelYn)                     _htmlData += `</label> `;
				if(divYn)                       _htmlData += `</div> `;

				_htmlData += `\r\n`;
			}
		} else if(type == 'select'){
			/**/
		}
	}

	//console.log( _htmlData );

	return _htmlData;
}

loadMakeUpHtmlSelect = function( upperCode, value, id, name, classappend, headerYn = false, headerTitle = undefined, headerValue = undefined, event = undefined){
	let _htmlData = "";
	let list = loadCodeList(upperCode);
//		console.log(list);

	if(headerYn){
	                            	_htmlData += `<select `;
	if(id != undefined)         	_htmlData += `id='${id}' `;
	if(name != undefined)       	_htmlData += `name='${name}' `;
	if(classappend != undefined)	_htmlData += `class='${classappend}' `;
	if(event != undefined)      	_htmlData += `${event} `;
	                            	_htmlData += `>\r\n`;
		                        	_htmlData += `<option ${headerValue != undefined ? `value='${headerValue}'` : '' } >` + headerTitle; + `</option>` + `\r\n`;
	}
	for( idx in list){
		var data = list[idx];
		                        	_htmlData += `<option value='${data.code}' data-upperCode='${data.upperCode}' `;
		if(data.codeAcronym !== undefined)
	                            	_htmlData += `data-acronym='${data.codeAcronym}' `;
		if(data.code === value) 	_htmlData += `selected`;
	                            	_htmlData += `>`;
	                            	_htmlData += data.name;
	                            	_htmlData += `</option>`;
		                        	_htmlData += `\r\n`;
	}
	if(headerYn){
	                            	_htmlData += `</select>`;
	                            	_htmlData += `\r\n`;
	}

	//console.log( _htmlData );

	return _htmlData;
}

/* 윤재웅
 * 공통코드정보를 가져온다.
 */

loadCodeList = function(code){
	let list;
	$.ajax({
        url: '/code-list',
        type: "GET",
        data:{'upperCode':code, 'useYn':'Y', length : -1},
        dataType : "json",
        async:false,
        success: function (res) {
            if ("success" == res.result) {
				list = res.data;
            } else {
                alert(res.message);
            }
        }
    });

    return list;
}

const _datetimepickerKor = {
	prevText: '이전 달',
	nextText: '다음 달',
	months: ['1월', '2월', '3월', '4월', '5월', '6월', '7월', '8월', '9월', '10월', '11월', '12월'],
	dayNames: ['일', '월', '화', '수', '목', '금', '토'],
	dayNamesShort: ['일', '월', '화', '수', '목', '금', '토'],
	dayNamesMin: ['일', '월', '화', '수', '목', '금', '토'],
	showMonthAfterYear: true,
	yearSuffix: '년',
}

/* 윤재웅
 * 특정문자열 개수를 센다.
 */

fnCmmnFindStrLength = function(text, findStr){
	var count = 0;
	var pos = text.indexOf(findStr); //pos는 0의 값을 가집니다.

	while (pos !== -1) {
		count++;
		pos = text.indexOf(findStr, pos + 1); //
	}

	return count;
}
/*
//뒤에서부터 n자리마다 원하는 문자 넣기
insertSpecialCharReverse = function (str, n, char){
	const regex = new RegExp(`(.)(?=(.{${n}})+$)`, "g");
	return str.toString().replace(regex, `$1${char}`);
}
*/
fnCmmnMoveLocation = function(url, target='_self'){
	if( url != undefined ){
		if( url != ''){
			var a = document.createElement('a');
			a.href=url;

			if(target != 'NONE') a.target = target;

			document.body.appendChild(a);
			a.click();
			a.remove();
		}
	}
}
/*
fnMoveInquriy = function(url){
	console.log(url);

	var newForm = $('<form></form>');

	newForm.attr("name","newForm");
	newForm.attr("method","get");
	newForm.attr("action",url);
	newForm.attr("target","_self");

	//newForm.append($('<input/>', {type: 'hidden', name: 'onlyCategory', value:'5' }));
	//newForm.append($('<input/>', {type: 'hidden', name: [[${_csrf.parameterName}]], value:[[${_csrf.token}]]} ));

	$(document.body).append(newForm);

	newForm.submit();
}
*/

fnCmmnBoardMoveLocation = function(url){

	let f = document.createElement('form');
    f.setAttribute('method', 'post');
	f.setAttribute('target', '_self');
	f.setAttribute('action', url);

    /*
    let obj;
    obj = document.createElement('input');
    obj.setAttribute('type', 'hidden');
    obj.setAttribute('name', 'InvNo');
    obj.setAttribute('value', no);
    f.appendChild(obj);
    */

    document.body.appendChild(f);
    f.submit();
}

fnCmmnSetNullDatetime = function(target){
	if(typeof target === 'string' ){
		$('#'+target).val('').data('is-null', true);
	} else {
		$(target).val('');
	}
}

fnCmmnTabSeleced =function( e ){
	$('.tab-btn').removeClass('active');
	$('.tab-content').hide();

	var id = $(e).data('id');
	var cid = $(e).data('category-id');

	//console.log(cid, id);
	$(e).addClass('active');

	var selector = `.tab-content[data-id='${id}']`;
	if(cid !== undefined){
		selector += `[data-category-id='${cid}']`;
	}

	$(selector).show();
}

//ResourceFile Download
fnDownloadFile = function(obj){
	var url = $(obj).data('fileurl');
	var name = $(obj).data('filename');
	var type = $(obj).data('mimetype');
	if(url != undefined || url != ''){
		var wait = $(this);
		//wait.waitMe();
		const req = new XMLHttpRequest();
		req.open("GET", url, true);
		req.responseType = "arraybuffer";
		req.onload = function() {
			const arrayBuffer = req.response;
			if (arrayBuffer) {
				var blob = new Blob([arrayBuffer], { type: type });
				var link=document.createElement('a');
				link.href=window.URL.createObjectURL(blob);
				link.download=name;
				link.click();
				link.remove();
			}
		};
		req.send();
	}
}

function fnCmmnGenerateRandomCode(length) {
	let result = '';
	const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
	const charactersLength = characters.length;

	for (let i = 0; i < length; i++) {
		const randomIndex = Math.floor(Math.random() * charactersLength);
		result += characters.charAt(randomIndex);
	}

	return result;
}

//빈스용
initMobileCategory = function () {
	$("#mo-category-box").click(function () {
		if ( $("#mo-category-list").css('overflow') === 'hidden' ) {
			$("#mo-category-list").css('overflow', 'unset');
			$("#mo-category-list").css('height', 'unset');
			$("#green-select-btn").css('transform','rotate(-180deg)');
			$("#green-select-btn").css('transition','transform 0.2s');
		}
		else {
			$("#mo-category-list").css('overflow', 'hidden');
			$("#mo-category-list").css('height', '45px');
			$("#green-select-btn").css('transform','rotate(0deg)');
			$("#green-select-btn").css('transition','transform 0.2s');
		}
	})

	$(".mo-category-item").each(function () {
		if ( $(this).hasClass('active') && !$(this).hasClass('my-page-circle-btn') ) {
			const parentElement = $('#mo-category-list');
			const elementToMove = $(this);

			parentElement.prepend(elementToMove);
		}
	})
}

function shareLink(type, title){
    switch (type){
		case 'LINK':
            let clipboard = location.href;
            const element = document.createElement('textarea');
            element.value = clipboard;
            element.setAttribute('readonly', '');
            document.body.appendChild(element);
            element.select();
            let returnValue = document.execCommand('copy');
            document.body.removeChild(element);
            if (!returnValue) {
                throw new Error('URL 복사 실패하였습니다.');
            }else{
                alert('URL을 복사했습니다.');
            }
            break;
        case 'KAKAO':
			if (!Kakao.isInitialized()) {
		        Kakao.init('27e6816394df5163aa5b64c5b7335756');
		    };

            Kakao.Share.sendDefault({
                objectType: 'feed',
                content: {
                    title: title,
                    description: 'HarimTour',
                    imageUrl: location.origin + $('.typea-product-view-image').eq(0).attr('src'),
                    link: { mobileWebUrl: location.href, },
                },
                buttons: [
                    {
                        title: '웹으로 이동',
                        link: { mobileWebUrl: location.href, },
                    },
                    {
                        title: '앱으로 이동',
                        link: { mobileWebUrl: location.href, },
                    },
                ]
            });
            break;
        /*
        case 'FACEBOOK':
            let shareTitle = "JoyfulTour";
            let shareURL = encodeURIComponent(window.location.href);
            // let shareURL = "http://www.facebook.com/sharer.php?u=" + location.href;
            window.open("http://www.facebook.com/sharer.php?u="+shareURL+"&title="+shareTitle);
            break;
        case 'NAVER':
            let naverTitle = "HarimTour";
            let naverUrl = location.href;
            window.open("https://share.naver.com/web/shareView?url=" + naverUrl + "&title=" + naverTitle);
            break;
        case 'BAND':
            let url = location.href;
            let body = "HarimTour"
            window.open("http://band.us/plugin/share?body="+body+"&route"+url);
            break;
        */
    }

}

//회원가입 폼 유효성 체크 (최적화)
function checkJoinValid(type) {

	// 각 필드의 유효성 검사 규칙 정의
	// 1.사용법 *** validationRules 객체안의 field의 값 가지고 html에서 찾는다.
	// 2. pattern을 가지고 요소의 값을 유효성 검사한다.
	// 3. 유효성에 부적합하면 invalidMessage 를 노출한다.
	// 4. 요소의 값 미입력시 message 노출.
	const validationRules = [
		{
			field: '#user-email',
			message: '아이디를 입력해 주세요.',
			pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
			invalidMessage: '아이디는 올바른 이메일 형식이어야 합니다.'
		},
		{
			field: '#user-name',
			message: '이름을 입력해 주세요.',
			pattern: /^[가-힣a-zA-Z]+$/,
			invalidMessage: '이름은 한글 또는 영어만 입력 가능합니다.'
		},
	];



	if ( type === 'join') {
		const obj = [{
			field: '#tel1',
			message: '연락처를 입력해 주세요.',
			pattern: /^[0-9]+$/,
			invalidMessage: '연락처는 숫자만 입력 가능합니다.'
		},
		{
			field: '#tel2',
			message: '연락처를 입력해 주세요.',
			pattern: /^[0-9]+$/,
			invalidMessage: '연락처는 숫자만 입력 가능합니다.'
		},
		{
			field: '#tel3',
			message: '연락처를 입력해 주세요.',
			pattern: /^[0-9]+$/,
			invalidMessage: '연락처는 숫자만 입력 가능합니다.'
		},
		{
			field: '#tel3',
			message: '연락처를 입력해 주세요.',
			pattern: /^[0-9]+$/,
			invalidMessage: '연락처는 숫자만 입력 가능합니다.'
		},{
			field: '#user-password',
			message: '비밀번호를 입력해 주세요.',
			pattern: /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{8,16}$/,
			invalidMessage: '비밀번호는 영문, 숫자, 특수문자를 포함하여 8~16자 사이여야 합니다.'
		},
		{
			field: '#user-password-check',
			message: '비밀번호 확인을 입력해 주세요.',
			matchField: '#user-password',
			invalidMessage: '비밀번호와 비밀번호 확인이 일치하지 않습니다.'
		}]

		validationRules.push(...obj);
	}
	else if (type === 'findpwd') {
		const obj = [{
			field: '#user-password',
			message: '비밀번호를 입력해 주세요.',
			pattern: /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{8,16}$/,
			invalidMessage: '비밀번호는 영문, 숫자, 특수문자를 포함하여 8~16자 사이여야 합니다.'
		},
		{
			field: '#user-password-check',
			message: '비밀번호 확인을 입력해 주세요.',
			matchField: '#user-password',
			invalidMessage: '비밀번호와 비밀번호 확인이 일치하지 않습니다.'
		},]

		validationRules.push(...obj);
	}
	// 모든 필드 검사
	for (let rule of validationRules) {
		if (!validateField(rule)) {
			return false; // 유효성 검사 실패 시 중단
		}
	}
	// 유효성 검사 함수
	function validateField(rule) {
		const value = $(rule.field).val();
		if (!value) {
			alert(rule.message);
			$(rule.field).focus();
			return false;

		}
		if ( type === 'join' ) {
			if ($('#id-check').val() !== 'Y') {
				// 아이디 중복 확인 체크
				alert('아이디 중복확인을 해주세요.');

				return false;
			}
		}
		if (rule.pattern && !value.match(rule.pattern)) {
			alert(rule.invalidMessage);
			$(rule.field).focus();
			return false;
		}
		if (rule.matchField && value !== $(rule.matchField).val()) {
			alert(rule.invalidMessage);
			$(rule.field).focus();
			return false;
		}
		return true;
	}

	if ( type === 'join' ) {
		if (!$('#use_chk').is(':checked')) {
			alert('이용약관 동의에 동의해야 합니다.');
			return false;
		}

		if (!$('#privacy_chk').is(':checked')) {
			alert('개인정보 수집 및 이용 동의에 동의해야 합니다.');
			return false;
		}

	}

	return true;
}

//ajax 용 페이징 처리
//적용된 곳은 코믹에서 참고 코드 볼 수 있음.
//필요 파라미터 ㅣ fetchDataFunction : ajax가 들어있는 함수, elParents : 데이터 set, 페이징 set할 요소들을 전부 감싼 부모 요소(부모 요소를 기반으로 자식요소를 찾기 위함)
// 			 ㅣ  totalCount : 전체 데이터 개수, currentPage : 현재 페이지 번호, pageSize : 한번에 보여줄 데이터 개수
function renderItemsAndPagination(fetchDataFunction, elParents, totalCount, currentPage, pageSize) {
	// 페이징 렌더링
	const totalPages = Math.ceil(totalCount / pageSize); // 전체 페이지 수 계산
	const paginationContainer = $(`#${elParents}`).find("[data-id='paging-number']");

	paginationContainer.empty(); // 이전 페이지 수를 비움
	const startPage = Math.max(1, currentPage - 2);
	const endPage = Math.min(totalPages, startPage + 4); //사용자에게 표시될 클릭가능한 페이지 최대 개수. < 1 2 3 4 5 > 형식.

	for (let i = startPage; i <= endPage; i++) {
		const pageItem = $(`<li class="page-item ${i === currentPage ? 'on' : ''}">${i}</li>`);
		pageItem.on('click', function () {
			fetchDataFunction(i, pageSize);// 해당 페이지 데이터 요청
		});
		paginationContainer.append(pageItem);
	}
	// Prev 및 Next 버튼 상태 업데이트
	const prevButton = $(`#${elParents}`).find("[data-id='paging-prev-btn']");
	const nextButton = $(`#${elParents}`).find("[data-id='paging-next-btn']");

	// 기존의 이벤트 핸들러 제거
	prevButton.off('click');
	nextButton.off('click');

	prevButton.prop('disabled', currentPage === 1);
	nextButton.prop('disabled', currentPage === totalPages || totalPages === 0 || totalPages === 1);

	prevButton.on('click', function () {
		if (currentPage > 1) {
			currentPage--; // 현재 페이지 -1
			fetchDataFunction(currentPage, pageSize); // 이전 페이지 데이터 요청
		}
	});

	nextButton.on('click', function () {
		if (currentPage < totalPages) {
			currentPage++; // 현재 페이지 +1
			fetchDataFunction(currentPage, pageSize); // 다음 페이지 데이터 요청
		}
	});
}

function textToColor(text) {
    // 텍스트를 UTF-8 인코딩으로 변환
    let hash = 0;

    // 각 문자의 유니코드 값을 더해서 해시값 생성
    for (let i = 0; i < text.length; i++) {
        hash = text.charCodeAt(i) + ((hash << 5) - hash);
    }

    // 해시값을 6자리 16진수로 변환
    let color = '#';
    for (let i = 0; i < 3; i++) {
        const value = (hash >> (i * 8)) & 0xFF;
        color += ('00' + value.toString(16)).substr(-2);
    }

    return color;
}
