package kr.co.wayplus.travel.web.manage;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.SortData;
import kr.co.wayplus.travel.model.Survey;
import kr.co.wayplus.travel.model.SurveyJsonWrapper;
import kr.co.wayplus.travel.model.SurveyQuestion;
import kr.co.wayplus.travel.model.SurveyQuestionAnswer;
import kr.co.wayplus.travel.model.SurveyRecommand;
import kr.co.wayplus.travel.model.SurveyImage;
import kr.co.wayplus.travel.service.manage.SurveyManageService;
import kr.co.wayplus.travel.util.FileInfoUtil;

@Controller
@RequestMapping("/manage/survey")
public class SurveyManageController extends BaseController {
	private final Logger logger = LoggerFactory.getLogger(getClass());

	private final SurveyManageService svcSurvey;

	@Value("${upload.file.path}")
	String externalImageUploadPath;

	final String addPath = "survey/";

	@Autowired
	public SurveyManageController(SurveyManageService svc1) {
		this.svcSurvey = svc1;
	}

	@GetMapping("/list")
	public ModelAndView survey_list(@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
			@RequestParam(value = "searchType", defaultValue = "") String searchType,
			@RequestParam(value = "searchKey", defaultValue = "") String searchKey) {
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> paramMap = new HashMap<String, Object>();

		mav.setViewName("manage/sub/survey/list");
		return mav;
	}
	@GetMapping("/recommand")
	public ModelAndView recommand_list(@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
			@RequestParam(value = "searchType", defaultValue = "") String searchType,
			@RequestParam(value = "searchKey", defaultValue = "") String searchKey) {
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> paramMap = new HashMap<String, Object>();

		mav.setViewName("manage/sub/survey/recommand");
		return mav;
	}

	@GetMapping("/form")
	public ModelAndView survey_form(
			@RequestParam(value = "mode", defaultValue = "I") String mode,
			@RequestParam(value = "id", defaultValue = "0") String id) {
		ModelAndView mav = new ModelAndView();

		if (mode.equals("U")) {
			HashMap<String, Object> paramMap = new HashMap<>();
			HashMap<String, Object> paramMap2 = new HashMap<>();
			paramMap.put("id", id);

			mav.addObject("survey", svcSurvey.selectOneSurvey( paramMap ));

			paramMap2.put("upperId", id);
			paramMap2.put("deleteYn", "N");
			ArrayList<SurveyQuestion> list = svcSurvey.selectListSurveyQuestion( paramMap2 );

			for (SurveyQuestion sq : list) {
				paramMap2.clear();
				paramMap2.put("questionId", sq.getId() );
				paramMap2.put("deleteYn", "N");

				sq.setFile( svcSurvey.selectOneSurveyImage(paramMap2) );

				paramMap2.clear();
				paramMap2.put("surveyId", sq.getUpperId() );
				paramMap2.put("questionId", sq.getId() );

				sq.setListSurveyQuestionAnswers( svcSurvey.selectListSurveyQuestionAnswer(paramMap2) );
			}

			mav.addObject("surveyItems", list );

			mav.addObject("p", paramMap);
		} else {
			mav.addObject("survey", new Survey());

		}
		mav.addObject("mode", mode);

		mav.setViewName("manage/sub/survey/form");
		return mav;
	}

	@PostMapping("/list")
	@ResponseBody
	public HashMap<String, Object> survey_ajax_list(
			HttpServletRequest request,
			Survey si,
			@RequestParam(value = "start", defaultValue = "0") int start,
			@RequestParam(value = "length", defaultValue = "10") int length,
			@Param(value = "id") String id,
			@Param(value = "titleLike") String titleLike) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			HashMap<String, Object> paramMap = new HashMap<>();

			List<SortData> listSort = getListOrder(request);
			paramMap.put("listSort", listSort);
			if (length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
			}

			paramMap.put("titleLike", titleLike);
			paramMap.put("deleteYn", si.getDeleteYn());

			int totalCount = svcSurvey.selectCountSurvey(paramMap);
			ArrayList<Survey> lists = svcSurvey.selectListSurvey(paramMap);
//
//   			for (ScheduleInfo scheduleInfo : lists) {
//   				HashMap<String, Object> paramMapH = new HashMap<>();
//
//   				paramMapH.put("scheduleId", scheduleInfo.getId() );
//
//   				scheduleInfo.setListHorse(svcHorse.selectListHorseInfo(paramMapH));
//			}
//
			resultMap.put("recordsTotal", totalCount);
			resultMap.put("recordsFiltered", totalCount);
			resultMap.put("data", lists);

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
//   			resultMap.put("message", "처리중 오류가 발생하였습니다.");
			resultMap.put("error", e.getMessage());
			resultMap.put("message", e.getMessage());
		}

		return resultMap;
	}


	@PostMapping("/save")
	@ResponseBody
	public HashMap<String, Object> survey_ajax_save(
			@RequestPart SurveyJsonWrapper JsonWrapper,
			MultipartHttpServletRequest request) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			if (JsonWrapper != null) {
				Survey survey = JsonWrapper.getSurvey();
				ArrayList<SurveyQuestion> surveyQuests = JsonWrapper.getSurveyQuestions();
				String userEmail = getBaseUserEmail();
				if( survey.getId() == null ) {
					survey.setCreateId(userEmail);
					svcSurvey.insertSurvey( survey );
				} else {
					survey.setLastUpdateId(userEmail);
					svcSurvey.updateSurvey( survey );
				}
//
				for( SurveyQuestion sq : surveyQuests ) {
					//System.out.println( sq.getFile() );

					sq.setUpperId( survey.getId() );
					if(sq.getId() == null) {
						sq.setCreateId(userEmail);
						svcSurvey.insertSurveyQuestion( sq );
					} else {
						sq.setLastUpdateId(userEmail);
						svcSurvey.updateSurveyQuestion( sq );
					}

					if( sq.getListSurveyQuestionAnswers() != null ) {
						for ( SurveyQuestionAnswer sqa : sq.getListSurveyQuestionAnswers() ) {
							sqa.setCreateId(userEmail);
							sqa.setLastUpdateId(userEmail);
							sqa.setSurveyId(survey.getId());

							svcSurvey.saveSurveyQuestionAnswer( sqa );
						}
					}
				}
			}

			List<MultipartFile> multipartFiles = null;
			if (request.getFile("attachs") != null) {
				multipartFiles = request.getFiles("attachs");

				if (multipartFiles.size() > 0) {
					File file = new File(externalImageUploadPath + addPath);
					if (!file.exists())
						file.mkdirs();

					for (MultipartFile multipartFile : multipartFiles) {
						logger.info( multipartFile.getOriginalFilename() );

						Integer questionId = 0;
						String originalFileName = "";
						if( multipartFile.getOriginalFilename().indexOf("¿") > -1 ) {
							String[] arr = multipartFile.getOriginalFilename().split("¿");

							questionId = Integer.valueOf( arr[0] );
							originalFileName  = arr[1];

							HashMap<String, Object> paramMap = new HashMap<>();
							paramMap.put( "questionId", questionId );
							paramMap.put( "deleteYn", "N" );

							int cnt = svcSurvey.selectCountSurveyImage( paramMap );
							if( cnt > 0 ) {
								SurveyImage savedfile = svcSurvey.selectOneSurveyImage( paramMap );
								FileInfoUtil.deleteImageFile_real(savedfile);
								svcSurvey.deleteSurveyImage( savedfile );
							}
						} else {
							originalFileName  = multipartFile.getOriginalFilename();
						}

						String uploadName = UUID.randomUUID().toString();
						multipartFile.transferTo(new File(externalImageUploadPath + addPath + uploadName));
						logger.debug("User Question File Uploaded : " + originalFileName);

						String serviceType = serviceType = "survey";
						SurveyImage sqi = new SurveyImage();
						//sqi.setTsId(ps.getTsId());
						sqi.setQuestionId(questionId);
						sqi.setServiceType(serviceType);
						sqi.setUploadPath(externalImageUploadPath + addPath);
						sqi.setUploadFilename(uploadName);
						sqi.setOriginFilename(originalFileName);
						sqi.setFileSize((int) multipartFile.getSize());
						sqi.setFileMimetype(multipartFile.getContentType());

						//sqi.setSortOrder(sortOrder);
						if (multipartFile.getOriginalFilename().contains(".")) {
							sqi.setFileExtension(multipartFile.getOriginalFilename()
									.substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
						}

						//paramMap.clear();
						// paramMap.put("content_id", ps.getTsId());

						if (user != null) {
							sqi.setUploadId(String.valueOf(user.getUserEmail()));
						}

						svcSurvey.insertSurveyImage(sqi);
					}
				}
			}

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		} catch (Exception e) {
			logger.error(e.getCause().getMessage());
			resultMap.put("result", "error");
			resultMap.put("message", e.getMessage());
		}

		return resultMap;
	}

	@PostMapping("/delete")
	@ResponseBody
	public HashMap<String, Object> survey_delete_ajax(
			@ModelAttribute Survey sq,
			BindingResult bindingResult,
			// Multipart
			HttpServletRequest request) {
		HashMap<String, Object> resultMap = new HashMap<>();
		try {
			svcSurvey.deleteSurvey(sq);

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}

	@PostMapping("/item/delete")
	@ResponseBody
	public HashMap<String, Object> survey_item_delete_ajax(
			@ModelAttribute SurveyQuestion sq,
			BindingResult bindingResult,
			// Multipart
			HttpServletRequest request) {
		HashMap<String, Object> resultMap = new HashMap<>();
		try {
			svcSurvey.deleteSurveyQuestion(sq);

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}


	@PostMapping("/recommand/list")
	@ResponseBody
	public HashMap<String, Object> survey_recommand_ajax_list(
			HttpServletRequest request,
			SurveyRecommand si,
			@RequestParam(value = "start", defaultValue = "0") int start,
			@RequestParam(value = "length", defaultValue = "10") int length,
			@Param(value = "id") String id,
			@Param(value = "titleLike") String titleLike) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			HashMap<String, Object> paramMap = new HashMap<>();

//			List<SortData> listSort = getListOrder(request);
//			paramMap.put("listSort", listSort);
//			if (length >= 0) {
//				paramMap.put("itemStartPosition", start);
//				paramMap.put("pagePerSize", length);
//			}

			//paramMap.put("titleLike", titleLike);
			//paramMap.put("deleteYn", si.getDeleteYn());
			int totalCount = svcSurvey.selectCountSurveyRecommand(paramMap);
			ArrayList<SurveyRecommand> lists = svcSurvey.selectListSurveyRecommand(paramMap);

			resultMap.put("recordsTotal", totalCount);
			resultMap.put("recordsFiltered", totalCount);
			resultMap.put("data", lists);

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
//   			resultMap.put("message", "처리중 오류가 발생하였습니다.");
			resultMap.put("error", e.getMessage());
			resultMap.put("message", e.getMessage());
		}

		return resultMap;
	}

	@PostMapping("/recommand/one")
	@ResponseBody
	public HashMap<String, Object> survey_ajax_recommand_one(
			HttpServletRequest request,
			Survey si,
			@Param(value = "id") String id,
			@Param(value = "titleLike") String titleLike) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			HashMap<String, Object> paramMap = new HashMap<>();

			List<SortData> listSort = getListOrder(request);

			paramMap.put("id", id);
			SurveyRecommand data = svcSurvey.selectOneSurveyRecommand(paramMap);

			paramMap.remove("id");
			paramMap.put("recommandId", id );
			paramMap.put("deleteYn", "N");

			data.setFile( svcSurvey.selectOneSurveyImage(paramMap) );

			resultMap.put("data", data);

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
//   			resultMap.put("message", "처리중 오류가 발생하였습니다.");
			resultMap.put("error", e.getMessage());
			resultMap.put("message", e.getMessage());
		}

		return resultMap;
	}

	@PostMapping("/recommand/save")
	@ResponseBody
	public HashMap<String, Object> survey_ajax_recommand_save(
			@RequestPart SurveyRecommand SurveyRecommand,
			MultipartHttpServletRequest request) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			HashMap<String, Object> paramMap = new HashMap<>();
			LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			if (SurveyRecommand != null) {

				String userEmail = getBaseUserEmail();
				if( SurveyRecommand.getId() == null ) {
					paramMap.put("menuId", SurveyRecommand.getMenuId());
					int cnt = svcSurvey.selectCountSurveyRecommand( paramMap );
					if( cnt == 0 ) {
						SurveyRecommand.setCreateId(userEmail);
						svcSurvey.insertSurveyRecommand( SurveyRecommand );
					} else {
						resultMap.put("result", "duplicate");
						resultMap.put("message", "메뉴 중복이 발생하였습니다.");
						return resultMap;
					}
				} else {
					SurveyRecommand.setLastUpdateId(userEmail);
					svcSurvey.updateSurveyRecommand( SurveyRecommand );
				}
			}

			List<MultipartFile> multipartFiles = null;
			if (request.getFile("attachs") != null) {
				multipartFiles = request.getFiles("attachs");

				if (multipartFiles.size() > 0) {
					File file = new File(externalImageUploadPath + addPath);
					if (!file.exists())
						file.mkdirs();

					for (MultipartFile multipartFile : multipartFiles) {
						logger.info( multipartFile.getOriginalFilename() );

						Integer recommandId = 0;
						String originalFileName = "";
						if( multipartFile.getOriginalFilename().indexOf("¿") > -1 ) {
							String[] arr = multipartFile.getOriginalFilename().split("¿");

							recommandId = Integer.valueOf( arr[0] );
							originalFileName  = arr[1];
						} else {
							recommandId = SurveyRecommand.getId();
							originalFileName  = multipartFile.getOriginalFilename();
						}

						paramMap.clear();
						paramMap.put( "recommandId", recommandId );
						paramMap.put( "deleteYn", "N" );

						int cnt = svcSurvey.selectCountSurveyImage( paramMap );
						if( cnt > 0 ) {
							SurveyImage savedfile = svcSurvey.selectOneSurveyImage( paramMap );
							FileInfoUtil.deleteImageFile_real(savedfile);
							svcSurvey.deleteSurveyImage( savedfile );
						}

						String uploadName = UUID.randomUUID().toString();
						multipartFile.transferTo(new File(externalImageUploadPath + addPath + uploadName));
						logger.debug("User Question File Uploaded : " + originalFileName);

						String serviceType = serviceType = "survey";
						SurveyImage sqi = new SurveyImage();
						//sqi.setTsId(ps.getTsId());
						sqi.setRecommandId( SurveyRecommand.getId() );
						sqi.setServiceType(serviceType);
						sqi.setUploadPath(externalImageUploadPath + addPath);
						sqi.setUploadFilename(uploadName);
						sqi.setOriginFilename(originalFileName);
						sqi.setFileSize((int) multipartFile.getSize());
						sqi.setFileMimetype(multipartFile.getContentType());

						//sqi.setSortOrder(sortOrder);
						if (multipartFile.getOriginalFilename().contains(".")) {
							sqi.setFileExtension(multipartFile.getOriginalFilename()
									.substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
						}

						//paramMap.clear();
						// paramMap.put("content_id", ps.getTsId());

						if (user != null) {
							sqi.setUploadId(String.valueOf(user.getUserEmail()));
						}

						svcSurvey.insertSurveyImage(sqi);
					}
				}
			}

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		} catch (Exception e) {
			logger.error(e.getCause().getMessage());
			resultMap.put("result", "error");
			resultMap.put("message", e.getMessage());
		}

		return resultMap;
	}

	@PostMapping("/recommand/delete")
	@ResponseBody
	public HashMap<String, Object> survey_ajax_recommand_delete(
			 SurveyRecommand SurveyRecommand) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			HashMap<String, Object> paramMap = new HashMap<>();
			LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			if (SurveyRecommand != null) {
				SurveyRecommand.setDeleteId(getBaseUserEmail());
				svcSurvey.deleteSurveyRecommand( SurveyRecommand );
			}


			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		} catch (Exception e) {
			logger.error(e.getCause().getMessage());
			resultMap.put("result", "error");
			resultMap.put("message", e.getMessage());
		}

		return resultMap;
	}
}
