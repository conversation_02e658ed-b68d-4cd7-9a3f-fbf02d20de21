let _tchart;
let dfsSlider = null;

function createDfsSlider() {
    var isHr1Fct = $('.cast_area').first().hasClass('hr1-fct');

	var isMovingToTab = false;
	var obj = '.dfs-tab-body .dfs-slider';
	var $slider = $(obj);
	var $slideWrap = $slider.find('.slide-wrap');
	var scrollWidth = 0;
	var slidePoints = null;
	var tchartWidth = 0;
	var midtchartWidth = 0;
	var midtchartLeft = 0;
	var tchartItemCount = 0;
	var isModeChart = false;
	var isModeTable = false;
	var $slides = $slider.find('.slide');
    var $dailyHeads = $slider.find('.slide:not(.day-ten) .daily-head');
	var slideLength = $slides.length;
	var lastTabIndex = 0;
	var sliderWidth = $slider.width();

	prepareSlider();

	if( $('.dfs-tab-body .dfs-slider').length === 0 ) console.error( `'.dfs-tab-body .dfs-slider' 선택자를 찾을수 없음.` );

	dfsSlider = new IScroll('.dfs-tab-body .dfs-slider', { eventPassthrough:true, scrollX: true, scrollY: false, mouseWheel: false, scrollbars: 'custom', resizePolling : 40, probeType : 3, interactiveScrollbars: true});

	updateChart();

	dfsSlider.on('scroll', function(){
        if(isMovingToTab) return;
        //console.log(this);
        //console.log( slidePoints, isHr1Fct, lastTabIndex );

        if(this.scrollerWidth == 0){
			console.log( 'slide with 조정 필요, 재조정!!');
			this.refresh()
		}

        var scrollX = -this.x;
        var selectedTabIndex = 0;
        for(var i = 0 ; i < slidePoints.length ; i++) {
            var p1 = slidePoints[i] - (i > 0 ? (isHr1Fct ? 110 : 30) : 0);
            var p2 = (i+1 >= slidePoints.length) ? 99999999 : (slidePoints[i+1] - (isHr1Fct ? 110 : 30));

            if(p1 <= scrollX && scrollX < p2) {
                selectedTabIndex = i;
                break;
            }
        }
        if(isHr1Fct) {
            if(selectedTabIndex < $dailyHeads.length && scrollX >= 0) {
                var selectedSlideLeft = slidePoints[selectedTabIndex];
                var slideWidth = (selectedTabIndex+1 < slidePoints.length) ? ( slidePoints[selectedTabIndex+1] - selectedSlideLeft ) : 9999;
                var marginLeft = scrollX - selectedSlideLeft;
                if(marginLeft < slideWidth - $dailyHeads.eq(selectedTabIndex).width() - 70) {
                    $dailyHeads.eq(selectedTabIndex).css({ 'margin-left': (selectedTabIndex == 0 ? marginLeft : marginLeft)+ 'px'});
                }
            } else if(scrollX < 0) {
                $dailyHeads.eq(0).css({ 'margin-left': 'auto'});
            }
        }
        if(lastTabIndex != selectedTabIndex) {
            if(isHr1Fct) {
                if(lastTabIndex < $dailyHeads.length) {
                	if(lastTabIndex == 0 || isModeTable) {
                		$dailyHeads.eq(lastTabIndex).css({ 'margin-left': 'auto'});
                	} else {
                		$dailyHeads.eq(lastTabIndex).css({ 'margin-left': '23px'});
                	}
                }
            }
			lastTabIndex = selectedTabIndex;
			if(sliderWidth + scrollX < scrollWidth) {
            	selectTab(lastTabIndex);
            }
			if(slidePoints.length - 1 == selectedTabIndex) {
				if(!$('.cast_area .item-lbl').hasClass('off')) {
					$('.cast_area .item-lbl').addClass('off');
				}
				if(!$('.cast_area .item-lbl-midterm').hasClass('on')) {
					$('.cast_area .item-lbl-midterm').addClass('on');
				}
			} else {
				$('.cast_area .item-lbl').removeClass('off');
				$('.cast_area .item-lbl-midterm').removeClass('on');
			}
		}
	});


	/*
		오늘 내일 모레 10일 이후 탭과 스크롤 연동.
	*/
	$(document).on('click', '.cast_area .dfs-tab .dfs-tab-head a', function(e) {
		e.preventDefault();
		$(this).parents('.dfs-tab-head').find('a.on').removeClass('on');
		$(this).addClass('on');
		var date = $(this).attr('data-date');
		var $slider = $('.cast_area .dfs-slider').first();
		var scrollLeft = 0;
		var isExperimentalTab = $(this).parent().attr('data-experimental') == 'Y';
		$slider.find('.daily').each(function(idx, ele) {
        	if(isExperimentalTab) {
        		if(date == $(ele).attr('data-date') && $(this).attr('data-experimental') == 'Y') return false;
        	} else {
        		if(date == $(ele).attr('data-date') && $(this).attr('data-experimental') != 'Y') return false;
        	}

            scrollLeft +=  Math.ceil($(ele).width());
        });
		var isMidTerm = $(this).parent().index() == $(this).parent().parent().find('li').last().index();
		if(isMidTerm && !isModeTable) scrollLeft += 0; //78;
        if(isHr1Fct) scrollLeft -= 0;
		isMovingToTab = true;
		if(dfsSlider) dfsSlider.scrollTo( -scrollLeft, 0, 500, IScroll.utils.ease.quadratic);
		window.setTimeout(function() { isMovingToTab = false;}, 500);
		if(isMidTerm) {
			if(!$('.cast_area .item-lbl').hasClass('off')) {
				$('.cast_area .item-lbl').addClass('off');
			}
			if(!$('.cast_area .item-lbl-midterm').hasClass('on')) {
				$('.cast_area .item-lbl-midterm').addClass('on');
			}
		} else {
			$('.cast_area .item-lbl').removeClass('off');
			$('.cast_area .item-lbl-midterm').removeClass('on');
		}
	});

	// 윈도우 사이즈 변경에 따라 슬라이더 갱신.
	var isMobileSize = $(window).width() <= 1100;
	$(window).resize(function(e) {
		var width = $(window).width();
		if(isMobileSize) {
			if(width > 1100) {
				window.setTimeout(function() {
					prepareSlider();
					if(dfsSlider) dfsSlider.refresh();
				},0);
				isMobileSize = false;
			}
		} else {
			if(width <= 1100) {
				window.setTimeout(function() {
					prepareSlider();
					if(dfsSlider) dfsSlider.refresh();
				},0);
				isMobileSize = true;
			}
		}
	});

	/*
		슬라이더 준비.
	*/
	function prepareSlider() {
		isModeChart = $('.cast_area').hasClass('mode-chart');
		isModeTable = $('.cast_area').hasClass('mode-table');
		scrollWidth = 0;
		slidePoints = [];
		tchartItemCount = 0;

		if(!isModeTable) {
			$slides.each(function(idx, ele) {
				var slideWidth = 0;
				$(ele).find('.item').each(function(idx2, ele2) {
					var w = Math.ceil($(ele2).width());
					slideWidth += w;
					if(idx < slideLength-1) tchartItemCount++;
				});
				$(ele).css('width', slideWidth + 'px');
				slidePoints.push(scrollWidth);
				scrollWidth += slideWidth;
			});

			scrollWidth += 133;
			$slideWrap.css('width', scrollWidth + 'px');
		} else {
			$slides.each(function(idx, ele) {
				var slideWidth = Math.ceil($(ele).width());
				slidePoints.push(scrollWidth);
				scrollWidth += slideWidth;
			});
			$slideWrap.css('width', scrollWidth + 'px');
		}
		sliderWidth = $slider.width();

        //console.log("isModeTable", isModeTable, "slidePoints", slidePoints, "scrollWidth", scrollWidth);
	}
	function selectTab(tabIndex) {
		$('.cast_area > .dfs-tab > .dfs-tab-head-wrap > .dfs-tab-head > li > a.on').removeClass('on').trigger('blur');
		var $a = $('.cast_area .dfs-tab .dfs-tab-head li').eq(tabIndex).find('a').addClass('on');
		//window.setTimeout(function(){$a.trigger('focus');}, 0);
	}
	function updateChart() {
		/* 기온 차트 */

		//tchartWidth = slidePoints[slidePoints.length - 1];
		console.log(`기온차트 업데이트 개수 : ${ $('.slide').length }`  );

		var slideSize = $('.slide').length;

		if( slideSize === 0 ){
			console.error( `기온 슬라이드 개수 0개, class의 '.slide'체크 필요` );
		} else {
			$('.slide').each(function(i,e){
				 //console.log(i,$(e).width());
				  tchartWidth += $(e).width();
			});
			//console.log(tchartWidth);
			var tchartSize = $('.cast_area .dfs-tab-body .slide-wrap .tchart').length;
			if( tchartSize === 0 ){
				console.error( `차트 개수 0개, class의 '.cast_area .dfs-tab-body .slide-wrap .tchart' 체크 필요` );
			} else {
				var $tchartWrap = $('.cast_area .dfs-tab-body .slide-wrap .tchart').css({ width: tchartWidth + 'px'});
				var tchartId = "my-tchart";
				var $tchart = $('<div>').attr('id', tchartId).css({ width: '100%', height: '100%'}).appendTo($tchartWrap);

				var data = '[['+$tchartWrap.attr('data-data')+']]';
				//console.log( data );
				var tempList = JSON.parse( data );
				//console.log( tempList );
				_tchart = createTChart(tchartId, tempList[0]);

				/* 중기 최저/최고 기온 차트 */
				/*
				midtchartWidth = scrollWidth - ((isModeTable) ? 0 : 154) - slidePoints[slidePoints.length - 1];
		        midtchartLeft = tchartWidth + ((isModeTable) ? 0 : (isHr1Fct ? 120 : 90) );
				var $midtchartWrap = $('.cast_area .dfs-tab-body .slide-wrap .midtchart').css({ left: midtchartLeft + 'px',width: midtchartWidth + 'px'});
				var midtchartId = "my-midtchart";
				var $midtchart = $('<div>').attr('id', midtchartId).css({ width: '100%', height: '100%'}).appendTo($midtchartWrap);
				var midchartSeries = JSON.parse($midtchartWrap.attr('data-data'));
				var tmnList = midchartSeries[0], tmxList = midchartSeries[1];
				var midtchart = createChart2(midtchartId, tmnList, tmxList);
				*/
			}

		}
	}
	/**
		차트
		series : array of array or..
		options : chart options
	*/


	function createTChart(id, tempList) {
		var minPadding = 1.0 / (tempList.length * 2.0);
		var maxPadding = minPadding;
		var minValue = 999;
		var maxValue = -999;
		for(var i = 0 ; i < tempList.length ; i++) {
			var tempValue = tempList[i];
			if(tempValue > maxValue) maxValue = tempValue;
			if(tempValue < minValue) minValue = tempValue;
		}
		var lineColor = "#0066DB", fontColor = "#3E3A39";

		var lineColor2 = "#D9d9d9", fontColor2 = "#333";

		var chart = Highcharts.chart(id, {
			plotOptions: {
				series: {
					pointStart: 1
				},
				spline: {
					color: lineColor,
					dataLabels: {
						enabled: true,
						style: { fontSize: '14px', fontWeight: 'normal', color: fontColor2, textOutline: false },
						formatter: function () { return this.y + '℃'; }
					},
					enableMouseTracking: false
				}
			},
			series: [{ name: '', data: tempList, showInLegend: false, animation:false }],
			yAxis: {
			    min: minValue-1, max: maxValue+1,
			    tickInterval: 0.1,
			    gridLineColor: "rgba(0,0,0,0)",
			    labels:{ enabled: false },
		    	title: { enabled: false },
			},
			xAxis: {
				lineColor: "rgba(0,0,0,0)",
				tickColor: "rgba(0,0,0,0)",
			    labels: { enabled: false },
		    	title: { enabled: false },
		    	minPadding: minPadding,
    			maxPadding: maxPadding,
    			margin: 0, padding: 0,
			},
			chart: { type:'spline', backgroundColor: "rgba(0,0,0,0)", margin: 0, padding: 0, style: { fontFamily: "'ns', sans-serif" } },
			title: { text: null },
			credits: { enabled:false },
		});
		return chart;
	}
	function createChart2(id, tmnList, tmxList) {
		var minPadding = 1.0 / (tmnList.length * 2.0);
		var maxPadding = minPadding;
		var minValue = 999;
		var maxValue = -999;
		for(var i = 0 ; i < tmnList.length ; i++) {
			var tempValue = tmnList[i];
			if(tempValue > maxValue) maxValue = tempValue;
			if(tempValue < minValue) minValue = tempValue;
		}
		for(var i = 0 ; i < tmxList.length ; i++) {
			var tempValue = tmxList[i];
			if(tempValue > maxValue) maxValue = tempValue;
			if(tempValue < minValue) minValue = tempValue;
		}
		var delta = maxValue - minValue;
		if(delta != 0) {
			maxValue = maxValue + (delta * 0.39);
			minValue = minValue - (delta * 0.5);
		} else {
			maxValue = maxValue + (maxValue * 0.39);
			minValue = minValue - (minValue * 0.5);
		}
		//#0066DB
		var lineColor = "#FF2C00", fontColor = "#3E3A39";
		var chart = Highcharts.chart(id, {
			plotOptions: {
				series: {
					pointStart: 1
				},
				spline: {
					color: '#FF2C00',
					dataLabels: {
						enabled: true,
						style: { fontSize: '14px', fontWeight: 'normal', color: fontColor, textOutline: false },
						formatter: function () { return this.y + '℃'; }
					},
					enableMouseTracking: false
				}
			},
			series: [
					{ name: '', data: tmnList, color:'#0066DB', marker: { symbol:'circle'}, showInLegend: false, animation:false, dataLabels: { overflow: 'allow', y: 25 } },
					{ name: '', data: tmxList, color:'#FF2C00', marker: { symbol:'circle'}, showInLegend: false, animation:false,}
			],
			yAxis: {
				min: minValue, max: maxValue,
				tickInterval: 0.1,
				gridLineColor: "rgba(0,0,0,0)",
				labels:{ enabled: false },
				title: { enabled: false },
			},
			xAxis: {
				lineColor: "rgba(0,0,0,0)",
				tickColor: "rgba(0,0,0,0)",
				labels: { enabled: false },
				title: { enabled: false },
				minPadding: minPadding,
				maxPadding: maxPadding,
				margin: 0, padding: 0,
			},
			chart: { type:'spline', backgroundColor: "rgba(0,0,0,0)", margin: 0, padding: 0, style: { fontFamily: "'ns', sans-serif" } },
			title: { text: null },
			credits: { enabled:false },
		});
		return chart;
	}
}

function createDfsSlider_new(layer_num) {
    var isHr1Fct = $('.cast_layer_'+layer_num+' .cast_area').first().hasClass('hr1-fct');
	var dfsSlider = null;
	var isMovingToTab = false;
	var obj = '.cast_layer_'+layer_num+' .dfs-tab-body .dfs-slider';
	var $slider = $(obj);
	var $slideWrap = $slider.find('.slide-wrap');
	var scrollWidth = 0;
	var slidePoints = null;
	var tchartWidth = 0;
	var midtchartWidth = 0;
	var midtchartLeft = 0;
	var tchartItemCount = 0;
	var isModeChart = false;
	var isModeTable = false;
	var $slides = $slider.find('.slide');
    var $dailyHeads = $slider.find('.slide:not(.day-ten) .daily-head');
	var slideLength = $slides.length;
	var lastTabIndex = 0;
	var sliderWidth = $slider.width();
	prepareSlider();

	dfsSlider = new IScroll('.cast_layer_'+layer_num+' .dfs-tab-body .dfs-slider', { eventPassthrough:true, scrollX: true, scrollY: false, mouseWheel: false, scrollbars: 'custom', resizePolling : 40, probeType : 3, interactiveScrollbars: true});

	updateChart();

	dfsSlider.on('scroll', function(){
        if(isMovingToTab) return;
        var scrollX = -this.x;
        var selectedTabIndex = 0;
        for(var i = 0 ; i < slidePoints.length ; i++) {
            var p1 = slidePoints[i] - (i > 0 ? (isHr1Fct ? 110 : 30) : 0);
            var p2 = (i+1 >= slidePoints.length) ? 99999999 : (slidePoints[i+1] - (isHr1Fct ? 110 : 30));
            if(p1 <= scrollX && scrollX < p2) {
                selectedTabIndex = i;
                break;
            }
        }
        if(isHr1Fct) {
            if(selectedTabIndex < $dailyHeads.length && scrollX >= 0) {
                var selectedSlideLeft = slidePoints[selectedTabIndex];
                var slideWidth = (selectedTabIndex+1 < slidePoints.length) ? ( slidePoints[selectedTabIndex+1] - selectedSlideLeft ) : 9999;
                var marginLeft = scrollX - selectedSlideLeft;
                if(marginLeft < slideWidth - $dailyHeads.eq(selectedTabIndex).width() - 70) {
                    $dailyHeads.eq(selectedTabIndex).css({ 'margin-left': (selectedTabIndex == 0 ? marginLeft : marginLeft)+ 'px'});
                }
            } else if(scrollX < 0) {
                $dailyHeads.eq(0).css({ 'margin-left': 'auto'});
            }
        }
        if(lastTabIndex != selectedTabIndex) {
            if(isHr1Fct) {
                if(lastTabIndex < $dailyHeads.length) {
                	if(lastTabIndex == 0 || isModeTable) {
                		$dailyHeads.eq(lastTabIndex).css({ 'margin-left': 'auto'});
                	} else {
                		$dailyHeads.eq(lastTabIndex).css({ 'margin-left': '23px'});
                	}
                }
            }
			lastTabIndex = selectedTabIndex;
			if(sliderWidth + scrollX < scrollWidth) {
            	selectTab(lastTabIndex);
            }
			if(slidePoints.length - 1 == selectedTabIndex) {
				if(!$('.cast_layer_'+layer_num+' .cast_area .item-lbl').hasClass('off')) {
					$('.cast_layer_'+layer_num+' .cast_area .item-lbl').addClass('off');
				}
				if(!$('.cast_layer_'+layer_num+' .cast_area .item-lbl-midterm').hasClass('on')) {
					$('.cast_layer_'+layer_num+' .cast_area .item-lbl-midterm').addClass('on');
				}
			} else {
				$('.cast_layer_'+layer_num+' .cast_area .item-lbl').removeClass('off');
				$('.cast_layer_'+layer_num+' .cast_area .item-lbl-midterm').removeClass('on');
			}
		}
	});


	/*
		오늘 내일 모레 10일 이후 탭과 스크롤 연동.
	*/
	$(document).on('click', '.cast_layer_'+layer_num+' .cast_area .dfs-tab .dfs-tab-head a', function(e) {
		e.preventDefault();
		$(this).parents('.dfs-tab-head').find('a.on').removeClass('on');
		$(this).addClass('on');
		var date = $(this).attr('data-date');
		var $slider = $('.cast_layer_'+layer_num+' .cast_area .dfs-slider').first();
		var scrollLeft = 0;
		var isExperimentalTab = $(this).parent().attr('data-experimental') == 'Y';
		$slider.find('.daily').each(function(idx, ele) {
        	if(isExperimentalTab) {
        		if(date == $(ele).attr('data-date') && $(this).attr('data-experimental') == 'Y') return false;
        	} else {
        		if(date == $(ele).attr('data-date') && $(this).attr('data-experimental') != 'Y') return false;
        	}

            scrollLeft +=  Math.ceil($(ele).width());
        });
		var isMidTerm = $(this).parent().index() == $(this).parent().parent().find('li').last().index();
		if(isMidTerm && !isModeTable) scrollLeft += 0; //78;
        if(isHr1Fct) scrollLeft -= 0;
		isMovingToTab = true;
		if(dfsSlider) dfsSlider.scrollTo( -scrollLeft, 0, 500, IScroll.utils.ease.quadratic);
		window.setTimeout(function() { isMovingToTab = false;}, 500);
		if(isMidTerm) {
			if(!$('.cast_layer_'+layer_num+' .cast_area .item-lbl').hasClass('off')) {
				$('.cast_layer_'+layer_num+' .cast_area .item-lbl').addClass('off');
			}
			if(!$('.cast_layer_'+layer_num+' .cast_area .item-lbl-midterm').hasClass('on')) {
				$('.cast_layer_'+layer_num+' .cast_area .item-lbl-midterm').addClass('on');
			}
		} else {
			$('.cast_layer_'+layer_num+' .cast_area .item-lbl').removeClass('off');
			$('.cast_layer_'+layer_num+' .cast_area .item-lbl-midterm').removeClass('on');
		}
	});

	// 윈도우 사이즈 변경에 따라 슬라이더 갱신.
	var isMobileSize = $(window).width() <= 1100;
	$(window).resize(function(e) {
		var width = $(window).width();
		if(isMobileSize) {
			if(width > 1100) {
				window.setTimeout(function() {
					prepareSlider();
					if(dfsSlider) dfsSlider.refresh();
				},0);
				isMobileSize = false;
			}
		} else {
			if(width <= 1100) {
				window.setTimeout(function() {
					prepareSlider();
					if(dfsSlider) dfsSlider.refresh();
				},0);
				isMobileSize = true;
			}
		}
	});

	/*
		슬라이더 준비.
	*/
	function prepareSlider() {
		isModeChart = $('.cast_layer_'+layer_num+' .cast_area').hasClass('mode-chart');
		isModeTable = $('.cast_layer_'+layer_num+' .cast_area').hasClass('mode-table');
		scrollWidth = 0;
		slidePoints = [];
		tchartItemCount = 0;

		if(!isModeTable) {
			$slides.each(function(idx, ele) {
				var slideWidth = 0;
				$(ele).find('.item').each(function(idx2, ele2) {
					var w = Math.ceil($(ele2).width());
					slideWidth += w;
					if(idx < slideLength-1) tchartItemCount++;
				});
				$(ele).css('width', slideWidth + 'px');
				slidePoints.push(scrollWidth);
				scrollWidth += slideWidth;
			});

			scrollWidth += 133;
			$slideWrap.css('width', scrollWidth + 'px');
		} else {
			$slides.each(function(idx, ele) {
				var slideWidth = Math.ceil($(ele).width());
				slidePoints.push(scrollWidth);
				scrollWidth += slideWidth;
			});
			$slideWrap.css('width', scrollWidth + 'px');
		}
		sliderWidth = $slider.width();

        //console.log("isModeTable", isModeTable, "slidePoints", slidePoints, "scrollWidth", scrollWidth);
	}
	function selectTab(tabIndex) {
		$('.cast_layer_'+layer_num+' .cast_area > .dfs-tab > .dfs-tab-head-wrap > .dfs-tab-head > li > a.on').removeClass('on').trigger('blur');
		var $a = $('.cast_layer_'+layer_num+' .cast_area .dfs-tab .dfs-tab-head li').eq(tabIndex).find('a').addClass('on');
		//window.setTimeout(function(){$a.trigger('focus');}, 0);
	}
	function updateChart() {
		/* 기온 차트 */
		tchartWidth = slidePoints[slidePoints.length - 1];
		var $tchartWrap = $('.cast_layer_'+layer_num+' .cast_area .dfs-tab-body .slide-wrap .tchart').css({ width: tchartWidth + 'px'});
		var tchartId = "my-tchart"+layer_num;
		var $tchart = $('<div>').attr('id', tchartId).css({ width: '100%', height: '100%'}).appendTo($tchartWrap);
		var tempList = JSON.parse($tchartWrap.attr('data-data'));
		var tchart = createTChart(tchartId, tempList[0]);
		/* 중기 최저/최고 기온 차트 */
		midtchartWidth = scrollWidth - ((isModeTable) ? 0 : 154) - slidePoints[slidePoints.length - 1];
        midtchartLeft = tchartWidth + ((isModeTable) ? 0 : (isHr1Fct ? 120 : 90) );
		var $midtchartWrap = $('.cast_layer_'+layer_num+' .cast_area .dfs-tab-body .slide-wrap .midtchart').css({ left: midtchartLeft + 'px',width: midtchartWidth + 'px'});
		var midtchartId = "my-midtchart"+layer_num;
		var $midtchart = $('<div>').attr('id', midtchartId).css({ width: '100%', height: '100%'}).appendTo($midtchartWrap);
		var midchartSeries = JSON.parse($midtchartWrap.attr('data-data'));
		var tmnList = midchartSeries[0], tmxList = midchartSeries[1];
		var midtchart = createChart2(midtchartId, tmnList, tmxList);
	}
	/**
		차트
		series : array of array or..
		options : chart options
	*/

	function createTChart(id, tempList) {
		var minPadding = 1.0 / (tempList.length * 2.0);
		var maxPadding = minPadding;
		var minValue = 999;
		var maxValue = -999;
		for(var i = 0 ; i < tempList.length ; i++) {
			var tempValue = tempList[i];
			if(tempValue > maxValue) maxValue = tempValue;
			if(tempValue < minValue) minValue = tempValue;
		}
		var lineColor = "#0066DB", fontColor = "#3E3A39";
		var chart = Highcharts.chart(id, {
			plotOptions: {
				series: {
					pointStart: 1
				},
				spline: {
					color: lineColor,
					dataLabels: {
						enabled: true,
						style: { fontSize: '14px', fontWeight: 'normal', color: fontColor, textOutline: false },
						formatter: function () { return this.y + '℃'; }
					},
					enableMouseTracking: false
				}
			},
			series: [{ name: '', data: tempList, showInLegend: false, animation:false }],
			yAxis: {
			    min: minValue-1, max: maxValue+1,
			    tickInterval: 0.1,
			    gridLineColor: "rgba(0,0,0,0)",
			    labels:{ enabled: false },
		    	title: { enabled: false },
			},
			xAxis: {
				lineColor: "rgba(0,0,0,0)",
				tickColor: "rgba(0,0,0,0)",
			    labels: { enabled: false },
		    	title: { enabled: false },
		    	minPadding: minPadding,
    			maxPadding: maxPadding,
    			margin: 0, padding: 0,
			},
			chart: { type:'spline', backgroundColor: "rgba(0,0,0,0)", margin: 0, padding: 0, style: { fontFamily: "'ns', sans-serif" } },
			title: { text: null },
			credits: { enabled:false },
		});
		return chart;
	}
	function createChart2(id, tmnList, tmxList) {
		var minPadding = 1.0 / (tmnList.length * 2.0);
		var maxPadding = minPadding;
		var minValue = 999;
		var maxValue = -999;
		for(var i = 0 ; i < tmnList.length ; i++) {
			var tempValue = tmnList[i];
			if(tempValue > maxValue) maxValue = tempValue;
			if(tempValue < minValue) minValue = tempValue;
		}
		for(var i = 0 ; i < tmxList.length ; i++) {
			var tempValue = tmxList[i];
			if(tempValue > maxValue) maxValue = tempValue;
			if(tempValue < minValue) minValue = tempValue;
		}
		var delta = maxValue - minValue;
		if(delta != 0) {
			maxValue = maxValue + (delta * 0.39);
			minValue = minValue - (delta * 0.5);
		} else {
			maxValue = maxValue + (maxValue * 0.39);
			minValue = minValue - (minValue * 0.5);
		}
		//#0066DB
		var lineColor = "#FF2C00", fontColor = "#3E3A39";
		var chart = Highcharts.chart(id, {
			plotOptions: {
				series: {
					pointStart: 1
				},
				spline: {
					color: '#FF2C00',
					dataLabels: {
						enabled: true,
						style: { fontSize: '14px', fontWeight: 'normal', color: fontColor, textOutline: false },
						formatter: function () { return this.y + '℃'; }
					},
					enableMouseTracking: false
				}
			},
			series: [
					{ name: '', data: tmnList, color:'#0066DB', marker: { symbol:'circle'}, showInLegend: false, animation:false, dataLabels: { overflow: 'allow', y: 25 } },
					{ name: '', data: tmxList, color:'#FF2C00', marker: { symbol:'circle'}, showInLegend: false, animation:false,}
			],
			yAxis: {
				min: minValue, max: maxValue,
				tickInterval: 0.1,
				gridLineColor: "rgba(0,0,0,0)",
				labels:{ enabled: false },
				title: { enabled: false },
			},
			xAxis: {
				lineColor: "rgba(0,0,0,0)",
				tickColor: "rgba(0,0,0,0)",
				labels: { enabled: false },
				title: { enabled: false },
				minPadding: minPadding,
				maxPadding: maxPadding,
				margin: 0, padding: 0,
			},
			chart: { type:'spline', backgroundColor: "rgba(0,0,0,0)", margin: 0, padding: 0, style: { fontFamily: "'ns', sans-serif" } },
			title: { text: null },
			credits: { enabled:false },
		});
		return chart;
	}
}