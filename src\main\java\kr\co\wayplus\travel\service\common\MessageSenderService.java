package kr.co.wayplus.travel.service.common;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.servlet.http.HttpServletRequest;
import kr.co.wayplus.travel.mapper.admin.AdminMapper;
import kr.co.wayplus.travel.mapper.common.MessageSenderMapper;
import kr.co.wayplus.travel.mapper.manage.AlertManageMapper;
import kr.co.wayplus.travel.mapper.manage.MenuManageMapper;
import kr.co.wayplus.travel.mapper.manage.PolicyManageMapper;
import kr.co.wayplus.travel.mapper.manage.ProductManageMapper;
import kr.co.wayplus.travel.mapper.manage.ReservationManageMapper;
import kr.co.wayplus.travel.model.*;
import kr.co.wayplus.travel.service.api.NCloudApiService;
import kr.co.wayplus.travel.service.user.UserService;
import kr.co.wayplus.travel.util.CryptoUtil;
import kr.co.wayplus.travel.util.LoggerUtil;
import kr.co.wayplus.travel.util.NCloudApiUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

import java.net.InetAddress;
import java.net.URISyntaxException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class MessageSenderService {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final NCloudApiUtil nCloudApiUtil;
    private final NCloudApiService nCloudApiService;
    private final LoggerService loggerService;
    private final UserService userService;
    private final MessageSenderMapper messageSenderMapper;
    private final AlertManageMapper alertManageMapper;
    private final ReservationManageMapper reservationManageMapper;
    private final ProductManageMapper productManageMapper;
    private final AdminMapper adminMapper;
    private final MenuManageMapper menuManageMapper;
    private final HttpServletRequest request;

    @Value("${server.address:localhost}")
    private String serverAddress;

    @Value("${server.port:8081}")
    private int serverPort;

    @Value("${server.servlet.context-path:}")
    private String contextPath;

    public MessageSenderService(MessageSenderMapper messageSenderMapper, NCloudApiUtil nCloudApiUtil, NCloudApiService nCloudApiService, LoggerService loggerService, UserService userService, AlertManageMapper alertManageMapper
    , ReservationManageMapper reservationManageMapper, ProductManageMapper productManageMapper, AdminMapper adminMapper, MenuManageMapper menuManageMapper, HttpServletRequest request) {
        this.nCloudApiUtil = nCloudApiUtil;
        this.nCloudApiService = nCloudApiService;
        this.loggerService = loggerService;
        this.userService = userService;
        this.messageSenderMapper = messageSenderMapper;
        this.alertManageMapper = alertManageMapper;
        this.reservationManageMapper = reservationManageMapper;
        this.productManageMapper = productManageMapper;
        this.adminMapper = adminMapper;
        this.menuManageMapper = menuManageMapper;
        this.request = request;
    }

    public void sendMailFromSet(String alarmType, int formatId, String type, Object paramUser) throws Exception{
        logger.debug("메일 발송 시작");
        SettingMailServer mailServer = messageSenderMapper.selectMailServer(alarmType);
        if(mailServer == null) throw new RuntimeException("SMTP 서버 정보를 찾을 수 없습니다.");
/* old ver
        SettingMailFormat mailFormat = messageSenderMapper.selectMailFormat(formatId);
        if(mailFormat == null) throw new RuntimeException("발신 메일 양식을 찾을 수 없습니다.");

        ArrayList<SettingMailReceiver> mailReceivers = messageSenderMapper.selectMailReceiverList(mailServer.getId());
*/      

        String userEmail = "";
        String subject = "";
        String content = "";

        ArrayList<SettingMailReceiver> mailReceivers = new ArrayList<>();
        SettingMailFormat mailFormat;
        if ( type.equals("pwd") ) {
            mailFormat = messageSenderMapper.selectMailFormat(formatId);
            subject = mailFormat.getTitle();

            if ( paramUser instanceof LoginUser ) {
                LoginUser user = (LoginUser) paramUser;
                SettingMailReceiver settingMailReceiver = new SettingMailReceiver();
                settingMailReceiver.setEmail(user.getUserEmail());
                mailReceivers.add(settingMailReceiver);
                //임시비밀번호로 변경
                String randomCode = generateRandomNumberString(6);
                user.setUserPassword(randomCode);
                userEmail = user.getUserEmail();

                userService.updateUserPasswordByLost(user, false);

                content = mailFormat.getMailContent()+"\n"+randomCode;
            }
            if(mailFormat == null) throw new RuntimeException("발신 메일 양식을 찾을 수 없습니다.");
        }
        else {
            mailFormat = messageSenderMapper.selectMailFormat(formatId);
            subject = mailFormat.getTitle();
            content = mailFormat.getMailContent();
            mailReceivers = messageSenderMapper.selectMailReceiverList(mailServer.getId());
            if(mailFormat == null) throw new RuntimeException("발신 메일 양식을 찾을 수 없습니다.");
        }

        if(!mailReceivers.isEmpty()) {
            LoginUser user = new LoginUser();
            if(SecurityContextHolder.getContext().getAuthentication() != null
				&& SecurityContextHolder.getContext().getAuthentication().getPrincipal() != null
				&& !SecurityContextHolder.getContext().getAuthentication().getPrincipal().equals("anonymousUser")){
                user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
                userEmail = user.getUserEmail();
            }

            Properties properties = new Properties();
            properties.put("mail.smtp.host", mailServer.getSmtpServer());
            properties.put("mail.smtp.port", mailServer.getSmtpPort());
            properties.put("mail.smtp.auth", mailServer.getSmtpAuthorizeRequired().equals("Y") ? true:false);
            properties.put("mail.smtp.starttls.enable", mailServer.getSmtpSecureType().equals("TLS") ? true:false);
            if(mailServer.getSmtpSecureType().equals("TLS")
                || mailServer.getSmtpSecureType().equals("SSL")) {
                properties.put("mail.smtp.starttls.trust", mailServer.getSmtpServer());
            }

            for (SettingMailReceiver receiver : mailReceivers) {
                logger.debug("Receiver Add : " + receiver.getEmail());
                Session mailSession = Session.getInstance(properties, new Authenticator() {
                    @Override
                    protected PasswordAuthentication getPasswordAuthentication() {
                        return new PasswordAuthentication(mailServer.getSmtpLoginId(), mailServer.getSmtpLoginPw());
                    }
                });

                Message message = new MimeMessage(mailSession);
                message.setFrom(new InternetAddress(mailServer.getSmtpEmail(), mailServer.getSmtpUsername(), "UTF-8"));
                message.addRecipient(Message.RecipientType.TO, new InternetAddress(receiver.getEmail(), receiver.getUsername()));
                message.setSubject(subject);
                message.setContent(content, "text/html; charset=utf-8");

                LogMailSend logMailSend = new LogMailSend(mailServer.getSmtpEmail(), receiver.getEmail(), LoggerUtil.getPropertiesAsString(properties), formatId, mailFormat.getTitle(), mailFormat.getMailContent(), "text/html; charset=utf-8", userEmail);
                logMailSend.setId(loggerService.writeMailingLog(logMailSend));
                try {
                    Transport.send(message);
                    logMailSend.setSendResult("SUCCESS");
                    loggerService.updateMailingLogResult(logMailSend);
                }catch (Exception e){
                    logger.error("Mail Send Error : " + e.getMessage());
                    logMailSend.setSendResult("ERROR");
                    logMailSend.setSendResultMessage(e.getMessage());
                    loggerService.updateMailingLogResult(logMailSend);
                }
            }

            logger.debug("메일 발송 완료");
        }else{
            logger.debug("메일 수신자 목록이 없습니다.");
        }
        
        logger.debug("메일 발송 종료");
    }

    public void pushMessageQueueFromSet(String alarmType, int formatId) {
        logger.debug("메시지 발송 QUEUE 시작");
        SettingMessage messageSet = messageSenderMapper.selectMessageSetting(alarmType);
        if(messageSet == null) throw new RuntimeException("메시지 발송 설정 정보를 찾을 수 없습니다.");

        SettingMessageFormat messageFormat = messageSenderMapper.selectMessageFormat(formatId);
        if(messageFormat == null) throw new RuntimeException("발신 메시지 양식을 찾을 수 없습니다.");

        ArrayList<SettingMessageReceiver> messageReceivers = messageSenderMapper.selectMessageReceiverList(messageSet.getId());

        if(!messageReceivers.isEmpty()) {
            LoginUser user = new LoginUser();
            if(SecurityContextHolder.getContext().getAuthentication() != null
                    && SecurityContextHolder.getContext().getAuthentication().getPrincipal() != null){
                user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            }

            String startTime, endTime, nowTime, tempTime[];
            MessageQueue message = new MessageQueue();
            message.setMessageType(messageSet.getApiType());
            message.setContentType(messageFormat.getTemplateCode());
            message.setSubject(messageFormat.getTitle());
            message.setContent(messageFormat.getContent());
            message.setSendRequestUserid(user.getUserEmail());
            message.setCreateId(user.getUserEmail());
            message.setSendStatusCode("A");

            if(messageSet.getApiUseTime() != null) {
                tempTime = messageSet.getApiUseTime().split("-");
                startTime = tempTime[0];
                endTime = tempTime[1];

                SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
                nowTime = timeFormat.format(new Date());

                try{
                    Date startDate = timeFormat.parse(startTime);
                    Date nowDate = timeFormat.parse(nowTime);
                    Date endDate = timeFormat.parse(endTime);
                    if(startDate.before(nowDate) && endDate.after(nowDate)){
                        logger.debug("메시지 설정시간 내 발송입니다.");
                        message.setQueueType("normal");

                    }else{
                        logger.debug("메시지 설정시간 외 발송입니다.");
                        SimpleDateFormat startFormat = new SimpleDateFormat("yyyy-MM-dd " + startTime + ":00");
                        Calendar cal = GregorianCalendar.getInstance();
                        if(endDate.before(nowDate)){
                            //발송 시간 초과 당일의 경우 익일 시작시 발송
                            cal.add(Calendar.DATE, 1);
                        }
                        message.setQueueType(messageSet.getApiQueueType());
                        message.setSendApplyTime(startFormat.format(cal.getTime()));
                    }
                }catch (Exception e){
                    logger.error("메시지 발송 설정시간 오류입니다.");
                    logger.error(e.getMessage());
                    message.setQueueType("normal");
                }
            }else{
                message.setQueueType("normal");
            }

            if(message.getQueueType().equals("no")){
                logger.debug("메시지 시간외 발송이 꺼져있습니다.");
            }else{
                for (SettingMessageReceiver receiver : messageReceivers) {
                    logger.debug("Receiver Add Queue : " + receiver.getMobile().replaceAll("-", ""));
                    message.setToName(receiver.getUsername());
                    message.setToAddress(receiver.getMobile().replaceAll("-", ""));
                    if(message.getMessageType().equals("ncloud.BizMessage")){
                        message.setContent(convertBizMessageVariable(messageFormat.getTemplateCode(), message.getContent()));
                    }

                    messageSenderMapper.insertMessageQueue(message);
                }
                logger.debug("메시지 발송 QUEUE 등록 완료");
            }
        }else{
            logger.debug("메시지 수신자 목록이 없습니다.");
        }
        logger.debug("메시지 발송 QUEUE 종료");
    }

    public void popMessageQueueSendMessage() {
        logger.debug("Message Send START");
        int sendPendingCount = messageSenderMapper.selectPendingMessageQueueCount("ncloud.BizMessage");
        if(sendPendingCount > 0){
            ArrayList<MessageQueue> messageQueues = messageSenderMapper.selectPendingMessageQueue("ncloud.BizMessage");
            ArrayList<MessageQueue> summaryMessage = new ArrayList<>();
            ArrayList<MessageQueue> normalMessage = new ArrayList<>();
            HashMap<String, ArrayList<MessageQueue>> messageHashMap = new HashMap<>();
            for (MessageQueue message : messageQueues) {
                switch (message.getQueueType()){
                    case "normal":
                        normalMessage.add(message);
                        break;
                    case "summary":
                        summaryMessage.add(message);
                        break;
                }
            }

            if(summaryMessage.size() > 0){
                logger.debug("요약 메시지 발송 처리");
                String apiUrl = "https://sens.apigw.ntruss.com/sms/v2";
                nCloudApiUtil.setApiBaseUrlAndFormat(apiUrl, "JSON");
                ArrayList<MessageGroupCount> groupCount = messageSenderMapper.selectPendingMessageQueueGroupCount("ncloud.BizMessage");

                for (MessageGroupCount counter: groupCount) {
                    try{
                        NCloudSmsMessageContainer messageContainer = new NCloudSmsMessageContainer();
                        NCloudSmsMessage message = new NCloudSmsMessage();
                        message.setTo(counter.getToAddress());
                        messageContainer.setMessages(Collections.singletonList(message));
                        messageContainer.setType("SMS");
                        messageContainer.setContent("COMM");
                        messageContainer.setFrom("0647570110");
                        messageContainer.setContent(String.format("시간외 설정된 메시지가 %s건 있습니다.", counter.getCnt()));
                        NCloudSmsMessageResult result = nCloudApiUtil.sendSmsMessage(messageContainer);
                        logger.debug(String.format("전달결과: [ID]: %s, 결과[%s] %s", result.getRequestId(), result.getStatusCode(), result.getStatusName()));
                        HashMap<String, Object> paramMap = new HashMap<>();
                        paramMap.put("messageType", "ncloud.BizMessage");
                        paramMap.put("to", counter.getToAddress());
                        paramMap.put("statusCode", "FIN");
                        paramMap.put("resultTime", result.getRequestTime());
                        paramMap.put("result", result.getStatusCode());
                        paramMap.put("resultMessage", result.getStatusName());
                        paramMap.put("lastUpdateId", "sendMessageQueueNCloudBizMessage");
                        messageSenderMapper.updatePendingSummaryMessageSendComplete(paramMap);
                    } catch (JsonProcessingException | URISyntaxException e) {
                        logger.error("Message Send Error : " + counter.getToAddress());
                        logger.error(e.getMessage());
                        throw new RuntimeException(e);
                    }
                }
            }else{
                logger.debug("일반 메시지 발송 처리");
                String apiUrl = "https://sens.apigw.ntruss.com/alimtalk/v2";
                nCloudApiUtil.setApiBaseUrlAndFormat(apiUrl, "JSON");
                for(MessageQueue bizMessage : normalMessage){
                    try {
                        NCloudBizMessageContainer messageContainer = new NCloudBizMessageContainer();
                        NCloudBizMessage ncloudBizMessage = new NCloudBizMessage();
                        ncloudBizMessage.setTo(bizMessage.getToAddress());
                        ncloudBizMessage.setContent(bizMessage.getContent());
                        ncloudBizMessage.setUseSmsFailover(true);
                        messageContainer.setPlusFriendId("@wayplus");
                        messageContainer.setTemplateCode(bizMessage.getContentType());
                        messageContainer.setMessages(Collections.singletonList(ncloudBizMessage));
                        NCloudBizMessageResult result = nCloudApiUtil.sendBizMessage(messageContainer);
                        logger.debug(String.format("전달결과: [ID]: %s, 결과[%s] %s", result.getRequestId(), result.getStatusCode(), result.getMessages()));
                        HashMap<String, Object> paramMap = new HashMap<>();
                        paramMap.put("id", bizMessage.getId());
                        paramMap.put("messageType", "ncloud.BizMessage");
                        paramMap.put("to", bizMessage.getToAddress());
                        paramMap.put("statusCode", "FIN");
                        paramMap.put("resultTime", result.getRequestTime());
                        paramMap.put("result", result.getStatusCode());
                        paramMap.put("resultMessage", LoggerUtil.getObjectListToString(result.getMessages()));
                        paramMap.put("lastUpdateId", "sendMessageQueueNCloudBizMessage");
                        messageSenderMapper.updatePendingNormalMessageSendComplete(paramMap);
                    } catch (JsonProcessingException | URISyntaxException | RuntimeException e) {
                        logger.error("Message Send Error : " + bizMessage.getToAddress());
                        logger.error(e.getMessage());
                        throw new RuntimeException(e);
                    }
                }
            }

        }else{
            logger.debug("발송 대기중인 메시지가 없습니다.");
        }
        logger.debug("Message Send END");
    }

    public void productMessageSendCheckService() {
        logger.debug("Product Message Send START");
        String[] sendMessageTypeArr = {"checkIn", "checkOut"};
        
        StringBuilder url = new StringBuilder();
        url.append("http://").append(serverAddress);
        
        if (serverPort != 80) {
            url.append(":").append(serverPort);
        }
        
        if (contextPath != null && !contextPath.isEmpty()) {
            url.append(contextPath);
        }
        
        try {
            for (String sendMessageType : sendMessageTypeArr) {
                // 1-1. SMS 정책 조회   
                HashMap<String, Object> smsPolicyParams = new HashMap<>();
                smsPolicyParams.put("policySendScheduleType", sendMessageType);
                smsPolicyParams.put("useYn", "Y");
                smsPolicyParams.put("deleteYn", "N");
                
                SmsPolicy smsPolicy = alertManageMapper.selectOneSmsPolicy(smsPolicyParams);
                if (smsPolicy == null || smsPolicy.getPolicySendScheduleTime() == null) {
                    logger.warn(sendMessageType+"에 관련한 SMS발신 설정을 찾을 수 없습니다.");
                    continue;
                }
                
                // 1-2. 유효한 예약 목록 조회
                HashMap<String, Object> reservationParams = new HashMap<>();
                reservationParams.put("cancelYn", "N");
                reservationParams.put("deleteYn", "N");
                List<Reservation> reservationList = reservationManageMapper.selectListReservation(reservationParams);
                
                ObjectMapper objectMapper = new ObjectMapper();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                // 1-3. 예약 목록 처리
                for (Reservation reservation : reservationList) {
                String formattedDate;
                    try {
                        // JSON 파싱
                        JsonNode rootNode = objectMapper.readTree(reservation.getTravelScheduleJson());
                        JsonNode dataNode = rootNode.get("data");

                        
                        //숙소용 체크인/체크아웃 메시지 발송용
                        if ( dataNode.has("productType") 
                            && "stay".equals(dataNode.get("productType").asText()) ) {
                            String travelSchedule = dataNode.get("travelSchedule").asText();
                            String checkInOutDate = "";
                            LocalDateTime checkInOutDateTime = null;
                            String messageContent = "";

                            HashMap<String, Object> productParams = new HashMap<>();
                            productParams.put("productTourId", reservation.getProductTourId());
                            ProductInfo productInfo = productManageMapper.selectProductInfo(productParams);
                            
                            if (productInfo == null || productInfo.getProductStayCheckIn() == null || productInfo.getProductStayCheckOut() == null) {
                                logger.error( " ProductTourId 상품의 체크인/체크아웃 시간을 찾을 수 없습니다.", reservation.getProductTourId());
                                continue;
                            }
                            
                            switch (sendMessageType) {
                                case "checkOut":
                                    LocalDate checkOutDate = LocalDate.now().minusDays(1);
                                    formattedDate = checkOutDate.format(formatter);
                                    checkInOutDate = travelSchedule.split("~")[1].trim();
                                    if ( checkInOutDate.equals(formattedDate) ) {
                                        String checkOutTime = productInfo.getProductStayCheckOut().replaceAll(".*?(\\d{2}:\\d{2}).*", "$1").trim();
                                        checkInOutDateTime = travelScheduleFormat(checkInOutDate, checkOutTime);
                                        messageContent = String.format(
                                            "안녕하시겨!\n\n" +
                                            "잠시섬 16기 크루와 호스트입니다:)\n\n" +
                                            "잠시섬 16기에서 보낸 시간은 어떠셨나요?\n" +
                                            "잠시섬을 통해 만나고 연결되고 확장되어 갈 수 있어 기쁘고 감사했습니다.\n\n" +
                                            "무더운 여름이지만, 돌아가셔도 하루하루 나답게 보내시고,\n강화유니버스에서 다시 만날 날을 소망합니다!\n\n" +
                                            "더불어 시간되실 때 ‘잠시섬’ 리뷰와 아래 설문조사를 작성해주시면 감사하겠습니다. 저희가 더 나은 ‘잠시섬’을 운영하고 많은 분들과 함께 이어가기 위해 리뷰와 설문조사가 큰 힘이 되니 꼭 부탁드릴게요!\n\n" +
                                            "▶ 리뷰 작성 : https://www.guniverse.net/jl-community/review \n" +
                                            "▶ 잠시섬 피드백 및 설문조사 : https://forms.gle/h76CB1EDJqjvJaf98 \n\n" +
                                            "그럼, 일상 평안하고 행복하게 지내시길 늘 응원하고 소망합니다. 또 만나요:) 감사합니다!"
                                        );
                                        break;
                                    }
                                default:
                                    break;
                            }
                            
                            if ( checkInOutDateTime != null ) {
                                SmsPolicy paramSmsPolicy = new SmsPolicy();
                                paramSmsPolicy.setSendMessageType(sendMessageType);
                                paramSmsPolicy.setMessageContent(messageContent);
                                paramSmsPolicy.setUseYn(smsPolicy.getUseYn());
                                paramSmsPolicy.setUserSendYn(smsPolicy.getUserSendYn());
                                paramSmsPolicy.setAdminSendYn(smsPolicy.getAdminSendYn());
                                sendMessage(reservation, paramSmsPolicy);
                            }
                        } 
                        // else if ( sendMessageType.equals("checkIn") 
                        //     && dataNode.has("productType") 
                        //     && "product".equals(dataNode.get("productType").asText()) ) {//프로그램 발송용
                        //     String rsvDate = dataNode.get("travelSchedule").asText();
                        //     String programMessageContent = "";
                           
                        //     HashMap<String, Object> productParams = new HashMap<>();
                        //     productParams.put("productTourId", reservation.getProductTourId());
                        //     ProductInfo productInfo = productManageMapper.selectProductInfo(productParams);
                        //     if (productInfo == null || productInfo.getProductRunStartTime() == null) {
                        //         logger.error( " ProductTourId 상품의 프로그램 시작 시간을 찾을 수 없습니다.", reservation.getProductTourId());
                        //         continue;
                        //     }
                        //     LocalDate programStartDate = travelScheduleFormat(rsvDate, productInfo.getProductRunStartTime()).toLocalDate();
                        //     LocalDateTime programStartDateTime = travelScheduleFormat(rsvDate, productInfo.getProductRunStartTime());
                        //     LocalDate checkInDate = LocalDate.now().plusDays(1);
                        //     formattedDate = checkInDate.format(formatter);
                        //     if ( programStartDate.format(formatter).equals(formattedDate) ) {
                        //         programMessageContent = String.format(
                        //             "안녕하세요. 협동조합 청풍입니다.\n예약하신 [%s] 프로그램의 시작 시간은 %s이며 하루전입니다.\n" +
                        //             "늦지 않게 방문해주세요!",
                        //             productInfo.getProductTitle(),
                        //             programStartDateTime.format(DateTimeFormatter.ofPattern("yyyy년 MM월 dd일 HH시 mm분")),
                        //             smsPolicy.getPolicySendScheduleTime()
                        //         );
                        //         if ( programStartDate != null ) {
                        //             SmsPolicy paramSmsPolicy = new SmsPolicy();
                        //             paramSmsPolicy.setSendMessageType(sendMessageType);
                        //             paramSmsPolicy.setMessageContent(programMessageContent);
                        //             paramSmsPolicy.setUseYn(smsPolicy.getUseYn());
                        //             paramSmsPolicy.setUserSendYn(smsPolicy.getUserSendYn());
                        //             paramSmsPolicy.setAdminSendYn(smsPolicy.getAdminSendYn());
                        //             sendMessage(reservation, paramSmsPolicy);
                        //         }
                        //     }
                        // }
                    } catch (Exception e) {
                        //개별 SMS 발송 실패 로그 저장
                        LogMessageFail logMessageFail = new LogMessageFail();
                        logMessageFail.setErrorContent(String.valueOf(e));
                        logMessageFail.setTo(reservation.getUserMobile().replaceAll("-", ""));
                        logMessageFail.setSendReservationId(reservation.getId());
                        logMessageFail.setSendMessageType(sendMessageType);
                        messageSenderMapper.insertLogMessageFail(logMessageFail);
                        
                        logger.error("SMS 발송 실패: reservationId={}, type={}, error={}", 
                        reservation.getId(), sendMessageType, e.getMessage(), e);
                        e.printStackTrace();
                    }
                }
            }
            
        } catch (Exception e) {
            // 전체 서비스 실패 로그 저장
            LogMessageFail logMessageFail = new LogMessageFail();
            logMessageFail.setErrorContent(String.valueOf(e));
            logMessageFail.setContent("문자 발송 서비스 오류");
            messageSenderMapper.insertLogMessageFail(logMessageFail);
            
            logger.error("Error in productMessageSendCheckService", e);
            e.printStackTrace();
            throw new RuntimeException("Failed to process check-in messages", e);
        }
        logger.debug("Product Message Send END");
    }

    private boolean isMessageAlreadySent(Long reservationId, String userMobile, String sendMessageType) {
        Map<String, Object> params = new HashMap<>();
        params.put("sendReservationId", reservationId);
        params.put("sendMessageType", sendMessageType);
        params.put("to", userMobile);
        return messageSenderMapper.existsMessageHistory(params);
    }

    private void sendMessage(Reservation reservation, SmsPolicy paramSmsPolicy) {
        String sendMessageType = paramSmsPolicy.getSendMessageType();
        String messageContent = paramSmsPolicy.getMessageContent();
        ZoneId zoneId = ZoneId.of("Asia/Seoul");
 
        // 중복 발송 체크
        if (!isMessageAlreadySent(reservation.getId(), reservation.getUserMobile().replaceAll("-", ""), sendMessageType)) {
            List<NCloudSmsMessage> messages = new ArrayList<>();
            if ( "Y".equals(paramSmsPolicy.getUseYn())
                && "Y".equals(paramSmsPolicy.getUserSendYn()) ) {
                    NCloudSmsMessage msg1 = new NCloudSmsMessage();
                    msg1.setTo(reservation.getUserMobile().replaceAll("-", ""));
                    msg1.setContent(messageContent);
                    msg1.setMessageType("LMS");
                    msg1.setSendReservationId(reservation.getId());
                    msg1.setSendMessageType(sendMessageType);
                    messages.add(msg1);
                    
            } 
            if ( "Y".equals(paramSmsPolicy.getUseYn())
                && "Y".equals(paramSmsPolicy.getAdminSendYn()) ) {
                    NCloudSmsMessage msg2 = new NCloudSmsMessage();

                    HashMap<String, Object> recivedParam = new HashMap<>();
                    recivedParam.put("useYn", "Y");
                    List<SmsRecivedUser> recivedUsers = adminMapper.selectListSmsRecivedUser(recivedParam);

                    for (SmsRecivedUser recivedUser : recivedUsers) {
                        HashMap<String, Object> userParam = new HashMap<>();
                        userParam.put("role", "ADMIN");
                        userParam.put("userEmail", recivedUser.getUserEmail());

                        msg2.setTo(userService.selectUserListByRole(userParam).get(0).getUserMobile().replaceAll("-", ""));
                        msg2.setContent("자동 문자발송 서비스입니다.\n[" + reservation.getProductTitle() + "]를 예약한 고객님들에게\n예약시간 관련한 안내 문자를 전송하였습니다.");
                        msg2.setMessageType("LMS");
                        msg2.setSendReservationId(reservation.getId());
                        msg2.setSendMessageType(sendMessageType);
                        messages.add(msg2);
                    }
            }

            nCloudApiService.sendMultipleSmsMessage(messages, "admin");
        }
    }

    private LocalDateTime travelScheduleFormat(String date, String time) {
        String[] dateParts = date.split("-");
        DecimalFormat df = new DecimalFormat("00");
        String formattedDate = dateParts[0] + "-" + 
        df.format(Integer.parseInt(dateParts[1])) + "-" + 
        df.format(Integer.parseInt(dateParts[2]));

        LocalDateTime returnDate = LocalDateTime.parse(
            formattedDate + " " + time,
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
        );

        return returnDate;
    }

    private String convertBizMessageVariable(String templateCode, String content) {
        switch (templateCode){
            case "AUTH0001":
                String randomCode = generateRandomNumberString(6);
                content = content.replaceAll("#\\{번호\\}", randomCode);
                logger.debug(String.format("TemplateCode: %s , Replace Random Code Number : %s", templateCode, randomCode));
            break;
        }
        return content;
    }

    private String generateRandomNumberString(int digit) {
        int min = (int) Math.pow(10, digit-1);
        int max = (int) Math.pow(10, digit) - 1;
        int random = (int) (Math.random() * (max - min + 1)) + min;
        return String.valueOf(random);
    }


}
