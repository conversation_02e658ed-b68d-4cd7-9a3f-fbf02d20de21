package kr.co.wayplus.travel.service.manage;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import kr.co.wayplus.travel.mapper.manage.SurveyManageMapper;
import kr.co.wayplus.travel.model.PlaceSpot;
import kr.co.wayplus.travel.model.Survey;
import kr.co.wayplus.travel.model.SurveyImage;
import kr.co.wayplus.travel.model.SurveyQuestion;
import kr.co.wayplus.travel.model.SurveyQuestionAnswer;
import kr.co.wayplus.travel.model.SurveyRecommand;
import kr.co.wayplus.travel.model.SurveyResult;

@Service
public class SurveyManageService {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final SurveyManageMapper mapper;

    public SurveyManageService(SurveyManageMapper mapper) {
        this.mapper = mapper;
    }

//	<!--################################### Survey ###################################-->
    public int selectCountSurvey(HashMap<String, Object> paramMap) {
    	return mapper.selectCountSurvey(paramMap);
    }
    public ArrayList<Survey> selectListSurvey(HashMap<String, Object> paramMap) {
    	return mapper.selectListSurvey(paramMap);
    }
	public Survey selectOneSurvey(HashMap<String, Object> paramMap) {
		return mapper.selectOneSurvey(paramMap);
	}
	public Survey saveSurvey(Survey survey) throws SQLException {
		if(survey.getId() == null) {
			mapper.insertSurvey(survey);
		} else {
			mapper.updateSurvey(survey);
		}
		return survey;
	}
	public void insertSurvey(Survey survey) throws SQLException {
		mapper.insertSurvey(survey);
	}
	public void updateSurvey(Survey survey) throws SQLException {
		mapper.updateSurvey(survey);
	}
	public void deleteSurvey(Survey survey) throws SQLException {
		mapper.deleteSurvey(survey);
	}
//	<!--################################### SurveyQuestion ###################################-->
    public int selectCountSurveyQuestion(HashMap<String, Object> paramMap) {
    	return mapper.selectCountSurveyQuestion(paramMap);
    }
    public ArrayList<SurveyQuestion> selectListSurveyQuestion(HashMap<String, Object> paramMap) {
    	return mapper.selectListSurveyQuestion(paramMap);
    }
	public SurveyQuestion selectOneSurveyQuestion(HashMap<String, Object> paramMap) {
		return mapper.selectOneSurveyQuestion(paramMap);
	}
	public SurveyQuestion saveSurveyQuestion(SurveyQuestion sq) throws SQLException {
		if(sq.getId() == null) {
			mapper.insertSurveyQuestion(sq);
		} else {
			mapper.updateSurveyQuestion(sq);
		}
		return sq;
	}
	public void insertSurveyQuestion(SurveyQuestion sq) throws SQLException {
		mapper.insertSurveyQuestion(sq);
	}
	public void updateSurveyQuestion(SurveyQuestion sq) throws SQLException {
		mapper.updateSurveyQuestion(sq);
	}
	public void deleteSurveyQuestion(SurveyQuestion sq) throws SQLException {
		mapper.deleteSurveyQuestion(sq);
	}
//	<!--################################### SurveyImage ###################################-->
	public int selectCountSurveyImage(HashMap<String, Object> paramMap) {
    	return mapper.selectCountSurveyImage(paramMap);
    }
    public ArrayList<PlaceSpot> selectListSurveyImage(HashMap<String, Object> paramMap) {
    	return mapper.selectListSurveyImage(paramMap);
    }
	public SurveyImage selectOneSurveyImage(HashMap<String, Object> paramMap) {
		return mapper.selectOneSurveyImage(paramMap);
	}
	public void insertSurveyImage(SurveyImage sqi) throws SQLException {
		mapper.insertSurveyImage(sqi);
	}
	public void updateSurveyImage(SurveyImage sqi) throws SQLException {
		mapper.updateSurveyImage(sqi);
	}
	public void deleteSurveyImage(SurveyImage sqi) throws SQLException {
		mapper.deleteSurveyImage(sqi);
	}
//	<!--################################### SurveyQuestionAnswer ###################################-->
    public int selectCountSurveyQuestionAnswer(HashMap<String, Object> paramMap) {
    	return mapper.selectCountSurveyQuestionAnswer(paramMap);
    }
    public ArrayList<SurveyQuestionAnswer> selectListSurveyQuestionAnswer(HashMap<String, Object> paramMap) {
    	return mapper.selectListSurveyQuestionAnswer(paramMap);
    }
	public SurveyQuestionAnswer selectOneSurveyQuestionAnswer(HashMap<String, Object> paramMap) {
		return mapper.selectOneSurveyQuestionAnswer(paramMap);
	}
	public SurveyQuestionAnswer saveSurveyQuestionAnswer(SurveyQuestionAnswer sq) throws SQLException {
		if(sq.getId() == null) {
			mapper.insertSurveyQuestionAnswer(sq);
		} else {
			mapper.updateSurveyQuestionAnswer(sq);
		}
		return sq;
	}
	public void insertSurveyQuestionAnswer(SurveyQuestionAnswer sq) throws SQLException {
		mapper.insertSurveyQuestionAnswer(sq);
	}
	public void updateSurveyQuestionAnswer(SurveyQuestionAnswer sq) throws SQLException {
		mapper.updateSurveyQuestionAnswer(sq);
	}
	public void deleteSurveyQuestionAnswer(SurveyQuestionAnswer sq) throws SQLException {
		mapper.deleteSurveyQuestionAnswer(sq);
	}

//	<!--################################### SurveyRecommand ###################################-->
    public int selectCountSurveyRecommand(HashMap<String, Object> paramMap) {
    	return mapper.selectCountSurveyRecommand(paramMap);
    }
    public ArrayList<SurveyRecommand> selectListSurveyRecommand(HashMap<String, Object> paramMap) {
    	return mapper.selectListSurveyRecommand(paramMap);
    }
	public SurveyRecommand selectOneSurveyRecommand(HashMap<String, Object> paramMap) {
		return mapper.selectOneSurveyRecommand(paramMap);
	}
	public SurveyRecommand saveSurveyRecommand(SurveyRecommand sq) throws SQLException {
		if(sq.getId() == null) {
			mapper.insertSurveyRecommand(sq);
		} else {
			mapper.updateSurveyRecommand(sq);
		}
		return sq;
	}
	public void insertSurveyRecommand(SurveyRecommand sq) throws SQLException {
		mapper.insertSurveyRecommand(sq);
	}
	public void updateSurveyRecommand(SurveyRecommand sq) throws SQLException {
		mapper.updateSurveyRecommand(sq);
	}
	public void deleteSurveyRecommand(SurveyRecommand sq) throws SQLException {
		mapper.deleteSurveyRecommand(sq);
	}

//	<!--################################### SurveyResult ###################################-->
    public int selectCountSurveyResult(HashMap<String, Object> paramMap) {
    	return mapper.selectCountSurveyResult(paramMap);
    }
    public ArrayList<SurveyResult> selectListSurveyResult(HashMap<String, Object> paramMap) {
    	return mapper.selectListSurveyResult(paramMap);
    }
	public SurveyResult selectOneSurveyResult(HashMap<String, Object> paramMap) {
		return mapper.selectOneSurveyResult(paramMap);
	}
	public SurveyResult saveSurveyResult(SurveyResult sq) throws SQLException {
		if(sq.getId() == null) {
			mapper.insertSurveyResult(sq);
		} else {
			mapper.updateSurveyResult(sq);
		}
		return sq;
	}
	public void insertSurveyResult(SurveyResult sq) throws SQLException {
		mapper.insertSurveyResult(sq);
	}
	public void updateSurveyResult(SurveyResult sq) throws SQLException {
		mapper.updateSurveyResult(sq);
	}
	public void deleteSurveyResult(SurveyResult sq) throws SQLException {
		mapper.deleteSurveyResult(sq);
	}

}
