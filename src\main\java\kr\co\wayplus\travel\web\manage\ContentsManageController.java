package kr.co.wayplus.travel.web.manage;


import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartRequest;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.BoardAttachFile;
import kr.co.wayplus.travel.model.BoardSetting;
import kr.co.wayplus.travel.model.Contents;
import kr.co.wayplus.travel.model.ContentsItem;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.MenuUser;
import kr.co.wayplus.travel.model.SortData;
import kr.co.wayplus.travel.service.front.PageService;
import kr.co.wayplus.travel.service.manage.ContentsManageService;

@Controller
@RequestMapping("/manage/contents")
public class ContentsManageController extends BaseController  {

    @Value("${cookie-set.domain}")
    private String cookieDomain;
    @Value("${cookie-set.prefix}")
    private String cookieName;

	@Value("${upload.file.path}")
	String externalImageUploadPath;

	final String addPath = "content/";

    @Value("${upload.file.max-size}")
    int maxFileSize;

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final PageService pageService; /*common하게*/
    private final ContentsManageService svcContents; /*common하게*/

    @Autowired
    public ContentsManageController(
    		PageService pageService,
    		ContentsManageService svcContents) {
        this.pageService = pageService;
        this.svcContents = svcContents;
    }

	@GetMapping("/list")
	public ModelAndView contentsList(  ){
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> paramMap = new HashMap<>();
		mav.addObject("p", paramMap);
		mav.setViewName("manage/sub/contents/list");
		return mav;
	}

	@GetMapping("/form")
	public ModelAndView contentsForm(
			@RequestParam(value="contentId", defaultValue="0") int contentId
		){
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> paramMap = new HashMap<>();
		mav.addObject("contentId", contentId);
		mav.addObject("p", paramMap);
		mav.setViewName("manage/sub/contents/form");
		return mav;
	}

	@PostMapping("/list")
    @ResponseBody
    public HashMap<String,Object> contents_list_ajax(
    		HttpServletRequest request,
    		Contents content,
    		@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length){
    	HashMap<String,Object> retMap = new HashMap<String, Object>();

    	try {
    		HashMap<String,Object> paramMap = new HashMap<String, Object>();

    		//svcContents.selectList(paramMap, retMap);

    		List<SortData> listSort = getListOrder(request);
    		paramMap.put("listSort", listSort);

    		paramMap.put("sort", "menuName");
    		paramMap.put("sortOrder", "asc");

    		if(length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
    		}

    		if( content.getUseYn() != null )    paramMap.put("useYn", content.getUseYn());
    		if( content.getDeleteYn() != null ) paramMap.put("deleteYn", content.getDeleteYn());

    		int totalCount = svcContents.selectCountContents(paramMap);

    		retMap.put("recordsTotal", totalCount);
    		retMap.put("recordsFiltered", totalCount);
    		retMap.put("data", svcContents.selectListContents(paramMap));

    		retMap.put("result","success");
    		retMap.put("message","처리 성공");

    		retMap.put("result","success");
    		retMap.put("message","처리 성공");
		} catch (Exception e) {
			retMap.put("result","ERROR");
			retMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
		}

        return retMap;
    }



	@PostMapping("/list-tree")
    @ResponseBody
    public HashMap<String,Object> contents_list_tree_ajax(
    		HttpServletRequest request,
    		Contents content,
    		@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length){
    	HashMap<String,Object> retMap = new HashMap<String, Object>();

    	try {
    		HashMap<String,Object> paramMap = new HashMap<String, Object>();


    		if( content.getDeleteYn() != null ) paramMap.put("deleteYn" , 'N');

    		svcContents.selectList(paramMap, retMap);

    		retMap.put("result","success");
    		retMap.put("message","처리 성공");
		} catch (Exception e) {
			retMap.put("result","ERROR");
			retMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
		}

        return retMap;
    }

	@PostMapping("/save")
    @ResponseBody
    public HashMap<String, Object> content_save_ajax(
    		@RequestParam(value="mode", defaultValue="I") String mode,
    		Contents bc
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser user = (LoginUser)_user;
//        		HashMap<String, Object> paramMap = new HashMap<>();

	    		if(mode.equals("I")) {
	    			bc.setCreateId(user.getUserEmail());
	    			svcContents.insertContents(bc);
	    		} else {
	    			bc.setLastUpdateId(user.getUserEmail());

	    			if( bc.getUpperContentId() != null  ) {
    					bc.setIsContentUpperId(true);
    				}

	    			svcContents.updateContents(bc);
	    		}

	    		retrunMap.put("result", "success");
	    		retrunMap.put("message", "처리가 완료 되었습니다.");
        	} else {
        		retrunMap.put("result", "fail");
	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
        	}
		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			retrunMap.put("info", e.getMessage());
			logger.debug(e.getCause().getLocalizedMessage());
		}
        return retrunMap;
    }
/*
    @PostMapping("/restore")
    @ResponseBody
    public HashMap<String, Object> content_restore_ajax(
    		Contents bs
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		svcContents.restoreContents(bs);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
		}
        return retrunMap;
    }
*/

    @PostMapping("/delete")
    @ResponseBody
    public HashMap<String, Object> content_delete_ajax(
    		Contents bc
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		svcContents.deleteContents(bc);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
		}

        return retrunMap;
    }
    @PostMapping("/order-save")
    @ResponseBody
    public HashMap<String,Object> menu_order_save(HttpServletRequest request ){
    	HashMap<String,Object> retMap = new HashMap<String, Object>();

    	try {
//    		ArrayList<MenuUser> listMenu = new ArrayList<MenuUser>();

    		String total = request.getParameter("total");

    		for (int i = 0; i < Integer.parseInt(total); i++) {

    			String menuId 		= request.getParameter("order["+i+"][menuId]");
    			String menuSort 	= request.getParameter("order["+i+"][menuSort]");
    			String menuUpperId 	= request.getParameter("order["+i+"][menuUpperId]");
    			Boolean isMenuUpperId 	= request.getParameter("order["+i+"][isMenuUpperId]").equals("true");

    			//System.out.println(menuId + "," + menuSort + "," + menuUpperId );


    			Integer _menuId   = Integer.parseInt( menuId );
    			Integer _menuSort = Integer.parseInt( menuSort );

    			Contents _menu = new Contents().addContentId( _menuId ).addContentSort( _menuSort ).addIsUpperContentId(isMenuUpperId);

    			if( _menuId == 37 ) {
    				System.out.println( _menuId );
    			}

    			if( menuUpperId != null ) {
    				_menu.addUpperContentId( Integer.parseInt( menuUpperId ) );
    			}

    			svcContents.updateContents(_menu);
			}

    		retMap.put("result","success");
    		retMap.put("message","처리 성공 하였습니다.");

		} catch (Exception e) {
			retMap.put("result","ERROR");
			retMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getCause().getMessage());
		}

        return retMap;
    }



	@PostMapping("/item/list")
    @ResponseBody
    public HashMap<String,Object> contents_item_list_ajax(
    		HttpServletRequest request,
    		Contents content,
    		ContentsItem contentItem,
    		@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length,
    		@Param(value="deleteYn") String deleteYn,
    		@Param(value="contentId") String contentId,
    		@Param(value="titleLike") String titleLike,
    		@Param(value="contentLike") String contentLike){
    	HashMap<String,Object> retMap = new HashMap<String, Object>();

    	try {
    		HashMap<String,Object> paramMap = new HashMap<String, Object>();

    		List<SortData> listSort = getListOrder(request);
    		paramMap.put("listSort", listSort);

    		if(deleteYn != null)  paramMap.put("deleteYn", deleteYn);

    		if(length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
    		}

    		if( contentItem.getContentId() != null )       paramMap.put("contentId", contentItem.getContentId());
    		if( contentItem.getContentLangType() != null ) paramMap.put("contentLangType", contentItem.getContentLangType());

    		int totalCount = svcContents.selectCountContentsItem(paramMap);

    		retMap.put("recordsTotal", totalCount);
    		retMap.put("recordsFiltered", totalCount);
    		retMap.put("data", svcContents.selectListContentsItem(paramMap));

    		retMap.put("result","success");
    		retMap.put("message","처리 성공");
		} catch (Exception e) {
			retMap.put("result","ERROR");
			retMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
		}

        return retMap;
    }

	@PostMapping("/item/save")
    @ResponseBody
    public HashMap<String, Object> content_item_save_ajax(
    		@RequestParam(value="mode", defaultValue="I") String mode,
    		ContentsItem ci
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser user = (LoginUser)_user;
//        		HashMap<String, Object> paramMap = new HashMap<>();


        		ContentsItem update = new ContentsItem().addOldUseYn("Y").addUseYn("N").addContentId( ci.getContentId() ).addContentLangType( ci.getContentLangType() );
    			svcContents.updateContentsItem(update);

        		ci.setCreateId(user.getUserEmail());
        		svcContents.insertContentsItem(ci);
        		/*

	    		if(mode.equals("I")) {
	    			bc.setCreateId(user.getUserEmail());
	    			svcContents.insertContentsItem(bc);
	    		} else {
	    			bc.setLastUpdateId(user.getUserEmail());
	    			svcContents.updateContentsItem(bc);
	    		}
	    		*/

	    		retrunMap.put("result", "success");
	    		retrunMap.put("message", "처리가 완료 되었습니다.");
        	} else {
        		retrunMap.put("result", "fail");
	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
        	}
		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			retrunMap.put("info", e.getMessage());
			logger.debug(e.getCause().getLocalizedMessage());
		}
        return retrunMap;
    }
    @PostMapping("/item/delete")
    @ResponseBody
    public HashMap<String, Object> content_item_resore_ajax(
    		ContentsItem bs
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		svcContents.deleteContentsItem(bs);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
		}
        return retrunMap;
    }
/*
    @PostMapping("/item/restore")
    @ResponseBody
    public HashMap<String, Object> content_item_resore_ajax(
    		ContentsItem bs
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		svcBorad.restoreBoardSetting(bs);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
		}
        return retrunMap;
    }
*/

    @PostMapping("/setting-delete")
    @ResponseBody
    public HashMap<String, Object> boardSetting_delete_ajax(
    		ContentsItem bc
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		svcContents.deleteContentsItem(bc);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
		}

        return retrunMap;
    }

	@PostMapping("/ck-image-upload")
	@ResponseBody
	public HashMap<String, Object> ckImageUpload(MultipartRequest request, @RequestParam(value="service_type", defaultValue="ck-editor") String serviceType){
		HashMap<String, Object> resultMap = new HashMap<>();
		LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		try{
			MultipartFile multipartFile = null;
			if(request.getFile("upload") != null){
				multipartFile = request.getFile("upload");
				if(multipartFile.getSize() > 0){
					if(multipartFile.getSize() > maxFileSize){
						throw new Exception(String.valueOf(maxFileSize/1024/1024) + "MB 이하의 파일만 첨부 가능합니다.");
					}

					String uploadName = UUID.randomUUID().toString();

					File file = new File(externalImageUploadPath+addPath);
					if(!file.exists()) file.mkdirs();

					multipartFile.transferTo(new File(externalImageUploadPath+addPath+uploadName));
					logger.debug("File Uploaded : " + multipartFile.getOriginalFilename());

					BoardAttachFile baf = new BoardAttachFile();
                    //baf.setBoardId(bc.getBoardId());
                    //baf.setContentId(bc.getId());
                    baf.setServiceType(serviceType);
                    baf.setUploadPath(externalImageUploadPath+ addPath);
                    baf.setUploadFilename(uploadName);
                    baf.setOriginFilename(multipartFile.getOriginalFilename());
                    baf.setFileSize((int) multipartFile.getSize());
                    baf.setFileMimetype(multipartFile.getContentType());
                    if (multipartFile.getOriginalFilename().contains(".")) {
                        baf.setFileExtension(multipartFile.getOriginalFilename().substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
                    }

                    if (user != null) {
                        baf.setUploadId(String.valueOf(user.getUserEmail()));
                    }

                    svcContents.insertContentsAttachFile(baf);

					resultMap.put("result", "success");
					resultMap.put("url", "/upload/"+addPath+ uploadName);
					resultMap.put("file_id", baf.getFileId());
				}
			}else{
				throw new Exception("첨부된 파일이 없습니다.");
			}
		}catch (Exception e){
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}

}
