/*
th { border-bottom: none !important; }
tr { background: #FFFFFF;}
td { height: 57px; font-weight: 400; font-size: 15px; color: #444444; }
button { background: none; border: none; }
p { margin: 0; padding: 0 }
select { -o-appearance: none; -webkit-appearance: none; -moz-appearance: none; appearance: none;}
label { margin-bottom: 0}
*/
.stay-name-box {width: 100%; height: 70px;padding-top: 13px; padding-right: 20px; padding-left: 20px;border: 1px solid #CCCCCC;}
.stay-option-box {display:flex;margin-bottom: 8px;}
.stay-option-box label {width: 170px;margin-right: 10px;}
.stay-option-box label:first-child {width: 480px;margin-right: 10px;}
.stay-option-box label:last-child {margin-right: 0;}
.stay-option-title-label {display: grid;height: 70px;padding: 13px 20px;font-weight: 600; font-size: 15px; color: #222222;border: solid 1px #CCCCCC;}
.stay-title-input {width: 100%; height: 19px;font-weight: 400; font-size: 15px; color: #666666}
.stay-content-inner-box {display:flex; width: 920px; margin-bottom: 20px;padding: 25px 25px 25px 20px; border: 1px solid #CCCCCC; background: #FFFFFF;}
