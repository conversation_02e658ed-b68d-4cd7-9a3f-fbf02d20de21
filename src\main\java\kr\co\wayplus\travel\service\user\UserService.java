package kr.co.wayplus.travel.service.user;

import kr.co.wayplus.travel.mapper.front.BoardMapper;
import kr.co.wayplus.travel.mapper.front.ProductMapper;
import kr.co.wayplus.travel.mapper.manage.ProductManageMapper;
import kr.co.wayplus.travel.mapper.user.UserMapper;
import kr.co.wayplus.travel.model.*;
import kr.co.wayplus.travel.service.front.MenuService;
import kr.co.wayplus.travel.util.CryptoUtil;
import kr.co.wayplus.travel.util.CustomBcryptPasswordEncoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.sql.SQLException;
import java.util.*;

@Service
public class UserService implements UserDetailsService {

    private final UserMapper userMapper;
    private final BoardMapper boardMapper;
    private final ProductMapper productMapper;
    private final ProductManageMapper productManageMapper;
    private final UserPointService userPointService;
    private final MenuService menuService;
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    public UserService(UserMapper userMapper, BoardMapper boardMapper, ProductMapper productMapper, ProductManageMapper productManageMapper
                    , UserPointService userPointService, MenuService menuService) {
        this.userMapper = userMapper;
        this.boardMapper = boardMapper;
        this.productMapper = productMapper;
        this.productManageMapper = productManageMapper;
        this.userPointService = userPointService;
        this.menuService = menuService;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        logger.debug("Call loadUserByUserName. Username : " + username);
        HashMap<String, Object> param = new HashMap<>();
        param.put("id", username);
        LoginUser user = userMapper.selectUserByUserid(param);

        Set<GrantedAuthority> roles = new LinkedHashSet<>();
        roles.add(new SimpleGrantedAuthority( "ROLE_"+user.getUserRole() ));
        user.setAuthorities(roles);
        if(user == null) throw new UsernameNotFoundException(username);
        logger.debug("User Find. User Has Role : " + user.getAuthorities().toString());
        return user;
    }

    public void writeUserLoginLog(HashMap<String, String> parameterMap) {
        userMapper.insertUserLoginLog(parameterMap);
    }

    public void updateUserLastLoginDate(LoginUser user) {
        userMapper.updateUserLastLoginDate(user);
    }

    public LoginUser findUserById(HashMap<String, Object> param) {
        return userMapper.selectUserByUserid(param);
    }
    public int findUserCountById(HashMap<String, Object> param) {
        return userMapper.selectUserCountById(param);
    }

    public void addUser(LoginUser user, boolean encrypted) throws RuntimeException, InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        if(encrypted){
            user.setUserPassword(CryptoUtil.aesDecode(user.getUserPassword(), user.getEncrypt(), user.getIv()));
        }
        CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();
        user.setUserPassword(passwordEncoder.encode(user.getPassword()));
        userMapper.insertNewUser(user);
    }

    public void withdrawalUser(LoginUser user, boolean encrypted) throws InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        if(encrypted){
            user.setUserPassword(CryptoUtil.aesDecode(user.getUserPassword(), user.getEncrypt(), user.getIv()));
        }
        CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();

        HashMap<String, Object> param = new HashMap<>();
        param.put("id", user.getUsername());
        LoginUser storedUser = userMapper.selectUserByUserid(param);
        if(storedUser == null) throw new RuntimeException("사용자 정보가 없습니다.");
        if(passwordEncoder.directMatches(user.getUserPassword(), storedUser.getPassword())){
            userMapper.insertWithdrawalUser(storedUser);
            userMapper.deleteUserByUserid(storedUser);
        }else{
            throw new RuntimeException("비밀번호가 일치하지 않습니다.");
        }
    }

    public ArrayList<LoginUser> findUserListByUserName(String name, String mobile) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("userName", name);
        param.put("userMobile", mobile);
        return userMapper.selectUserListByUserName(param);
    }

    public LoginUser findRePasswordUserByUserInfo(HashMap<String, Object> param) {
        return userMapper.selectRePasswordUserByUserInfo(param);
    }

    public void updateUserPasswordByLost(LoginUser user, boolean encrypted) throws InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        if(encrypted){
            user.setUserPassword(CryptoUtil.aesDecode(user.getUserPassword(), user.getEncrypt(), user.getIv()));
        }
        CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();
        user.setUserPassword(passwordEncoder.encode(user.getUserPassword()));
        userMapper.updateUserPassword(user);
    }


    public void updateUserPassword(LoginUser user, String newPassword) throws Exception {
        HashMap<String, Object> param = new HashMap<>();
        param.put("id", user.getUserEmail());
        LoginUser storedUser = userMapper.selectUserByUserid(param);
        if(storedUser == null){
            throw new Exception("사용자를 찾을 수 없습니다.");
        }

        CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();
        if(passwordEncoder.directMatches(user.getPassword(), storedUser.getPassword())){
            user.setUserPassword(passwordEncoder.encode(newPassword));
            userMapper.updateUserPassword(user);
        }else{
            throw new Exception("현재 비밀번호가 일치하지 않습니다.");
        }

    }

    //창풍용으로 수정. 파라미터 타입 수정
    public ArrayList<LoginUser> selectUserListByRole(HashMap<String, Object> param) {
        return userMapper.selectUserListByRole(param);
    }

    public int selectCountUserListByRole(HashMap<String, Object> param) {
        return userMapper.selectCountUserListByRole(param);
    }


    public void updateUserSessionLogout(LoginUserSession loginUserSession) {
        userMapper.updateUserSessionLogout(loginUserSession);
    }

    public void writeUserLoginAttemptLog(LoginAttemptLog attemptLog) {
        userMapper.insertUserLoginAttemptLog(attemptLog);
    }

    public void updateUserNewTokenId(LoginUser user) {
        userMapper.updateUserNewTokenId(user);
    }

    public void updateUserWebLog(HashMap<String, String> param) {
        userMapper.updateUserWebLog(param);
    }

    public void writeUserWebLog(WebServiceLog webLog) {
        userMapper.insertUserWebLog(webLog);
    }


    public void updateAdminUserInfo(LoginUser user) throws Exception {
        HashMap<String, Object> param = new HashMap<>();
        param.put("id", user.getUserEmail());
        LoginUser storedUser = userMapper.selectUserByUserid(param);
        if(storedUser == null){
            throw new Exception("사용자를 찾을 수 없습니다.");
        }

        CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();
        if(passwordEncoder.directMatches(user.getPassword(), storedUser.getPassword())){
            userMapper.updateUserInfo(user);
        }else{
            throw new Exception("비밀번호가 일치하지 않습니다.");
        }
    }

    public void updateUserInfo(LoginUser user, boolean encrypted) throws InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        if(user.getUserPassword() != null && !user.getUserPassword().isEmpty()) {
            if (encrypted) {
                user.setUserPassword(CryptoUtil.aesDecode(user.getUserPassword(), user.getEncrypt(), user.getIv()));
            }
            CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();
            user.setUserPassword(passwordEncoder.encode(user.getUserPassword()));
        }
        userMapper.updateUserInfo(user);
    }



    public LoginUser loadUserByOAuthRegistration(String registrationId, String id) {
        logger.debug(String.format("Call loadUserByOAuthRegistration. registration: %s, ID: %s", registrationId, id));
        HashMap<String, Object> param = new HashMap<>();
        param.put("registrationId", registrationId);
        param.put("id", id);
        LoginUser user = userMapper.selectUserByUserid(param);

        Set<GrantedAuthority> roles = new HashSet<>();
        roles.add(new SimpleGrantedAuthority( "ROLE_"+user.getUserRole()));
        user.setAuthorities(roles);

        if(user == null) throw new UsernameNotFoundException(String.format("%s_%s", registrationId, id));
        logger.debug("User Find. User Has Role : " + user.getAuthorities().toString());
        return user;
    }

    public LoginUser findUserByIdToken(HashMap<String, Object> param) {
        return userMapper.selectUserByIdToken(param);
    }

	public void createUser(LoginUser user, boolean encrypted) throws InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        if(encrypted){
            user.setUserPassword(CryptoUtil.aesDecode(user.getUserPassword(), user.getEncrypt(), user.getIv()));
        }
        CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();
        user.setUserPassword(passwordEncoder.encode(user.getPassword()));
        userMapper.insertNewUser(user);
    }

    public void updateUserSubscribeInfo(LoginUser user) {
        userMapper.updateUserSubscribeInfo(user);
    }

    public LoginUser checkUserPassword(HashMap<String, Object> param) {
        return userMapper.selectUserByNameMobile(param);
    }

    public UserCustomerPayment selectUserPayTotalSummary(HashMap<String, Object> param) {
        return userMapper.selectUserPayTotalSummary(param);
    }

    public void insertUserSubscribeServiceInfo(UserSubscribeServiceInfo userSubscribeServiceInfo) {
        userMapper.insertUserSubscribeServiceInfo(userSubscribeServiceInfo);
    }

    public int selectUserSubscribeServiceInfoListCount(HashMap<String, Object> param) {
        return userMapper.selectUserSubscribeServiceInfoListCount(param);
    }
    public ArrayList<UserSubscribeServiceInfo> selectUserSubscribeServiceInfoList(HashMap<String, Object> param) {
        return userMapper.selectUserSubscribeServiceInfoList(param);
    }
    public LoginUser findUserByIdPassword(LoginUser user, boolean encrypted) throws InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        if(encrypted){
            user.setUserPassword(CryptoUtil.aesDecode(user.getUserPassword(), user.getEncrypt(), user.getIv()));
        }
        CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();
        HashMap<String, Object> param = new HashMap<>();
        param.put("id", user.getUsername());
        LoginUser storedUser = userMapper.selectUserByUserid(param);
        if(storedUser == null) throw new RuntimeException("사용자 정보가 없습니다.");
        if(passwordEncoder.directMatches(user.getUserPassword(), storedUser.getPassword())){
            return storedUser;
        }else{
            return null;
        }
    }

    public ArrayList<UserFavorite> getListUserFavorite(HashMap<String, Object> paramMap) {
        return userMapper.selectListUserFavorite(paramMap);
    }

    public int getCountUserFavorite(HashMap<String, Object> paramMap) {
        return userMapper.selectCountUserFavorite(paramMap);
    }

    public UserFavorite getOneUserFavorite(UserFavorite userFavorite) {
        return userMapper.selectOneUserFavorite(userFavorite);
    }

    public void addUserFavorite(UserFavorite userFavorite) {
        userMapper.insertUserFavorite(userFavorite);
    }

    public void updateUserFavoriteCount(HashMap<String, Object> userParam) {
        userMapper.updateUserFavoriteCount(userParam);
    }

    @Transactional
    public String addUserFavoriteSv(UserFavorite userFavorite) throws Exception {
        if ( getOneUserFavorite(userFavorite) != null ) {
            return "already";
        }
        else {
            addUserFavorite(userFavorite);
            switch (userFavorite.getType()) {
                case "product":
                        HashMap<String, Object> param = new HashMap<>();
                        param.put("productSerial", userFavorite.getProductSerial());

                        ProductInfo pi = productMapper.selectOneProduct(param);
                        pi.setProductTourId(pi.getProductTourId());
                        pi.setFavoriteCalcType(userFavorite.getFavoriteCalcType());
                        productManageMapper.updateProductInfo(pi);
                    break;
                case "board":
                        BoardContents bc = new BoardContents();
                        bc.setId(userFavorite.getBoardId());
                        bc.setBoardFavoriteType(userFavorite.getType());
                        bc.setFavoriteCalcType(userFavorite.getFavoriteCalcType());
                        bc.setBoardId(null);
                        bc.setCategoryId(null);
                        bc.setSeriesId(null);
                        bc.setScrapCount(null);
                        bc.setComment(null);
                        bc.setViewCount(null);
                        bc.setAttachmentCount(null);
                        boardMapper.updateBoardContents(bc);
                    break;
                case "comment":
                        if ( userFavorite.getFavoriteType().equals("product") ) {
                            ProductComment pc = new ProductComment();
                            pc.setCommentId(userFavorite.getCommentId());
                            pc.setFavoriteCalcType(userFavorite.getFavoriteCalcType());
                            productMapper.updateProductCommentFavorite(pc);
                        }
                        else if ( userFavorite.getFavoriteType().equals("board") ) {
                            BoardComment bct = new BoardComment();
                            bct.setCommentId(userFavorite.getCommentId());
                            bct.setFavoriteCalcType(userFavorite.getFavoriteCalcType());
                            boardMapper.updateBoardCommentFavorite(bct);

                            BoardContents _bc = new BoardContents();
                            _bc.setId(userFavorite.getBoardId());
                            _bc.setBoardFavoriteType(userFavorite.getType());
                            _bc.setFavoriteCalcType(userFavorite.getFavoriteCalcType());
                            _bc.setBoardId(null);
                            _bc.setCategoryId(null);
                            _bc.setSeriesId(null);
                            _bc.setScrapCount(null);
                            _bc.setComment(null);
                            _bc.setViewCount(null);
                            _bc.setAttachmentCount(null);
                            boardMapper.updateBoardContents(_bc);
                        }
                    break;
                case "user":
                        HashMap<String, Object> userParam = new HashMap<>();
                        userParam.put("favoriteType", userFavorite.getFavoriteCalcType());
                        //좋아요한 사용자 +1 처리
                        userParam.put("userEmail", userFavorite.getUserEmail());
                        userParam.put("updateType", "send");
                        this.updateUserFavoriteCount(userParam);
                        //좋아요 받은 사용자 +1 처리
                        userParam.put("updateType", "get");
                        userParam.put("userEmail", userFavorite.getTargetUserEmail());
                        this.updateUserFavoriteCount(userParam);
                    break;
                default:
                    break;
            }

            //포인트 추가 부분
            UserPointSet userPointSet = null;
            HashMap<String, Object> pointParam = new HashMap<>();
            pointParam.put("menuId", userFavorite.getMenuId());
            MenuUser menu = menuService.selectOneMenu(pointParam);
            if (menu != null && menu.getPointUseYn() != null
                && menu.getPointUseYn().equals("Y")
                && userFavorite.getUserRole().toUpperCase().equals("USER")) {
                    if (menu.getUserFavoritePointId() != null
                    && menu.getUserFavoritePointId() != 0 ) {
                        userPointSet = userPointService.getUserPointSettingById(String.valueOf(menu.getUserFavoritePointId()));
                    }
            }
            if ( userPointSet != null && userPointSet.getDeleteYn().equals("N") ) {
                UserPointAccrued pointAccrued = new UserPointAccrued();
                pointAccrued.setUserEmail(userFavorite.getUserEmail());
                pointAccrued.setUserName(userFavorite.getUserName());
                pointAccrued.setAccruedType(userPointSet.getName());
                pointAccrued.setAccruedCode(userPointSet.getAccruedCode());
                pointAccrued.setPointAccrued(userPointSet.getAccruedPoint());
                pointAccrued.setExpireDate(userPointSet.getExpireDate());
                pointAccrued.setCreateId(userFavorite.getUserEmail());

                pointAccrued.setUserFavoriteId(userFavorite.getId());

                userPointService.createUserPoint(pointAccrued);
            }

            return "success";
        }
    }

    @Transactional
    public void removeUserFavorite(UserFavorite userFavorite) throws SQLException {
        HashMap<String, Object> pointParam = new HashMap<>();
       
        userMapper.deleteUserFavorite(userFavorite);
        switch (userFavorite.getType()) {
            case "product":
                    pointParam.put("productSerial", userFavorite.getProductSerial());

                    HashMap<String, Object> param = new HashMap<>();
                    param.put("productSerial", userFavorite.getProductSerial());

                    ProductInfo pi = productMapper.selectOneProduct(param);
                    pi.setProductTourId(pi.getProductTourId());
                    pi.setFavoriteCalcType(userFavorite.getFavoriteCalcType());
                    productManageMapper.updateProductInfo(pi);
                break;
            case "board":
                    pointParam.put("boardContentsId", userFavorite.getBoardId());

                    BoardContents bc = new BoardContents();
                    bc.setId(userFavorite.getBoardId());
                    bc.setBoardFavoriteType(userFavorite.getType());
                    bc.setFavoriteCalcType(userFavorite.getFavoriteCalcType());
                    bc.setBoardId(null);
                    bc.setCategoryId(null);
                    bc.setSeriesId(null);
                    bc.setScrapCount(null);
                    bc.setComment(null);
                    bc.setViewCount(null);
                    bc.setAttachmentCount(null);
                    boardMapper.updateBoardContents(bc);
                break;
            case "comment":
                    if ( userFavorite.getFavoriteType().equals("product") ) {
                        pointParam.put("productCommentId", userFavorite.getCommentId());

                        ProductComment pc = new ProductComment();
                        pc.setCommentId(userFavorite.getCommentId());
                        pc.setFavoriteCalcType(userFavorite.getFavoriteCalcType());
                        productMapper.updateProductCommentFavorite(pc);
                    }
                    else if ( userFavorite.getFavoriteType().equals("board") ) {
                        pointParam.put("boardCommentId", userFavorite.getCommentId());

                        BoardComment bct = new BoardComment();
                        bct.setCommentId(userFavorite.getCommentId());
                        bct.setFavoriteCalcType(userFavorite.getFavoriteCalcType());
                        boardMapper.updateBoardCommentFavorite(bct);
                    }
                break;
            case "user":
                    pointParam.put("userFavoriteId", userFavorite.getUserFavoriteId());

                    HashMap<String, Object> userParam = new HashMap<>();
                    userParam.put("favoriteType", userFavorite.getFavoriteCalcType());
                    //좋아요한 사용자 +1 처리
                    userParam.put("userEmail", userFavorite.getUserEmail());
                    userParam.put("updateType", "send");
                    this.updateUserFavoriteCount(userParam);
                    //좋아요 받은 사용자 +1 처리
                    userParam.put("updateType", "get");
                    userParam.put("userEmail", userFavorite.getTargetUserEmail());
                    this.updateUserFavoriteCount(userParam);
                break;
            default:
                break;
        }

        // 포인트 감소 부분
        UserPointAccrued pointAccrued = userPointService.getUserPointAccruedById(pointParam);
        if (pointAccrued != null) {
            pointAccrued.setCancelCode("좋아요 취소");
            pointAccrued.setCancelId(userFavorite.getUserEmail());

            pointParam.put("accruedId", pointAccrued.getId());
            pointParam.put("userEmail", userFavorite.getUserEmail());
            UserPointRecord userPointRecord = userPointService.getOneUserPointRecord(pointParam);
            
            userPointService.cancelUserPoint(userPointRecord.getId(), pointAccrued);
        }
    }

    public void writeUserAttachFile(UserAttachFile userAttachFile) {
        userMapper.insertUserAttachFile(userAttachFile);
    }

    public ArrayList<BadgeAcquireHistory> selectListUserBadgeAcquireHistory(HashMap<String, Object> userParam) {
        return userMapper.selectListUserBadgeAcquireHistory(userParam);
    }

    public ArrayList<BoardContents> selectListUserCompleteMission(HashMap<String, Object> userParam) {
        return userMapper.selectListUserCompleteMission(userParam);
    }

    public int selectCountUserComment(HashMap<String, Object> userParam) {
        return userMapper.selectCountUserComment(userParam);
    }

    public void updateUserIntroPageLink(HashMap<String, Object> paramMap) {
        userMapper.updateUserIntroPageLink(paramMap);
    }

    public ArrayList<Islandlife> selectListIslandlife(HashMap<String, Object> paramMap) {
        return userMapper.selectListIslandlife(paramMap);
    }

    public void updateUserIslandLife(HashMap<String, Object> paramMap) {
        userMapper.updateUserIslandLife(paramMap);
    }
}
