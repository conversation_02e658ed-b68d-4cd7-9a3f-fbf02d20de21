package kr.co.wayplus.travel.model;

import java.util.ArrayList;

import com.fasterxml.jackson.annotation.JsonInclude;
import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductComment extends CommonDataSet {
	private Integer id;	//고유번호
	private Integer menuId;	//메뉴 고유번호
	private String productFavoriteType;	// 타입
	private String favoriteCalcType;	// 타입
	private Integer commentId;	//댓글 고유번호
	private Integer upperCommentId;	// 상위 댓글 아이디
	private String productSerial;	// 상품 고유번호
	private String userEmail;	// 유저 이메일
	private String content;	// 댓글 내용
	private String favoriteCount;	// 좋아요 개수
	private String userName;
	private String userRole;
	private String expireDate;
	private String userFavorite;
	private String userFavoriteId;
}
