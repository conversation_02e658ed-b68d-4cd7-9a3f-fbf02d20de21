<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.front.BoardMapper">
<!--
	 * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
-->
<!--################################### boardContents ###################################-->
	<select id="selectOneBoardContents" parameterType="HashMap" resultType="BoardContents">
        select *
          from (
	        SELECT id, board_id, mission_type,
	               category_id, (SELECT title FROM board_category ct WHERE ct.board_id = bc.id AND ct.id = bc.category_id) category_name,
	               title, content, apply_code,
	               series_id, tags, thumbnail_url,
	               favorite_count, scrap_count, comment_count,
	               view_count, attachment_count, use_yn, fix_yn, secret_yn,
	               start_date, expire_date, assemble_date, assemble_time,
	               delete_yn, delete_id, delete_date,
	               create_id, create_date, last_update_id, last_update_date,
	               ifnull(u.user_nick_name, u.user_name) user_nick_name,
	               ifnull(u.user_name, bc.guest_name) user_name
	               , (SELECT bctts.id FROM board_contents bctts
					WHERE
						bctts.id &lt; bc.id
						AND bctts.board_id = bc.board_id
						<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)">AND bctts.category_id = bc.category_id</if>
						AND delete_yn = 'N'
					ORDER BY
						bctts.id desc
					LIMIT 1) AS id_prev
				   ,(SELECT bctts.id FROM board_contents bctts
				   WHERE bctts.id > bc.id
				   AND bctts.board_id = bc.board_id
				    <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)">AND bctts.category_id = bc.category_id</if>
				   AND delete_yn = 'N' ORDER BY bctts.id asc LIMIT 1) AS id_next
				   , (CASE WHEN (
						SELECT COUNT(*)
						FROM user_favorites uf
						WHERE user_email = #{likeUserEmail}
						AND delete_yn = 'N'
						AND bc.id = uf.board_id
						AND uf.comment_id IS NULL
						) != '0' then 'Y'
					ELSE 'N' END) as user_favorite,
					( SELECT id
						FROM user_favorites uf
						WHERE user_email = #{likeUserEmail}
						AND delete_yn = 'N'
						AND bc.id = uf.board_id
						AND uf.comment_id IS NULL ) user_favorite_id
	          FROM board_contents bc
	          left join `user` u on bc.create_id = u.user_email
	          join (SELECT @rownum:= 0) rnum ) a
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)">and category_id = #{categoryId}</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardUrl)">and board_id in (select id from board_setting where url = #{boardUrl})</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">and board_id = #{boardId}</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >and delete_yn=#{deleteYn}</if>
         </where>
	</select>

	<select id="selectListBoardContents" parameterType="HashMap" resultType="BoardContents">
		SELECT *
		  FROM(
	        SELECT @rownum:=@rownum+1 AS rownum, bc.id, board_id, upper_board_id, mission_type,
	               category_id, (SELECT title FROM board_category ct WHERE ct.board_id = bc.id AND ct.id = bc.category_id) category_name,
	               board_link, title, content, apply_code,
	               series_id, tags, thumbnail_url,
	               favorite_count, scrap_count, comment_count,
	               view_count, attachment_count, bc.use_yn, fix_yn, secret_yn,
	               bc.start_date, bc.expire_date, assemble_date, assemble_time,
	               bc.delete_yn, bc.delete_id, bc.delete_date,
	               bc.create_id, bc.create_date, bc.last_update_id, bc.last_update_date,
	               ifnull(u.user_nick_name, u.user_name) user_nick_name,
	               ifnull(u.user_name, bc.guest_name) user_name
				   , CONCAT( ( SELECT menu_url FROM menu_user WHERE menu_id = mu.upper_menu_id), mu.menu_url ) as full_menu_url
				   , (CASE WHEN (
								SELECT COUNT(*)
								FROM user_favorites uf
								WHERE user_email = #{likeUserEmail}
								AND uf.delete_yn = 'N'
								AND bc.id = uf.board_id
								AND uf.comment_id IS NULL
							) != '0' then 'Y'
						ELSE 'N' END) as user_favorite,
					( SELECT id
						FROM user_favorites uf
						WHERE user_email = #{likeUserEmail}
							AND uf.delete_yn = 'N'
							AND bc.id = uf.board_id
							AND uf.comment_id IS NULL ) user_favorite_id
				   , (select id from board_contents bc2 where upper_board_id = bc.id and board_id = 7 and create_id = #{possibleUserEmail} and delete_yn = 'N' ) is_possible
				   , bc.user_point_set_id, bc.user_badge_set_id
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isUpperBoardId) and !isUpperBoardId" >
                   , bc.mission_check_yn, ups.accrued_reason, ups.accrued_point, bc2.badge_name, bc2.badge_file_url
                   </if>
	          FROM board_contents bc
	          left join `user` u on bc.create_id = u.user_email
			  LEFT JOIN menu_user mu on mu.menu_id = (SELECT menu_id FROM menu_user AS mu2 WHERE REPLACE(mu2.menu_url, '/','') = (SELECT REPLACE(url, '/','') FROM board_setting bs WHERE bs.id = bc.board_id) )
	          <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isUpperBoardId) and !isUpperBoardId" >
	          left join user_point_set ups on bc.user_point_set_id =ups.id
			  left join (
				  select bc.badge_id, bc.badge_name, bai.upload_filename badge_file_url
				    from badge_contents bc
				    join badge_attach_image bai on bc.badge_image_file_id  = bai.file_id) bc2 on bc.user_badge_set_id =bc2.badge_id
	          </if>
	          join (SELECT @rownum:= 0) rnum
	         <where>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and bc.id = #{id}</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">and bc.create_id = #{userEmail}</if>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)">and bc.category_id = #{categoryId}</if>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and bc.use_yn=#{useYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardUrl)">and bc.board_id in (select id from board_setting where url = #{boardUrl})</if>
         		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">and bc.board_id = #{boardId}</if>
         		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionType)">and mission_check_yn IS NOT NULL</if>
         		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(titleLike)">and bc.title like concat('%',#{titleLike},'%')</if>
         		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentLike)">and bc.content like concat('%',#{titleLike},'%')</if>
         		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and concat(COALESCE(bc.title, ''), COALESCE(bc.content, '')) like concat('%',#{searchKey},'%')</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)">and bc.create_id = #{createId}</if>
         		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >and bc.delete_yn=#{deleteYn}</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isUpperBoardId)" >
		         	<if test="!isUpperBoardId" >and upper_board_id is null</if>
		         	<if test="isUpperBoardId" >and upper_board_id is not null</if>
	         	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(assembleDateRange) and assembleDateRange == 'month'" >
				AND (
					bc.assemble_date > CURDATE()
					OR (
						bc.assemble_date = CURDATE()
						AND bc.assemble_time > TIME(NOW())
					)
				)
				</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(assembleDateRange) and assembleDateRange == 'finish'" >
				AND (
					bc.assemble_date &lt; CURDATE()
					OR (
						bc.assemble_date = CURDATE()
						AND bc.assemble_time &lt; TIME(NOW())
					)
				)
				</if>
				AND ( (bc.start_date IS NOT NULL AND bc.expire_date IS NULL AND bc.start_date >= CURDATE())
				     OR (bc.start_date IS NULL AND bc.expire_date IS NOT NULL AND CURDATE() >= bc.expire_date)
				     OR (bc.start_date IS NOT NULL AND bc.expire_date IS NOT NULL)
				     OR (bc.start_date IS NULL AND bc.expire_date IS NULL) )
			</where>
	         <if test="sort eq null or sortOrder eq null">
	         	ORDER BY bc.id
	         </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort == 'id'">bc.id</when>
		            <when test="item.sort == 'title'">bc.title</when>
		            <when test="item.sort == 'viewCount'">view_count</when>
		            <when test="item.sort == 'useYn'">bc.use_yn</when>
		            <when test="item.sort == 'deleteYn'">bc.delete_yn</when>
		            <when test="item.sort == 'lastUpdateDate'">bc.last_update_date</when>
		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort == 'id'">a.id</when>
		            <when test="sort == 'title'">a.title</when>
		            <when test="sort == 'viewCount'">a.view_count</when>
		            <when test="sort == 'useYn'">a.use_yn</when>
		            <when test="sort == 'deleteYn'">a.delete_yn</when>
		            <when test="sort == 'createDate'">a.create_date</when>
		            <when test="sort == 'lastUpdateDate'">a.last_update_date</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	 <select id="selectCountBoardContents" parameterType="HashMap" resultType="Integer">
        SELECT count(bc.id) FROM board_contents bc
		 left join `user` u on bc.create_id = u.user_email
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and bc.id = #{id}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and bc.use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)">and bc.category_id = #{categoryId}</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardUrl)">and bc.board_id in (select id from board_setting where url = #{boardUrl})</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">and bc.board_id = #{boardId}</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(titleLike)">and bc.title like concat('%',#{titleLike},'%')</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentLike)">and bc.content like concat('%',#{titleLike},'%')</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and concat(bc.title, bc.content) like concat('%',#{searchKey},'%')</if>
         	<if test="isComment">and comment_count = 0 </if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >and bc.delete_yn=#{deleteYn}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">and bc.create_id = #{userEmail}</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isUpperBoardId)" >
	         	<if test="!isUpperBoardId" >and bc.upper_board_id is null</if>
	         	<if test="isUpperBoardId" >and bc.upper_board_id is not null</if>
         	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionCheckYn) and missionCheckYn.equals('YN')">
	         	and mission_check_yn IS NOT NULL
         	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(assembleDateRange) and assembleDateRange == 'month'" >
				AND (
					bc.assemble_date > CURDATE()
					OR (
						bc.assemble_date = CURDATE()
						AND bc.assemble_time > TIME(NOW())
					)
				)
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(assembleDateRange) and assembleDateRange == 'finish'" >
				AND (
					bc.assemble_date &lt; CURDATE()
					OR (
						bc.assemble_date = CURDATE()
						AND bc.assemble_time &lt; TIME(NOW())
					)
				)
			</if>
			AND ( (bc.start_date IS NOT NULL AND bc.expire_date IS NULL AND bc.start_date >= CURDATE())
			     OR (bc.start_date IS NULL AND bc.expire_date IS NOT NULL AND CURDATE() >= bc.expire_date)
			     OR (bc.start_date IS NOT NULL AND bc.expire_date IS NOT NULL)
			     OR (bc.start_date IS NULL AND bc.expire_date IS NULL) )
         </where>
    </select>

	<select id="selectListGreethingContents" parameterType="HashMap" resultType="BoardContents">
		SELECT *, u.user_profile_image,
		(CASE WHEN (
			SELECT COUNT(*)
			FROM user_favorites uf
			WHERE user_email = #{likeUserEmail}
			AND uf.delete_yn = 'N'
			AND bc.id = uf.comment_id
		) != '0' then 'Y'
		ELSE 'N' END) as user_favorite,
		( SELECT id
			FROM user_favorites uf
			WHERE user_email = #{likeUserEmail}
			AND uf.delete_yn = 'N'
			AND bc.id = uf.comment_id ) user_favorite_id
		FROM board_contents bc
		LEFT JOIN user u on u.user_email = bc.create_id
		WHERE bc.id IN (
			WITH RECURSIVE greething AS (
			SELECT
				id
			FROM board_contents
			WHERE id IN (
			SELECT *
			FROM
				(
				SELECT
					bc.id
				FROM
					board_contents bc
				LEFT JOIN menu_user mu on
					mu.menu_id = (
					SELECT
						menu_id
					FROM
						menu_user AS mu2
					WHERE
						REPLACE(mu2.menu_url, '/', '') = (
						SELECT
							REPLACE(url, '/', '')
						FROM
							board_setting bs
						WHERE
							bs.id = bc.board_id) )
				WHERE
					bc.delete_yn ='N'
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and bc.id = #{id}</if>
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and bc.use_yn=#{useYn}	</if>
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)">and bc.category_id = #{categoryId}</if>
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardUrl)">and bc.board_id in (select id from board_setting where url = #{boardUrl})</if>
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">and bc.board_id = #{boardId}</if>

					and bc.upper_board_id IS NULL
					ORDER BY bc.create_date	DESC
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
						LIMIT #{itemStartPosition}, #{pagePerSize}
					</if>
				) as boardContents
			)
			UNION ALL
			SELECT
				t.id
			FROM  board_contents t
			INNER JOIN greething h ON t.upper_board_id = h.id
		)
		SELECT
			id
		FROM greething)
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and content like concat('%',#{searchKey},'%')</if>
		ORDER BY id DESC
	</select>

	<insert id="insertBoardContents" parameterType="BoardContents" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO board_contents
               (<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	board_id,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperBoardId)" >	upper_board_id,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionType)" >	mission_type,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionCheckYn)" >	mission_check_yn,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)" >	category_id,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardLink)" >	board_link,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	title,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	content,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	apply_code,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(seriesId)" >	series_id,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tags)" >	tags,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(thumbnailUrl)" >	thumbnail_url,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(favoriteCount)" >	favorite_count,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(scrapCount)" >	scrap_count,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentCount)" >	comment_count,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(viewCount)" >	view_count,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(attachmentCount)" >	attachment_count,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fixYn)" >	fix_yn,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	secret_yn,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestName)" >	guest_name,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestPass)" >	guest_pass,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	start_date,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	expire_date,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(assembleDate)" >	assemble_date,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(assembleTime)" >	assemble_time,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id,	</if>
                create_date)
        VALUES (<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	#{boardId},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperBoardId)" >	#{upperBoardId},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionType)" >	#{missionType},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionCheckYn)" >	#{missionCheckYn},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)" >	#{categoryId},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardLink)" >	#{boardLink},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	#{title},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	#{content},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	#{applyCode},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(seriesId)" >	#{seriesId},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tags)" >	#{tags},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(thumbnailUrl)" >	#{thumbnailUrl},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(favoriteCount)" >	#{favoriteCount},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(scrapCount)" >	#{scrapCount},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentCount)" >	#{commentCount},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(viewCount)" >	#{viewCount},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(attachmentCount)" >	#{attachmentCount},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	#{useYn},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fixYn)" >	#{fixYn},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	#{secretYn},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestName)" >	#{guestName},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestPass)" >	#{guestPass},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	#{startDate},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	#{expireDate},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(assembleDate)" >	#{assembleDate},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(assembleTime)" >	#{assembleTime},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	#{createId},	</if>
                now()
                )
    </insert>

    <update id="updateBoardContents" parameterType="BoardContents">
        UPDATE board_contents
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId) and boardId != 0" >	board_id=#{boardId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperBoardId) and upperBoardId != 0" >	upper_board_id=#{upperBoardId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionType)" >	mission_type=#{missionType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId) and categoryId != 0" >	category_id=#{categoryId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardLink)" >	board_link=#{boardLink},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	title=#{title},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	content=#{content},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	apply_code=#{applyCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(seriesId)" >	series_id=#{seriesId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tags)" >	tags=#{tags},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(thumbnailUrl)" >	thumbnail_url=#{thumbnailUrl},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardFavoriteType)">
				<choose>
					<when test="boardFavoriteType == 'board'">
						<choose>
							<when test="favoriteCalcType == 'plus'">
								favorite_count = favorite_count + 1,
							</when>
							<when test="favoriteCalcType == 'minus'">
								favorite_count = favorite_count - 1,
							</when>
						</choose>
					</when>
					<when test="boardFavoriteType == 'comment'">
						<choose>
							<when test="favoriteCalcType == 'plus'">
								comment_count = comment_count + 1,
							</when>
							<when test="favoriteCalcType == 'minus'">
								comment_count = comment_count - 1,
							</when>
						</choose>
					</when>
				</choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(scrapCount)" >	scrap_count=#{scrapCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(viewCount)" >	view_count=#{viewCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(attachmentCount)" >	attachment_count=#{attachmentCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fixYn)" >	fix_yn=#{fixYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	secret_yn=#{secretYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestName)" >	guest_name=#{guestName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestPass)" >	guest_pass=#{guestPass},	</if>
			<choose>
				<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">start_date=#{startDate},</when>
				<otherwise>start_date=null,</otherwise>
			</choose>
			<choose>
				<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)">expire_date=#{expireDate},</when>
				<otherwise>expire_date=null,</otherwise>
			</choose>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(assembleDate)" >	assemble_date=#{assembleDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(assembleTime)" >	assemble_time=#{assembleTime},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	delete_yn=#{deleteYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	delete_id=#{deleteId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	delete_date=#{deleteDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
		</where>
    </update>

    <update id="restoreBoardContents" parameterType="BoardContents">
        UPDATE board_contents
           SET delete_yn='N'
        WHERE id = #{id}
    </update>

    <update id="deleteBoardContents" parameterType="BoardContents">
        UPDATE board_contents
           SET delete_yn='Y'
        WHERE id = #{id}
    </update>

    <update id="updateBoardContentDelete" parameterType="BoardContents">
        UPDATE board_contents
           <set>
               view_count = view_count + 1
           </set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
		</where>
    </update>

    <update id="updateBoardContent_addViewCount" parameterType="HashMap">
        UPDATE board_contents
           SET comment_count = 1
        WHERE id = #{content_id} AND delete_yn='N'
    </update>

    <update id="updateBoardContent_CommentCount" parameterType="HashMap">
        UPDATE board_contents
        SET comment_count = 1
        WHERE id = #{content_id} AND delete_yn='N'
    </update>
<!--################################### boardSetting ###################################-->
	<select id="selectOneBoardSetting" parameterType="HashMap" resultType="BoardSetting">
		SELECT a.id,b.menu_id, a.url,a.type_code,a.title,a.subtitle,
			   a.thumbnail_yn,a.banner_large_yn,a.banner_small_yn,a.category_yn,a.apply_code_yn,
			   a.secret_yn,a.guest_yn,a.comment_yn,a.tag_yn,a.favorite_yn, a.sub_content_yn,
			   a.scrap_yn,a.series_yn,a.default_page_size,a.content_store_days,a.use_yn,
			   a.fix_yn,a.start_date,a.expire_date,a.attach_file_size,
			   a.create_id,a.create_date,a.last_update_id,a.last_update_date,a.delete_yn,a.delete_id,a.delete_date,mu.menu_url
		  FROM board_setting a
		  inner join menu_connect_board b on a.id = b.board_id
		  left join menu_user mu on mu.menu_id = b.menu_id
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and a.id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and b.menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(url)" >	and a.url=#{url}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(typeCode)" >	and a.type_code=#{typeCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and a.title=#{title}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subtitle)" >	and a.subtitle=#{subtitle}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(thumbnailYn)" >	and a.thumbnail_yn=#{thumbnailYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(bannerLargeYn)" >	and a.banner_large_yn=#{bannerLargeYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(bannerSmallYn)" >	and a.banner_small_yn=#{bannerSmallYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryYn)" >	and a.category_yn=#{categoryYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCodeYn)" >	and a.apply_code_yn=#{applyCodeYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	and a.secret_yn=#{secretYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestYn)" >	and a.guest_yn=#{guestYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentYn)" >	and a.comment_yn=#{commentYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tagYn)" >	and a.tag_yn=#{tagYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(favoriteYn)" >	and a.favorite_yn=#{favoriteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(scrapYn)" >	and a.scrap_yn=#{scrapYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(seriesYn)" >	and a.series_yn=#{seriesYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(defaultPageSize)" >	and a.default_page_size=#{defaultPageSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentStoreDays)" >	and a.content_store_days=#{contentStoreDays}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and a.use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fixYn)" >	and a.fix_yn=#{fixYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(attachFileSize)" >	and a.attach_file_size=#{attachFileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	and a.start_date=#{startDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	and a.expire_date=#{expireDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and a.create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and a.create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and a.last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and a.last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and a.delete_date=#{deleteDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuUrl)" >	and mu.menu_url=#{menuUrl}	</if>
		</where>
	</select>
	<select id="selectOneBoardSettingTypeCode" parameterType="HashMap" resultType="BoardSetting">
		SELECT a.id,a.url,a.type_code,a.title,a.subtitle,
			   a.thumbnail_yn,a.banner_large_yn,a.banner_small_yn,a.category_yn,a.apply_code_yn,
			   a.secret_yn,a.guest_yn,a.comment_yn,a.tag_yn,a.favorite_yn, a.sub_content_yn,
			   a.scrap_yn,a.series_yn,a.default_page_size,a.content_store_days,a.use_yn,
			   a.fix_yn,a.start_date,a.expire_date,a.attach_file_size,
			   a.create_id,a.create_date,a.last_update_id,a.last_update_date,a.delete_yn,a.delete_id,a.delete_date
		  FROM board_setting a
		  WHERE a.type_code = #{typeCode}
	</select>

	<select id="selectListBoardSettingTypeCode" parameterType="HashMap" resultType="BoardSetting">
		SELECT a.id,a.url,a.type_code,a.title,a.subtitle,
			   a.thumbnail_yn,a.banner_large_yn,a.banner_small_yn,a.category_yn,a.apply_code_yn,
			   a.secret_yn,a.guest_yn,a.comment_yn,a.tag_yn,a.favorite_yn, a.sub_content_yn,
			   a.scrap_yn,a.series_yn,a.default_page_size,a.content_store_days,a.use_yn,
			   a.fix_yn,a.start_date,a.expire_date,a.attach_file_size,
			   a.create_id,a.create_date,a.last_update_id,a.last_update_date,a.delete_yn,a.delete_id,a.delete_date
		  FROM board_setting a
		  WHERE a.type_code IN (
		    <foreach item="item" index="index" collection="typeCode.split(',')" separator=",">
		      #{item}
		    </foreach>
		  )
	</select>
<!--################################### boardComment ###################################-->
    <select id="selectOneBoardComment" parameterType="HashMap" resultType="BoardComment">
        SELECT id, board_id, content_id,
               tab_index, upper_id, note,
               warning_id, blind_yn,
               create_id, create_date
        FROM board_comment
	<where>
		AND delete_yn = 'N'
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
   		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">and board_id = #{boardId}</if>
   		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)">and content_id = #{contentId}</if>
	</where>
        ORDER BY id DESC
        LIMIT 1
    </select>

	<select id="selectCountBoardComment" parameterType="HashMap" resultType="Integer">
		SELECT count(id)
		  FROM board_comment
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">and board_id = #{boardId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)">and content_id = #{contentId}</if>
		</where>
	</select>

	<select id="selectListBoardComment" parameterType="HashMap" resultType="BoardComment">
		SELECT ROW_NUMBER() OVER(ORDER BY id DESC) AS rownum, bct.*, u.user_profile_image,
		(CASE WHEN (
			SELECT COUNT(*)
			FROM user_favorites uf
			WHERE user_email = #{likeUserEmail}
			AND uf.delete_yn = 'N'
			AND bct.id = uf.comment_id
		) != '0' then 'Y'
		ELSE 'N' END) as user_favorite,
		( SELECT id
			FROM user_favorites uf
			WHERE user_email = #{likeUserEmail}
			AND uf.delete_yn = 'N'
			AND bct.id = uf.comment_id ) user_favorite_id
		FROM board_comment bct
		LEFT JOIN user u on u.user_email = bct.create_id
		WHERE
			bct.id IN ( WITH RECURSIVE comment AS (
			SELECT
				id
			FROM
				board_comment
			WHERE
				id IN (
				SELECT
					*
				FROM
					(
					SELECT
						bct.id
					FROM
						board_comment bct
					WHERE
						bct.delete_yn = 'N'
						<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
						<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">and board_id = #{boardId}</if>
						<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)">and content_id = #{contentId}</if>
						and bct.upper_id = 0
					ORDER BY
						bct.create_date DESC
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
						LIMIT #{itemStartPosition}, #{pagePerSize}
					</if>
					) as boardComment )
		UNION ALL
			SELECT
				t.id
			FROM
				board_comment t
			INNER JOIN comment h ON
				t.upper_id = h.id )
			SELECT
				id
			FROM
				comment)
		ORDER BY
			id DESC
	</select>

    <insert id="insertBoardComment" parameterType="BoardComment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO board_comment
        (
            board_id, content_id,
            tab_index, upper_id, note, mentions,
            create_id, create_date
        )
        VALUES
		(
			#{boardId}, #{contentId},
			#{tabIndex}, #{upperId}, #{note}, #{mentions},
			#{createId}, now()
		)
    </insert>

    <update id="updateBoardComment" parameterType="BoardComment">
        UPDATE board_comment
        SET note = #{note}, last_update_id = #{createId}, last_update_date = now()
        WHERE id = #{id} AND board_id = #{boardId} AND content_id = #{contentId}
    </update>

	<update id="updateBoardCommentFavorite" parameterType="BoardComment">
        UPDATE board_comment
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(favoriteCalcType)">
				<choose>
					<when test="favoriteCalcType == 'plus'">
						favorite_count = favorite_count + 1,
					</when>
					<when test="favoriteCalcType == 'minus'">
						favorite_count = favorite_count - 1,
					</when>
				</choose>
			</if>
		</set>
	    WHERE id = #{commentId}
    </update>

	<!-- 금주 가장 많이 참여한 회원 -->
	<select id="selectMostMissionUser" parameterType="HashMap" resultType="BoardContents">
	<![CDATA[
		SELECT
			create_id, COUNT(*) as mission_count, u.user_email, u.user_nick_name, u.user_profile_image
			, RANK() OVER (ORDER BY COUNT(*) DESC) as rank
		FROM
			board_contents as bc
            LEFT JOIN user as u on u.user_email = bc.create_id
		WHERE
			board_id = #{boardId} AND use_yn = 'Y' AND delete_yn = 'N' AND bc.upper_board_id is not null
			AND DATE_FORMAT( create_date, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
			AND DATE_FORMAT( create_date, '%Y-%m-%d') <= NOW()
		GROUP BY
			create_id
		ORDER BY
				rank ASC, create_id
		LIMIT 5
	]]>
	</select>
	<!-- 금주 가장 많이 수행된 미션 -->
	<select id="selectMostMissionMonth" parameterType="HashMap" resultType="BoardCategory">
	<![CDATA[
		SELECT bc.board_id, bc.id, bc.title
			, RANK() OVER (ORDER BY bc2.cnt DESC) as rank
		FROM board_contents bc
		left join (
			SELECT bc.board_id, bc.upper_board_id, count(*) cnt
		 	  FROM board_contents bc
		 	 WHERE bc.board_id = #{boardId} and bc.upper_board_id is not null AND bc.use_yn = 'Y' AND bc.delete_yn = 'N'
				AND DATE_FORMAT(bc.create_date, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
				AND bc.create_date <= NOW()
		     GROUP BY bc.upper_board_id) bc2 on bc.upper_board_id = bc2.upper_board_id
		WHERE bc.board_id = #{boardId} and bc.upper_board_id is not null
		  AND bc.use_yn = 'Y' AND bc.delete_yn = 'N'
		  AND DATE_FORMAT(bc.create_date, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
          AND bc.create_date <= NOW()
		GROUP BY bc.board_id, bc.title
		ORDER BY rank asc, bc.title ASC
		LIMIT 5
	]]>
	</select>
<!--################################### boardCategory ###################################-->
	<select id="selectCountBoardCategory" parameterType="HashMap" resultType="Integer">
        SELECT count(id)
          FROM board_category
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	and board_id=#{boardId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and title=#{title}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(note)" >	and note=#{note}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderNum)" >	and order_num=#{orderNum}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	and start_date=#{startDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	and expire_date=#{expireDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and a.delete_date=#{deleteDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
         </where>
    </select>

    <select id="selectListBoardCategory" parameterType="HashMap" resultType="BoardCategory">
		SELECT *
		  FROM(
	        SELECT @rownum:=@rownum+1 AS rownum
		        , a.id
				, a.board_id
				, a.title
				, a.note
				, a.order_num
				, a.use_yn
				, a.default_yn
				, a.search_yn
				, a.start_date
				, a.expire_date
				, a.create_id
				, a.create_date
				, a.last_update_id
				, a.last_update_date
				, a.delete_yn
				, a.delete_id
				, a.delete_date
				, a.point_use_yn
				, a.user_point_set_id
				, ifnull(b.category_count, 0) category_count
	          FROM board_category a
	          left join (select category_id, count(category_id) category_count from board_contents bc group by category_id) b on a.id = b.category_id
	          join (SELECT @rownum:= 0) rnum
	         <where>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	and board_id=#{boardId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and title=#{title}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(note)" >	and note=#{note}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderNum)" >	and order_num=#{orderNum}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(defaultYn)" >	and default_yn=#{defaultYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchYn)" >	and search_yn=#{searchYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	and start_date=#{startDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	and expire_date=#{expireDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and a.delete_date=#{deleteDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
	         </where>

	         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort == 'id'">id</when>
		            <when test="sort == 'title'">title</when>
		            <when test="sort == 'orderNum'">order_num</when>
		            <when test="sort == 'viewCount'">view_count</when>
		            <when test="sort == 'useYn'">use_yn</when>
		            <when test="sort == 'deleteYn'">delete_yn</when>
		            <when test="sort == 'lastUpdateDate'">last_update_date</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort == 'id'">id</when>
		            <when test="item.sort == 'title'">title</when>
		            <when test="item.sort == 'orderNum'">order_num</when>
		            <when test="item.sort == 'viewCount'">view_count</when>
		            <when test="item.sort == 'useYn'">use_yn</when>
		            <when test="item.sort == 'deleteYn'">delete_yn</when>
		            <when test="item.sort == 'lastUpdateDate'">last_update_date</when>
		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectListBoardProjectCategory" parameterType="HashMap" resultType="BoardCategory">
		SELECT *
		  FROM(
	        SELECT @rownum:=@rownum+1 AS rownum
		        , a.id
				, a.board_id
				, a.title
				, a.note
				, a.order_num
				, a.use_yn
				, a.default_yn
				, a.search_yn
				, a.start_date
				, a.expire_date
				, a.create_id
				, a.create_date
				, a.last_update_id
				, a.last_update_date
				, a.delete_yn
				, a.delete_id
				, a.delete_date
				, a.point_use_yn
				, a.user_point_set_id
				, ifnull(b.category_count, 0) category_count
	          FROM board_category a
	          left join (select category_id, count(category_id) category_count from board_contents bc group by category_id) b on a.id = b.category_id
	          join (SELECT @rownum:= 0) rnum
	         <where>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	and board_id=#{boardId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and title=#{title}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(note)" >	and note=#{note}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderNum)" >	and order_num=#{orderNum}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(defaultYn)" >	and default_yn=#{defaultYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchYn)" >	and search_yn=#{searchYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	and start_date=#{startDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	and expire_date=#{expireDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and a.delete_date=#{deleteDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
	         </where>

	         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort == 'id'">id</when>
		            <when test="sort == 'title'">title</when>
		            <when test="sort == 'orderNum'">order_num</when>
		            <when test="sort == 'viewCount'">view_count</when>
		            <when test="sort == 'useYn'">use_yn</when>
		            <when test="sort == 'deleteYn'">delete_yn</when>
		            <when test="sort == 'lastUpdateDate'">last_update_date</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort == 'id'">id</when>
		            <when test="item.sort == 'title'">title</when>
		            <when test="item.sort == 'orderNum'">order_num</when>
		            <when test="item.sort == 'viewCount'">view_count</when>
		            <when test="item.sort == 'useYn'">use_yn</when>
		            <when test="item.sort == 'deleteYn'">delete_yn</when>
		            <when test="item.sort == 'lastUpdateDate'">last_update_date</when>
		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

    <select id="selectOneBoardCategory" parameterType="HashMap" resultType="BoardCategory">
        SELECT @rownum:=@rownum+1 AS rownum
        	, a.id
			, a.board_id
			, a.title
			, a.note
			, a.order_num
			, a.use_yn
			, a.default_yn
			, a.search_yn
			, a.start_date
			, a.expire_date
			, a.create_id
			, a.create_date
			, a.last_update_id
			, a.last_update_date
			, a.delete_yn
			, a.delete_id
			, a.delete_date
			, a.point_use_yn
			, a.user_point_set_id
          FROM board_category a
          join (SELECT @rownum:= 0) rnum
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	and board_id=#{boardId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and title=#{title}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(note)" >	and note=#{note}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderNum)" >	and order_num=#{orderNum}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(defaultYn)" >	and default_yn=#{defaultYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchYn)" >	and search_yn=#{searchYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	and start_date=#{startDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	and expire_date=#{expireDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and a.delete_date=#{deleteDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
         </where>
	</select>

	<!-- 미션 관련 카테고리 mission_complete_count 업데이트 -->
	<update id="updateBoardCategoryMissionCompleteCount" parameterType="HashMap">
		UPDATE board_category SET mission_complete_count = mission_complete_count + 1
		WHERE id = #{categoryId} AND board_id = #{boardId} AND delete_yn = 'N'
	</update>



<!--################################### BoardAttachFile ###################################-->
	<select id="selectCountBoardAttachFile" parameterType="HashMap" resultType="Integer">
		SELECT count(id)
		  FROM board_attach_file
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType) and
					  @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey) ">
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType=='userEmail'" >and title LIKE CONCAT('%', #{searchKey}, '%')</if>
			</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	and board_id=#{boardId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)" >	and content_id=#{contentId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>
			and delete_yn = 'N'
		</where>
	</select>

	<select id="selectListBoardAttachFile" parameterType="HashMap" resultType="BoardAttachFile">
		select *
		  from (
			SELECT @rownum:=@rownum+1 AS rownum,
			       file_id, board_id, content_id, service_type, upload_path, upload_filename, file_extension, file_size, file_mimetype, origin_filename, upload_id, upload_date
			  FROM board_attach_file
			  join (SELECT @rownum:= 0) rnum
			<where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType) and
						  @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey) ">
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType=='userEmail'" >and title LIKE CONCAT('%', #{searchKey}, '%')</if>
				</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	and board_id=#{boardId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)" >	and content_id=#{contentId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and content_id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>

			</where> ) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneBoardAttachFile" parameterType="HashMap" resultType="BoardAttachFile">
		SELECT file_id, board_id, content_id, service_type, upload_path, upload_filename, file_extension, file_size, file_mimetype, origin_filename, upload_id, upload_date
		  FROM board_attach_file
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	and board_id=#{boardId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)" >	and content_id=#{contentId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>
		</where>
	</select>

    <insert id="insertBoardAttachFile" parameterType="BoardAttachFile">
        INSERT INTO board_attach_file
               (
	                board_id, content_id, service_type,
	                upload_path, upload_filename,
	                file_extension, file_size, file_mimetype,
	                origin_filename,
	                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	upload_id,	</if>
	                 upload_date
                )
        VALUES (
                   #{boardId}, #{contentId}, #{serviceType},
                   #{uploadPath}, #{uploadFilename},
                   #{fileExtension}, #{fileSize}, #{fileMimetype},
                   #{originFilename},
                   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	#{uploadId},	</if>
                    now()
               )
    </insert>

    <delete id="deleteBoardAttachFile" parameterType="BoardAttachFile">
		delete from board_attach_file
		 WHERE file_id = #{fileId}
	</delete>






	<!-- 청풍용 -->
	<select id="selectListWroteBoardContents" parameterType="HashMap" resultType="BoardContents">
		SELECT bc.id, bc.board_id, bc.category_id, bc.title, bc.content, bc.thumbnail_url
		, bs.type_code, CONCAT( ( SELECT menu_url FROM menu_user WHERE menu_id = mu.upper_menu_id), mu.menu_url )  as full_menu_url
		, bc.create_date, bc.view_count, bc.favorite_count
		FROM board_contents bc
		LEFT JOIN board_setting bs on bs.id = bc.board_id
		LEFT JOIN menu_user mu on REPLACE(mu.menu_url, '/', '') = bs.url
		WHERE bc.use_yn = 'Y' AND bc.delete_yn = 'N'
		AND bc.create_id = #{userEmail}
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">
			   AND bc.board_id IN (${boardId})
		</if>
		ORDER BY bc.create_date DESC
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
        </if>
	</select>
	<select id="selectOneMenuConnectBoard" parameterType="HashMap" resultType="MenuConnectBoard">
		SELECT *
		  FROM menu_connect_board
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
		</where>
	</select>


	<select id="selectCountWroteBoardContents" parameterType="HashMap" resultType="Integer">
		SELECT count(id)
		  FROM board_contents bc
		 WHERE bc.use_yn = 'Y' AND bc.delete_yn = 'N' AND bc.create_id = #{userEmail}
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">
			   AND bc.board_id IN (#{boardId})
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardType) and boardType == 'board'">
			   AND board_id NOT IN (7)
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardType) and boardType == 'mission'">
			   AND board_id IN (7)
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardType) and boardType == 'review'">
			   AND board_id IN (3)
		</if>
	</select>

	<select id="selectCountWroteBoardComment" parameterType="HashMap" resultType="Integer">
		SELECT count(id)
		  FROM board_comment
		 WHERE blind_yn = 'N' AND delete_yn = 'N' AND create_id = #{userEmail}
	</select>

	<select id="selectListTotalComment" parameterType="HashMap" resultType="HashMap">
		SELECT
			bc.* 
			, u.user_nick_name, u.user_profile_image
		FROM
			board_comment bc
		LEFT JOIN user as u on u.user_email = bc.create_id 
		WHERE
			bc.upper_id = 0
			AND bc.content_id = #{contentId}
			AND bc.delete_yn = 'N'
		ORDER BY
			bc.create_date asc
		LIMIT #{size} OFFSET #{offset}
	</select>

	<select id="selectTotalCommentCount" parameterType="HashMap" resultType="Integer">
		SELECT count(id)
		FROM board_comment 
		WHERE upper_id = 0 AND delete_yn = 'N'
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)">
			AND content_id = #{contentId}
		</if>
		ORDER BY create_date asc 
	</select>

	<update id="updateBoardComments" parameterType="HashMap">
		UPDATE board_comment 
		SET note = #{note}, mentions = #{mentions},
			last_update_id = #{lastUpdateId},
			last_update_date = now() 
		WHERE id = #{id}
	</update>

	<update id="deleteBoardComments" parameterType="HashMap">
		UPDATE board_comment 
		SET delete_yn = 'Y',
		last_update_id = #{lastUpdateId},
		last_update_date = now() 
		WHERE id = #{id}
	</update>

	<select id="selectCommentReply" parameterType="HashMap" resultType="HashMap">
		SELECT bc.* 
		, u.user_nick_name, u.user_profile_image
		FROM board_comment AS bc
		LEFT JOIN user as u on u.user_email = bc.create_id 
		WHERE upper_id = #{upperId} AND delete_yn = 'N'
		ORDER BY create_date asc 
		LIMIT #{size} OFFSET #{offset}
	</select>

	<select id="selectOneCommentReply" parameterType="HashMap" resultType="HashMap">
		SELECT bc.* 
		, u.user_nick_name, u.user_profile_image
		FROM board_comment AS bc
		LEFT JOIN user as u on u.user_email = bc.create_id 
		WHERE id = #{id}
	</select>

	<select id="selectCommentReplyCount" parameterType="HashMap" resultType="Integer">
		SELECT count(id)
		FROM board_comment 
		WHERE upper_id = #{upperId} AND delete_yn = 'N'
	</select>

	<select id="selectOneCommentMentions" parameterType="HashMap" resultType="string">
		SELECT DISTINCT u.user_nick_name
		FROM board_comment AS c 
		JOIN user AS u ON c.create_id = u.user_email 
		WHERE c.upper_id = #{upperId}
	</select>

	<select id="isMissionAlreadyCheck" parameterType="HashMap" resultType="Integer">
		SELECT COUNT(*)
		FROM board_contents
		WHERE board_id = #{boardId}
		AND upper_board_id = #{upperBoardId}
		AND create_id = #{createId}
		AND delete_yn = 'N'
		AND use_yn = 'Y'
	</select>
</mapper>