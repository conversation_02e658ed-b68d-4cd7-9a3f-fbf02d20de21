package kr.co.wayplus.travel.service.front;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import kr.co.wayplus.travel.mapper.front.CodeMapper;
import kr.co.wayplus.travel.mapper.front.SurveyMapper;
import kr.co.wayplus.travel.model.CodeItem;
import kr.co.wayplus.travel.model.PlaceSpot;
import kr.co.wayplus.travel.model.Survey;
import kr.co.wayplus.travel.model.SurveyImage;
import kr.co.wayplus.travel.model.SurveyQuestion;
import kr.co.wayplus.travel.model.SurveyQuestionAnswer;
import kr.co.wayplus.travel.model.SurveyRecommand;
import kr.co.wayplus.travel.model.SurveyResult;

@Service
public class CodeService {

    private final CodeMapper codeMapper;

    @Autowired
    public CodeService(CodeMapper codeMapper) {
        this.codeMapper = codeMapper;
    }

//	<!--################################### CodeItem ###################################-->
	public CodeItem selectOneCodeItem(HashMap<String, Object> paramMap) {
		return codeMapper.selectOneCodeItem(paramMap);
	}
}
