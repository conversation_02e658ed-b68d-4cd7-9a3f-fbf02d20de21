package kr.co.wayplus.travel.scheduler;

import kr.co.wayplus.travel.service.user.UserPointService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class PointScheduler {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    protected static String ACTIVE_PROFILE;

    private final UserPointService userPointService;

    public PointScheduler(UserPointService userPointService) {
        this.userPointService = userPointService;
        ACTIVE_PROFILE = System.getProperty("spring.profiles.active");
    }

    @Scheduled(cron = "0 0,30 * * * *")
    @Async
    public void pointCheck() {
        logger.debug("Point Scheduled Start");
        userPointService.pointExpiredCheck();
        logger.debug("Point Scheduled End");
    }
}
