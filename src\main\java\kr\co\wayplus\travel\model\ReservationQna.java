package kr.co.wayplus.travel.model;

import java.util.ArrayList;

import com.fasterxml.jackson.annotation.JsonInclude;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReservationQna extends CommonDataSet {
	private Integer id;
	private Long reservationId;	//예약번호
	private String applyCode;	//접수상태코드(0:접수, 1:접수확인,2:답변완료)
	private String requestDate;	//상담일시
	private String requestText;	//상담고객 요청사항
	private String responseDate;	//응대일시
	private String responseName;	//상담사명
	private String responseText;	//상담사 응대내용
	private String responseCategory;	//처리유형 코드
	private String responseSubcategory;	//세부처리유형 코드
}
