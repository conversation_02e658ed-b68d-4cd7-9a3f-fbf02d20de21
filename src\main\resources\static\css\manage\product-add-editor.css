@font-face {font-family: 'NanumSquareNeo-Variable';src: url('https://cdn.jsdelivr.net/gh/projectnoonnu/noonfonts_11-01@1.0/NanumSquareNeo-Variable.woff2') format('woff2');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Pretendard-Regular';src: url('https://cdn.jsdelivr.net/gh/Project-Noonnu/noonfonts_2107@1.1/Pretendard-Regular.woff') format('woff');font-weight: 400;font-style: normal;}
@font-face {font-family: 'GmarketSansMedium';src: url('https://cdn.jsdelivr.net/gh/projectnoonnu/noonfonts_2001@1.1/GmarketSansMedium.woff') format('woff');font-weight: normal;font-style: normal;}
/* editor frame style 용*/
*{margin:0;padding:0;}
ul{list-style: none;padding-left:0;}
body{margin:0;padding:0;}
input:disabled{cursor:not-allowed;}
.template-container{position:relative;}
#library-section{width:320px;/*height:100%;*/position:relative;display:flex;/* float:left; */border-top:solid 1px #CCCCCC;background-color:#FFFFFF;margin-left:30px;margin-top:40px;}
#editor-section{width:calc(100% - 320px);height:100%;/* float:left; */background-color:#efefef;overflow-y:scroll;position:relative;}
#editor-section header{/*visibility: hidden;*/position:fixed;top:240px;left:45%;z-index:9999;background-color:rgba(255,255,255,0.7);border-radius:5px;border:solid 1px #CCCCCC;user-select:none;}
#editor-section header div{display:inline-block;width:95px;height:30px;line-height:30px;margin-right:15px;text-align: center;}
#editor-section header div.zoom-out,#editor-section header div.zoom-in{cursor:pointer;}
#editor-section header div:last-child{margin-right:0;}
#editor-section header div.slide-width-height{margin-left:0;margin-right:10px;}
#editor-section header div.slide-width-height::before{content:"";width:2px;height:15px;background-color:#CCCCCC;position:absolute;top:9px;right:120px;}
#transform{display:flex;position:absolute;top:0;right:0%;}
#transform li{margin-right:30px;cursor:pointer;}
#transform li:last-child{margin-right:0;}
#component-design{width:100%;text-align: center;margin-top:25px;/* width:55px;border-right:solid 1px #CCCCCC; */}
#component-design li{cursor:pointer;display:inline-block;width:240px;height:50px;margin-right:15px;line-height:50px;text-align: center;background-color:#383838;color:#FFFFFF;}
#component-design li.edit-insert-template img{vertical-align: middle;margin-right:7px;}
#component-design li:last-child{margin-right:0;}
#component-design li.active{background-color:#0062D4;color:#FFFFFF;}
#library-section #button{position:absolute;top:50%;right:-29px;transform:translate(-50%,-50%);}
#font li{display:inline-block;width:100%;margin-bottom:15px;}
/*#align-components{display:none;}#align-components.active{display:block;}*/
#font li:last-child{margin-bottom:0;}
#text-radio-chk{display:flex;justify-content: flex-start;align-items: flex-start;}
.clear-fix{clear:both;}
.library{display:none;}
.library .notification-icon{margin-left: 20px;font-size: 13px;margin-bottom: 10px;}
.library.active{display:block;background-color:#FFFFFF;}
.library.active h3{text-align: center;background-color:#333333;color:#FFFFFF;height:40px;line-height:40px;font-size:18px;}
.library.active h4{width:100%;font-size:16px;font-family: 'Pretendard-Regular';margin-bottom:10px;}
.library.active select{display: block;width: 100%;padding: 0.375rem 2.25rem 0.375rem 0.75rem;-moz-padding-start: calc(0.75rem - 3px);font-size: 1rem;font-weight: 400;line-height: 1.5;color: #212529;background-color: #fff;background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");background-repeat: no-repeat;background-position: right 0.75rem center;background-size: 16px 12px;border: 1px solid #ced4da;/* border-radius: 0.375rem; */transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;-webkit-appearance: none;-moz-appearance: none;appearance: none;}
.library.active input[type='text']{display: block;width: 100%;/* padding: 0.375rem 0.75rem; */font-size: 1rem;font-weight: 400;line-height: 1.5;color: #212529;background-color: #fff;background-clip: padding-box;border: 1px solid #ced4da;-webkit-appearance: none;-moz-appearance: none;appearance: none;border-radius: 0.375rem;transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}
.library.active div.input-wrapper{width:100%;height:35px;line-height:35px;border:solid 1px #CCCCCC;margin-bottom:10px;overflow: hidden;}
.library.active input[type='range']{/* range input custom */position:relative;width:100%;}
.library.active div.hex-value{width:100%;height:35px;line-height:35px;text-align: center;border:solid 1px #CCCCCC;}
.library.active div.input-wrapper input{width:100%;height:100%;border-style:none;outline: none;}
.library.active div.input-wrapper input[type='number']{background-color:#FFFFFF;padding-left:15px;}
.library.active input:focus{color: #212529;background-color: #fff;border-color: #86b7fe;outline: 0;box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);}
.library.active select:focus{border-color: #86b7fe;outline: 0;box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);}
.library.active ul{padding:20px 15px;}
.library.active ul li{margin-bottom:10px;}
.library.active .insert-button li{display:inline-block;width:130px;height:40px;line-height:40px;margin-right:17px;text-align: center;}
.library.active .insert-button li button{background-color:transparent;border-style:none;color:#FFFFFF;font-size:18px;cursor:pointer;}
.library.active .insert-button li:first-child{background-color:#0062D4;}
.library.active .insert-button li:last-child{background-color:#383838;margin-right:0;}
.library.active .insert-button li.text-create,.library.active .insert-button li.text-apply{background-color:#0062D4;}
.section-wrapper{width:100%;display:flex;flex-direction:row;/* 아래 margin-top은 추후 지워야 함 */margin-top:55px;background-color:#efefef;height:815px;}
.section-wrapper.ck{display:block;}
.library-wrapper{width:100%;background-color:#efefef;}
.image-wrapper{display:flex;justify-content: flex-start;align-items: flex-start;}
.image-block{background-size:contain;background-repeat:no-repeat;background-position:center;overflow: hidden;/* width:320px;height:280px; */}
.image-block img{vertical-align: middle;}
.image-wrapper img{width:100%;}
.upload-btn{position: relative;display: inline-block;width:100%;height:45px;line-height:45px;overflow: hidden;border: 2px solid #ccc;border-radius: 4px;/*padding: 8px 0px;*/cursor: pointer;background-color: #f1f1f1;}
.upload-btn input[type="file"] {position: absolute;left: 0;top: 0;opacity: 0;cursor: pointer;height: 100%;width: 100%;}
.upload-btn label {display:flex;justify-content: center;align-items: center;/* display: block;white-space: nowrap;overflow: hidden;text-overflow: ellipsis; */}
.upload-btn label .icon{background-repeat:no-repeat;background-size:cover;background-position:center;width:18px;height:18px;margin-right:8px;}
.upload-btn:hover {background-color: #e1e1e1;}
.upload-btn:active {background-color: #d1d1d1;}
.preview-container{max-height:250px;overflow-y: scroll;}
.preview-image{height:150px;overflow: hidden;position:relative;}
.preview-image img{width:100%;height:100%;}
.component-item.text{font-family:'Pretendard-Regular';font-size:20px;font-weight:600;}
.component-item.image{width:350px;height:fit-content;}
.component-item.bgImage{width:100%;height:200px;}
.component-item p{text-align: center;}
#align-components > .align-types{padding:0px;}
#align-components > .align-types li{display: inline-block;width: 50px;height: 30px;cursor:pointer;}
#align-components > .align-types li div.align-img{background-size:cover;background-repeat:no-repeat;background-position: center;width:20px;height:20px;margin:auto;}
#align-components > .align-types li div.align-text{font-size: 12px;text-wrap: nowrap;text-align: center;}
