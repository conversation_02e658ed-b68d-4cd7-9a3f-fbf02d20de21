package kr.co.wayplus.travel.web.manage;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.service.manage.ExcelManageService;
import kr.co.wayplus.travel.service.manage.ReservationManageService;


/**
 * 외부 사이트 API 연계 처리
 */
@RequestMapping("/manage/excel")
@RestController
public class ExcelManangeController extends BaseController {

	private final Logger logger = LoggerFactory.getLogger(ExcelManangeController.class);

	ReservationManageService reservationManageService;
	ExcelManageService excelManageService;

	@Autowired
	public ExcelManangeController(
		 ReservationManageService reservationManageService
		 , ExcelManageService excelManageService) {
        this.reservationManageService = reservationManageService;
        this.excelManageService = excelManageService;
	}

	@GetMapping("/download")
	public ResponseEntity<byte[]> downloadCrmBoard(
        HttpServletRequest request
        , @RequestParam (required = false, defaultValue = "0") int start
        , @RequestParam (required = false, defaultValue = "10") int length
        , @RequestParam (required = false, defaultValue = "") String excelType
        , @RequestParam (required = false, defaultValue = "") String dateType
        , @RequestParam (required = false, defaultValue = "") String dateRange
        , @RequestParam (required = false, defaultValue = "") String reservationCode
        , @RequestParam (required = false, defaultValue = "") String searchKey
        , @RequestParam (required = false, defaultValue = "") String payDivision
        , @RequestParam (required = false, defaultValue = "") String responseName
        , @RequestParam (required = false, defaultValue = "") String sort
        , @RequestParam (required = false, defaultValue = "") String sortOrder
        , @RequestParam (required = false, defaultValue = "") String type
    ) {
		try {
            HashMap<String, Object> param = new HashMap<>();
            if(length >= 0) {
				param.put("itemStartPosition", start);
				param.put("pagePerSize", length);
    		}

            param.put("excelType", excelType);
            param.put("dateType", dateType);
            param.put("dateRange", dateRange);
            param.put("reservationCode", reservationCode);
            param.put("searchKey", searchKey);
            param.put("payDivision", payDivision);
            param.put("responseName", responseName);
            param.put("sort", sort);
            param.put("sortOrder", sortOrder);
            param.put("type", type);

			// Excel 생성
			byte[] excelFile = excelManageService.generateExcel(param);

            String excelFileName = new java.text.SimpleDateFormat("yyyy-MM-dd-HHmmss").format(new java.util.Date()) + " ";
            switch (excelType) {
                case "reservation":
                    excelFileName += "예약관리";
                    break;
                case "paidList":
                    excelFileName += "결제내역조회";
                    break;
                case "userList":
                    excelFileName += "회원관리";
                    break;
                case "groupList":
                    excelFileName += "단체관리";
                    break;
                default:
                    excelFileName += "제목없음";
                    break;
            }

			//응답 헤더 설정
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
			headers.setContentDisposition(ContentDisposition.builder("attachment")
					.filename(excelFileName+".xlsx", StandardCharsets.UTF_8).build());

			return new ResponseEntity<>(excelFile, headers, HttpStatus.OK);
		} catch (Exception e) {
			logger.error("Failed to generate excel file", e);
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

    

}