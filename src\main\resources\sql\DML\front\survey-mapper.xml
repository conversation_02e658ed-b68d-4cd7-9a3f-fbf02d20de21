<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.front.SurveyMapper">
<!--
	 * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
-->
	<!--################################### Survey ###################################-->
	<!--<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(surveyVersion)" >	and survey_version=#{surveyVersion}	</if> -->
	<select id="selectOneLastSurvey" parameterType="HashMap" resultType="Survey">
		SELECT a.id, a.survey_version, a.survey_title, a.survey_answer,
		       a.create_id, a.create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.delete_id, a.delete_date
		  FROM survey a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
		</where>
		 order by a.last_update_date desc
		 limit 1
	</select>
	<!--################################### SurveyQuestion ###################################-->
	<select id="selectCountLastSurveyQuestion" parameterType="HashMap" resultType="Integer">
		SELECT count(a.id)
		  FROM survey_question a
		 inner join  (SELECT id FROM survey a
		 			   <where><if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(surveyVersion)" >	and survey_version = #{surveyVersion}	</if></where>
					   order by id desc
					   limit 1) b on a.upper_id = b.id
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperId)" >	and upper_id=#{upperId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(question)" >	and question=#{question}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(agreePoint)" >	and agree_point=#{agreePoint}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(disagreePoint)" >	and disagree_point=#{disagreePoint}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortNumber)" >	and sort_number=#{sortNumber}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteCode)" >	and delete_code=#{deleteCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>
	</select>

	<select id="selectListLastSurveyQuestion" parameterType="HashMap" resultType="SurveyQuestion">
		SELECT @rownum:=@rownum+1 AS rownum,
		       a.id, a.upper_id, a.question, a.agree_point, a.disagree_point, a.sort_number, a.answer_type,
		       a.create_id, a.create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.delete_id, a.delete_date
		  FROM survey_question a
		 inner join  (SELECT id FROM survey a
		 			   <where>
		 			   and delete_yn = 'N'
		 			   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(surveyVersion)" >	and survey_version = #{surveyVersion}	</if>
		 			   </where>
					   order by id desc
					   limit 1) b on a.upper_id = b.id
		  join (SELECT @rownum:= 0) rnum
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperId)" >	and upper_id=#{upperId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(question)" >	and question=#{question}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(agreePoint)" >	and agree_point=#{agreePoint}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(disagreePoint)" >	and disagree_point=#{disagreePoint}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortNumber)" >	and sort_number=#{sortNumber}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteCode)" >	and delete_code=#{deleteCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>

		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
	    	ORDER BY
	        <choose>
	            <when test="sort=='id'" >	a.id	</when>
				<when test="sort=='scheduleTitle'" >	a.schedule_title	</when>
				<when test="sort=='scheduleDate'" >	a.schedule_date	</when>
				<when test="sort=='scheduleStartDttm'" >	a.schedule_start_dttm	</when>
				<when test="sort=='scheduleEndDttm'" >	a.schedule_end_dttm	</when>
				<when test="sort=='scheduleComment'" >	a.schedule_comment	</when>
				<when test="sort=='createId'" >a.create_id	</when>
				<when test="sort=='createDate'" >	a.create_date	</when>
				<when test="sort=='lastUpdateId'" >	a.last_update_id	</when>
				<when test="sort=='lastUpdateDate'" >	a.last_update_date	</when>
				<when test="sort=='deleteYn'" >	a.delete_yn	</when>
				<when test="sort=='deleteId'" >	a.delete_id	</when>
				<when test="sort=='deleteDate'" >	a.delete_date	</when>
	            <otherwise>rownum</otherwise>
	        </choose>
	        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
	    	ORDER BY <foreach item="item" index="index" collection="listSort" separator=",">
	    	<choose>
				<when test="item.sort=='id'" >	a.id	</when>
				<when test="item.sort=='scheduleTitle'" >	a.schedule_title	</when>
				<when test="item.sort=='scheduleDate'" >	a.schedule_date	</when>
				<when test="item.sort=='scheduleStartDttm'" >	a.schedule_start_dttm	</when>
				<when test="item.sort=='scheduleEndDttm'" >	a.schedule_end_dttm	</when>
				<when test="item.sort=='scheduleComment'" >	a.schedule_comment	</when>
				<when test="item.sort=='createId'" >a.create_id	</when>
				<when test="item.sort=='createDate'" >	a.create_date	</when>
				<when test="item.sort=='lastUpdateId'" >	a.last_update_id	</when>
				<when test="item.sort=='lastUpdateDate'" >	a.last_update_date	</when>
				<when test="item.sort=='deleteYn'" >	a.delete_yn	</when>
				<when test="item.sort=='deleteId'" >	a.delete_id	</when>
				<when test="item.sort=='deleteDate'" >	a.delete_date	</when>
	        </choose>
	    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>
<!--################################### SurveyQuestionAnswer ###################################-->
	<select id="selectCountSurveyQuestionAnswer" parameterType="HashMap" resultType="Integer">
		SELECT count(*)
		  FROM survey_question_answer a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(surveyId)" >	and survey_id=#{surveyId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(questionId)" >	and question_id=#{questionId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(answer)" >	and answer=#{answer}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortNumber)" >	and sort_number=#{sortNumber}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteCode)" >	and delete_code=#{deleteCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>
	</select>

	<select id="selectListSurveyQuestionAnswer" parameterType="HashMap" resultType="SurveyQuestionAnswer">
		SELECT @rownum:=@rownum+1 AS rownum,
		       a.id,a.survey_id,a.question_id,a.menu_id,a.answer,a.sort_number,
		       a.create_id, a.create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.delete_id, a.delete_date
		  FROM survey_question_answer a
		  join (SELECT @rownum:= 0) rnum
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(surveyId)" >	and survey_id=#{surveyId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(questionId)" >	and question_id=#{questionId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(answer)" >	and answer=#{answer}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortNumber)" >	and sort_number=#{sortNumber}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteCode)" >	and delete_code=#{deleteCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>

		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
	    	ORDER BY
	        <choose>
	            <when test="sort=='id'" >	id	</when>
				<when test="sort=='surveyId'" >	survey_id	</when>
				<when test="sort=='questionId'" >	question_id	</when>
				<when test="sort=='menuId'" >	menu_id	</when>
				<when test="sort=='answer'" >	answer	</when>
				<when test="sort=='sortNumber'" >	sort_number	</when>
				<when test="sort=='createId'" >	create_id	</when>
				<when test="sort=='createDate'" >	create_date	</when>
				<when test="sort=='lastUpdateId'" >	last_update_id	</when>
				<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
				<when test="sort=='deleteYn'" >	delete_yn	</when>
				<when test="sort=='deleteCode'" >	delete_code	</when>
				<when test="sort=='deleteId'" >	delete_id	</when>
				<when test="sort=='deleteDate'" >	delete_date	</when>
	            <otherwise>rownum</otherwise>
	        </choose>
	        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
	    	ORDER BY <foreach item="item" index="index" collection="listSort" separator=",">
	    	<choose>
				<when test="item.sort=='id'" >	a.id	</when>
				<when test="item.sort=='surveyId'" >	survey_id	</when>
				<when test="item.sort=='questionId'" >	question_id	</when>
				<when test="item.sort=='menuId'" >	menu_id	</when>
				<when test="item.sort=='answer'" >	answer	</when>
				<when test="item.sort=='sortNumber'" >	sort_number	</when>
				<when test="item.sort=='createId'" >	create_id	</when>
				<when test="item.sort=='createDate'" >	create_date	</when>
				<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
				<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
				<when test="item.sort=='deleteYn'" >	delete_yn	</when>
				<when test="item.sort=='deleteCode'" >	delete_code	</when>
				<when test="item.sort=='deleteId'" >	delete_id	</when>
				<when test="item.sort=='deleteDate'" >	delete_date	</when>
	        </choose>
	    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneSurveyQuestionAnswer" parameterType="HashMap" resultType="SurveyQuestionAnswer">
		SELECT a.id,a.survey_id,a.question_id,a.menu_id,a.answer,a.sort_number,
		       a.create_id, a.create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.delete_id, a.delete_date
		  FROM survey_question_answer a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(surveyId)" >	and survey_id=#{surveyId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(questionId)" >	and question_id=#{questionId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(answer)" >	and answer=#{answer}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortNumber)" >	and sort_number=#{sortNumber}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteCode)" >	and delete_code=#{deleteCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>
	</select>
<!--################################### surveyResult ###################################-->
	<select id="selectCountSurveyResult" parameterType="HashMap" resultType="Integer">
		SELECT count(*)
		  FROM survey_result a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperId)" >	and upper_id=#{upperId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(resultRecord)" >	and result_record=#{resultRecord}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(analyticsShareUrl)" >	and analytics_share_url=#{analyticsShareUrl}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteCode)" >	and delete_code=#{deleteCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>
	</select>

	<select id="selectListSurveyResult" parameterType="HashMap" resultType="SurveyResult">
		SELECT @rownum:=@rownum+1 AS rownum,
		       a.id, a.user_email, a.upper_id, a.result_record, a.analytics_share_url,
		       a.create_id, a.create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.delete_id, a.delete_date,
		       JSON_LENGTH( result_record,'$.result' ) allcnt,
		       IFNULL( JSON_LENGTH(JSON_SEARCH(result_record, 'all', "yes", null, '$.result[*].answer')),0) yescnt
		  FROM survey_result a
		  join (SELECT @rownum:= 0) rnum
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperId)" >	and upper_id=#{upperId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(resultRecord)" >	and result_record=#{resultRecord}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(analyticsShareUrl)" >	and analytics_share_url=#{analyticsShareUrl}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteCode)" >	and delete_code=#{deleteCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>

		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
	    	ORDER BY
	        <choose>
	            <when test="sort=='id'" >	id	</when>
				<when test="sort=='userEmail'" >	user_email	</when>
				<when test="sort=='upperId'" >	upper_id	</when>
				<when test="sort=='resultRecord'" >	result_record	</when>
				<when test="sort=='analyticsShareUrl'" >	analytics_share_url	</when>
				<when test="sort=='createId'" >	create_id	</when>
				<when test="sort=='createDate'" >	create_date	</when>
				<when test="sort=='lastUpdateId'" >	last_update_id	</when>
				<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
				<when test="sort=='deleteYn'" >	delete_yn	</when>
				<when test="sort=='deleteCode'" >	delete_code	</when>
				<when test="sort=='deleteId'" >	delete_id	</when>
				<when test="sort=='deleteDate'" >	delete_date	</when>
	            <otherwise>rownum</otherwise>
	        </choose>
	        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
	    	ORDER BY <foreach item="item" index="index" collection="listSort" separator=",">
	    	<choose>
				<when test="item.sort=='id'" >	a.id	</when>
				<when test="item.sort=='userEmail'" >	user_email	</when>
				<when test="item.sort=='upperId'" >	upper_id	</when>
				<when test="item.sort=='resultRecord'" >	result_record	</when>
				<when test="item.sort=='analyticsShareUrl'" >	analytics_share_url	</when>
				<when test="item.sort=='createId'" >	create_id	</when>
				<when test="item.sort=='createDate'" >	create_date	</when>
				<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
				<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
				<when test="item.sort=='deleteYn'" >	delete_yn	</when>
				<when test="item.sort=='deleteCode'" >	delete_code	</when>
				<when test="item.sort=='deleteId'" >	delete_id	</when>
				<when test="item.sort=='deleteDate'" >	delete_date	</when>
	        </choose>
	    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneSurveyResult" parameterType="HashMap" resultType="SurveyResult">
		SELECT a.id, a.user_email, a.upper_id, a.result_record, a.analytics_share_url, a.recommand_menu_ids,
		       a.create_id, a.create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.delete_id, a.delete_date,
		       JSON_LENGTH( result_record,'$.result' ) allcnt,
		       IFNULL( JSON_LENGTH(JSON_SEARCH(result_record, 'all', "yes", null, '$.result[*].answer')),0) yescnt
		  FROM survey_result a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperId)" >	and upper_id=#{upperId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(resultRecord)" >	and result_record=#{resultRecord}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(analyticsShareUrl)" >	and analytics_share_url=#{analyticsShareUrl}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteCode)" >	and delete_code=#{deleteCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isLast) and isLast">
		ORDER BY a.create_date desc limit 1
		</if>
	</select>

	<insert id="insertSurveyResult" parameterType="SurveyResult" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO survey_result
       	<set>
        	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	id=#{id},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperId)" >	upper_id=#{upperId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(resultRecord)" >	result_record=#{resultRecord},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandMenuIds)" >	recommand_menu_ids=#{recommandMenuIds},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(analyticsShareUrl)" >	analytics_share_url=#{analyticsShareUrl},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId}, last_update_id=#{createId},	</if>
			create_date=now(), last_update_date=now()
       	</set>
    </insert>

<!--################################### survey_image ###################################-->
	<select id="selectCountSurveyImage" parameterType="HashMap" resultType="Integer">
		SELECT count(*)
		  FROM survey_image
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(questionId)" >	and question_id=#{questionId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandId)" >	and recommand_id=#{recommandId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>

		</where>
	</select>

	<select id="selectListSurveyImage" parameterType="HashMap" resultType="SurveyImage">
	SELECT * FROM (
		SELECT @rownum:=@rownum+1 AS rownum,
		       file_id, question_id, recommand_id, service_type, upload_path, upload_filename, file_extension, file_size, file_mimetype, origin_filename, upload_id, upload_date, sort_order
		  FROM survey_image
		  join (SELECT @rownum:= 0) rnum
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(questionId)" >	and question_id=#{questionId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandId)" >	and recommand_id=#{recommandId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>
		ORDER BY sort_order asc) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneSurveyImage" parameterType="HashMap" resultType="SurveyImage">
		SELECT file_id, question_id, recommand_id, service_type, upload_path, upload_filename, file_extension, file_size, file_mimetype, origin_filename, upload_id, upload_date
		  FROM survey_image
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(questionId)" >	and question_id=#{questionId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandId)" >	and recommand_id=#{recommandId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>
	</select>
<!--################################### surveyRecommand ###################################-->
	<select id="selectCountSurveyRecommand" parameterType="HashMap" resultType="Integer">
		SELECT count(*)
		  FROM survey_recommand a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandTitle)" >	and recommand_title=#{recommandTitle}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandSmallName)" >	and recommand_small_name=#{recommandSmallName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandBigName)" >	and recommand_big_name=#{recommandBigName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandContents)" >	and recommand_contents=#{recommandContents}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteCode)" >	and delete_code=#{deleteCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>
	</select>

	<select id="selectListSurveyRecommand" parameterType="HashMap" resultType="SurveyRecommand">
		SELECT @rownum:=@rownum+1 AS rownum,
		       a.id, a.menu_id, a.recommand_title, a.recommand_small_name, a.recommand_big_name, a.recommand_contents,
		       a.create_id, a.create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.delete_id, a.delete_date,
		       b.menu_name
		  FROM survey_recommand a
		  left join menu_user b on a.menu_id = b.menu_id
		  join (SELECT @rownum:= 0) rnum
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandTitle)" >	and recommand_title=#{recommandTitle}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandSmallName)" >	and recommand_small_name=#{recommandSmallName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandBigName)" >	and recommand_big_name=#{recommandBigName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandContents)" >	and recommand_contents=#{recommandContents}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteCode)" >	and delete_code=#{deleteCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>

		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
	    	ORDER BY
	        <choose>
	            <when test="sort=='id'" >	a.id	</when>
				<when test="sort=='scheduleTitle'" >	a.schedule_title	</when>
				<when test="sort=='scheduleDate'" >	a.schedule_date	</when>
				<when test="sort=='scheduleStartDttm'" >	a.schedule_start_dttm	</when>
				<when test="sort=='scheduleEndDttm'" >	a.schedule_end_dttm	</when>
				<when test="sort=='scheduleComment'" >	a.schedule_comment	</when>
				<when test="sort=='createId'" >a.create_id	</when>
				<when test="sort=='createDate'" >	a.create_date	</when>
				<when test="sort=='lastUpdateId'" >	a.last_update_id	</when>
				<when test="sort=='lastUpdateDate'" >	a.last_update_date	</when>
				<when test="sort=='deleteYn'" >	a.delete_yn	</when>
				<when test="sort=='deleteId'" >	a.delete_id	</when>
				<when test="sort=='deleteDate'" >	a.delete_date	</when>
	            <otherwise>rownum</otherwise>
	        </choose>
	        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
	    	ORDER BY <foreach item="item" index="index" collection="listSort" separator=",">
	    	<choose>
				<when test="item.sort=='id'" >	a.id	</when>
				<when test="item.sort=='scheduleTitle'" >	a.schedule_title	</when>
				<when test="item.sort=='scheduleDate'" >	a.schedule_date	</when>
				<when test="item.sort=='scheduleStartDttm'" >	a.schedule_start_dttm	</when>
				<when test="item.sort=='scheduleEndDttm'" >	a.schedule_end_dttm	</when>
				<when test="item.sort=='scheduleComment'" >	a.schedule_comment	</when>
				<when test="item.sort=='createId'" >a.create_id	</when>
				<when test="item.sort=='createDate'" >	a.create_date	</when>
				<when test="item.sort=='lastUpdateId'" >	a.last_update_id	</when>
				<when test="item.sort=='lastUpdateDate'" >	a.last_update_date	</when>
				<when test="item.sort=='deleteYn'" >	a.delete_yn	</when>
				<when test="item.sort=='deleteId'" >	a.delete_id	</when>
				<when test="item.sort=='deleteDate'" >	a.delete_date	</when>
	        </choose>
	    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneSurveyRecommand" parameterType="HashMap" resultType="SurveyRecommand">
		SELECT a.id, a.menu_id, a.recommand_title, a.recommand_small_name, a.recommand_big_name, a.recommand_mid_name, a.recommand_big_name_color, a.recommand_contents, a.recommand_contents_bg_color,
		       a.create_id, a.create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.delete_id, a.delete_date,
		       b.menu_name
		  FROM survey_recommand a
		  left join menu_user b on a.menu_id = b.menu_id
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and a.menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandTitle)" >	and recommand_title=#{recommandTitle}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandSmallName)" >	and recommand_small_name=#{recommandSmallName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandBigName)" >	and recommand_big_name=#{recommandBigName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandMidName)" >	and recommand_mid_name=#{recommandMidName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(recommandContents)" >	and recommand_contents=#{recommandContents}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and a.create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and a.create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and a.last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and a.last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteCode)" >	and a.delete_code=#{deleteCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and a.delete_date=#{deleteDate}	</if>
		</where>

		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isLastData) and isLastData" >
		 order by create_date  desc
 		 limit 1
		</if>
	</select>
</mapper>