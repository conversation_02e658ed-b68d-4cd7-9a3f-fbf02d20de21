package kr.co.wayplus.travel.util;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import kr.co.wayplus.travel.mapper.front.ReservationMapper;
import kr.co.wayplus.travel.mapper.manage.ReservationManageMapper;
import kr.co.wayplus.travel.model.Reservation;

@Component
public class ReservationUtil {

    @Autowired
    private final ReservationManageMapper reservationManageMapper;
    private final ReservationMapper reservationMapper;

    public ReservationUtil(ReservationManageMapper reservationManageMapper, ReservationMapper reservationMapper) {
		this.reservationManageMapper = reservationManageMapper;
		this.reservationMapper = reservationMapper;
	}

   public ArrayList<Reservation> selectListCalcReservation(HashMap<String, Object> rsvParam) throws SQLException {
      ArrayList<Reservation> reservation = reservationManageMapper.selectListCalcReservation(rsvParam);

      return reservation;
   }

   public ArrayList<Reservation> selectListCalcReservationForUser(HashMap<String, Object> rsvParam) throws SQLException {
      ArrayList<Reservation> reservation = reservationMapper.selectListCalcReservationForUser(rsvParam);

      return reservation;
  }
}
