<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.manage.BoardManageMapper">
<!--################################### boardSetting ###################################-->
	<select id="selectCountBoardSetting" parameterType="HashMap" resultType="Integer">
		SELECT count(b.id)
		  FROM board_setting b
		  <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentsExis)">
		  join (select board_id, count(*) bcnt from board_contents where create_id= #{contentsExis} group by board_id) bc on b.id = bc.board_id
		  </if>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentsExis)"> and bc.bcnt > 1 and bc.bcnt is not null</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType) and
					  @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey) ">
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType=='userEmail'" >and b.title LIKE CONCAT('%', #{searchKey}, '%')</if>
			</if>

       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(titleLike)">and b.title like concat('%',#{titleLike},'%')</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(notIds)">
				AND b.id not IN<foreach collection="notIds" item="item" index="index" open="(" separator="," close=")">#{item}</foreach>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and b.id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(url)" >	and b.url=#{url}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(typeCode)" >	and b.type_code=#{typeCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and b.title=#{title}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subtitle)" >	and b.subtitle=#{subtitle}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(thumbnailYn)" >	and b.thumbnail_yn=#{thumbnailYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(bannerLargeYn)" >	and b.banner_large_yn=#{bannerLargeYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(bannerSmallYn)" >	and b.banner_small_yn=#{bannerSmallYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryYn)" >	and b.category_yn=#{categoryYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCodeYn)" >	and b.apply_code_yn=#{applyCodeYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	and b.secret_yn=#{secretYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestYn)" >	and b.guest_yn=#{guestYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subContentYn)" >	and b.sub_content_yn=#{subContentYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentYn)" >	and b.comment_yn=#{commentYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tagYn)" >	and b.tag_yn=#{tagYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(favoriteYn)" >	and b.favorite_yn=#{favoriteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(scrapYn)" >	and b.scrap_yn=#{scrapYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(seriesYn)" >	and b.series_yn=#{seriesYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(defaultPageSize)" >	and b.default_page_size=#{defaultPageSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentStoreDays)" >	and b.content_store_days=#{contentStoreDays}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and b.use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fixYn)" >	and b.fix_yn=#{fixYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(attachFileSize)" >	and b.attach_file_size=#{attachFileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	and b.start_date=#{startDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	and b.expire_date=#{expireDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and b.create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and b.create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and b.last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and b.last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and b.delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and b.delete_date=#{deleteDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and b.delete_yn=#{deleteYn}	</if>
		</where>
	</select>

	<select id="selectListBoardSetting" parameterType="HashMap" resultType="BoardSetting">
		select *
		  from (
			SELECT @rownum:=@rownum+1 AS rownum
				, b.id
				, b.url
				, b.type_code
				, b.title
				, b.subtitle
				, b.thumbnail_yn
				, b.banner_large_yn
				, b.banner_small_yn
				, b.category_yn
				, b.apply_code_yn
				, b.secret_yn
				, b.guest_yn
				, b.sub_content_yn
				, b.comment_yn
				, b.tag_yn
				, b.favorite_yn
				, b.scrap_yn
				, b.series_yn
				, b.default_page_size
				, b.content_store_days
				, b.use_yn
				, b.fix_yn
				, b.start_date
				, b.expire_date
				, b.create_id
				, b.create_date
				, b.last_update_id
				, b.last_update_date
				, b.delete_yn
				, b.delete_id
				, b.delete_date
				, b.attach_file_size
			  FROM board_setting b
			  <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentsExis)">
			  join (select board_id, count(*) bcnt from board_contents where create_id= #{contentsExis} group by board_id) bc on b.id = bc.board_id
			  </if>
			  join (SELECT @rownum:= 0) rnum
			<where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentsExis)"> and bc.bcnt > 1 and bc.bcnt is not null</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType) and
						  @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey) ">
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType=='userEmail'" >and b.title LIKE CONCAT('%', #{searchKey}, '%')</if>
				</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(titleLike)">and title like concat('%',#{titleLike},'%')</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(notIds)">
					AND b.id not IN<foreach collection="notIds" item="item" index="index" open="(" separator="," close=")">#{item}</foreach>
				</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and b.id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(url)" >	and b.url=#{url}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(typeCode)" >	and b.type_code=#{typeCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and b.title=#{title}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subtitle)" >	and b.subtitle=#{subtitle}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(thumbnailYn)" >	and b.thumbnail_yn=#{thumbnailYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(bannerLargeYn)" >	and b.banner_large_yn=#{bannerLargeYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(bannerSmallYn)" >	and b.banner_small_yn=#{bannerSmallYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryYn)" >	and b.category_yn=#{categoryYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCodeYn)" >	and b.apply_code_yn=#{applyCodeYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	and b.secret_yn=#{secretYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestYn)" >	and b.guest_yn=#{guestYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subContentYn)" >	and b.sub_content_yn=#{subContentYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentYn)" >	and b.comment_yn=#{commentYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tagYn)" >	and b.tag_yn=#{tagYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(favoriteYn)" >	and b.favorite_yn=#{favoriteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(scrapYn)" >	and b.scrap_yn=#{scrapYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(seriesYn)" >	and b.series_yn=#{seriesYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(defaultPageSize)" >	and b.default_page_size=#{defaultPageSize}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentStoreDays)" >	and b.content_store_days=#{contentStoreDays}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and b.use_yn=#{useYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fixYn)" >	and b.fix_yn=#{fixYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(attachFileSize)" >	and b.attach_file_size=#{attachFileSize}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	and b.start_date=#{startDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	and b.expire_date=#{expireDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and b.create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and b.create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and b.last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and b.last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and b.delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and b.delete_date=#{deleteDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and b.delete_yn=#{deleteYn}	</if>

			</where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort=='id'" >	b.id	</when>
					<when test="sort=='url'" >	b.url	</when>
					<when test="sort=='typeCode'" >	b.type_code	</when>
					<when test="sort=='title'" >	b.title	</when>
					<when test="sort=='subtitle'" >	b.subtitle	</when>
					<when test="sort=='thumbnailYn'" >	b.thumbnail_yn	</when>
					<when test="sort=='bannerLargeYn'" >	b.banner_large_yn	</when>
					<when test="sort=='bannerSmallYn'" >	b.banner_small_yn	</when>
					<when test="sort=='categoryYn'" >	b.category_yn	</when>
					<when test="sort=='applyCodeYn'" >	b.apply_code_yn	</when>
					<when test="sort=='secretYn'" >	b.secret_yn	</when>
					<when test="sort=='guestYn'" >	b.guest_yn	</when>
					<when test="sort=='commentYn'" >	b.comment_yn	</when>
					<when test="sort=='tagYn'" >	b.tag_yn	</when>
					<when test="sort=='favoriteYn'" >	b.favorite_yn	</when>
					<when test="sort=='scrapYn'" >	b.scrap_yn	</when>
					<when test="sort=='seriesYn'" >	b.series_yn	</when>
					<when test="sort=='defaultPageSize'" >	b.default_page_size	</when>
					<when test="sort=='contentStoreDays'" >	b.content_store_days	</when>
					<when test="sort=='useYn'" >	b.use_yn	</when>
					<when test="sort=='fixYn'" >	b.fix_yn	</when>
					<when test="sort=='attachFileSize'" >	b.attach_file_size	</when>
					<when test="sort=='startDate'" >	b.start_date	</when>
					<when test="sort=='expireDate'" >	b.expire_date	</when>
					<when test="sort=='createId'" >	b.create_id	</when>
					<when test="sort=='createDate'" >	b.create_date	</when>
					<when test="sort=='lastUpdateId'" >	b.last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	b.last_update_date	</when>
					<when test="sort=='deleteYn'" >	b.delete_yn	</when>
					<when test="sort=='deleteId'" >	b.delete_id	</when>
					<when test="sort=='deleteDate'" >	b.delete_date	</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort=='id'" >	b.id	</when>
					<when test="item.sort=='url'" >	b.url	</when>
					<when test="item.sort=='typeCode'" >	b.type_code	</when>
					<when test="item.sort=='title'" >	b.title	</when>
					<when test="item.sort=='subtitle'" >	b.subtitle	</when>
					<when test="item.sort=='thumbnailYn'" >	b.thumbnail_yn	</when>
					<when test="item.sort=='bannerLargeYn'" >	b.banner_large_yn	</when>
					<when test="item.sort=='bannerSmallYn'" >	b.banner_small_yn	</when>
					<when test="item.sort=='categoryYn'" >	b.category_yn	</when>
					<when test="item.sort=='applyCodeYn'" >	b.apply_code_yn	</when>
					<when test="item.sort=='secretYn'" >	b.secret_yn	</when>
					<when test="item.sort=='guestYn'" >	b.guest_yn	</when>
					<when test="item.sort=='commentYn'" >	b.comment_yn	</when>
					<when test="item.sort=='tagYn'" >	b.tag_yn	</when>
					<when test="item.sort=='favoriteYn'" >	b.favorite_yn	</when>
					<when test="item.sort=='scrapYn'" >	b.scrap_yn	</when>
					<when test="item.sort=='seriesYn'" >	b.series_yn	</when>
					<when test="item.sort=='defaultPageSize'" >	b.default_page_size	</when>
					<when test="item.sort=='contentStoreDays'" >	b.content_store_days	</when>
					<when test="item.sort=='useYn'" >	b.use_yn	</when>
					<when test="item.sort=='fixYn'" >	b.fix_yn	</when>
					<when test="item.sort=='attachFileSize'" >	b.attach_file_size	</when>
					<when test="item.sort=='startDate'" >	b.start_date	</when>
					<when test="item.sort=='expireDate'" >	b.expire_date	</when>
					<when test="item.sort=='createId'" >	b.create_id	</when>
					<when test="item.sort=='createDate'" >	b.create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	b.last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	b.last_update_date	</when>
					<when test="item.sort=='deleteYn'" >	b.delete_yn	</when>
					<when test="item.sort=='deleteId'" >	b.delete_id	</when>
					<when test="item.sort=='deleteDate'" >	b.delete_date	</when>
		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if> ) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneBoardSetting" parameterType="HashMap" resultType="BoardSetting">
		SELECT b.id
			, b.url
			, b.type_code
			, b.title
			, b.subtitle
			, b.thumbnail_yn
			, b.banner_large_yn
			, b.banner_small_yn
			, b.category_yn
			, b.apply_code_yn
			, b.secret_yn
			, b.guest_yn
			, b.sub_content_yn
			, b.comment_yn
			, b.tag_yn
			, b.favorite_yn
			, b.scrap_yn
			, b.series_yn
			, b.default_page_size
			, b.content_store_days
			, b.use_yn
			, b.fix_yn
			, b.start_date
			, b.expire_date

			, b.create_id
			, b.create_date
			, b.last_update_id
			, b.last_update_date
			, b.delete_yn
			, b.delete_id
			, b.delete_date
			, b.attach_file_size
		  FROM board_setting b
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and b.id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(url)" >	and b.url=#{url}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(typeCode)" >	and b.type_code=#{typeCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and b.title=#{title}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subtitle)" >	and b.subtitle=#{subtitle}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(thumbnailYn)" >	and b.thumbnail_yn=#{thumbnailYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(bannerLargeYn)" >	and b.banner_large_yn=#{bannerLargeYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(bannerSmallYn)" >	and b.banner_small_yn=#{bannerSmallYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryYn)" >	and b.category_yn=#{categoryYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCodeYn)" >	and b.apply_code_yn=#{applyCodeYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	and b.secret_yn=#{secretYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestYn)" >	and b.guest_yn=#{guestYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subContentYn)" >	and b.sub_content_yn=#{subContentYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentYn)" >	and b.comment_yn=#{commentYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tagYn)" >	and b.tag_yn=#{tagYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(favoriteYn)" >	and b.favorite_yn=#{favoriteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(scrapYn)" >	and b.scrap_yn=#{scrapYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(seriesYn)" >	and b.series_yn=#{seriesYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(defaultPageSize)" >	and b.default_page_size=#{defaultPageSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentStoreDays)" >	and b.content_store_days=#{contentStoreDays}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and b.use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fixYn)" >	and b.fix_yn=#{fixYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(attachFileSize)" >	and b.attach_file_size=#{attachFileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	and b.start_date=#{startDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	and b.expire_date=#{expireDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and b.create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and b.create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and b.last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and b.last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and b.delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and b.delete_date=#{deleteDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and b.delete_yn=#{deleteYn}	</if>
		</where>
	</select>

	<insert id="insertBoardSetting" parameterType="BoardSetting" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO board_setting
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(url)" >	url=#{url},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(typeCode)" >	type_code=#{typeCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	title=#{title},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subtitle)" >	subtitle=#{subtitle},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(thumbnailYn)" >	thumbnail_yn=#{thumbnailYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(bannerLargeYn)" >	banner_large_yn=#{bannerLargeYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(bannerSmallYn)" >	banner_small_yn=#{bannerSmallYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryYn)" >	category_yn=#{categoryYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCodeYn)" >	apply_code_yn=#{applyCodeYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	secret_yn=#{secretYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestYn)" >	guest_yn=#{guestYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subContentYn)" >	sub_content_yn=#{subContentYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentYn)" >	comment_yn=#{commentYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tagYn)" >	tag_yn=#{tagYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(favoriteYn)" >	favorite_yn=#{favoriteYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(scrapYn)" >	scrap_yn=#{scrapYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(seriesYn)" >	series_yn=#{seriesYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(defaultPageSize)" >	default_page_size=#{defaultPageSize},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentStoreDays)" >	content_store_days=#{contentStoreDays},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fixYn)" >	fix_yn=#{fixYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(attachFileSize)" >	attach_file_size=#{attachFileSize},	</if>
			<choose>
				<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">start_date=#{startDate},</when>
				<otherwise>start_date=null,</otherwise>
			</choose>
			<choose>
				<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)">expire_date=#{expireDate},</when>
				<otherwise>expire_date=null,</otherwise>
			</choose>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	delete_yn=#{deleteYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},last_update_id=#{createId},	</if>
			create_date=now(),last_update_date=now()
        </set>
    </insert>

	<update id="updateBoardSetting" parameterType="BoardSetting">
        Update board_setting
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(url)" >	url=#{url},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(typeCode)" >	type_code=#{typeCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	title=#{title},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subtitle)" >	subtitle=#{subtitle},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(thumbnailYn)" >	thumbnail_yn=#{thumbnailYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(bannerLargeYn)" >	banner_large_yn=#{bannerLargeYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(bannerSmallYn)" >	banner_small_yn=#{bannerSmallYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryYn)" >	category_yn=#{categoryYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCodeYn)" >	apply_code_yn=#{applyCodeYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	secret_yn=#{secretYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestYn)" >	guest_yn=#{guestYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subContentYn)" >	sub_content_yn=#{subContentYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentYn)" >	comment_yn=#{commentYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tagYn)" >	tag_yn=#{tagYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(favoriteYn)" >	favorite_yn=#{favoriteYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(scrapYn)" >	scrap_yn=#{scrapYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(seriesYn)" >	series_yn=#{seriesYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(defaultPageSize)" >	default_page_size=#{defaultPageSize},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentStoreDays)" >	content_store_days=#{contentStoreDays},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fixYn)" >	fix_yn=#{fixYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(attachFileSize)" >	attach_file_size=#{attachFileSize},	</if>
			<choose>
				<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">start_date=#{startDate},</when>
				<otherwise>start_date=null,</otherwise>
			</choose>
			<choose>
				<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)">expire_date=#{expireDate},</when>
				<otherwise>expire_date=null,</otherwise>
			</choose>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	delete_yn=#{deleteYn},	</if>
			last_update_date=now()
        </set>
        <where>
        	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
        </where>
    </update>

    <update id="restoreBoardSetting" parameterType="BoardSetting">
        UPDATE board_setting
           SET delete_yn='N'
        WHERE id = #{id}
    </update>

    <update id="deleteBoardSetting" parameterType="BoardSetting">
        Update board_setting
        <set>
			delete_yn='Y',
			delete_id=#{id},
			delete_date=now()
        </set>
        <where>
        	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
        </where>
    </update>

<!--################################### boardContents ###################################-->
	<select id="selectOneBoardContents" parameterType="HashMap" resultType="BoardContents">
        SELECT bc.id, bc.board_id, bc.upper_board_id,
               category_id, (SELECT title FROM board_category ct WHERE ct.board_id = bc.id AND ct.id = bc.category_id) category_name,
               board_link, title, content, apply_code,
               series_id, tags, thumbnail_url,
               favorite_count, scrap_count, comment_count,
               view_count, attachment_count, bc.use_yn, cancel_yn, fix_yn, secret_yn,
               bc.start_date, bc.expire_date,
			   guest_name, guest_pass,
               bc.delete_yn, bc.delete_id, bc.delete_date,
               bc.create_id, bc.create_date, bc.last_update_id, bc.last_update_date, u.user_name,
               mission_type, mission_team, mission_area, mission_check_yn, mission_complete_count, user_point_set_id, 
               case when apply_code = 0 then '접수' when apply_code = 1 then '접수확인' when apply_code = 2 then '처리중' when apply_code = 3 then '답변완료' end apply_code_name
               , user_day, people_num , destination , phone_num
				<choose>
					<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isUpperBoardId) and !isUpperBoardId" >
					, user_point_set_id 
					, CASE WHEN bc2.badge_name IS NULL THEN 0 ELSE user_badge_set_id end as user_badge_set_id
					, ups.accrued_reason, ups.accrued_point, bc2.badge_name, bc2.badge_file_url
					</when>
					<otherwise>
						, user_badge_set_id
					</otherwise>
				</choose>
          FROM board_contents bc
          left join `user` u on bc.create_id = u.user_email
          join (SELECT @rownum:= 0) rnum
          <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isUpperBoardId) and !isUpperBoardId" >
          left join user_point_set ups on bc.user_point_set_id =ups.id
		  left join (
			  select bc.badge_id, bc.badge_name, bai.upload_filename badge_file_url
			    from badge_contents bc
			    join badge_attach_image bai on bc.badge_image_file_id  = bai.file_id
			    WHERE bc.badge_type = 'normal') bc2 on bc.user_badge_set_id =bc2.badge_id
          </if>
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and bc.id = #{id}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)">and bc.category_id = #{categoryId}</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">and bc.board_id = #{boardId}</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperBoardId)">and bc.upper_board_id = #{upperBoardId}</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(titleLike)">and title like concat('%',#{titleLike},'%')</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentLike)">and content like concat('%',#{contentLike},'%')</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionCheckYn)" > and mission_check_yn=#{missionCheckYn}	</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isUpperBoardId)" >
	         	<if test="!isUpperBoardId" >and bc.upper_board_id is null</if>
	         	<if test="isUpperBoardId" >and bc.upper_board_id is not null</if>
         	</if>
         </where>
	</select>

	<select id="selectListBoardContents" parameterType="HashMap" resultType="BoardContents">
		SELECT *
		  FROM(
	        SELECT @rownum:=@rownum+1 AS rownum, bc.id,
	               board_id,
	               (SELECT title FROM board_setting bs WHERE bs.id = bc.board_id) board_name,
	               category_id,
	               (SELECT title FROM board_category ct WHERE ct.board_id = bc.id AND ct.id = bc.category_id) category_name,
	               board_link,
	               ifnull(title, '') title,
	               content, ifnull(apply_code,'') apply_code,
	               series_id, tags, thumbnail_url,
	               favorite_count, scrap_count, comment_count,
	               view_count, attachment_count, bc.use_yn, cancel_yn, fix_yn, secret_yn,
	               bc.start_date, bc.expire_date,
			       bc.delete_yn, bc.delete_id, bc.delete_date,
			       ifnull(bc.create_id,'') create_id, bc.create_date, bc.last_update_id, bc.last_update_date,
	               ifnull(ifnull(u.user_name, bc.guest_name),'이름없음') user_name,
                   case when apply_code = 0 then '접수' when apply_code = 1 then '접수확인' when apply_code = 2 then '처리중' when apply_code = 3 then '답변완료' end apply_code_name,
                   mission_type, mission_team, mission_area, mission_check_yn, mission_complete_count, user_point_set_id, 
				   <choose>
						<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isUpperBoardId) and !isUpperBoardId">
							CASE WHEN bc2.badge_name IS NULL THEN 0 ELSE user_badge_set_id end as user_badge_set_id
						</when>
						<otherwise>
							user_badge_set_id
						</otherwise>
				   </choose>
                   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isUpperBoardId) and !isUpperBoardId" >
                   , ups.accrued_reason, ups.accrued_point, bc2.badge_name, bc2.badge_file_url
                   </if>
	          FROM board_contents bc
	          left join `user` u on bc.create_id = u.user_email
	          join (SELECT @rownum:= 0) rnum
	          <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isUpperBoardId) and !isUpperBoardId" >
	          left join user_point_set ups on bc.user_point_set_id =ups.id
			  left join (
				  select bc.badge_id, bc.badge_name, bai.upload_filename badge_file_url
				    from badge_contents bc
				    join badge_attach_image bai on bc.badge_image_file_id  = bai.file_id
					WHERE bc.badge_type = 'normal') bc2 on bc.user_badge_set_id =bc2.badge_id
	          </if>
	         <where>
	         	and bc.delete_yn = 'N'
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and bc.id = #{id}</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)">and bc.category_id = #{categoryId}</if>
         		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">and bc.board_id = #{boardId}</if>
         		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperBoardId)">and bc.upper_board_id = #{upperBoardId}</if>
         		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(titleLike)">and bc.title like concat('%',#{titleLike},'%')</if>
         		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentLike)">and bc.content like concat('%',#{contentLike},'%')</if>
         		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionCheckYn)" > and mission_check_yn=#{missionCheckYn}	</if>
         		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateFrom) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateTo)">
					and bc.create_date between DATE_FORMAT(#{dateFrom},'%Y-%m-%d') and DATE_FORMAT(#{dateTo},'%Y-%m-%d')
				</if>
         		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isUpperBoardId)" >
		         	<if test="!isUpperBoardId" >and bc.upper_board_id is null</if>
		         	<if test="isUpperBoardId" >and bc.upper_board_id is not null</if>
	         	</if>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >and bc.create_id=#{createId}	</if>

	         </where>

	         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort == 'id'">bc.id</when>
		            <when test="sort == 'title'">bc.title</when>
		            <when test="sort == 'viewCount'">bc.view_count</when>
		            <when test="sort == 'useYn'">bc.use_yn</when>
		            <when test="sort == 'deleteYn'">bc.delete_yn</when>
		            <when test="sort == 'createDate'">bc.create_date</when>
		            <when test="sort == 'lastUpdateDate'">bc.last_update_date</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort == 'id'">bc.id</when>
		            <when test="item.sort == 'title'">bc.title</when>
		            <when test="item.sort == 'viewCount'">bc.view_count</when>
		            <when test="item.sort == 'useYn'">bc.use_yn</when>
		            <when test="item.sort == 'deleteYn'">bc.delete_yn</when>
		            <when test="item.sort == 'lastUpdateDate'">bc.last_update_date</when>
		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
		ORDER BY rownum
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	 <select id="selectCountBoardContents" parameterType="HashMap" resultType="Integer">
        SELECT count(id) 
          FROM board_contents
		<where>
			and delete_yn = 'N'
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)">and category_id = #{categoryId}</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">and board_id = #{boardId}</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperBoardId)">and upper_board_id = #{upperBoardId}</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(titleLike)">and title like concat('%',#{titleLike},'%')</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentLike)">and content like concat('%',#{contentLike},'%')</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentLike)">and content like concat('%',#{contentLike},'%')</if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionCheckYn)" > and mission_check_yn=#{missionCheckYn}	</if>
         	<if test="isComment">and comment_count = 0 </if>
       		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardIdLike)">and board_id in (select id from board_setting where url like concat('%',#{boardIdLike},'%') )</if>
         	<if test="isOnlyReview">and id not in (select user_email from user where user_role = 'admin') and create_date BETWEEN  now() - interval 2 day and now()</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >and create_id=#{createId}	</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateFrom) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateTo)">
				and create_date between DATE_FORMAT(#{dateFrom},'%Y-%m-%d') and DATE_FORMAT(#{dateTo},'%Y-%m-%d')
			</if>

         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isUpperBoardId)" >
	         	<if test="!isUpperBoardId" >and upper_board_id is null</if>
	         	<if test="isUpperBoardId" >and upper_board_id is not null</if>
         	</if>
         </where>
    </select>

	<insert id="insertBoardContents" parameterType="BoardContents" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO board_contents
        <set>
        	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId) and boardId neq 0" >	board_id=#{boardId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId) and categoryId neq 0" >	category_id=#{categoryId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(seriesId) and seriesId neq 0" >	series_id=#{seriesId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardLink)" >	board_link=#{boardLink},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	title=#{title},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	content=#{content},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	apply_code=#{applyCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tags)" >	tags=#{tags},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(thumbnailUrl)" >	thumbnail_url=#{thumbnailUrl},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(favoriteCount)" >	favorite_count=#{favoriteCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(scrapCount)" >	scrap_count=#{scrapCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentCount)" >	comment_count=#{commentCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(viewCount)" >	view_count=#{viewCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(attachmentCount)" >	attachment_count=#{attachmentCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fixYn)" >	fix_yn=#{fixYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	secret_yn=#{secretYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestName)" >	guest_name=#{guestName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestPass)" >	guest_pass=#{guestPass},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	start_date=#{startDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	expire_date=#{expireDate},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionType)" >	mission_type=#{missionType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionTeam)" >	mission_team=#{missionTeam},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionArea)" >	guest_pass=#{missionArea},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionCheckYn)" >	mission_check_yn=#{missionCheckYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionCompleteCount)" >	mission_complete_count=#{missionCompleteCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPointSetId)" >	user_point_set_id=#{userPointSetId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userBadgeSetId)" >	user_badge_set_id=#{userBadgeSetId},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			create_date = now()
        </set>
    </insert>

    <update id="updateBoardContents" parameterType="BoardContents">
        UPDATE board_contents
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId) and boardId neq 0" >	board_id=#{boardId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId) and categoryId neq 0" >	category_id=#{categoryId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(seriesId) and seriesId neq 0" >	series_id=#{seriesId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardLink)" >	board_link=#{boardLink},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	title=#{title},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	content=#{content},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	apply_code=#{applyCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(seriesId)" >	series_id=#{seriesId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tags)" >	tags=#{tags},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(thumbnailUrl)" >	thumbnail_url=#{thumbnailUrl},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(favoriteCount) and favoriteCount neq 0" >	favorite_count=#{favoriteCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(scrapCount)" >	scrap_count=#{scrapCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentCount)" >	comment_count=#{commentCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(viewCount)" >	view_count=#{viewCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(attachmentCount)" >	attachment_count=#{attachmentCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fixYn)" >	fix_yn=#{fixYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	cancel_yn=#{cancelYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	secret_yn=#{secretYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestName)" >	guest_name=#{guestName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestPass)" >	guest_pass=#{guestPass},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	start_date=#{startDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	expire_date=#{expireDate},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionType)" >	mission_type=#{missionType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionTeam)" >	mission_team=#{missionTeam},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionArea)" >	guest_pass=#{missionArea},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionCheckYn)" >	mission_check_yn=#{missionCheckYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionCompleteCount)" >	mission_complete_count=#{missionCompleteCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPointSetId)" >	user_point_set_id=#{userPointSetId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userBadgeSetId)" >	user_badge_set_id=#{userBadgeSetId},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	delete_yn=#{deleteYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	delete_id=#{deleteId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	delete_date=#{deleteDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
		</where>
    </update>

    <update id="updateBoardContentsByMissionCheck" parameterType="BoardContents">
        UPDATE board_contents
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(missionCheckYn)" >	mission_check_yn=#{missionCheckYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(thumbnailUrl)" >	thumbnail_url=#{thumbnailUrl},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
		</where>
    </update>

    <update id="restoreBoardContents" parameterType="BoardContents">
        UPDATE board_contents
           SET delete_yn='N'
        WHERE id = #{id}
    </update>

    <update id="deleteBoardContents" parameterType="BoardContents">
        UPDATE board_contents
           SET delete_yn='Y'
        WHERE id = #{id}
    </update>

    <update id="updateBoardContentDelete" parameterType="BoardContents">
        UPDATE board_contents
           <set>
               view_count = view_count + 1
           </set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
		</where>
    </update>

    <update id="updateBoardContent_addViewCount" parameterType="HashMap">
        UPDATE board_contents
           SET comment_count = 1
        WHERE id = #{content_id} AND delete_yn='N'
    </update>

    <update id="updateBoardContent_CommentCount" parameterType="HashMap">
        UPDATE board_contents
        SET comment_count = 1
        WHERE id = #{content_id} AND delete_yn='N'
    </update>

	<update id="updateBoardContentsStatus" parameterType="BoardComment">
		UPDATE board_contents
		SET apply_code = #{applyCode}
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)">,secret_yn = #{secretYn}</if>
		,last_update_date = now()
		WHERE id = #{contentId} AND board_id = #{boardId}
	</update>
<!--################################### boardCategory ###################################-->
	<select id="selectCountBoardCategory" parameterType="HashMap" resultType="Integer">
        SELECT count(id)
          FROM board_category
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	and board_id=#{boardId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and title=#{title}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(note)" >	and note=#{note}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderNum)" >	and order_num=#{orderNum}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(defaultYn)" >	and default_yn=#{defaultYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchYn)" >	and search_yn=#{searchYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	and start_date=#{startDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	and expire_date=#{expireDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and a.delete_date=#{deleteDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
         </where>
    </select>

    <select id="selectListBoardCategory" parameterType="HashMap" resultType="BoardCategory">
		SELECT *
		  FROM(
	        SELECT @rownum:=@rownum+1 AS rownum
		        , a.id
				, a.board_id
				, a.title
				, a.note
				, a.order_num
				, a.use_yn
				, a.default_yn
				, a.search_yn
				, a.point_use_yn
				, a.start_date
				, a.expire_date
				, a.create_id
				, a.create_date
				, a.last_update_id
				, a.last_update_date
				, a.delete_yn
				, a.delete_id
				, a.delete_date
				, a.user_point_set_id
				, ifnull(b.category_count, 0) category_count
	          FROM board_category a
	          left join (select category_id, count(category_id) category_count from board_contents bc group by category_id) b on a.id = b.category_id
	          join (SELECT @rownum:= 0) rnum
	         <where>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	and board_id=#{boardId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and title=#{title}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(note)" >	and note=#{note}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderNum)" >	and order_num=#{orderNum}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(defaultYn)" >	and default_yn=#{defaultYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchYn)" >	and search_yn=#{searchYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	and start_date=#{startDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	and expire_date=#{expireDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and a.delete_date=#{deleteDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
	         </where>

	         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort == 'id'">id</when>
		            <when test="sort == 'title'">title</when>
		            <when test="sort == 'viewCount'">view_count</when>
		            <when test="sort == 'useYn'">use_yn</when>
		            <when test="sort == 'searchYn'">search_yn</when>
		            <when test="sort == 'deleteYn'">delete_yn</when>
		            <when test="sort == 'lastUpdateDate'">last_update_date</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort == 'id'">id</when>
		            <when test="item.sort == 'title'">title</when>
		            <when test="item.sort == 'viewCount'">view_count</when>
		            <when test="item.sort == 'useYn'">use_yn</when>
		            <when test="item.sort == 'searchYn'">search_yn</when>
		            <when test="item.sort == 'deleteYn'">delete_yn</when>
		            <when test="item.sort == 'lastUpdateDate'">last_update_date</when>
		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

    <select id="selectOneBoardCategory" parameterType="HashMap" resultType="BoardCategory">
        SELECT @rownum:=@rownum+1 AS rownum
        	, a.id
			, a.board_id
			, a.title
			, a.note
			, a.order_num
			, a.use_yn
			, a.default_yn
			, a.search_yn
			, a.start_date
			, a.expire_date
			, a.create_id
			, a.create_date
			, a.last_update_id
			, a.last_update_date
			, a.delete_yn
			, a.delete_id
			, a.delete_date
			, a.user_point_set_id
          FROM board_category a
          join (SELECT @rownum:= 0) rnum
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	and board_id=#{boardId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and title=#{title}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(note)" >	and note=#{note}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderNum)" >	and order_num=#{orderNum}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(defaultYn)" >	and default_yn=#{defaultYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchYn)" >	and search_yn=#{searchYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	and start_date=#{startDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	and expire_date=#{expireDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and a.delete_date=#{deleteDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
         </where>
	</select>

	<insert id="insertBoardCategory" parameterType="BoardCategory" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO board_category
        <set>
        	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" > id=#{id}, </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" > board_id=#{boardId}, </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" > title=#{title}, </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(note)" > note=#{note}, </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderNum)" > order_num=#{orderNum}, </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" > use_yn=#{useYn}, </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(defaultYn)" >	default_yn=#{defaultYn},</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchYn)" > search_yn=#{searchYn}, </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pointUseYn)" > point_use_yn=#{pointUseYn}, </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPointSetId)" > user_point_set_id=#{userPointSetId}, </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" > start_date=#{startDate}, </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" > expire_date=#{expireDate}, </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" > create_id=#{createId}, last_update_id=#{createId}, </if>
			create_date = now(), last_update_date = now()
        </set>
    </insert>

    <update id="updateBoardCategory" parameterType="BoardCategory">
        UPDATE board_category
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	id=#{id},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	title=#{title},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(note)" >	note=#{note},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderNum)" >	order_num=#{orderNum},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(defaultYn)" >	default_yn=#{defaultYn},</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchYn)" > search_yn=#{searchYn}, </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pointUseYn)" > point_use_yn=#{pointUseYn}, </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPointSetId)" > user_point_set_id=#{userPointSetId}, </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	start_date=#{startDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	expire_date=#{expireDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >and board_id=#{boardId}	</if>
		</where>
    </update>

    <update id="restoreBoardCategory" parameterType="BoardCategory">
        UPDATE board_category
           SET delete_yn='N'
        WHERE id = #{id}
    </update>

    <update id="deleteBoardCategory" parameterType="BoardCategory">
        UPDATE board_category
           SET delete_yn='Y'
        WHERE id = #{id}
    </update>
    <delete id="deleteBoardCategoryReal" parameterType="BoardCategory">
        DELETE FROM board_category WHERE id = #{id}
    </delete>
<!--################################### boardComment ###################################-->
    <select id="selectOneBoardComment" parameterType="HashMap" resultType="BoardComment">
        SELECT id, board_id, content_id,
               tab_index, upper_id, note,
               warning_id, blind_yn,
               create_id, create_date
        FROM board_comment
		<where>
			AND delete_yn = 'N'
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
	   		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">and board_id = #{boardId}</if>
	   		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)">and content_id = #{contentId}</if>
		</where>
        ORDER BY id DESC
        LIMIT 1
    </select>

    <insert id="insertBoardComment" parameterType="BoardComment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO board_comment
        (
            board_id, content_id,
            tab_index, upper_id, note,
            create_id, create_date
        )
        VALUES (
                   #{boardId}, #{contentId},
                   #{tabIndex}, #{upperId}, #{note},
                   #{createId}, now()
               )
    </insert>

    <update id="updateBoardComment" parameterType="BoardComment">
        UPDATE board_comment
           SET note = #{note}, last_update_id = #{createId}, last_update_date = now()
         WHERE id = #{id} AND board_id = #{boardId} AND content_id = #{contentId}
    </update>

<!--################################### BoardAttachFile ###################################-->
	<select id="selectCountBoardAttachFile" parameterType="HashMap" resultType="Integer">
		SELECT count(id)
		  FROM board_attach_file
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType) and
					  @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey) ">
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType=='userEmail'" >and title LIKE CONCAT('%', #{searchKey}, '%')</if>
			</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	and board_id=#{boardId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)" >	and content_id=#{contentId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>
			and delete_yn = 'N'
		</where>
	</select>

	<select id="selectListBoardAttachFile" parameterType="HashMap" resultType="BoardAttachFile">
		select *
		  from (
			SELECT @rownum:=@rownum+1 AS rownum,
			       file_id, board_id, content_id, service_type, upload_path, upload_filename, file_extension, file_size, file_mimetype, origin_filename, upload_id, upload_date
			  FROM board_attach_file
			  join (SELECT @rownum:= 0) rnum
			<where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType) and
						  @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey) ">
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType=='userEmail'" >and title LIKE CONCAT('%', #{searchKey}, '%')</if>
				</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	and board_id=#{boardId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)" >	and content_id=#{contentId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>

			</where> ) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneBoardAttachFile" parameterType="HashMap" resultType="BoardAttachFile">
		SELECT file_id, board_id, content_id, service_type, upload_path, upload_filename, file_extension, file_size, file_mimetype, origin_filename, upload_id, upload_date
		  FROM board_attach_file
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	and board_id=#{boardId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)" >	and content_id=#{contentId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>
		</where>
	</select>

    <insert id="insertBoardAttachFile" parameterType="BoardAttachFile" useGeneratedKeys="true" keyProperty="fileId">
        INSERT INTO board_attach_file
               (
	                board_id, content_id, service_type,
	                upload_path, upload_filename,
	                file_extension, file_size, file_mimetype,
	                origin_filename,
	                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	upload_id,	</if>
	                 upload_date
                )
        VALUES (
                   #{boardId}, #{contentId}, #{serviceType},
                   #{uploadPath}, #{uploadFilename},
                   #{fileExtension}, #{fileSize}, #{fileMimetype},
                   #{originFilename},
                   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	#{uploadId},	</if>
                    now()
               )
    </insert>

    <delete id="deleteBoardAttachFile" parameterType="BoardAttachFile">
		delete from board_attach_file
		 WHERE file_id = #{fileId}
	</delete>

</mapper>
