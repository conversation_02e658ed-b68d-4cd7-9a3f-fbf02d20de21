package kr.co.wayplus.travel.mapper.front;

import kr.co.wayplus.travel.model.*;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

@Mapper
@Repository
public interface ContentsMapper {
	/**
	 * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
	 */
	//	<!--################################### #{테이블명칭!!} ###################################-->
	int selectCountContents(HashMap<String, Object> paramMap);
	ArrayList<Contents> selectListContents(HashMap<String, Object> paramMap);
	Contents selectOneContents(HashMap<String, Object> paramMap);

	//contentsItem
	int selectCountContentsItem(HashMap<String, Object> paramMap);
	ArrayList<ContentsItem> selectListContentsItem(HashMap<String, Object> paramMap);
	ContentsItem selectOneContentsItem(HashMap<String, Object> paramMap);
}
