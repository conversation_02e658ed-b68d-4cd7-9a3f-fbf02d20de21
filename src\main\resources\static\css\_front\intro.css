/*여행사 소개_인사말*/
.greetings_box {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    margin-bottom: 140px;
}

.greetings_img {
    width: 498px;
    height: 625px;
    background: url(/images/greetings.jpg) no-repeat;
    background-position: center center;
    background-size: cover;
    margin-right: 70px;
    border-radius: 10px;
    overflow: hidden;
}

.info_contents_box {
    width: calc(100% - 498px - 70px);
}

.info_contents_title {
    margin-bottom: 50px;
}

.info_contents_title .point {
    color: #0062D4;
}

.info_content_txt {
    color: #222;
    line-height: 24px;
}

.info_content_txt p {
    margin-bottom: 20px;
}

.greetings_end {
    float: right;
    text-align: right;
}

.greetings_end .bold {
    color: #222;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
}

.greetings_end .all {
    color: #666;
}

/*여행사 소개_ESC CI*/
.info_title {
    margin-bottom: 40px;
}

.info_title_line {
    display: flex;
    margin-top: 20px;
}

.info_title_line .half {
    width: 50%;
}

.info_title_line .long {
    width: 100%;
}

.info_title_line .black {
    border-bottom: 1px solid #666;
}

.info_title_line .blue {
    border-bottom: 2px solid #0062D4;
}

.esc_ci_contents {
    padding-top: 20px;
}

.esc_ci_logo_box {
    display: flex;
    margin-bottom: 100px;
}

.esc_ci_logo {
    margin-right: 70px;
}

.download_btn {
    margin-top: 40px;
}

.esc_ci_color_box {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
}

.esc_ci_color_title_box {
    display: flex;
    align-items: baseline;
}

.esc_ci_color_title {
    font-size: 28px;
    font-weight: 600;
}

.esc_ci_blue {
    color: #4E84C4;
}

.esc_ci_yellow {
    color: #D7BB28;
}

.esc_ci_green {
    color: #3EAE52;
}

.esc_ci_orange {
    color: #E0692C;
}

.esc_ci_color_line {
    width: 1px;
    height: 11px;
    border-right: 1px solid #d9d9d9;
    margin: 0 8px;
}

.esc_ci_color_ex {
    color: #222;
    margin: 10px 0 20px 0;
}

.esc_ci_color_rgb {
    color: #666;
    font-size: 14px;
    margin-top: 12px;
}

.esc_ci_color_rgb .bold {
    font-weight: 500;
}

/*여행사 소개_오시는길*/
.esc_way {
    padding-top: 140px;
}

.esc_way_address {
    color: #222;
    font-size: 20px;
    font-weight: 500;
}

.esc_way_map {
    width: 100%; height: 500px;
    border-radius: 10px;
    overflow: hidden;
    margin-top: 40px;
    background: url(/images/map.jpg);
    background-position: center center;
    background-size: cover;
}

/*비전/연혁*/
.vison_banner {
    width: 100%; height: 300px;
    background: url(/images/vison_banner.jpg) no-repeat;
    background-position: center center;
    background-size: cover;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    margin-bottom: 100px;
}

.vison_banner_title_box {
    text-align: center;
}

.vison_banner_title {
    color: #D9D9D9;
    font-size: 20px;
    margin-bottom: 18px;
}

.vison_banner_ex {
    color: #FFF;
    font-size: 22px;
    font-weight: 500;
    line-height: 36px;
}

.vison_banner_ex .bold {
    font-size: 24px;
    font-weight: 600;
}

.vison_title {
    margin-bottom: 40px;
}

.vison_img {
    width: 100%; height: auto;
}

.vison_img img{
    width: 100%; height: auto;
}

/*수상인증*/
.awards_contents {
    display: grid;
    grid-template-columns: repeat(5, calc(20% - 20px));
    grid-column-gap: 25px;
    grid-row-gap: 28px;
}

.awards_contents_img {
    width: 100%;
    height: auto;
    max-height: 280px;
    border: 1px solid #D9D9D9;
    box-sizing: border-box;
    overflow: hidden;
}

.awards_contents_img img {
    width: 100%; height: auto;
    object-fit: cover;
    aspect-ratio: 218/151;
}

.awards_contents_cartegory {
    color: #666;
    text-align: center;
    margin: 12px 0 7px 0;
    min-height: 19px;
}

.awards_contents_title {
    color: #222;
    text-align: center;
    font-size: 18px;
    font-weight: 500;
}

/*반응형 쿼리*/
@media screen and (max-width:768px) {
    /*여행사소개*/
    .greetings_box {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 50px;
    }

    .greetings_img {
        width: 100%;
        height: 350px;
        margin-right: 0px;
    }

    .info_contents_box {
        width: 100%;
        margin-top: 20px;
    }

    .esc_ci_logo_box {
        flex-wrap: wrap;
    }

    .esc_ci_logo {
        width: 100%;
        text-align: center;
    }

    .esc_ci_color_box {
        flex-wrap: wrap;
    }

    .esc_ci_color_contents {
        width: calc(50% - 10px);
        margin-bottom: 20px;
    }

    .esc_way {
        padding-top: 50px;
    }

    .esc_way_map {
        height: 250px;
    }

    /*비전/연혁*/
    .vison_banner_ex {
        font-size: 18px;
    }

    .vison_banner_ex .bold {
        display: block;
        font-size: 20px;
    }

    /*수상/인증*/
    .awards_contents {
        display: grid;
        grid-template-columns: repeat(3, calc(33.3% - 16.6px));
        grid-column-gap: 25px;
        grid-row-gap: 28px;
    }
}

@media screen and (max-width:425px) {
    /*여행사소개*/
    .esc_ci_color_contents {
        width: 100%;
    }

    /*수상/인증*/
    .awards_contents {
        display: grid;
        grid-template-columns: repeat(2, calc(50% - 10px));
        grid-column-gap: 20px;
        grid-row-gap: 28px;
    }
}