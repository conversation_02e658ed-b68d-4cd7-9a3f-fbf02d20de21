package kr.co.wayplus.travel.service.front;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import kr.co.wayplus.travel.mapper.front.MenuMapper;
import kr.co.wayplus.travel.model.Contents;
import kr.co.wayplus.travel.model.ContentsItem;
import kr.co.wayplus.travel.model.MenuUser;

@Service
public class MenuService {

    private final MenuMapper mapper;

    @Autowired
    public MenuService(MenuMapper mapper) {
        this.mapper = mapper;
    }


	public int selectListMenuCount(HashMap<String, Object> paramMap) {
		return mapper.selectListMenuCount(paramMap);
	}

	public ArrayList<MenuUser> selectListMenu(HashMap<String, Object> paramMap) {
		return mapper.selectListMenu(paramMap);
	}

	public MenuUser selectOneMenu(HashMap<String, Object> paramMap) {
		return mapper.selectOneMenu(paramMap);
	}


    public ArrayList<MenuUser> selectListCommunityMenu(HashMap<String, Object> paramMap) {
        return mapper.selectListCommunityMenu(paramMap);
    }
}
