package kr.co.wayplus.travel.service.manage;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import kr.co.wayplus.travel.mapper.manage.ContentsManageMapper;
import kr.co.wayplus.travel.model.BoardAttachFile;
import kr.co.wayplus.travel.model.Contents;
import kr.co.wayplus.travel.model.ContentsItem;

@Service
public class ContentsManageService {

	@Value("${upload.file.path}")
	private String imageUploadPath;
    private final ContentsManageMapper mapper;
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    public ContentsManageService(ContentsManageMapper mapper) {
        this.mapper = mapper;
    }

    public int selectCountContents(HashMap<String, Object> paramMap) {
    	return mapper.selectCountContents(paramMap);
    }
	public ArrayList<Contents> selectListContents(HashMap<String, Object> paramMap) {
		return mapper.selectListContents(paramMap);
	}
	public Contents selectOneContents(HashMap<String, Object> paramMap) {
		return mapper.selectOneContents(paramMap);
	}

	public void insertContents(Contents cnt) throws SQLException {
		mapper.insertContents(cnt);
	}

	public void updateContents(Contents cnt) throws SQLException {
		mapper.updateContents(cnt);
	}

	public void deleteContents(Contents cnt) throws SQLException {
		mapper.deleteContents(cnt);
	}

	public void selectList(HashMap<String, Object> paramMap, HashMap<String, Object> retMap) {
		ArrayList<Contents> listTopMenu = new ArrayList<Contents>(); /*최상위 메뉴 뽑기*/
		HashMap<String,Contents> _map = new HashMap<String, Contents>(); /*색인용 Map*/
		HashMap<String,ArrayList<Contents>> _mapUpper = new HashMap<String, ArrayList<Contents>>(); /*상위 메뉴 색인용 Map*/
		ArrayList<Contents> list = this.selectListContents(paramMap);

		for (Contents _contents : list) {
			_map.put(_contents.getContentId().toString(), _contents);

			if(_contents.getUpperContentId() != null) {
				ArrayList<Contents> subList = null;
				if( _mapUpper.containsKey(_contents.getUpperContentId().toString()) ) {
					subList = _mapUpper.get( _contents.getUpperContentId().toString() );
				} else {
					subList = new ArrayList<Contents>();
				}
				_mapUpper.put( _contents.getUpperContentId().toString(), subList );
				subList.add( _contents );
			}
		}

		for (Contents _contents : list) {
			Integer key = _contents.getContentId();

			if(key != null)
    			if( _mapUpper.containsKey( key.toString() ) ) {
    				ArrayList<Contents> contentsList = _mapUpper.get( key.toString());
    				Collections.sort( contentsList );
    				_contents.setListChildContentL( contentsList );
    			}
		}

		for (Contents _contents : list) {
			if(_contents.getUpperContentId() == null) {
				listTopMenu.add(_contents);
			}
		}
		Collections.sort( listTopMenu );

		retMap.put("data",listTopMenu);
		retMap.put("list",list);
	}

	public int selectCountContentsItem(HashMap<String, Object> paramMap) {
    	return mapper.selectCountContentsItem(paramMap);
    }
	public ArrayList<ContentsItem> selectListContentsItem(HashMap<String, Object> paramMap) {
		return mapper.selectListContentsItem(paramMap);
	}
	public ContentsItem selectOneContentsItem(HashMap<String, Object> paramMap) {
		return mapper.selectOneContentsItem(paramMap);
	}

	public void insertContentsItem(ContentsItem cnt) throws SQLException {
		mapper.insertContentsItem(cnt);
	}

	public void updateContentsItem(ContentsItem cnt) throws SQLException {
		mapper.updateContentsItem(cnt);
	}

	public void deleteContentsItem(ContentsItem cnt) throws SQLException {
		mapper.deleteContentsItem(cnt);
	}

	public void insertContentsAttachFile(BoardAttachFile baf) {
		mapper.insertContentsAttachFile(baf);
	}
}
