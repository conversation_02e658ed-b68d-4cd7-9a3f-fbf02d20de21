/*font*/
.terms_title {
    color: #333;
    text-align: center;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 100px;
}

.terms_sub_title {
    color: #222;
    font-size: 18px;
    font-weight: 500;
    line-height: 24px;
}

.terms_txt {
    color: #222;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
}

/*contents*/
.terms_contents {
    width: 1120px;
    margin: 0 auto;
    margin-top: 50px;
    margin-bottom: 100px;
}
.terms_contents p{
    line-height:26px;
}
.terms_list {
    margin-bottom: 30px;
}

/*반응형쿼리*/
@media screen and (max-width:768px) {
    .terms_contents {
        width: calc(100% - 40px);
        margin-top: 110px;
        margin-bottom: 50px;
    }
}

@media screen and (max-width:425px) {
    .terms_title {
        text-align: left;
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 40px;
    }

    .terms_sub_title {
        font-size: 16px;
    }

    .terms_txt {
        font-size: 14px;
        line-height: 20px;
    }
}