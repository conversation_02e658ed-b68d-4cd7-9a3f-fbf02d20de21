package kr.co.wayplus.travel.web.admin;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.BoardAttachFile;
import kr.co.wayplus.travel.model.BoardContents;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.MailLog;
import kr.co.wayplus.travel.model.ManageMenu;
import kr.co.wayplus.travel.model.ManageMenuAuth;
import kr.co.wayplus.travel.model.ManageMenuConnectAuth;
import kr.co.wayplus.travel.model.PagingDTO;
import kr.co.wayplus.travel.model.SmsRecivedUser;
import kr.co.wayplus.travel.model.SortData;
import kr.co.wayplus.travel.service.admin.AdminService;
import kr.co.wayplus.travel.service.manage.ManageService;
import kr.co.wayplus.travel.util.CryptoUtil;
import kr.co.wayplus.travel.util.FileInfoUtil;
import kr.co.wayplus.travel.util.MybatisUtil;

@Controller
@RequestMapping("/admin")
public class AdminController extends BaseController {
	private final Logger logger = LoggerFactory.getLogger(getClass());

	@Value("${key.crypto.encrypt}")
	private String encrypt;
	@Value("${key.crypto.iv}")
	private String iv;

	private AdminService adminService;

	private ManageService service;

	@Autowired
	private DataSourceTransactionManager transactionManager;

	@Autowired
	private AdminController(AdminService adminService, ManageService svc ) {
		this.adminService = adminService;
		this.service = svc;
	}

	@GetMapping("/menu/manage/")
	public ModelAndView menuList(){
		ModelAndView mav = new ModelAndView();
		mav.setViewName("manage/sub/menu/manage/list");
		return mav;
	}

    @GetMapping("/menu/manage/list")
    @ResponseBody
    public HashMap<String,Object> menu_List(){
    	HashMap<String,Object> retMap = new HashMap<String, Object>();

    	try {
    		service.selectList(null, retMap);

//    		HashMap<String,Object> paramMap = new HashMap<String, Object>();
//
//    		ArrayList<ManageMenu> listTopMenu = new ArrayList<ManageMenu>(); /*최상위 메뉴 뽑기*/
//    		HashMap<String,ManageMenu> _map = new HashMap<String, ManageMenu>(); /*색인용 Map*/
//    		HashMap<String,ArrayList<ManageMenu>> _mapUpper = new HashMap<String, ArrayList<ManageMenu>>(); /*상위 메뉴 색인용 Map*/
//    		ArrayList<ManageMenu> list = service.selectListManageMenu(paramMap);
//
//    		for (ManageMenu _menu : list) {
//				_map.put(_menu.getMenuId().toString(), _menu);
//
//				if(_menu.getUpperMenuId() != null) {
//					ArrayList<ManageMenu> subList = null;
//					if( _mapUpper.containsKey(_menu.getUpperMenuId().toString()) ) {
//						subList = _mapUpper.get( _menu.getUpperMenuId().toString() );
//					} else {
//						subList = new ArrayList<ManageMenu>();
//					}
//					_mapUpper.put( _menu.getUpperMenuId().toString(), subList );
//					subList.add( _menu );
//				}
//			}
//
//    		for (ManageMenu _tmenu : list) {
//    			Long key = _tmenu.getMenuId();
//
//    			if(key != null)
//	    			if( _mapUpper.containsKey( key.toString() ) ) {
//	    				ArrayList<ManageMenu> menuList = _mapUpper.get( key.toString());
//	    				Collections.sort( menuList );
//	    				_tmenu.setListChildMenuL( menuList );
//	    			}
//			}
//
//    		for (ManageMenu _menu : list) {
//    			if(_menu.getUpperMenuId() == null) {
//					listTopMenu.add(_menu);
//				}
//			}
//    		Collections.sort( listTopMenu );
//
//    		retMap.put("data",listTopMenu);
//    		retMap.put("list",list);

    		retMap.put("result","success");
    		retMap.put("message","처리 성공");
		} catch (Exception e) {
			retMap.put("result","ERROR");
			retMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
		}

        return retMap;
    }

    @GetMapping("/menu/manage/jstree-list")
    @ResponseBody
	public HashMap<String, Object> jstreeList(
			HttpServletRequest request
			//, MngrMenuSearch menuSearch
			) {
    	HashMap<String, Object> result = new HashMap<String, Object>();
		try {
			result.put ("data", service.getJstreeList() );
			result.put ("result", "success");
		}catch (Exception e) {
			e.printStackTrace();
			result.put ("result", "fail");
			result.put ("message", e.getMessage());
		}
		return result;
	}

    @PostMapping("/menu/manage/save")
    @ResponseBody
    public HashMap<String,Object> menu_save(ManageMenu menu){
    	HashMap<String,Object> retMap = new HashMap<String, Object>();

    	try {
    		HashMap<String,Object> paramMap = new HashMap<String, Object>();

//    		ArrayList<ManageMenu> list = service.selectListManageMenu(paramMap);
//    		retMap.put("data",list);

    		if(menu != null) {
    			if(menu.getMenuId() == null) {
    				service.insertManageMenu(menu);
    			} else {
    				if( MybatisUtil.isEmpty( menu.getMenuUrl() ) ) { menu.setIsMenuUrlSetNull( true ); }

    				if( menu.getUpperMenuId() != null  ) {
    					menu.setIsMenuUpperId(true);
    				}

    				service.updateManageMenu(menu);
    			}

    			retMap.put("result","success");
    			retMap.put("message","처리 성공 하였습니다.");
    		} else {
    			retMap.put("result","fail");
    			retMap.put("message","처리 실패 하였습니다.");
    		}
		} catch (Exception e) {
			retMap.put("result","ERROR");
			retMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getCause().getMessage());
		}

        return retMap;
    }

    @PostMapping("/menu/manage/order-save")
    @ResponseBody
    public HashMap<String,Object> menu_order_save(HttpServletRequest request ){
    	HashMap<String,Object> retMap = new HashMap<String, Object>();

    	try {
//    		ArrayList<ManageMenu> listMenu = new ArrayList<ManageMenu>();

    		String total = request.getParameter("total");

    		for (int i = 0; i < Integer.parseInt(total); i++) {

    			String menuId 		= request.getParameter("order["+i+"][menuId]");
    			String menuSort 	= request.getParameter("order["+i+"][menuSort]");
    			String menuUpperId 	= request.getParameter("order["+i+"][menuUpperId]");
    			Boolean isMenuUpperId 	= request.getParameter("order["+i+"][isMenuUpperId]").equals("true");

    			//System.out.println(menuId + "," + menuSort + "," + menuUpperId + "," + isMenuUpperId  );

    			Long _menuId 	  = Long.parseLong( menuId );
    			Integer _menuSort = Integer.parseInt( menuSort );

    			ManageMenu _menu = new ManageMenu().addMenuId( _menuId ).addMenuSort( _menuSort ).addIsUpperMenuId(isMenuUpperId);

    			if( menuUpperId != null ) {
    				_menu.addUpperMenuId( Long.parseLong( menuUpperId ) );
    			}

    			service.updateManageMenu(_menu);
			}

    		retMap.put("result","success");
    		retMap.put("message","처리 성공 하였습니다.");

		} catch (Exception e) {
			retMap.put("result","ERROR");
			retMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getCause().getMessage());
		}

        return retMap;
    }
    @PostMapping("/menu/manage/del")
    @ResponseBody
    public HashMap<String,Object> menu_del(ManageMenu menu){
    	HashMap<String,Object> retMap = new HashMap<String, Object>();

    	try {
    		service.deleteManageMenu(menu);

    		retMap.put("result","success");
    		retMap.put("message","처리 성공 하였습니다.");

		} catch (Exception e) {
			retMap.put("result","ERROR");
			retMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getCause().getMessage());
		}

        return retMap;
    }


	@GetMapping("/administrator/list")
	public ModelAndView administratorList(@RequestParam(value="page", defaultValue="1") int page,
										  @RequestParam(value="pageSize", defaultValue="10") int pageSize){
		ModelAndView mav = new ModelAndView();
		int totalCount = adminService.getAdministratorUserListCount();
		HashMap<String, Object> param = new HashMap<>();
		PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
		param.put("itemStartPosition", paging.getItemStartPosition());
		param.put("pagePerSize", paging.getPagePerSize());
		mav.addObject("paging", paging);
		mav.addObject("administratorList", adminService.getAdministratorUserList(param));
		mav.setViewName("manage/sub/administrator/list");
		return mav;
	}

	@GetMapping("/administrator/add")
	public ModelAndView administratorAdd(HttpSession session){
		ModelAndView mav = new ModelAndView();
		mav.setViewName("manage/sub/administrator/form");
		if(session.getAttribute("encrypt") == null || session.getAttribute("iv") == null){
			CryptoUtil cryptoUtil = new CryptoUtil();
			session.setAttribute("encrypt", cryptoUtil.generateRandomEncryptKey(""));
			session.setAttribute("iv", cryptoUtil.generateRandomIv(""));
		}
		mav.addObject("mode", "I");
		return mav;
	}

	@GetMapping("/administrator/view")
	public ModelAndView administratorView(HttpSession session,
										  @RequestParam(value="userEmail", defaultValue="") String userEmail){
		ModelAndView mav = new ModelAndView();
		mav.addObject("user", adminService.getAdministratorInfo(userEmail));
		mav.setViewName("manage/sub/administrator/form");
		if(session.getAttribute("encrypt") == null || session.getAttribute("iv") == null){
			CryptoUtil cryptoUtil = new CryptoUtil();
			session.setAttribute("encrypt", cryptoUtil.generateRandomEncryptKey(""));
			session.setAttribute("iv", cryptoUtil.generateRandomIv(""));
		}
		mav.addObject("mode", "U");
		return mav;
	}

	@PostMapping("/administrator/create")
	@ResponseBody
	public HashMap<String,Object> administratorCreate(HttpSession session,
													  @RequestParam(value="encrypted", defaultValue="true") String encrypted,
													  @ModelAttribute LoginUser user, BindingResult bindingResult){
		HashMap<String,Object> resultMap = new HashMap<String, Object>();

		try {
			if(user == null
					|| user.getUserEmail() == null
					|| user.getUserPassword() == null
					|| user.getUserName() == null
			) {
				throw new Exception("필수 입력 정보를 확인 해 주세요.");
			}
			if(session.getAttribute("encrypt") != null) encrypt = (String) session.getAttribute("encrypt");
			if(session.getAttribute("iv") != null) iv = (String) session.getAttribute("iv");
			user.setEncrypt(encrypt);
			user.setIv(iv);
			user.setUserTokenId(String.valueOf(UUID.randomUUID()));
			adminService.createAdministrator(user, Boolean.parseBoolean(encrypted));

			resultMap.put("result","success");
			resultMap.put("message","처리 완료.");
		} catch (Exception e) {
			resultMap.put("result","ERROR");
			resultMap.put("message","처리중 오류가 발생했습니다.");
			logger.debug(e.getCause().getMessage());
		}

		return resultMap;
	}

	@PutMapping("/administrator/update")
	@ResponseBody
	public HashMap<String,Object> administratorUpdate(HttpSession session,
													  @RequestParam(value="encrypted", defaultValue="true") String encrypted,
													  @ModelAttribute LoginUser user, BindingResult bindingResult){
		HashMap<String,Object> resultMap = new HashMap<String, Object>();

		try {
			if(user == null
					|| user.getUserEmail() == null
					|| user.getUserPassword() == null
					|| user.getUserName() == null
			) {
				throw new Exception("필수 입력 정보를 확인 해 주세요.");
			}
			if(session.getAttribute("encrypt") != null) encrypt = (String) session.getAttribute("encrypt");
			if(session.getAttribute("iv") != null) iv = (String) session.getAttribute("iv");
			user.setEncrypt(encrypt);
			user.setIv(iv);
			adminService.updateAdministrator(user, Boolean.parseBoolean(encrypted));

			resultMap.put("result","success");
			resultMap.put("message","처리 완료.");
		} catch (Exception e) {
			resultMap.put("result","ERROR");
			resultMap.put("message","처리중 오류가 발생했습니다.");
			logger.debug(e.getCause().getMessage());
		}

		return resultMap;
	}

	@PutMapping("/administrator/withdraw")
	@ResponseBody
	public HashMap<String,Object> administratorWithdraw(@ModelAttribute LoginUser user, BindingResult bindingResult){
		HashMap<String,Object> resultMap = new HashMap<String, Object>();

		try {
			if(user == null
					|| user.getUserEmail() == null
					|| user.getUserTokenId() == null
			) {
				throw new Exception("필수 입력 정보를 확인 해 주세요.");
			}

			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			user.setOperator(loginUser.getUserEmail());
			adminService.withdrawalAdministrator(user);

			resultMap.put("result","success");
			resultMap.put("message","처리 완료.");
		} catch (Exception e) {
			resultMap.put("result","ERROR");
			resultMap.put("message","처리중 오류가 발생했습니다.");
			logger.debug(e.getCause().getMessage());
		}

		return resultMap;
	}

	@GetMapping("/administrator/smsRecivedUser")
	public ModelAndView administratorSmsRecivedUser(){
		ModelAndView mav = new ModelAndView();
		mav.setViewName("manage/sub/administrator/smsRecivedUser");
		return mav;
	}

	@PostMapping("/administrator/smsRecivedUser/list")
    @ResponseBody
    public HashMap<String, Object> deposit_recived_list_ajax(
    		HttpServletRequest request,
    		@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length,
    		SmsRecivedUser sru ){
		HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		HashMap<String, Object> paramMap = new HashMap<>();

    		List<SortData> listSort = getListOrder(request);
    		paramMap.put("listSort", listSort);

    		paramMap.put("sort", "menuName");
    		paramMap.put("sortOrder", "asc");

    		if(length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
    		}

//			paramMap.put("boardId", boardId);
//			paramMap.put("useYn", useYn);
//			paramMap.put("titleLike", titleLike);
//			paramMap.put("contentLike", contentLike);

    		int totalCount = adminService.selectCountSmsRecivedUser(paramMap);

    		retrunMap.put("recordsTotal", totalCount);
    		retrunMap.put("recordsFiltered", totalCount);
    		retrunMap.put("data", adminService.selectListSmsRecivedUser(paramMap));

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

        return retrunMap;
    }

	@PostMapping("/administrator/smsRecivedUser/save")
    @ResponseBody
    public HashMap<String, Object> deposit_recived_save_ajax(
    		HttpServletRequest request,
    		@RequestParam(value="mode", defaultValue="I") String mode,
    		SmsRecivedUser bc ){
		HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		HashMap<String, Object> paramMap = new HashMap<>();

    		if(mode.equals("I")) {
    			bc.setCreateId( getBaseUserEmail() );
    			adminService.insertSmsRecivedUser(bc);
    		} else {
    			bc.setLastUpdateId( getBaseUserEmail() );
    			adminService.updateSmsRecivedUser(bc);
    		}

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

        return retrunMap;
    }

	@PostMapping("/administrator/smsRecivedUser/delete")
    @ResponseBody
    public HashMap<String, Object> deposit_recived_delete_ajax(
    		HttpServletRequest request,
    		SmsRecivedUser bc ){
		HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		HashMap<String, Object> paramMap = new HashMap<>();

    		adminService.deleteSmsRecivedUser(bc);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

        return retrunMap;
    }

//	<!--################################### manageMenuAuth ###################################-->
	@GetMapping("/auth-menu/list")
	public ModelAndView auth_menu_list(){
		ModelAndView mav = new ModelAndView();
		mav.setViewName("manage/sub/auth/menu/list");
		return mav;
	}

	@PostMapping("/auth/menu/list")
    @ResponseBody
    public HashMap<String, Object> auth_menu_list_ajax(HttpServletRequest request, BoardContents bc,
    		@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length,
    		@Param(value="boardId") String boardId,
    		@Param(value="titleLike") String titleLike,
    		@Param(value="contentLike") String contentLike
    		){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		HashMap<String, Object> paramMap = new HashMap<>();

    		List<SortData> listSort = getListOrder(request);
    		paramMap.put("listSort", listSort);

    		if(length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
    		}

			paramMap.put("boardId", boardId);
			paramMap.put("titleLike", titleLike);
			paramMap.put("contentLike", contentLike);

    		int totalCount = adminService.selectCountManageMenuAuth(paramMap);

    		retrunMap.put("recordsTotal", totalCount);
    		retrunMap.put("recordsFiltered", totalCount);
    		retrunMap.put("data", adminService.selectListManageMenuAuth(paramMap));

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

        return retrunMap;
    }

    @PostMapping("/auth/menu/save")
    @ResponseBody
    public HashMap<String, Object> auth_menu_save_ajax(
    		@RequestParam(value="mode", defaultValue="I") String mode,
    		ManageMenuAuth bc,
    		@RequestParam(value="checkIdList") String[] checkIdList,
    		//Multipart
    		HttpServletRequest request
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	TransactionStatus txStatus =  transactionManager.getTransaction(new DefaultTransactionDefinition());
    	try {
    		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser user = (LoginUser)_user;
        		HashMap<String, Object> paramMap = new HashMap<>();

        		String thumbnailUrl = "";

	    		if(mode.equals("I")) {
	    			bc.setCreateId( user.getUserEmail());
	    			adminService.insertManageMenuAuth(bc);
	    		} else {
	    			bc.setLastUpdateId( user.getUserEmail());
	    			adminService.updateManageMenuAuth(bc);
	    		}

    			ManageMenuConnectAuth mmca = new ManageMenuConnectAuth().addAuthId( bc.getAuthId() );

        		adminService.deleteManageMenuConnectAuth( mmca );
        		if(checkIdList != null){
					Arrays.sort(checkIdList);
					for(String checkId : checkIdList) {
						if(!"#".equals(checkId)){
							mmca.setMenuId( Long.valueOf(checkId) );

							adminService.insertManageMenuConnectAuth( mmca );
						}
					}
				}

	    		retrunMap.put("result", "success");
	    		retrunMap.put("message", "처리가 완료 되었습니다.");
        	} else {
        		retrunMap.put("result", "fail");
	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
        	}
        	transactionManager.commit(txStatus);
		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			retrunMap.put("info", e.getMessage());
			logger.error(e.getCause().getMessage());
			transactionManager.rollback(txStatus);
		}
        return retrunMap;
    }

    @PostMapping("/auth/menu/restore")
    @ResponseBody
    public HashMap<String, Object> auth_menu_restore_ajax(
    		ManageMenuAuth bc
    		){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		bc.setLastUpdateId( getBaseUserEmail() );
    		adminService.restoreManageMenuAuth(bc);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
    	} catch (Exception e) {
    		retrunMap.put("result", "fail");
    		retrunMap.put("message", "처리중 문제가 발생했습니다.");
    	}

    	return retrunMap;
    }
    @PostMapping("/auth/menu/delete")
    @ResponseBody
    public HashMap<String, Object> auth_menu_delete_ajax(
    		ManageMenuAuth bc
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		bc.setDeleteId( getBaseUserEmail() );
    		adminService.deleteManageMenuAuth(bc);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
		}

        return retrunMap;
    }

    @GetMapping("/auth/menu/jstree-list-selected")
    @ResponseBody
	public HashMap<String, Object> jstreeSelectedList(
			HttpServletRequest request
			, ManageMenuAuth mma
			, HashMap<String, Object> paramMap
			) {
    	HashMap<String, Object> result = new HashMap<String, Object>();
		try {

			result.put ("data", adminService.selectListManageMenuConnectAuth(mma.getAuthId()) );
			result.put ("result", "success");
		}catch (Exception e) {
			e.printStackTrace();
			result.put ("result", "fail");
			result.put ("message", e.getMessage());
		}
		return result;
	}

}
