/*리스트_다국어_탭*/
.tab_sheet {
    margin: 50px 0 30px 0;
}
.tab_sheet .tab_item{ display: none; }
.tab_sheet .tab_item.active{ display: block; }

.select_search_box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}

.select_search_box_mg_b {
    margin-bottom: 50px;
}
/*
#tab_01_chk, #tab_02_chk {display: none;}

#tab_01_chk:checked ~ .tab_sheet .sheet_01 {display: block;}
#tab_01_chk:checked ~ .tab_sheet .sheet_02 {display: none;}

#tab_02_chk:checked ~.tab_sheet .sheet_01 {display: none;}
#tab_02_chk:checked ~.tab_sheet .sheet_02 {display: block;}
*/

.tab_label {
    overflow: hidden;
}

.tab_label label {
    display: block;
    width: 50%; height: 40px;
    float: left;
    border-bottom: 1px solid #0062D4;
    border-top: 1px solid #D9D9D9;
    border-right: 1px solid #D9D9D9;
    border-left: 1px solid #D9D9D9;
    box-sizing: border-box;
    color: #222;
    text-align: center;
    font-size: 16px;
    line-height: 40px;
    cursor: pointer;
}

.tab_label label.active {
    border-top: 1px solid #0062D4;
    border-left: 1px solid #0062D4;
    border-right: 1px solid #0062D4;
    border-bottom: 1px solid #fff;
    color: #0062D4;
    font-weight: 600;
}

/*리스트_검색영역*/
.event_select_box {
    width: 200px;
    height: 40px;
    border-radius: 5px;
    background: #F5F5F5;
}

.event_select {
    border-radius: 5px;
    border: 1px solid #D9D9D9;
    width: 100%; height: 100%;
    padding: 0 20px; box-sizing: border-box;
    color: #222;
    font-family: Pretendard;
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    appearance:none;
    background:url('/images/icon/i_drop.svg') no-repeat right 20px center;
}

.select_search {
    width: calc(100% - 200px - 20px);
    height: 40px;
    border-radius: 5px;
    border: 1px solid #D9D9D9;
    background: #FFF;
    padding: 8px 20px;
    box-sizing: border-box;
}

/*리스트_상품*/
.product_box {
    display: grid;
    grid-template-columns: repeat(4, calc(25% - 18px));
    grid-column-gap: 24px;
    grid-row-gap: 40px;
}

.product_list {
    cursor: pointer;
}

.product_list:hover .product_img {
    transform: scale(1.1);
}

.product_img_box {
    position: relative;
    overflow: hidden;
}

.product_category {
    position: absolute;
    top: 0; left: 0;
    width: 102px;
    height: 30px;
    color: #FFF;
    font-size: 14px;
    text-align: center;
    font-weight: 500;
    line-height: 30px;
    z-index: 9;
}

.category_green {
    background: #31764D;
}

.category_blue {
    background: #0062D4;
}

.category_black {
    background: #333;
}

.product_img {
    width: 100%; height: 282px;/* max-width: 282px;*/
    transition: all 0.3s;
}

.product_img img {
    width: 100%; height: 100%;
}

/*뷰페이지*/
.view_contents {
    margin: 88px 0 110px 0;

}

/*뷰페이지_다국어 탭*/
.view_tab_label {
    overflow: hidden;
}

.view_tab_label label {
    display: block;
    width: calc(50% - 10px); height: 40px;
    float: left;
    background: #D9D9D9;
    color: #222;
    text-align: center;
    font-size: 16px;
    line-height: 40px;
    cursor: pointer;
}
.view_tab_label .active,
.view_tab_label label:hover {
    background: #0062D4;
    color: #fff;
    font-weight: 600;
}
.view_tab_label label:first-child {
    margin-right: 20px;
}

.view_tab_sheet {
    margin-top: 100px;
}

.view_tab_sheet .tab_item{ display: none; }
.view_tab_sheet .tab_item.active{ display: block; }

/*뷰페이지_이미지_정보*/
.view_contens_img_info_box {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    margin-bottom: 100px;
}

.view_contens_img_box {
	max-width: 556px;
	width: 100%;
	min-height: 550px;
}

.view_contens_big_box .navigation{
	position: absolute;
	top:50%;
	width: 100%;
	margin-left: -58px;
}
.view_contens_big_box .navigation .slide_arrow_prev{
	left: 5px;
}
.view_contens_big_box .navigation .slide_arrow_next{
	right: 5px;
}

.view_contens_big_box {
	max-width: 440px;
    padding: 0 58px !important;
    position: relative;
}
.view_contens_big {
    position: relative;
/*    overflow: hidden;*/
    width: 440px;
    height: 440px;
    object-fit: cover;
}

.view_contens_big .slide_arrow_prev {
    position: absolute;
    top: 50%; left: 0;
    margin-top: -18px;
}

.view_contens_big .slide_arrow {
    position: absolute;
    top: 50%; right: 0;
    margin-top: -18px;
}

.view_contens_img {
    max-width: 440px;
    max-height: 440px;
    object-fit: cover;
/*    overflow: hidden;*/
}

.view_contens_img img {
    width: 100%;
}

.view_contens_img_small_box {
    margin-left: 58px !important;
    margin-right: 58px !important;
    margin-top: 10px;
    display: flex;
    min-height: 100px;
}

.view_contens_small_box {
    max-width: 440px;
    width: 100%;
    /*overflow: hidden;*/
}

.view_contens_img_small {
    width: calc(25% - 7.5px);
    height: 100px;
    overflow: hidden;
    margin-right: 10px;
    cursor: pointer;
    max-width: 100px;
    max-height: 100px;
}

.view_contens_img_small img {
    width: 100%; height: 100%;
}

.view_contens_img_small:last-child {
    margin: 0;
}

.view_contens_info_box {
    position: relative;
    width: 564px;
}

.view_contents_category {
    width: 100px;
    height: 30px;
    color: #FFF;
    font-size: 14px;
    font-weight: 500;
    line-height: 30px;
    text-align: center;
    margin-bottom: 30PX;
}

.view_contens_ex {
    color: #666;
    margin-top: 20px;
}

.view_contens_price_box {
    width: 100%;
    position: absolute;
    bottom: 0;
}

.view_contens_price_info {
    margin-top: 40px;
    padding: 40px 0;
    border-top: 1px solid #666;
    border-bottom: 1px solid #666;
}

.view_contens_price_info li {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #222;
}

.view_contens_price_info li:last-child {
    margin-bottom: 0;
}

.view_price_list {
    width: 120px;
    height: 30px;
    border-radius: 5px;
    background: #222;
    color: #FFF;
    text-align: center;
    line-height: 30px;
    margin-right: 20px;
}

.park_golf_equipment {
    height: 210px;
    border-top: 1px solid #666;
    margin-top: 40px;
}

/*뷰페이지_상세정보*/
.product_info_select_box {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
}

.product_info_box {
    width: calc(100% - 320px - 60px);
}
.product_tab_label {
    overflow: hidden;
}

.product_info_box .product_tab_label label {
    display: block;
    width: 20%;
    padding: 16px 0;
    float: left;
    background: #F3F3F3;
    border-bottom: 1px solid #222;
    border-top: 1px solid #D9D9D9;
    border-right: 1px solid #D9D9D9;
    border-left: 1px solid #D9D9D9;
    box-sizing: border-box;
    color: #666;
    text-align: center;
    font-size: 18px;
    cursor: pointer;
}
.product_info_box .product_tab_label label.active {
    border-top: 1px solid #222;
    border-left: 1px solid #222;
    border-right: 1px solid #222;
    border-bottom: 1px solid #fff;
    color: #222;
    font-weight: 600;
    background: #fff;
}

.product_info_box .product_info_tab_sheet {
    margin-top: 50px;
}

.product_info_box .product_info_tab_sheet .sheet{
	display: none;
}

.product_info_box .product_info_tab_sheet .sheet.active{
	display: block;
}

.product_tab_label .info_btn_none {
    color: #fff;
    width: 25%;
    background: #fff;
    border: none;
    border-bottom: 1px solid #222;
    border-top: 1px solid #fff;
    cursor: unset;
}

/*상품상세이미지*/
.product_sample_img img {
    width: 100%;
}

/*여행표준약관*/
.view_terms {
    color: #222;
}

.view_terms_bold {
    font-size: 18px;
    font-weight: 600;
}

/*주변관광지정보*/
.view_place_list, .view_place_list a {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
}

.view_place_list:last-child {
    margin: 0;
}

.view_place_title_box{width: calc(100% - 300px - 24px);}

.view_place_img {
    width: 300px;
    height: 200px;
    margin-right: 24px;
}

.view_place_img img {
    width: 100%;
    aspect-ratio: 3/2;
}

.view_place_title {
    color: #222;
    font-size: 18px;
    font-weight: 600;
}

.view_place_address {
    display: flex;
    align-items: center;
    margin: 13px 0 14px;
    color: #666;
}

.view_place_address img {
    margin-right: 5px;
}

.view_place_ex {
    font-size: 14px;
    line-height: 20px;
    color: #666;
}

/*상품 선택 영역*/
.product_select_box {
    width: 320px; height: fit-content;
    padding: 30px; box-sizing: border-box;
    border: 1px solid #666;
}

.product_select {
    margin-bottom: 24px;
}

.product_select_title {
    color: #222;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
}

.calender {
    width: 100%; height: 250px;
    /*border: 1px solid #D9D9D9;*/
    position: relative;
}

.option {
    padding: 13px 15px;
    box-sizing: border-box;
    border: 1px solid #D9D9D9;
    margin-bottom: 12px;
}

.option_title {
    color: #222;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 12px;
}

.option_mg_n {
    margin: 0;
}

.option_number_price {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.option_number_box {
    display: flex;
    align-items: center;
}

.option_plus, .option_minus  {
    color: #222;
    font-size: 14px;
    cursor: pointer;
    padding: 5px;
}

.option_number {
    width: 60px;
    height: 28px;
    border-radius: 3px;
    border: 1px solid #D9D9D9;
    margin: 0 10px;
    text-align: center;
}

.option_number_price_red {
    color: #E74133;
    font-size: 14px;
    font-weight: 500;
}

.all_price {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
}

.all_price_title {
    color: #222;
    font-size: 18px;
    font-weight: 600;
}

.all_price_red {
    color: #E74133;
    font-size: 16px;
    font-weight: 600;
}

.view_btn {
    width: 100%;
    padding: 13px 0px;
    background: #0062D4;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
}

.view_btn:nth-child(n+2){margin-top:12px;}

.view_btn:hover {
    cursor: pointer;
    opacity: 0.9;
}

.view_btn_icon {
    width: 24px;
    height: 24px;
    margin-right: 12px;
}

.view_btn_close {background: #d0d0d0; color : #666;}
.view_btn_close .view_btn_icon{
    background: url(/images/icon/close.svg) no-repeat right 5px center;
}
.view_btn_money .view_btn_icon{
    background: url(/images/icon/money.svg);
}

.view_btn_estimate .view_btn_icon{
    background: url(/images/icon/estimate.svg);
}
.view_btn_estimate {background: #222;}

.schedule-wrapper{margin-top:20px;}
.schedule-wrapper .box{ background-color: #F5F5F5;padding: 30px 20px;margin-bottom: 20px;}
.box .item{margin-bottom: 20px;}
.box .item .subject{ color: #005DBF; font-size:19px; font-weight: 600  }
.box .item .subject:before{content: ''; background-color: #005DBF; padding-right:10px;margin-right:10px;}
.box .item .note {padding-left:30px;padding-top:5px;]}
.box .item .note .bold{font-weight: 600;}
.box .item.stay li.note:before{content: '-';margin-right:10px;}

.schedule > li{padding: 10px;border-bottom: 1px solid #BBBBBB;}

.day{font-family: "Pretendard";font-size: 23px;font-weight: 600;color: #007822;}
.schedule .day .bold{font-size: 40px;}
.schedule .day i{display: inline-block;width:1.5rem;height:30px;background-repeat:no-repeat;background-position:50%; background-size: 100% 100%;vertical-align:middle;float: right}

.schedule .day i{background-image: url("/images/icon/icon-schedule-open.svg");}
.schedule .detail{margin-top:24px;display: none;}

.schedule.active .day i{background-image: url("/images/icon/icon-schedule-close.svg");}
.schedule.active .detail{display: block;transition: visibility 0s, opacity 0.5s linear;}
.schedule .detail li{padding-left: 20px;list-style: circle;display: flex;flex-direction: column;margin-bottom:20px;display: inherit;}

.detail .subject{color: #007822;font-family: Pretendard;font-size: 21px;font-weight: 700;margin-bottom:10px;}
.detail .note{color: #000;font-family: Pretendard;font-size: 16px;font-weight: 400;padding-left: 5px;}
.detail .note::before{content: '·';padding-right: 5px}

.detail .image-wrapper{display:flex;justify-content: flex-start;align-items: center;}
.detail .image-wrapper .image{background-repeat:no-repeat;background-size:cover;background-position:center;width:130px;height:130px;margin-right:10px;}
.detail .image-wrapper .image img{aspect-ratio: 1 / 1;object-fit: cover;height:100%;width:100%;}


.view_popup_box{display:none;}
.view_popup_box.active{display:block;}
.view_popup_box.active .view_popup_bg {width: 100%;height: 100vh;background: rgba(0, 0, 0, 0.70);position: fixed;top: 0; left: 0;z-index: 999999999;}
.view_popup_box.active .view_popup {position: fixed;top: 50%;left: 50%;z-index: 9999999999;transform: translate(-50%,-50%);}
.view_popup_box.active .view_popup_squre {position: relative;width: 843px;height: fit-content;padding: 40px;box-sizing: border-box;border-radius: 4px;background: #FFF;}
.view_popup_box.active .view_popup_close {position: absolute;right: 0; top: -42px;background-color:transparent;border-style:none;}
.view_popup_box.active .view_popup_title {color: #333;font-size: 30px;font-weight: 600;margin-bottom: 5px;}
.view_popup_box.active .view_popup_ex {color: #444;font-size: 18px;font-weight: 400;line-height: 24px;}
.view_popup_box.active .view_popup_line {width: 100%;height: 2px;background: #444;margin: 15px 0;}
.view_popup_box.active .view_popup_list {margin-bottom: 10px;overflow: hidden;}
.view_popup_box.active .view_popup_list span {display: block;float: left;color: #444;font-size: 18px;font-weight: 500;line-height: 44px;}
.view_popup_box.active .view_popup_list input {float: right;width: 100%;height: 44px;border: 1px solid #CCC;background: #FFF;color: #888;font-family: Pretendard;font-size: 18px;font-weight: 400;padding: 0 20px;box-sizing: border-box;}
.view_popup_box.active .view_popup_btn {display: block;width: 95px;height: 48px;border-radius: 4px;background: #080C52;color: #FFF;font-size: 20px;font-weight: 500;line-height: 48px;text-align: center;margin: 0 auto;margin-top: 20px;}
.view_popup_box.active .view_popup_btn:hover {cursor: pointer;background: #00A5BD;}
.view_info_product{float:left;}
.view_info_product .product-menu,
.view_info_product .product-type{display:inline-block;width:96px;height:30px;font-family: "Pretendard";font-size:16px;font-weight:500;text-align: center;line-height:30px;border-radius:5px;}
.view_info_product .product-menu{background-color:#00A307;color:#FFFFFF;margin-right:10px;}
.view_info_product .product-type{background-color:#E74133;color:#FFFFFF;}

.view_popup_squre .popup_title{display: flex;justify-content: space-between;padding-bottom: 10px;}

.print_content{height: 500px; overflow-y: scroll;}
.print_content .product-tab-content{display: block !important;}

.product_info {display: flex;height: 300px;border-bottom: 1px solid #BBBBBB;}
.product_info .product_thumb{ margin-right: 20px;}
.product_info .product_thumb img{aspect-ratio: 1/1; height: 100%;max-height: 250px;}
.product_info .product_desc{}

.view_img img {aspect-ratio: 1 / 1;object-fit: cover;height: 100%;width: 100%;}


/*반응형 쿼리*/
@media screen and (max-width:1024px) {
    .product_box {
        display: grid;
        grid-template-columns: repeat(3, calc(33.3% - 16px));
        grid-column-gap: 24px;
        grid-row-gap: 40px;
    }
}

@media screen and (max-width:768px) {
    .product_box {
        display: grid;
        grid-template-columns: repeat(2, calc(50% - 12px));
        grid-column-gap: 24px;
        grid-row-gap: 40px;
    }

    /*뷰페이지*/
    .view_contens_img_info_box {
        flex-wrap: wrap;
        justify-content: center;
        margin-bottom: 50px;
    }

    .view_contens_info_box {
        width: 100%;
        height: 500px;
        margin-top: 50px;
    }

    .park_golf_equipment_view_contens_info_box {
        height: 300px;

    }

    .park_golf_equipment {
        height: unset;
    }

    .product_info_select_box {
        flex-wrap: wrap;
    }

    .product_info_box {
        order: 2;
        width: 100%;
    }

    .product_select_box {
        order: 1;
        width: 100%;
        margin-bottom: 50px;
    }
}

@media screen and (max-width:600px) {
    .view_contens_img {
        width: 260px;
        height: 260px;
    }

    .view_contens_img_small {
        height: 57px;
    }

    .product_tab_label label {
        min-height: 72px;
        font-size: 16px;
    }

    .select_search_box {
        flex-wrap: wrap;
    }

    .select_search {
        width: 100%;
        margin-top: 10px;
    }

    .view_place_list, .view_place_list a {
        flex-wrap: wrap;
    }

    .view_place_img {
        width: 100%;
        height: auto;
        margin: 0 0 20px 0;
    }
}

@media screen and (max-width:425px) {
    .product_box {
        display: block;
    }
    
    .product_list {
        margin-bottom: 20px;
    }

    .view_contens_big {
        padding: 0;
    }

    .view_contens_img_small_box {
        padding: 0;
    }

    .view_contens_big .slide_arrow_next {
        right: 10px;
    }

    .view_contens_big .slide_arrow_prev {
        left: 10px;
    }
}