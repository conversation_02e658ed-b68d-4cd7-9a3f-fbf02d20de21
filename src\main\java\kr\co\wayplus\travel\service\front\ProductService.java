package kr.co.wayplus.travel.service.front;

import java.sql.SQLException;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import kr.co.wayplus.travel.mapper.front.ProductMapper;
import kr.co.wayplus.travel.model.MenuUser;
import kr.co.wayplus.travel.model.ProductCategory;
import kr.co.wayplus.travel.model.ProductComment;
import kr.co.wayplus.travel.model.ProductDetailSchedule;
import kr.co.wayplus.travel.model.ProductDetailScheduleImage;
import kr.co.wayplus.travel.model.ProductInfo;
import kr.co.wayplus.travel.model.ProductInventory;
import kr.co.wayplus.travel.model.ProductPriceOption;
import kr.co.wayplus.travel.model.ProductPriceOption.DayList;
import kr.co.wayplus.travel.model.ProductPriceOption.FixPriceList;
import kr.co.wayplus.travel.model.ProductTourImages;
import kr.co.wayplus.travel.model.Reservation;
import kr.co.wayplus.travel.model.UserPointAccrued;
import kr.co.wayplus.travel.model.UserPointSet;
import kr.co.wayplus.travel.service.user.UserPointService;
import kr.co.wayplus.travel.util.ReservationUtil;
import kr.co.wayplus.travel.service.front.MenuService;
import kr.co.wayplus.travel.mapper.front.PageMapper;

@Service
public class ProductService {

    private final ProductMapper mapper;
	private final PageMapper pageMapper;

	private final MenuService menuService;
    private final UserPointService userPointService;

	private final ReservationUtil reservationUtil;

    public ProductService(ProductMapper mapper, UserPointService userPointService, MenuService menuService, PageMapper pageMapper, ReservationUtil reservationUtil) {
        this.mapper = mapper;
		this.pageMapper = pageMapper;
        this.userPointService = userPointService;
		this.menuService = menuService;
		this.reservationUtil = reservationUtil;
    }

    public int selectCountProduct(HashMap<String, Object> param) {
    	param.put("productStatus", "S");
		return mapper.selectCountProduct( param );
	}
	public ArrayList<ProductInfo> selectListProduct(HashMap<String, Object> param) {
		param.put("productStatus", "S");
		return mapper.selectListProduct( param );
	}
	public ArrayList<ProductInfo> selectListProgram(HashMap<String, Object> param) {
		param.put("productStatus", "S");
		return mapper.selectListProgram( param );
	}

	public int selectCountProductRecommend(HashMap<String, Object> param) {
		param.put("productStatus", "S");
		return mapper.selectCountProductRecommend( param );
	}
	public ArrayList<ProductInfo> selectListProductRecommend(HashMap<String, Object> param) {
		param.put("productStatus", "S");
		return mapper.selectListProductRecommend( param );
	}

	public int selectCountProductListByProductMenuId( Long menuId ) {
		HashMap<String, Object> param = new HashMap<String, Object>();
		param.put("productMenuId", menuId);
		return this.selectCountProduct( param );
	}
	public ArrayList<ProductInfo> selectListProductByProductMenuId( Long menuId ) {
		HashMap<String, Object> param = new HashMap<String, Object>();
		param.put("productMenuId", menuId);

		return this.selectListProduct( param );
	}
	public int selectCountProductListByMenuId( Long menuId ) {
		HashMap<String, Object> param = new HashMap<String, Object>();
		param.put("menuId", menuId);
		return this.selectCountProduct( param );
	}
	public int selectCountProductListByMenuIdWithLang( Long menuId, String lang ) {
		HashMap<String, Object> param = new HashMap<String, Object>();
		param.put("menuId", menuId);
		param.put("productLangType", lang);

		return this.selectCountProduct( param );
	}
	public ArrayList<ProductInfo> selectListProductByMenuId(Long menuId ) {
		HashMap<String, Object> param = new HashMap<String, Object>();
		param.put("menuId", menuId);
		param.put("productUseYn", "Y");
		param.put("regcyYn", "N");
		param.put("deleteYn", "N");

		return this.selectListProduct( param );
	}
	public ArrayList<ProductInfo> selectListProductByMenuIdWithLang(Long menuId, String lang ) {
		HashMap<String, Object> param = new HashMap<String, Object>();
		param.put("menuId", menuId);
		param.put("productLangType", lang);
		param.put("productUseYn", "Y");
		param.put("regcyYn", "N");
		param.put("deleteYn", "N");

		return this.selectListProduct( param );
	}

	public ArrayList<ProductCategory> selectListProductCategory(HashMap<String, Object> param) {
		return mapper.selectListProductCategory( param );
	}
	public ProductInfo selectOneProductTourId(HashMap<String, Object> paramProduct) {
		return mapper.selectOneProductTourId(paramProduct);
	}
	public ArrayList<ProductCategory> selectListProductCategoryByMenuId(Long menuId) {
		HashMap<String, Object> param = new HashMap<String, Object>();
		param.put("productMenuId", menuId);
		return this.selectListProductCategory( param );
	}

	public ProductInfo selectOneProduct(HashMap<String, Object> param) {
		return mapper.selectOneProduct( param );
	}
	public ProductInfo selectOneProductByProductSerial(String productSerial) {
		HashMap<String, Object> param = new HashMap<String, Object>();
		param.put("productSerial", productSerial);
		param.put("productUseYn", "Y");
		param.put("regcyYn", "N");
		param.put("deleteYn", "N");
		return this.selectOneProduct( param );
	}
	public ProductInfo selectOneProductByProductSerialWithTourId(HashMap<String, Object> paramProduct) {
		return this.selectOneProduct( paramProduct );
	}
	public ArrayList<ProductPriceOption> selectListPriceOption(HashMap<String, Object> paramMap) {
		return mapper.selectListPriceOption(paramMap);
	}
	public ArrayList<ProductPriceOption> selectListPriceOptionStayOnly(HashMap<String, Object> paramMap) {
		return mapper.selectListPriceOptionStayOnly(paramMap);
	}
	public ArrayList<ProductPriceOption> selectListPriceOptionList(HashMap<String, Object> paramMap) {
		return mapper.selectListPriceOptionList(paramMap);
	}
	public ProductPriceOption selectOnePriceOption(HashMap<String, Object> paramMap) {
		return mapper.selectOnePriceOption(paramMap);
	}
	public ArrayList<ProductPriceOption> selectListPriceOptionCalendarPossible(HashMap<String, Object> paramMap) {
		return mapper.selectListPriceOptionCalendarPossible(paramMap);
	}
	public ArrayList<ProductPriceOption> selectListPriceOptionProgramCalendarPossible(HashMap<String, Object> paramMap) {
		return mapper.selectListPriceOptionProgramCalendarPossible(paramMap);
	}

	public FixPriceList selectOneProductFixPriceList(HashMap<String, Object> param) {
		return mapper.selectOneProductFixPriceList( param );
	}

	public ArrayList<FixPriceList> selectListProductFixPrice(HashMap<String, Object> param) {
		return mapper.selectListProductFixPrice( param );
	}

	public DayList selectOneProductDayPriceList(HashMap<String, Object> param) {
		return mapper.selectOneProductDayPriceList( param );
	}

	public ArrayList<DayList> selectListProductDayPrice(HashMap<String, Object> param) {
		return mapper.selectListProductDayPrice( param );
	}

	public ArrayList<ProductTourImages> selectListProductImages(HashMap<String, Object> param) {
		return mapper.selectListProductImages( param );
	}

	public ArrayList<ProductDetailSchedule> selectListProductDetailSchedule(HashMap<String, Object> param) {
		return mapper.selectListProductDetailSchedule( param );
	}
//	public ArrayList<ProductPriceOption> selectListPriceOption(HashMap<String, Object> paramMap) {
//		return mapper.selectListPriceOption(paramMap);
//	}
	public ArrayList<ProductDetailScheduleImage> selectListProductTourDetailImage(HashMap<String, Object> paramMap) {
		return mapper.selectListProductTourDetailImage(paramMap);
	}
	public ProductInventory selectSummaryProductInventory(HashMap<String, Object> param) {
		return mapper.selectSummaryProductInventory( param );
	}
	@Transactional(rollbackFor = Exception.class)
	public void insertProductInventory(ProductInventory data) throws SQLException {
		mapper.insertProductInventory(data);
	}

	@Transactional
    public void insertProductComment(ProductComment productComment) {
        mapper.insertProductComment(productComment);

		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("menuId", productComment.getMenuId());
		MenuUser menu = menuService.selectOneMenu(paramMap);
		UserPointSet userPointSet = new UserPointSet();

		if (menu != null && menu.getPointUseYn() != null && menu.getPointUseYn().equals("Y")
			&& productComment.getUserRole().toUpperCase().equals("USER")) {
				if (menu.getCommentPointId() != null && menu.getCommentPointId() != 0 ) {
						userPointSet = userPointService.getUserPointSettingById(String.valueOf(menu.getCommentPointId()));
						if (userPointSet != null && userPointSet.getDeleteYn().equals("N")) {
							UserPointAccrued pointAccrued = new UserPointAccrued();
							pointAccrued.setUserEmail(productComment.getUserEmail());
							pointAccrued.setUserName(productComment.getUserName());
							pointAccrued.setAccruedType(userPointSet.getName());
							pointAccrued.setAccruedCode(userPointSet.getAccruedCode());
							pointAccrued.setPointAccrued(userPointSet.getAccruedPoint());
							pointAccrued.setExpireDate(userPointSet.getExpireDate());
							pointAccrued.setCreateId(productComment.getCreateId());

							userPointService.createUserPoint(pointAccrued);
						}
				}
		}
    }

    public ArrayList<ProductComment> selectListProductComment(HashMap<String, Object> paramProduct) {
        return mapper.selectListProductComment(paramProduct);
    }

	public ArrayList<ProductComment> selectProductComment(HashMap<String, Object> paramMap) {
		return mapper.selectProductComment(paramMap);
	}
    public int selectCountProductComment(HashMap<String, Object> paramMap) {
        return mapper.selectCountProductComment(paramMap);
    }
    public ArrayList<DayList> selectEarlyBirdPrice(HashMap<String, Object> paramProduct) {
        return mapper.selectEarlyBirdPrice(paramProduct);
    }
	public ProductPriceOption selectOneEarlyBirdPrice(HashMap<String, Object> paramProduct) {
        return mapper.selectOneEarlyBirdPrice(paramProduct);
    }
	public void updateProductViewCount(HashMap<String, Object> paramProduct) throws SQLException {
		mapper.updateProductViewCount(paramProduct);
	}


    public int getProductPaymentTypeCount(HashMap<String, Object> param) {
		return mapper.selectProductPaymentTypeCount(param);
    }

	public ArrayList<String> getCalendarResvPossibleList(ProductInfo product) throws SQLException {
		ArrayList<String> calendarResvPossibleList = new ArrayList<String>();
		HashMap<String, Object> rsvParam = new HashMap<>();
		LocalDate now = LocalDate.now();
		YearMonth currentYearMonth = product.getYear() == null && product.getMonth() == null ? YearMonth.from(now) : YearMonth.of(product.getYear(), product.getMonth());
		LocalDate startDateParam = currentYearMonth.atDay(1).minusMonths(1).withDayOfMonth(1).plusWeeks(3).plusDays(1);
		LocalDate endDateParam = currentYearMonth.atEndOfMonth().plusMonths(1).withDayOfMonth(7);

		rsvParam.put("startDate", startDateParam);
		rsvParam.put("endDate", endDateParam);
		rsvParam.put("productSerial", product.getProductSerial());
		ArrayList<Reservation> reservationList = reservationUtil.selectListCalcReservationForUser(rsvParam);

		int pendingStock = product.getPickPeopleCount() == null ? 1 : product.getPickPeopleCount();
		for (Reservation item : reservationList) {
			if ( item.getIsRestDate() == 1 ) {
				continue;
			}

			if ( product.getPolicyInventory().equals("1") ) {
				boolean isSpecialNextStepPass = true;
				//선택한 날짜범위안에 특가일때
				if ( item.getSpecialQuantity() != null ) {
					//예약재고 불가능 판단
					if ( isSpecialNextStepPass && item.getMaxCapacity()-item.getTotalOrderCount() < pendingStock  ) {
						continue;
					}
				}
				//선택한 날짜범위안에 정가일때
				if ( "remain".equals(item.getRsvPossible()) ){
					//예약재고 불가능 판단
					if ( item.getMaxCapacity() - item.getTotalOrderCount() < pendingStock  ) {
						continue;
					}
				}
	
				if ( !"remain".equals(item.getRsvPossible()) && !"remain".equals(item.getSpecialRsvPossible()) ) {
					continue;
				} 
			}

			calendarResvPossibleList.add(item.getDate());
		}

		return calendarResvPossibleList;
	}

	public ArrayList<Reservation> selectListReservation(HashMap<String, Object> paramMap) {
		return pageMapper.selectListReservation(paramMap);
	}

	//특가 존재안함. 프로그램 캘린더용
	public ArrayList<String> getCalendarOneProgramResvPossibleDayList(ProductInfo product) throws SQLException {
		ArrayList<String> calendarResvPossibleList = new ArrayList<String>();
		HashMap<String, Object> rsvParam = new HashMap<>();
		LocalDate now = LocalDate.now();
		YearMonth currentYearMonth = product.getYear() == null && product.getMonth() == null ? YearMonth.from(now) : YearMonth.of(product.getYear(), product.getMonth());
		LocalDate startDateParam = currentYearMonth.atDay(1).minusMonths(1).withDayOfMonth(1).plusWeeks(3).plusDays(1);
		LocalDate endDateParam = currentYearMonth.atEndOfMonth().plusMonths(1).withDayOfMonth(7);

		rsvParam.put("productSerial", product.getProductSerial());
		rsvParam.put("startDate", startDateParam);
		rsvParam.put("endDate", endDateParam);
		ArrayList<Reservation> reservationList = reservationUtil.selectListCalcReservationForUser(rsvParam);
		for (Reservation reservation : reservationList) {
			if ( product.getPolicyInventory().equals("1") ) {
				if ( "remain".equals(reservation.getRsvPossible().trim())) {
					int remainStock = reservation.getMaxCapacity() - reservation.getTotalOrderCount();
					calendarResvPossibleList.add(reservation.getDate()+","+reservation.getPriceOptionId()+","+remainStock+","+reservation.getProductPriceParam());
				}
			}
			else {
				calendarResvPossibleList.add(reservation.getDate()+","+reservation.getPriceOptionId()+",0,"+reservation.getProductPriceParam());
			}
			
		}

		return calendarResvPossibleList;
	}

	public ArrayList<String> getCalendarOneProgramResvNotCalendarPossibleDayList(ProductInfo product) throws SQLException {
		ArrayList<String> calendarResvPossibleList = new ArrayList<String>();
		HashMap<String, Object> rsvParam = new HashMap<>();
		LocalDate now = LocalDate.now();
		YearMonth currentYearMonth = product.getYear() == null && product.getMonth() == null ? YearMonth.from(now) : YearMonth.of(product.getYear(), product.getMonth());
		LocalDate startDateParam = currentYearMonth.atDay(1).minusMonths(1).withDayOfMonth(1).plusWeeks(3).plusDays(1);
		LocalDate endDateParam = currentYearMonth.atEndOfMonth().plusMonths(12);

		rsvParam.put("productSerial", product.getProductSerial());
		rsvParam.put("startDate", startDateParam);
		rsvParam.put("endDate", endDateParam);
		ArrayList<Reservation> reservationList = reservationUtil.selectListCalcReservationForUser(rsvParam);
		for (Reservation reservation : reservationList) {
			if ( product.getPolicyInventory().equals("1") ) {
				if ( "remain".equals(reservation.getRsvPossible().trim())) {
					int remainStock = reservation.getMaxCapacity() - reservation.getTotalOrderCount();
					calendarResvPossibleList.add(reservation.getDate()+","+reservation.getPriceOptionId()+","+remainStock+","+reservation.getProductPriceParam());
				}
			}
			else {
				calendarResvPossibleList.add(reservation.getDate()+","+reservation.getPriceOptionId()+",0,"+reservation.getProductPriceParam());
			}
			
		}

		return calendarResvPossibleList;
	}

	public ProductPriceOption selectOnePriceOptionById(HashMap<String, Object> paramMap) {
		return mapper.selectOnePriceOptionById(paramMap);
	}
	
	private String padDate(String date) {
		String[] parts = date.split("-");
		String year = parts[0];
		String month = parts[1].length() == 1 ? "0" + parts[1] : parts[1];
		String day = parts[2].length() == 1 ? "0" + parts[2] : parts[2];
		return year + "-" + month + "-" + day;
	}

    public ArrayList<DayList> selectOneProductOperationDay(HashMap<String, Object> paramProduct) {
		return mapper.selectOneProductOperationDay(paramProduct);
    }
}
