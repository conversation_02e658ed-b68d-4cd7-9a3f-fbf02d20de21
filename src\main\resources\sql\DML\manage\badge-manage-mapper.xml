<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.manage.BadgeManageMapper">
	<!-- ######################### contents ######################### -->
	<select id="selectCountBadgeContents" parameterType="HashMap" resultType="int">
		SELECT COUNT(*)
		FROM badge_contents AS bc
		LEFT JOIN badge_attach_image AS bai on bai.file_id = bc.badge_image_file_id
		WHERE delete_yn = 'N'
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)">
			<if test="useYn != 'ALL'">
				AND use_yn = #{useYn}
			</if>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
			AND CONCAT(COALESCE(badge_name, ''), COALESCE(badge_desc, '')) LIKE CONCAT('%', #{searchKey}, '%')
		</if>
	</select>

	<select id="selectListBadgeContents" parameterType="HashMap" resultType="BadgeContents">
		SELECT * FROM badge_contents AS bc
		LEFT JOIN badge_attach_image AS bai on bai.file_id = bc.badge_image_file_id
		WHERE delete_yn = 'N'
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)">
			<if test="useYn != 'ALL'">
				AND use_yn = #{useYn}
			</if>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
			AND CONCAT(COALESCE(badge_name, ''), COALESCE(badge_desc, '')) LIKE CONCAT('%', #{searchKey}, '%') </if>
         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
			ORDER BY
			<choose>
				<when test="sort == 'badgeId'">bc.badge_id</when>
				<when test="sort == 'title'">bc.title</when>
				<when test="sort == 'useYn'">bc.use_yn</when>
				<when test="sort == 'deleteYn'">bc.delete_yn</when>
				<when test="sort == 'lastUpdateDate'">bc.last_update_date</when>
				<otherwise>rownum</otherwise>
			</choose>
			<choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
		</if>
         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneBadgeContents" parameterType="HashMap" resultType="BadgeContents">
		SELECT *
		FROM badge_contents AS bc
		LEFT JOIN badge_attach_image AS bai on bai.file_id = bc.badge_image_file_id
		WHERE badge_id = #{badgeId} 
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(badgeType) and badgeType == 'normal'">
			AND badge_type = 'normal'
		</if>
	</select>

	<insert id="insertBadgeAttachImage" parameterType="BadgeAttachImage" useGeneratedKeys="true" keyProperty="fileId">
        INSERT INTO badge_attach_image
        (
            service_type,
            upload_path, upload_filename,
            file_extension, file_size, file_mimetype,
            origin_filename,
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >
            	upload_id,
            </if>
            upload_date
        )
        VALUES (
            #{serviceType},
            #{uploadPath}, #{uploadFilename},
            #{fileExtension}, #{fileSize}, #{fileMimetype},
            #{originFilename},
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >
            	#{uploadId},
            </if>
            now()
        )
    </insert>

	<insert id="insertBadgeContents" parameterType="BadgeContents">
        INSERT INTO badge_contents
           SET badge_type = #{badgeType},
               badge_desc = #{badgeDesc},
               badge_name = #{badgeName},
               badge_image_file_id = #{badgeImageFileId},
				badge_automation_type = #{badgeAutomationType},
				badge_automation_count = #{badgeAutomationCount},
			   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(badgeCategoryId)">
               badge_category_id = #{badgeCategoryId},
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)">
               use_yn = #{useYn},
               </if>
               create_id = #{createId},
               create_date = now()
    </insert>

	<update id="updateBadgeContents" parameterType="BadgeContents">
        UPDATE badge_contents
           SET badge_name = #{badgeName},
		   	    badge_type = #{badgeType},
				badge_automation_type = #{badgeAutomationType},
				badge_automation_count = #{badgeAutomationCount},
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(badgeDesc)">
               	   badge_desc = #{badgeDesc},
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(badgeImageFileId)">
               	   badge_image_file_id = #{badgeImageFileId},
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)">
                   use_yn = #{useYn},
               </if>
               last_update_id = #{lastUpdateId},
               last_update_date = now()
         WHERE delete_yn = 'N' AND badge_id = #{badgeId}
    </update>

	<update id="updateBadgeContentsDelete" parameterType="HashMap">
        UPDATE badge_contents
           SET delete_yn = 'Y', delete_id = #{deleteId}, delete_date = now()
         WHERE badge_id = #{badgeId}
    </update>

	<!-- ######################### acquire ######################### -->
	<insert id="insertBadgeAcquireHistory" parameterType="BadgeAcquireHistory">
		INSERT INTO badge_acquire_history
		   SET badge_id = #{badgeId},
		       user_email = #{userEmail},
		       create_id = #{createId},
		       create_date = now()
	</insert>

	<update id="updateBadgeAcquireHistory" parameterType="BadgeAcquireHistory">
		UPDATE badge_acquire_history
		SET delete_yn = 'Y', delete_id = #{deleteId}, delete_date = now()
		WHERE user_email = #{userEmail} AND badge_id = #{badgeId}
	</update>

	<!-- 뱃지 획득 카운트 -->
	<select id="selectCountBadgeAcquireHistory" parameterType="HashMap" resultType="int">
		SELECT COUNT(*)
		FROM badge_acquire_history AS bah
		LEFT JOIN badge_contents AS bc on bc.badge_id = bah.badge_id
		LEFT JOIN badge_attach_image AS bai on bai.file_id = bc.badge_image_file_id
		WHERE bah.delete_yn = 'N'
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)">
			<if test="useYn != 'ALL'">
				AND bah.use_yn = #{useYn}
			</if>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType)">
			<choose>
				<when test="searchType == 'userEmail'">
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
						AND bah.user_email LIKE CONCAT('%', #{searchKey}, '%')
					</if>
				</when>
				<when test="searchType == 'badgeName'">
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
						AND bc.badge_name LIKE CONCAT('%', #{searchKey}, '%')
					</if>
				</when>
				<when test="searchType == 'badgeDesc'">
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
						AND bc.badge_desc LIKE CONCAT('%', #{searchKey}, '%')
					</if>
				</when>
			</choose>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(badgeId)">
			AND bah.badge_id = #{badgeId}
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">
			AND bah.user_email = #{userEmail}
		</if>
	</select>

	<!-- 뱃지 획득 이력 -->
	<select id="selectListBadgeAcquireHistory" parameterType="HashMap" resultType="BadgeAcquireHistory">
		SELECT *, row_number() over(order by bah.create_date asc) as row_num
		FROM badge_acquire_history AS bah
		LEFT JOIN badge_contents AS bc on bc.badge_id = bah.badge_id
		LEFT JOIN badge_attach_image AS bai on bai.file_id = bc.badge_image_file_id
		LEFT JOIN `user` u on u.user_email = bah.user_email
		WHERE bah.delete_yn = 'N' AND u.user_role = 'USER'
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)">
			<if test="useYn != 'ALL'">
				AND bah.use_yn = #{useYn}
			</if>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType)">
			<choose>
				<when test="searchType == 'userEmail'">
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
						AND bah.user_email LIKE CONCAT('%', #{searchKey}, '%')
					</if>
				</when>
				<when test="searchType == 'badgeName'">
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
						AND bc.badge_name LIKE CONCAT('%', #{searchKey}, '%')
					</if>
				</when>
				<when test="searchType == 'badgeDesc'">
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
						AND bc.badge_desc LIKE CONCAT('%', #{searchKey}, '%')
					</if>
				</when>
			</choose>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">
			AND bah.user_email = #{userEmail}
		</if>
         ORDER BY row_num DESC
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
        </if>
	</select>


	<select id="selectBadgeListByAutomationType" parameterType="HashMap" resultType="BadgeContents">
		SELECT badge_id, badge_category_id,
		       badge_type, badge_name, badge_desc,
		       badge_image_file_id,
		       badge_automation_type, badge_automation_count,
		       use_yn
		  FROM badge_contents
		 WHERE use_yn = 'Y' AND delete_yn = 'N'
		 <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(badgeId)">
		 	AND badge_id = #{badgeId}
		 </if>
		 <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(badgeType) and badgeType == 'normal'">
				AND badge_type = 'normal'
		 </if>
		<choose>
			<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(automationType) and automationType == 'login'">
				AND badge_automation_type IN ('login', 'continuous-login')
			</when>
			<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(automationType) and automationType == 'board'">
				AND badge_automation_type IN ('board')
			</when>
			<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(automationType) and automationType == 'profile'">
				AND badge_automation_type IN ('profile')
			</when>
			<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(automationType) and automationType == 'program'">
				AND badge_automation_type IN ('program')
			</when>
			<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(automationType) and automationType == 'jamsisum'">
				AND badge_automation_type IN ('jamsisum')
			</when>
			<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(automationType) and automationType == 'mission'">
				AND badge_automation_type IN ('mission')
			</when>
			<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(automationType) and automationType == 'review'">
				AND badge_automation_type IN ('review')
			</when>
			<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(automationType) and automationType == 'point-achieved'">
				AND badge_automation_type IN ('point-achieved')
			</when>
			<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(automationType) and automationType == 'point-used'">
				AND badge_automation_type IN ('point-used')
			</when>
			
		</choose>
	</select>

	<select id="selectTotalLoginCount" parameterType="LoginUser" resultType="Integer">
		SELECT COUNT(*) FROM (
			 SELECT DATE_FORMAT(login_time, '%y%m%d'), COUNT(seq)
			   FROM user_login_session
			  WHERE user_email = #{userEmail}
			  GROUP BY DATE_FORMAT(login_time, '%y%m%d')
		) a
	</select>

	<select id="selectContinuousLoginCount" parameterType="LoginUser" resultType="Integer">
		SELECT max_consecutive_login_days FROM (
		   WITH LoginDates AS
		       	(SELECT user_email, DATE(login_time) AS login_date,
						DATEDIFF(DATE(login_time),
							LAG(DATE(login_time)) OVER (PARTITION BY user_email ORDER BY login_time)
						) AS date_diff
			   	   FROM user_login_session
			      WHERE user_email = #{userEmail}
		   		),
				ConsecutiveLogins AS (
					SELECT user_email, login_date,
						   CASE WHEN date_diff = 1 THEN 1
								WHEN date_diff IS NULL THEN 1
								ELSE 0
							END AS is_consecutive
					  FROM LoginDates
				),
				ConsecutiveLoginGroups AS (
					SELECT user_email, login_date,
						   is_consecutive,
						   SUM(CASE WHEN is_consecutive = 0 THEN 1 ELSE 0 END) OVER (PARTITION BY user_email ORDER BY login_date) AS grp
					  FROM ConsecutiveLogins
				),
				ConsecutiveLoginStreaks AS (
					SELECT user_email, grp,
						   COUNT(*) AS consecutive_days
					  FROM ConsecutiveLoginGroups
					 WHERE is_consecutive = 1
					 GROUP BY user_email, grp
				)
		   SELECT user_email, MAX(consecutive_days) AS max_consecutive_login_days
		     FROM ConsecutiveLoginStreaks
		    GROUP BY user_email
		    ORDER BY max_consecutive_login_days DESC
		) a
	</select>
</mapper>