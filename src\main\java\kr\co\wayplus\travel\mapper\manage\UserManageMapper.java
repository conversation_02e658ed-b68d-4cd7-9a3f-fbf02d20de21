package kr.co.wayplus.travel.mapper.manage;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import kr.co.wayplus.travel.model.UserAttachFile;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.UserCustomerCounsel;
import kr.co.wayplus.travel.model.UserCustomerInfo;
import kr.co.wayplus.travel.model.UserCustomerOrder;
import kr.co.wayplus.travel.model.UserCustomerOrderHistory;
import kr.co.wayplus.travel.model.UserCustomerOrderList;
import kr.co.wayplus.travel.model.UserCustomerPayment;
import kr.co.wayplus.travel.model.UserGroup;
import kr.co.wayplus.travel.model.UserGroupConnect;

@Mapper
@Repository
public interface UserManageMapper {


	ArrayList<HashMap> selectUserAccountStatusCount(HashMap<String, Object> param);

    int selectUserListCount(HashMap<String, Object> param);

    ArrayList<LoginUser> selectUserList(HashMap<String, Object> param);

    void updateUserAccountStatus(HashMap<String, Object> param);

    //LoginUser selectUserDetail(String userEmail);

    LoginUser selectUserDetail(HashMap<String, Object> param);

    void insertUser(LoginUser user);

    void updateMyUserInfo(LoginUser user) throws SQLException;

    UserCustomerInfo selectUserCustomerInfo(String userEmail);

    void updateUserInfoByManager(LoginUser user);

    void updateUserInfoByPassportImgPath(LoginUser user);

    void insertUserCustomerInfo(UserCustomerInfo customerInfo);

    int selectUserCustomerCounselListCount(HashMap<String, Object> param);

    ArrayList<UserCustomerCounsel> selectUserCustomerCounselList(HashMap<String, Object> userEmail);
    UserCustomerCounsel selectUserCustomerCounsel(HashMap<String, Object> param);

    void insertUserCustomerCounsel(UserCustomerCounsel counsel);

	void deleteUserCustomerCounsel(UserCustomerCounsel counsel) throws Exception;

	void restoreUserCustomerCounsel(UserCustomerCounsel counsel) throws Exception;

	void updateUserAccountSimpleInfo(HashMap<String, Object> param);

	void updateUserShowYn(LoginUser user);

//	<!--################################### UserCustomerPayment( ###################################-->
	int selectCountUserCustomerPayment(HashMap<String, Object> paramMap);
	int selectCountUserCustomerPayment(UserCustomerPayment data);
	ArrayList<UserCustomerPayment> selectListUserCustomerPayment(HashMap<String, Object> paramMap);
	ArrayList<UserCustomerPayment> selectListUserCustomerPayment(UserCustomerPayment data);
	UserCustomerPayment selectOneUserCustomerPayment(HashMap<String, Object> paramMap);
	ArrayList<HashMap<String, Object>> selectListInquiryCountStatusType(HashMap<String, Object> paramMap);
	void insertUserCustomerPayment(UserCustomerPayment data) throws SQLException;
	void updateUserCustomerPayment(UserCustomerPayment data) throws SQLException;
	void restoreUserCustomerPayment(UserCustomerPayment data) throws SQLException;
	void deleteUserCustomerPayment(UserCustomerPayment data) throws SQLException;

	int selectCountUserCustomerPaymentVirtual(HashMap<String, Object> param);
	ArrayList<HashMap<String, Object>> selectListUserCustomerPaymentVirtual(HashMap<String, Object> param);

	HashMap<String, Object> selectListUserCustomerPaymentVirtualTotal(HashMap<String, Object> param);
//	<!--################################### UserCustomerOrder ###################################-->
	int selectCountUserCustomerOrder(HashMap<String, Object> paramMap);
	ArrayList<UserCustomerOrder> selectListUserCustomerOrder(HashMap param);
	UserCustomerOrder selectOneUserCustomerOrder(HashMap param);
	void insertUserCustomerOrder(UserCustomerOrder ord) throws SQLException;
	void updateUserCustomerOrder(UserCustomerOrder ord) throws SQLException;
//	<!--################################### UserCustomerPayment( ###################################-->
	Integer selectCountUserCustomerOrderList(HashMap param);
	ArrayList<UserCustomerOrderList> selectListUserCustomerOrderList(HashMap param);
	UserCustomerOrderList selectOneUserCustomerOrderList(HashMap param);
	void insertUserCustomerOrderList(UserCustomerOrderList ord) throws SQLException;
	void updateUserCustomerOrderList(UserCustomerOrderList ord);
	void deleteUserCustomerOrderList(UserCustomerOrderList ord);
//	<!--################################### UserCustomerOrderHistory ###################################-->
	int selectCountUserCustomerOrderHistory(HashMap<String, Object> paramMap);
	ArrayList<UserCustomerOrderHistory> selectListUserCustomerOrderHistory(HashMap param);
	UserCustomerOrderHistory selectOneUserCustomerOrderHistory(HashMap param);
	void insertUserCustomerOrderHistory(UserCustomerOrderHistory ord) throws SQLException;

	//	<!--################################### UserAttachFile ###################################-->
	Integer selectCountUserAttachFile(HashMap<String, Object> paramMap);
	ArrayList<UserAttachFile> selectListUserAttachFile(HashMap<String, Object> paramMap);
	UserAttachFile selectOneUserAttachFile(HashMap<String, Object> paramMap);
	void insertUserAttachFile(UserAttachFile baf);
	void deleteUserAttachFile(UserAttachFile baf);

	//	<!--################################### UserGroup ###################################-->
	Integer selectCountUserGroup(HashMap<String, Object> paramMap);
	ArrayList<UserGroup> selectListUserGroup(HashMap<String, Object> paramMap);
	UserGroup selectOneUserGroup(HashMap<String, Object> paramMap);
	void insertUserGroup(UserGroup ug) throws SQLException;
	void updateUserGroup(UserGroup ug) throws SQLException;
	void deleteUserGroup(UserGroup ug) throws SQLException;
	void restoreUserGroup(UserGroup ug) throws SQLException;
	//	<!--################################### UserGroupConnect ###################################-->
	Integer selectCountUserGroupConnect(HashMap<String, Object> paramMap);
	ArrayList<UserGroupConnect> selectListUserGroupConnect(HashMap<String, Object> paramMap);
	UserGroupConnect selectOneUserGroupConnect(HashMap<String, Object> paramMap);
	void insertUserGroupConnect(UserGroupConnect ugc) throws SQLException;
	void deleteUserGroupConnect(UserGroupConnect ugc) throws SQLException;

	int selectCountUserGroupCustomer(HashMap<String, Object> paramMap);
	ArrayList<LoginUser> selectListUserGroupCustomer(HashMap<String, Object> paramMap);

    ArrayList<LoginUser> selectUserListByRole(HashMap<String, Object> userParam);

    int selectCountUserListByRole(HashMap<String, Object> userParam);


}
