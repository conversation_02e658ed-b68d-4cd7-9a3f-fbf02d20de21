<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.user.UserMapper">

    <select id="selectUserByUserid" parameterType="HashMap" resultType="LoginUser">
        SELECT user_email, user_password, user_name, user_nick_name, user_nationality,
               user_birthday, user_gender, user_profile_image,
               user_mobile, user_tel, user_role,
               user_addr_zipcode, user_addr_extra,
               user_addr_jibun, user_addr_road, user_addr_detail,
               user_class_code, user_class_name,
               user_group_code, user_token_id,
               user_grade_id, user_grade,
               user_grade_start, user_grade_end,
               user_membership_id, user_membership_grade,
               user_membership_start, user_membership_end,
               user_verified_email, user_verified_mobile,
               user_ci, user_di, user_di_corp,
               user_join_type, user_auth_id,
               user_join_date, user_modify_date,
               last_login_date, last_password_date,
               last_login_fail_count, account_status,
               naver_token, kakao_token, google_token, facebook_token,
               secondary_email,
               mailing_yn,
               DATE_FORMAT(user_membership_start, '%Y. %m. %d') AS user_membership_start_format,
               DATE_FORMAT(user_membership_end, '%Y. %m. %d') AS user_membership_end_format,
               user_intro_page_link, user_intro, user_show_yn, user_island_life_id, user_profile_image,
               user_get_like_count, user_send_like_count, user_wrote_comment_count,
               join_root_type, join_root, join_reason_type, join_reason, user_island_life_id, user_live_place_type, user_live_place
          FROM user
        WHERE user_role != 'MASTER' AND account_status IN ('active', 'inactive')
            <choose>
                <when test="registrationId == 'kakao'">
                    AND kakao_token = #{id}
                </when>
                <when test="registrationId == 'naver'">
                    AND naver_token = #{id}
                </when>
                <when test="registrationId == 'google'">
                    AND google_token = #{id}
                </when>
                <otherwise>
                    AND user_email = #{id}
                </otherwise>
            </choose>
    </select>

    <select id="selectUserByNameMobile" parameterType="String" resultType="LoginUser">
        SELECT user_email, user_password,
               user_name, user_nick_name, user_nationality,
               user_birthday, user_gender,
               user_mobile, user_tel, user_role,
               user_addr_zipcode, user_addr_extra,
               user_addr_jibun, user_addr_road, user_addr_detail,
               user_class_code, user_class_name,
               user_group_code, user_token_id,
               user_grade_id, user_grade,
               user_grade_start, user_grade_end,
               user_membership_id, user_membership_grade,
               user_membership_start, user_membership_end,
               user_verified_email, user_verified_mobile,
               user_ci, user_di, user_di_corp,
               user_join_type, user_auth_id,
               user_join_date, user_modify_date,
               last_login_date, last_password_date,
               last_login_fail_count, account_status,
               naver_token, kakao_token, google_token, facebook_token,
               secondary_email, mailing_yn, DATE_FORMAT(user_membership_start, '%Y. %m. %d') AS user_membership_start_format,
               DATE_FORMAT(user_membership_end, '%Y. %m. %d') AS user_membership_end_format,
               user_intro_page_link, user_intro, user_show_yn, user_island_life_id, user_profile_image,
               user_get_like_count, user_send_like_count, user_wrote_comment_count,
               join_root_type, join_root, join_reason_type, join_reason, user_live_place_type, user_live_place
        FROM user
        WHERE user_name = #{userName} AND user_mobile = #{userMobile} AND user_role != 'MASTER'
          AND account_status IN ('active', 'inactive')
    </select>

    <insert id="insertUserLoginLog" parameterType="HashMap">
        INSERT INTO user_login_session
           SET user_email = #{userEmail},
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(loginType)">
               login_type = #{loginType},
               </if>
               login_session = #{sessionId},
               login_ip = #{loginIp},
               login_agent = #{userAgent},
               login_referer = #{referer},
               login_time = now()
    </insert>

    <update id="updateUserLastLoginDate" parameterType="LoginUser">
        UPDATE user SET last_login_date = now(), last_login_fail_count = 0
         WHERE user_email = #{userEmail} AND user_name = #{userName}
    </update>


    <update id="updateUserSessionLogout" parameterType="LoginUserSession">
        UPDATE user_login_session
        SET logout_time = now(), logout_type = #{logoutType}
        WHERE user_email = #{userEmail} AND login_session = #{loginSession}
    </update>

    <insert id="insertUserLoginAttemptLog" parameterType="LoginAttemptLog">
        INSERT INTO user_login_attempt_log
        (user_email, attempt_ip,
         attempt_agent, attempt_referer, attempt_time,
         error_code, error_message)
        VALUES (#{userEmail}, #{attemptIp},
                #{attemptAgent}, #{attemptReferer}, now(),
                #{errorCode}, #{errorMessage})
    </insert>

    <select id="selectUserCountById" parameterType="HashMap" resultType="Integer">
        SELECT COUNT(*)
          FROM user
         WHERE user_role != 'MASTER' AND account_status IN ('active', 'inactive')
        <choose>
            <when test="registrationId == 'kakao'">
                AND kakao_token = #{id}
            </when>
            <when test="registrationId == 'naver'">
                AND naver_token = #{id}
            </when>
            <when test="registrationId == 'google'">
                AND google_token = #{id}
            </when>
            <otherwise>
                AND user_email = #{id}
            </otherwise>
        </choose>
    </select>

    <insert id="insertNewUser" parameterType="LoginUser">
        INSERT INTO user
           SET user_email = #{userEmail}
               , user_password = #{userPassword}
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userJoinType)">, user_join_type = #{userJoinType}</if>
               , user_name = #{userName}
               , user_mobile = #{userMobile}
               , user_tel = #{userTel}
               , user_addr_zipcode = #{userAddrZipcode}
               , user_addr_extra = #{userAddrExtra}
               , user_addr_jibun = #{userAddrJibun}
               , user_addr_road = #{userAddrRoad}
               , user_addr_detail = #{userAddrDetail}
               , user_nationality = #{userNationality}
               , user_birthday = #{userBirthday}
               , user_gender = #{userGender}
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userRole)">, user_role = #{userRole}</if>
               , user_class_name = #{userClassName}
               , user_class_code = #{userClassCode}
               , user_group_code = #{userGroupCode}
               , user_token_id = #{userTokenId}
               , user_grade = #{userGrade}
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userVerifiedEmail)">, user_verified_email = #{userVerifiedEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userVerifiedMobile)">, user_verified_mobile = #{userVerifiedMobile}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userCi)">, user_ci = #{userCi}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userDi)">, user_di = #{userDi}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userDiCorp)">, user_di_corp = #{userDiCorp}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverToken)">, naver_token = #{naverToken}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverEmail)">, naver_email = #{naverEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverToken)">, naver_join_date = now()</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoToken)">, kakao_token = #{kakaoToken}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoEmail)">, kakao_email = #{kakaoEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoToken)">, kakao_join_date = now()</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleToken)">, google_token = #{googleToken}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleEmail)">, google_email = #{googleEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleToken)">, google_join_date = now()</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookToken)">, facebook_token = #{facebookToken}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookEmail)">, facebook_email = #{facebookEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookToken)">, facebook_join_date = now()</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secondaryEmail)">, secondary_email = #{secondaryEmail}</if>
               <if test="privacyRetentionDays > 0">, privacy_retention_days = #{privacyRetentionDays}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mailingYn)">, mailing_yn = #{mailingYn}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userLivePlaceType)">, user_live_place_type = #{userLivePlaceType}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userLivePlace)">, user_live_place = #{userLivePlace}</if>
        ON DUPLICATE KEY UPDATE
            account_status = 'active'
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverToken)">, naver_token = #{naverToken}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverEmail)">, naver_email = #{naverEmail}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverToken)">, naver_join_date = now()</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoToken)">, kakao_token = #{kakaoToken}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoEmail)">, kakao_email = #{kakaoEmail}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoToken)">, kakao_join_date = now()</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleToken)">, google_token = #{googleToken}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleEmail)">, google_email = #{googleEmail}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleToken)">, google_join_date = now()</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookToken)">, facebook_token = #{facebookToken}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookEmail)">, facebook_email = #{facebookEmail}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookToken)">, facebook_join_date = now()</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userLivePlaceType)">, user_live_place_type = #{userLivePlaceType}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userLivePlace)">, user_live_place = #{userLivePlace}</if>
    </insert>

    <insert id="insertWithdrawalUser" parameterType="LoginUser">
        INSERT INTO user_stored_withdrawal(
                    user_email, user_password, user_join_type,
                    user_name, user_nationality, user_birthday, user_gender,
                    user_mobile, user_tel,
                    user_addr_zipcode, user_addr_extra,
                    user_addr_jibun, user_addr_road, user_addr_detail,
                    user_role, user_class_name, user_class_code,
                    user_group_code, user_token_id,
                    user_grade_id, user_grade, user_grade_start, user_grade_end,
                    user_membership_id, user_membership_grade, user_membership_start, user_membership_end,
                    user_verified_email, user_verified_mobile,
                    user_ci, user_di, user_di_corp,
                    user_join_date, user_modify_date, last_login_date,
                    last_password_date, last_login_fail_count, account_status,
                    naver_token, naver_email, naver_join_date,
                    kakao_token, kakao_email, kakao_join_date,
                    google_token, google_email, google_join_date,
                    facebook_token, facebook_email, facebook_join_date,
                    secondary_email, privacy_retention_days, mailing_yn,
                    delete_date, delete_id)
        SELECT user_email, user_password, user_join_type,
               user_name, user_nationality, user_birthday, user_gender,
               user_mobile, user_tel,
               user_addr_zipcode, user_addr_extra,
               user_addr_jibun, user_addr_road, user_addr_detail,
               user_role, user_class_name, user_class_code,
               user_group_code, user_token_id,
               user_grade_id, user_grade, user_grade_start, user_grade_end,
               user_membership_id, user_membership_grade, user_membership_start, user_membership_end,
               user_verified_email, user_verified_mobile,
               user_ci, user_di, user_di_corp,
               user_join_date, user_modify_date, last_login_date,
               last_password_date, last_login_fail_count, account_status,
               naver_token, naver_email, naver_join_date,
               kakao_token, kakao_email, kakao_join_date,
               google_token, google_email, google_join_date,
               facebook_token, facebook_email, facebook_join_date,
               secondary_email, privacy_retention_days, mailing_yn,
               now(), #{userEmail}
          FROM user
         WHERE user_email = #{userEmail} AND user_token_id = #{userTokenId}
           AND user_role = 'USER'
    </insert>

    <delete id="deleteUserByUserid" parameterType="LoginUser">
        DELETE FROM user
         WHERE user_email = #{userEmail} AND user_token_id = #{userTokenId}
           AND user_role = 'USER'
    </delete>

    <select id="selectUserListByUserName" parameterType="HashMap" resultType="LoginUser">
        SELECT user_email, user_name, user_nick_name, user_mobile
          FROM user
         WHERE user_name =  #{userName}
           AND REPLACE(user_mobile, '-', '') = REPLACE(#{userMobile}, '-', '')
           AND account_status IN ('active', 'inactive')
    </select>

    <select id="selectRePasswordUserByUserInfo" parameterType="HashMap" resultType="LoginUser">
        SELECT user_email, user_token_id, user_name, user_mobile
          FROM user
         WHERE user_email = #{userEmail} AND user_name =  #{userName}
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userMobile)">AND REPLACE(user_mobile, '-', '') = REPLACE(#{userMobile}, '-', '')</if>
           AND account_status IN ('active', 'inactive')
    </select>

    <update id="updateUserPassword" parameterType="LoginUser">
        UPDATE user SET user_password = #{userPassword}
         WHERE user_email = #{userEmail} AND user_token_id = #{userTokenId}
           AND account_status IN ('active', 'inactive')
    </update>

    <select id="selectUserListByRole" parameterType="map" resultType="LoginUser">
        SELECT user_email, user_name, user_nick_name, user_mobile, user_role,
               user_join_date, last_login_date, naver_token, kakao_token, user_profile_image, user_intro, user_intro_page_link, user_live_place_type, user_live_place
        FROM user as u
        WHERE account_status IN ('active', 'inactive')
          AND user_role = #{role}
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userShowYn)">AND user_show_yn = #{userShowYn}</if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">AND user_email = #{userEmail}</if>
        ORDER BY user_join_date
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
            LIMIT #{itemStartPosition}, #{pagePerSize}
        </if>
    </select>

    <select id="selectCountUserListByRole" parameterType="map" resultType="int">
        SELECT COUNT(*)
        FROM user as u
        WHERE account_status IN ('active', 'inactive')
          AND user_role = #{role}
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userShowYn)">AND user_show_yn = #{userShowYn}</if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">AND user_email = #{userEmail}</if>
    </select>

    <update id="updateUserNewTokenId" parameterType="LoginUser">
        UPDATE user
           SET user_token_id = #{userTokenId}
         WHERE user_email = #{userEmail}
    </update>

    <update id="updateUserWebLog" parameterType="HashMap">
        UPDATE webservice_log
           SET user_token = #{after}, tracking = 'Y'
         WHERE user_token = #{before} AND tracking = 'N'
    </update>

    <insert id="insertUserWebLog" parameterType="WebServiceLog">
        INSERT INTO webservice_log
               (user_token, user_email,
                referer, request_uri, request_params, request_time,
                request_host, request_agent,
                session_id, response_status, tracking)
        VALUES (#{userToken}, #{userEmail},
                #{referer}, #{requestUri}, #{requestParams}, now(),
                #{requestHost}, #{requestAgent},
                #{sessionId}, #{responseStatus}, #{tracking})
    </insert>

    <update id="updateUserInfo" parameterType="LoginUser">
        UPDATE user
           SET <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)">user_name = #{userName},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userNickName)">user_nick_name = #{userNickName},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userMobile)">user_mobile = #{userMobile},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userProfileImage)">user_profile_image = #{userProfileImage},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userGender)">user_gender = #{userGender},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userBirthday)">user_birthday = #{userBirthday},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userIntroPageLink)">user_intro_page_link = #{userIntroPageLink},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(joinRootType)">join_root_type = #{joinRootType},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(joinReasonType)">join_reason_type = #{joinReasonType},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userShowYn)">user_show_yn = #{userShowYn},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userIslandLifeId)">user_island_life_id = #{userIslandLifeId},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPassword)">user_password = #{userPassword},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPassword)">last_password_date = NOW(),</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userClassName)">user_class_name = #{userClassName},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userClassCode)">user_class_code = #{userClassCode},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secondaryEmail)">secondary_email = #{secondaryEmail},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userAddrZipcode)">user_addr_zipcode = #{userAddrZipcode},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userAddrExtra)">user_addr_extra = #{userAddrExtra},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userAddrJibun)">user_addr_jibun = #{userAddrJibun},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mailingYn)">mailing_yn = #{mailingYn},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userGrade)">user_grade = #{userGrade},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userGetLikeCount)">user_get_like_count = #{userGetLikeCount},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userLivePlaceType)">user_live_place_type = #{userLivePlaceType},</if> 
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userLivePlace)">user_live_place = #{userLivePlace},</if> 
               user_intro = #{userIntro},
               join_root = #{joinRoot},
               join_reason = #{joinReason},
               user_addr_road = #{userAddrRoad},
               user_addr_detail = #{userAddrDetail},
               user_modify_date = now()
         WHERE user_email = #{userEmail} AND user_token_id = #{userTokenId}
    </update>

    <!-- 청풍용 -->
    <update id="updateUserFavoriteCount" parameterType="map">
        UPDATE user
           <set>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(updateType)">
                <choose>
                    <when test="updateType == 'send'">
                        <choose>
                            <when test="favoriteType == 'plus'">
                                user_send_like_count = user_send_like_count + 1,
                            </when>
                            <when test="favoriteType == 'minus'">
                                user_send_like_count = user_send_like_count - 1,
                            </when>
                        </choose>
                    </when>
                    <when test="updateType == 'get'">
                        <choose>
                            <when test="favoriteType == 'plus'">
                                user_get_like_count = user_get_like_count + 1,
                            </when>
                            <when test="favoriteType == 'minus'">
                                user_get_like_count = user_get_like_count - 1,
                            </when>
                        </choose>
                    </when>
                </choose>
               </if>
               user_modify_date = now()
           </set>
         WHERE user_email = #{userEmail}
    </update>
    <!-- 청풍용 -->
    <select id="selectCountUserComment" parameterType="HashMap" resultType="int">
        SELECT SUM(cnt)
            FROM
                (   SELECT COUNT(*) as cnt
                    FROM board_comment bc
                    WHERE  delete_yn = 'N' AND create_id = #{userEmail}
            UNION ALL
                    SELECT COUNT(*) as cnt
                    FROM product_comment pc
                    WHERE use_yn = 'Y' AND delete_yn = 'N' AND create_id = #{userEmail}
            ) as count
    </select>
    <!-- 청풍용 -->
    <update id="updateUserIntroPageLink" parameterType="HashMap">
        UPDATE user
           SET user_intro_page_link = #{userIntroPageLink}
         WHERE user_email = #{userEmail}
    </update>

    <select id="selectOAuthUserCount" resultType="Integer">
        SELECT COUNT(*) FROM user
         WHERE user_role = 'USER'
            <choose>
                <when test="registrationId == 'kakao'">
                AND kakao_token = #{id}
                </when>
                <when test="registrationId == 'naver'">
                AND naver_token = #{id}
                </when>
                <when test="registrationId == 'google'">
                AND google_token = #{id}
                </when>
                <otherwise>
                AND user_token_id = #{id}
                </otherwise>
            </choose>
    </select>

    <select id="selectOAuthUser" resultType="OAuthUser">
        SELECT user_email, user_password,
               user_name, user_nick_name, user_nationality,
               user_birthday, user_gender,
               user_mobile, user_tel, user_role,
               user_addr_zipcode, user_addr_extra,
               user_addr_jibun, user_addr_road, user_addr_detail,
               user_class_code, user_class_name,
               user_group_code, user_token_id,
               user_grade_id, user_grade,
               user_grade_start, user_grade_end,
               user_membership_id, user_membership_grade,
               user_membership_start, user_membership_end,
               user_verified_email, user_verified_mobile,
               user_ci, user_di, user_di_corp,
               user_join_type, user_join_date, user_modify_date,
               last_login_date, last_password_date,
               last_login_fail_count, account_status,
               naver_token, kakao_token, google_token, facebook_token,
               secondary_email, mailing_yn, user_live_place_type, user_live_place
          FROM user
         WHERE user_role != 'MASTER' AND account_status IN ('active', 'inactive')
            <choose>
                <when test="registrationId == 'kakao'">
                AND kakao_token = #{id}
                </when>
                <when test="registrationId == 'naver'">
                AND naver_token = #{id}
                </when>
                <when test="registrationId == 'google'">
                AND google_token = #{id}
                </when>
                <otherwise>
                AND user_token_id = #{id}
                </otherwise>
            </choose>
    </select>

    <insert id="insertOAuthUser" parameterType="OAuthUser">
        INSERT INTO user
           SET user_email = #{userEmail}
               , user_password = #{userPassword}
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userJoinType)">, user_join_type = #{userJoinType}</if>
               , user_name = #{userName}
               , user_mobile = #{userMobile}
               , user_tel = #{userTel}
               , user_addr_zipcode = #{userAddrZipcode}
               , user_addr_extra = #{userAddrExtra}
               , user_addr_jibun = #{userAddrJibun}
               , user_addr_road = #{userAddrRoad}
               , user_addr_detail = #{userAddrDetail}
               , user_nationality = #{userNationality}
               , user_birthday = #{userBirthday}
               , user_gender = #{userGender}
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userRole)">, user_role = #{userRole}</if>
               , user_class_name = #{userClassName}
               , user_class_code = #{userClassCode}
               , user_group_code = #{userGroupCode}
               , user_token_id = #{userTokenId}
               , user_grade = #{userGrade}
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userVerifiedEmail)">, user_verified_email = #{userVerifiedEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userVerifiedMobile)">, user_verified_mobile = #{userVerifiedMobile}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userCi)">, user_ci = #{userCi}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userDi)">, user_di = #{userDi}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userDiCorp)">, user_di_corp = #{userDiCorp}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverToken)">, naver_token = #{naverToken}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverEmail)">, naver_email = #{naverEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverToken)">, naver_join_date = now()</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoToken)">, kakao_token = #{kakaoToken}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoEmail)">, kakao_email = #{kakaoEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoToken)">, kakao_join_date = now()</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleToken)">, google_token = #{googleToken}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleEmail)">, google_email = #{googleEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleToken)">, google_join_date = now()</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookToken)">, facebook_token = #{facebookToken}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookEmail)">, facebook_email = #{facebookEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookToken)">, facebook_join_date = now()</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secondaryEmail)">, secondary_email = #{secondary_email}</if>
               <if test="privacyRetentionDays > 0">, privacy_retention_days = #{privacyRetentionDays}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mailingYn)">, mailing_yn = #{mailingYn}</if>
			ON DUPLICATE KEY UPDATE
				account_status = 'active'
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverToken)">, naver_token = #{naverToken}</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverEmail)">, naver_email = #{naverEmail}</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverToken)">, naver_join_date = now()</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoToken)">, kakao_token = #{kakaoToken}</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoEmail)">, kakao_email = #{kakaoEmail}</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoToken)">, kakao_join_date = now()</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleToken)">, google_token = #{googleToken}</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleEmail)">, google_email = #{googleEmail}</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleToken)">, google_join_date = now()</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookToken)">, facebook_token = #{facebookToken}</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookEmail)">, facebook_email = #{facebookEmail}</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookToken)">, facebook_join_date = now()</if>
    </insert>

    <update id="updateOAuthUserLastLoginDate" parameterType="OAuthUser">
        UPDATE user SET last_login_date = now(), last_login_fail_count = 0
        WHERE user_email = #{userEmail} AND user_name = #{userName}
    </update>

    <select id="selectUserByIdToken" parameterType="HashMap" resultType="LoginUser">
        SELECT user_email, user_name, user_nick_name, user_token_id FROM user
         WHERE user_email = #{userEmail} AND user_token_id = #{userToken}
    </select>

    <update id="updateUserSubscribeInfo" parameterType="LoginUser">
        UPDATE user
        SET mailing_yn = #{mailingYn}
            , user_modify_date = NOW()
            <choose>
                <when test="mailingYn eq 'Y'.toString()">
                    , user_membership_grade = 'mailing'
                    , user_membership_start = NOW()
                    , user_membership_end = DATE_ADD(NOW(), INTERVAL 1 MONTH)
                </when>
                <when test="mailingYn eq 'N'.toString()">
                    , user_membership_grade = NULL
                    , user_membership_start = NULL
                    , user_membership_end = NULL
                </when>
            </choose>
        WHERE user_email = #{userEmail} AND user_token_id = #{userTokenId}
    </update>

    <select id="selectUserPayTotalSummary" parameterType="Hashmap" resultType="UserCustomerPayment">
        <![CDATA[
        SELECT user_email,
               SUM(pay_amount) pay_amount
        FROM user_customer_payment
        WHERE user_email = #{userEmail} AND delete_yn = 'N' AND pay_date >= DATE_SUB(CURDATE(), INTERVAL 3 YEAR)
        AND pay_date <= CURDATE()
        GROUP BY user_email
        ]]>
    </select>

    <insert id="insertUserSubscribeServiceInfo" parameterType="UserSubscribeServiceInfo">
        INSERT INTO user_subscribe_service_info
        SET product_tour_id= #{productTourId}
        , user_email = #{userEmail}
        , user_grade = #{userGrade}
        , general_price = #{generalPrice}
        , subscribe_price = #{subscribePrice}
        , create_id = #{createId}
        , create_date= NOW()
    </insert>

    <select id="selectUserSubscribeServiceInfoListCount" parameterType="Hashmap" resultType="Integer">
        SELECT COUNT(*)
        FROM user_subscribe_service_info ussi
                 LEFT JOIN product_tour pt on pt.product_tour_id = ussi.product_tour_id
        WHERE user_email = #{userEmail} AND ussi.delete_yn = 'N'
    </select>

    <select id="selectUserSubscribeServiceInfoList" parameterType="Hashmap" resultType="UserSubscribeServiceInfo">
        SELECT id, user_email, general_price, subscribe_price, DATE_FORMAT(ussi.create_date, '%Y. %m. %d') create_date, pt.product_title
        FROM user_subscribe_service_info ussi
        LEFT JOIN product_tour pt on pt.product_tour_id = ussi.product_tour_id
        WHERE user_email = #{userEmail} AND ussi.delete_yn = 'N'
    </select>

    <select id="selectCountUserFavorite" parameterType="Hashmap" resultType="int">
        SELECT COUNT(*)
        FROM user_favorites AS uf
        LEFT JOIN place_spot AS ps ON ps.ts_id = uf.ts_id
        WHERE user_email = #{userEmail}
        AND uf.ts_id IS NOT NULL
        AND uf.delete_yn = 'N'
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(selectType)">
            <choose>
                <when test="selectType == 'place'"> AND uf.ts_id IS NOT NULL</when>
                <when test="selectType == 'product'"> AND uf.product_serial IS NOT NULL</when>
            </choose>
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(titleLike)">and ps.ts_title like concat('%',#{titleLike},'%')</if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsId)">AND uf.ts_id = #{tsId}</if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(areaType)">and ps.area_type=#{areaType}	</if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(placeType)">
            <choose>
                <when test="placeType == 'stay'">
                    AND place_type = 'stay'
                </when>
                <when test="placeType == 'traffic'">
                    AND place_type = 'traffic'
                </when>
                <otherwise>
                    AND place_type != 'stay' AND place_type != 'traffic'
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="selectListUserFavorite" parameterType="Hashmap" resultType="UserFavorite">
        SELECT uf.id, uf.ts_id, uf.into_time, ps.menu_id, ps.ts_title, ps.area_type
        , ps.place_type, ps.ts_road, ps.ts_append, ps.ts_price
        FROM user_favorites AS uf
        LEFT JOIN place_spot AS ps ON ps.ts_id = uf.ts_id
        WHERE user_email = #{userEmail}
        AND uf.ts_id IS NOT NULL
        AND uf.delete_yn = 'N'
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(selectType)">
            <choose>
                <when test="selectType == 'place'"> AND uf.ts_id IS NOT NULL</when>
                <when test="selectType == 'product'"> AND uf.product_serial IS NOT NULL</when>
            </choose>
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(titleLike)">and ps.ts_title like concat('%',#{titleLike},'%')</if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsId)">AND uf.ts_id = #{tsId}</if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(areaType)">and ps.area_type=#{areaType}	</if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(placeType)">
            <choose>
                <when test="placeType == 'stay'">
                    AND place_type = 'stay'
                </when>
                <when test="placeType == 'traffic'">
                    AND place_type = 'traffic'
                </when>
                <otherwise>
                    AND place_type != 'stay' AND place_type != 'traffic'
                </otherwise>
            </choose>
        </if>
        ORDER BY uf.into_time DESC
    </select>

    <select id="selectOneUserFavorite" parameterType="UserFavorite" resultType="UserFavorite">
        SELECT *
        FROM user_favorites
        WHERE user_email = #{userEmail}
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)">AND product_serial = #{productSerial}</if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsId)">AND ts_id = #{tsId}</if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">AND board_id = #{boardId}</if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentId)">AND comment_id = #{commentId}</if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isEmpty(commentId)">AND comment_id IS NULL</if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(targetUserEmail)">AND target_user_email = #{targetUserEmail}</if>
        AND delete_yn = 'N'
    </select>

    <insert id="insertUserFavorite" parameterType="UserFavorite" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_favorites
        SET user_email = #{userEmail},
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)">
            product_serial = #{productSerial},
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsId)">
            ts_id = #{tsId},
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">
            board_id = #{boardId},
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentId)">
            comment_id = #{commentId},
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(targetUserEmail)">
            target_user_email = #{targetUserEmail},
        </if>
        into_time = NOW(),
        create_id = #{userEmail},
        create_date = NOW()
    </insert>

    <update id="deleteUserFavorite" parameterType="userFavorite">
        UPDATE user_favorites
        <set>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)">product_serial=#{productSerial}, </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsId)">ts_id=#{tsId}, </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">board_id=#{boardId}, </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentId)">comment_id=#{commentId}, </if>
            delete_yn = 'Y',
            delete_id = #{userEmail},
            delete_date = NOW()
        </set>
        WHERE id = #{id} AND user_email = #{userEmail}
    </update>

    <select id="selectListUserFavoriteProduct" parameterType="map" resultType="kr.co.wayplus.travel.model.UserFavorite">
        SELECT *, ROW_NUMBER() OVER (ORDER BY create_date DESC) AS rownum
        FROM (
            with
            fixmin as (
                SELECT
                'fix' gubn,
                pOption.product_tour_id,
                MIN(CASE WHEN price_sale != 0 THEN price_sale ELSE price_normal END ) AS product_price
                FROM product_tour_price_fix_set AS pFitx
                LEFT JOIN product_tour_price_option AS pOption on pOption.price_option_id = pFitx.price_option_id
                WHERE pOption.use_yn = 'Y'
                AND pOption.delete_yn = 'N' AND pOption.option_sequence = 0 AND pOption.option_group_code IS NOT NULL
                group by pOption.product_tour_id),
            daymin as (
                SELECT
                    'day' gubn,
                    pOption.product_tour_id,
                    MIN(CASE WHEN price_sale != 0 THEN price_sale ELSE price_normal END ) AS product_price
                FROM product_tour_price_set AS pPrice
                LEFT JOIN product_tour_price_option AS pOption on pOption.price_option_id = pPrice.price_option_id
                WHERE  pOption.use_yn = 'Y'
                AND pOption.delete_yn = 'N'
                AND pOption.option_group_code IS NOT NULL
                AND pPrice.price_set_date >= DATE_FORMAT(now(),'%Y-%m-%d')
                group by pOption.product_tour_id),
            mix as (
                select *
                from(
                select *,
                (case when gubn = 'day' then 0 else 1 end) sort,
                RANK() OVER (PARTITION BY product_tour_id ORDER BY product_tour_id, sort) as ranking
                from (
                    select * from fixmin
                    union all
                    select * from daymin )b
                    order by sort) a
                    where ranking = 1)
                SELECT pt.*,
                    mu.upper_menu_id,
                    case when mu.main_expose_type is null and mu.upper_menu_id is not null then (select main_expose_type from menu_user u where u.menu_id = mu.upper_menu_id)
                    else mu.main_expose_type end main_expose_type,
                    mp.product_price, mp.gubn,
                    (SELECT menu_url FROM menu_user WHERE menu_id = pt.product_menu_id) menuUrl,
                    (SELECT menu_name FROM menu_user WHERE menu_id = pt.product_menu_id) categoryTitle,
                    pc.category_title subCategoryTitle,
                    pc.category_class subCategoryClass,
                    (case when mu.upper_menu_id is not null then (select umu.menu_url from menu_user umu where umu.menu_id = mu.upper_menu_id ) else mu.menu_url end) menu_url,
                    concat( (case when mu.upper_menu_id is not null
                    then
                    (case when menu_type != 'out-link'
                    then (select menu_url from menu_user b where b.menu_id = mu.upper_menu_id)
                    else '' end )
                else '' end ), menu_url  ) full_menu_url,
            (CASE WHEN (
                SELECT COUNT(*)
                FROM user_favorites uf
                WHERE user_email = #{likeUserEmail}
                AND delete_yn = 'N'
                AND pt.product_serial = uf.product_serial
                ) != '0' then 'Y'
                else 'N' END) as user_favorite,
            ( SELECT id
                FROM user_favorites uf
                WHERE user_email = #{likeUserEmail}
                AND delete_yn = 'N'
                AND pt.product_serial = uf.product_serial ) user_favorite_id
                FROM product_tour pt
                LEFT JOIN menu_user mu on mu.menu_id = pt.product_menu_id
                LEFT JOIN mix mp on pt.product_tour_id = mp.product_tour_id
                LEFT JOIN product_common_category pc on pc.product_menu_id = pt.product_menu_id AND pc.product_category_id = pt.product_category_id
            <where>
                <if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productUseYn)'>AND pt.product_use_yn = #{productUseYn}</if>
                <if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productUseYn)'>AND pt.product_use_yn = #{productUseYn}</if>
                <if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(regacyYn)'>AND pt.regacy_yn = #{regacyYn}</if>
                <if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)'>AND pt.delete_yn = #{deleteYn}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)">AND (mu.upper_menu_id = #{menuId} or mu.menu_id = #{menuId})</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productLangType)" >	AND product_lang_type=#{productLangType}	</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)">AND pt.product_tour_id = #{productTourId}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productMenuId)">AND pt.product_menu_id = #{productMenuId}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCategoryId)">AND pt.product_category_id=#{productCategoryId}</if>
                <if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus) and !"ALL".equals(productStatus)'>AND pt.product_status LIKE CONCAT(#{productStatus},'%')</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subCategory) and subCategory != 'ALL'">AND ( mu.menu_id = #{subCategory} or pt.product_category_id = #{subCategory})</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
                    <if test="searchType.equals('title')">AND pt.product_title LIKE CONCAT('%', #{searchKey}, '%')</if>
                    <if test="searchType.equals('tag')">AND pt.product_tag LIKE CONCAT('%', #{searchKey}, '%')</if>
                    <if test="!searchType.equals('all')">AND pt.product_category_id=#{searchType}</if>
                    AND concat(pt.product_title, COALESCE(pt.product_tag, '')) LIKE CONCAT('%', #{searchKey}, '%')
                </if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productType)">AND pt.isPopular = 'Y'</if>

                <if test='isMainExpose'>and pt.product_menu_id in ( select a.menu_id from menu_user a join menu_user b on b.main_expose_type is not null and (a.menu_id = b.menu_id or a.upper_menu_id = b.menu_id) )</if>
            </where>
            <choose>
                <when test='"Y".equals(orderYn)'>ORDER BY pt.product_sort_order ASC</when>
                <otherwise>ORDER BY pt.product_sort_order, pt.product_tour_id DESC</otherwise>
            </choose>

            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
                ORDER BY
                <choose>
                    <when test="sort=='rownum'" >	menu_id	</when>
                    <otherwise>rownum</otherwise>
                </choose>
                <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
            </if>
        ) AS info
        WHERE info.user_favorite = 'Y'
            AND info.product_use_yn = 'Y'
        ORDER BY rownum DESC
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
            LIMIT #{itemStartPosition}, #{pagePerSize}
        </if>
    </select>

    <select id="selectListUserFavoriteProductCount" parameterType="map" resultType="int">
        SELECT COUNT(*)
        FROM (
        with
        fixmin as (
        SELECT
        'fix' gubn,
        pOption.product_tour_id,
        MIN(CASE WHEN price_sale != 0 THEN price_sale ELSE price_normal END ) AS product_price
        FROM product_tour_price_fix_set AS pFitx
        LEFT JOIN product_tour_price_option AS pOption on pOption.price_option_id = pFitx.price_option_id
        WHERE pOption.use_yn = 'Y'
        AND pOption.delete_yn = 'N' AND pOption.option_sequence = 0 AND pOption.option_group_code IS NOT NULL
        group by pOption.product_tour_id),
        daymin as (
        SELECT
        'day' gubn,
        pOption.product_tour_id,
        MIN(CASE WHEN price_sale != 0 THEN price_sale ELSE price_normal END ) AS product_price
        FROM product_tour_price_set AS pPrice
        LEFT JOIN product_tour_price_option AS pOption on pOption.price_option_id = pPrice.price_option_id
        WHERE  pOption.use_yn = 'Y'
        AND pOption.delete_yn = 'N'
        AND pOption.option_group_code IS NOT NULL
        AND pPrice.price_set_date >= DATE_FORMAT(now(),'%Y-%m-%d')
        group by pOption.product_tour_id),
        mix as (
        select *
        from(
        select *,
        (case when gubn = 'day' then 0 else 1 end) sort,
        RANK() OVER (PARTITION BY product_tour_id ORDER BY product_tour_id, sort) as ranking
        from (
        select * from fixmin
        union all
        select * from daymin )b
        order by sort) a
        where ranking = 1)
        SELECT pt.*,
        mu.upper_menu_id,
        case when mu.main_expose_type is null and mu.upper_menu_id is not null then (select main_expose_type from menu_user u where u.menu_id = mu.upper_menu_id)
        else mu.main_expose_type end main_expose_type,
        mp.product_price, mp.gubn,
        (SELECT menu_url FROM menu_user WHERE menu_id = pt.product_menu_id) menuUrl,
        (SELECT menu_name FROM menu_user WHERE menu_id = pt.product_menu_id) categoryTitle,
        pc.category_title subCategoryTitle,
        pc.category_class subCategoryClass,
        (case when mu.upper_menu_id is not null then (select umu.menu_url from menu_user umu where umu.menu_id = mu.upper_menu_id ) else mu.menu_url end) menu_url,
        concat( (case when mu.upper_menu_id is not null
        then
        (case when menu_type != 'out-link'
        then (select menu_url from menu_user b where b.menu_id = mu.upper_menu_id)
        else '' end )
        else '' end ), menu_url  ) full_menu_url,
        (CASE WHEN (
        SELECT COUNT(*)
        FROM user_favorites uf
        WHERE user_email = #{likeUserEmail}
        AND delete_yn = 'N'
        AND pt.product_serial = uf.product_serial
        ) != '0' then 'Y'
        else 'N' END) as user_favorite,
        ( SELECT id
        FROM user_favorites uf
        WHERE user_email = #{likeUserEmail}
        AND delete_yn = 'N'
        AND pt.product_serial = uf.product_serial ) user_favorite_id
        FROM product_tour pt
        LEFT JOIN menu_user mu on mu.menu_id = pt.product_menu_id
        LEFT JOIN mix mp on pt.product_tour_id = mp.product_tour_id
        LEFT JOIN product_common_category pc on pc.product_menu_id = pt.product_menu_id AND pc.product_category_id = pt.product_category_id
        <where>
            <if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productUseYn)'>AND pt.product_use_yn = #{productUseYn}</if>
            <if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productUseYn)'>AND pt.product_use_yn = #{productUseYn}</if>
            <if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(regacyYn)'>AND pt.regacy_yn = #{regacyYn}</if>
            <if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)'>AND pt.delete_yn = #{deleteYn}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)">AND (mu.upper_menu_id = #{menuId} or mu.menu_id = #{menuId})</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productLangType)" >	AND product_lang_type=#{productLangType}	</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)">AND pt.product_tour_id = #{productTourId}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productMenuId)">AND pt.product_menu_id = #{productMenuId}</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCategoryId)">AND pt.product_category_id=#{productCategoryId}</if>
            <if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus) and !"ALL".equals(productStatus)'>AND pt.product_status LIKE CONCAT(#{productStatus},'%')</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subCategory) and subCategory != 'ALL'">AND ( mu.menu_id = #{subCategory} or pt.product_category_id = #{subCategory})</if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
                <if test="searchType.equals('title')">AND pt.product_title LIKE CONCAT('%', #{searchKey}, '%')</if>
                <if test="searchType.equals('tag')">AND pt.product_tag LIKE CONCAT('%', #{searchKey}, '%')</if>
                <if test="!searchType.equals('all')">AND pt.product_category_id=#{searchType}</if>
                AND concat(pt.product_title, COALESCE(pt.product_tag, '')) LIKE CONCAT('%', #{searchKey}, '%')
            </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productType)">AND pt.isPopular = 'Y'</if>

            <if test='isMainExpose'>and pt.product_menu_id in ( select a.menu_id from menu_user a join menu_user b on b.main_expose_type is not null and (a.menu_id = b.menu_id or a.upper_menu_id = b.menu_id) )</if>
        </where>
        ) AS info
        WHERE info.user_favorite = 'Y'
    </select>

    <insert id="insertUserAttachFile" parameterType="UserAttachFile" useGeneratedKeys="true" keyProperty="fileId">
        INSERT INTO user_attach_file
        (
            service_type, user_email,
            upload_path, upload_filename,
            file_extension, file_size, file_mimetype,
            origin_filename,
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >
            	upload_id,
            </if>
            upload_date
        )
        VALUES (
            #{serviceType}, #{userEmail},
            #{uploadPath}, #{uploadFilename},
            #{fileExtension}, #{fileSize}, #{fileMimetype},
            #{originFilename},
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >
            	#{uploadId},
            </if>
            now()
        )
    </insert>

    <select id="selectListUserBadgeAcquireHistory" parameterType="map" resultType="BadgeAcquireHistory">
        SELECT *
        FROM badge_acquire_history AS bah
        LEFT JOIN badge_contents AS bc on
            bc.badge_id = bah.badge_id
        LEFT JOIN badge_attach_image AS bai on
            bai.file_id = bc.badge_image_file_id
        WHERE bah.user_email = #{userEmail} AND bc.use_yn = 'Y' AND bah.delete_yn = 'N'
        ORDER BY bah.create_date DESC
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
		 LIMIT #{itemStartPosition}, #{pagePerSize}
		 </if>
    </select>

    <select id="selectListUserCompleteMission" parameterType="map" resultType="BoardContents">
        SELECT *
        FROM board_contents
        WHERE create_id = #{userEmail} AND use_yn = 'Y' AND delete_yn = 'N' AND cancel_yn = 'N' 
        AND upper_board_id IS NOT NULL
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)">AND board_id = #{boardId}</if>
        ORDER BY create_date DESC
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
		 LIMIT #{itemStartPosition}, #{pagePerSize}
		</if>
    </select>

    <!-- 청풍용 -->
    <select id="selectListIslandlife" parameterType="HashMap" resultType="Islandlife">
		SELECT *
		  FROM(
			SELECT @rownum:=@rownum+1 AS rownum,
				   id, title, sub_title, content, islandlife_thumbnail,
				   create_id, create_date, last_update_id, last_update_date, use_yn, delete_yn, delete_id, delete_date,
                   select_color
			  FROM island_life a
			  join (SELECT @rownum:= 0) rnum
			<where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and title=#{title}	</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subTitle)" >	and subTitle=#{subTitle}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	and content=#{content}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
			</where>

			 <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
				ORDER BY
				<choose>
					<when test="sort=='id'" >	id	</when>
					<when test="sort=='title'" >	title	</when>
                    <when test="sort=='subTitle'" >	subTitle	</when>
					<when test="sort=='content'" >	content	</when>
					<when test="sort=='createId'" >	create_id	</when>
					<when test="sort=='createDate'" >	create_date	</when>
					<when test="sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="sort=='useYn'" >	use_yn	</when>
					<when test="sort=='deleteYn'" >	delete_yn	</when>
					<when test="sort=='deleteId'" >	delete_id	</when>
					<when test="sort=='deleteDate'" >	delete_date	</when>

					<otherwise>rownum</otherwise>
				</choose>
				<choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
				ORDER BY <foreach item="item" index="index" collection="listSort" separator=",">
				<choose>
					<when test="item.sort=='id'" >	id	</when>
					<when test="item.sort=='title'" >	title	</when>
                    <when test="item.sort=='subTitle'" >	subTitle	</when>
					<when test="item.sort=='content'" >	content	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="item.sort=='useYn'" >	use_yn	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='deleteId'" >	delete_id	</when>
					<when test="item.sort=='deleteDate'" >	delete_date	</when>
				</choose>
				<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>

			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
		 LIMIT #{itemStartPosition}, #{pagePerSize}
		 </if>
	</select>

    <!-- 청풍용 -->
    <update id="updateUserIslandLife" parameterType="HashMap">
        UPDATE user
           SET user_island_life_id = #{islandLifeId}
         WHERE user_email = #{userEmail}
    </update>
</mapper>