package kr.co.wayplus.travel.model;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BadgeAcquireHistory extends CommonDataSet {
	private Integer rowNum;
	private Integer badgeHistoryId;	//배지ID
	private Integer badgeId;	//배지  ID
	private String badgeCategory;	//배지 카테고리
	private String badgeType;	//배지 분류
	private String badgeName;	//배지 이름
	private String badgeDesc;	//배지 설명
	private String badgeImageFileId;	//배지 이미지 파일 ID
	private String userEmail;	//배지 습득자
	private String uploadFilename;	//배지 이미지 이름
}
