package kr.co.wayplus.travel.builder;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import kr.co.wayplus.travel.model.excel.ExcelData;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ExcelDataBuilder {
    private String id;
    private Map<String, Object> data;
    private Map<String, String> format;
    private Map<String, CellStyle> styles;

    private ExcelDataBuilder() {
        this.data = new LinkedHashMap<>();
        this.format = new HashMap<>();
        this.styles = new HashMap<>();
    }

    // 정적 팩토리 메소드
    public static ExcelDataBuilder create() {
        return new ExcelDataBuilder();
    }

    public ExcelDataBuilder id(String id) {
        this.id = id;
        return this;
    }

    public ExcelDataBuilder value(String key, Object value) {
        this.data.put(key, value);
        return this;
    }

    public ExcelDataBuilder format(String key, String format) {
        this.format.put(key, format);
        return this;
    }

    public ExcelDataBuilder cellStyle(String key, String backgroundColor, String textColor) {
        CellStyle style = new CellStyle(backgroundColor, textColor);
        this.styles.put(key, style);
        return this;
    }

    public ExcelData build() {
        ExcelData excelData = new ExcelData();
        excelData.setId(id);
        excelData.setData(data);
        excelData.setFormat(format);
        excelData.setStyles(styles);
        return excelData;
    }

    @Getter
    @AllArgsConstructor
    public static class CellStyle {
        private String backgroundColor;
        private String textColor;
    }
}
