package kr.co.wayplus.travel.model;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LogMessageSend {
    private int id;
    private String from;
    private String to;
    private String messageType;
    private String subject;
    private String content;
    private String files;
    private String reserveTime;
    private String reserveTimeZone;

    private String requestTime;
    private String requestUserid;

    private String resultRequestId; //발송 요청 ID
    private String resultStatusCode;

    private Long sendReservationId;
    private String sendMessageType;

}
