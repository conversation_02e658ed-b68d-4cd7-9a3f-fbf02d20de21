<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.front.MenuMapper">
<!--
	 * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
-->
<!--################################### menuUser ###################################-->
	<select id="selectListMenuCount" parameterType="HashMap" resultType="Integer">
		SELECT COUNT(*) FROM menu_user
		WHERE use_yn = 'Y'
	</select>

	<select id="selectListMenu" parameterType="HashMap" resultType="MenuUser">
		SELECT * FROM menu_user
		WHERE use_yn = 'Y'
		ORDER BY menu_sort
	</select>

	<select id="selectOneMenu" parameterType="HashMap" resultType="MenuUser">
		SELECT * FROM menu_user
		WHERE menu_id = #{menuId} AND use_yn = 'Y'
	</select>

	<select id="selectListCommunityMenu" parameterType="HashMap" resultType="MenuUser">
		WITH RECURSIVE menuUser AS (
			SELECT
				menu_id, upper_menu_id, menu_name,
				menu_url, menu_sort, virtual_yn,1 AS depth
			FROM menu_user
			WHERE delete_yn ='N'
				  AND use_yn = 'Y'
				  AND menu_id = #{menuId}
			UNION ALL
			SELECT
				mm.menu_id, mm.upper_menu_id, mm.menu_name,
				mm.menu_url, mm.menu_sort, mm.virtual_yn, mmu.depth + 1
			FROM  menu_user mm
			INNER JOIN menuUser mmu ON mm.upper_menu_id = mmu.menu_id
			WHERE mm.delete_yn = 'N'
		)
		SELECT 
			m.*,
			(SELECT menu_url 
			FROM menu_user innermu 
			WHERE innermu.menu_id = 4) as root_menu_url,
			CASE 
				WHEN EXISTS (
					SELECT 1 
					FROM menu_user child 
					WHERE child.upper_menu_id = m.menu_id 
					AND child.delete_yn = 'N'
				) THEN 'Y'
				ELSE 'N'
			END as has_child,
			(
				SELECT child.menu_url 
				FROM menu_user child 
				WHERE child.upper_menu_id = m.menu_id 
				AND child.delete_yn = 'N'
				LIMIT 1
			) as child_menu_url,
			(
				SELECT parent.menu_url
				FROM menu_user parent
				WHERE parent.menu_id = m.upper_menu_id
				AND parent.delete_yn = 'N'
			) as upper_menu_url
		FROM menuUser m
		WHERE m.upper_menu_id IS NOT NULL
		ORDER BY menu_sort ASC;
	</select>


</mapper>