package kr.co.wayplus.travel.mapper.front;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import kr.co.wayplus.travel.model.*;

@Mapper
@Repository
public interface BoardMapper {
//	<!--################################### BoardSetting ###################################-->
	BoardSetting selectOneBoardSetting(HashMap<String, Object> paramMap);

//	<!--################################### BoardContents ###################################-->
	int selectCountBoardContents(HashMap<String, Object> paramMap);
	int selectCountBoardContents(BoardContents bc);
	ArrayList<BoardContents> selectListBoardContents(HashMap<String, Object> paramMap);
	ArrayList<BoardContents> selectListBoardContents(BoardContents bc);
	BoardContents selectOneBoardContents(HashMap<String, Object> paramMap);
	void insertBoardContents(BoardContents bc)throws SQLException;
	void updateBoardContents(BoardContents bc) throws SQLException;
	void restoreBoardContents(BoardContents bc) throws SQLException;
	void deleteBoardContents(BoardContents bc) throws SQLException;

	//	<!--################################### BoardCategory ###################################-->
	int selectCountBoardCategory(HashMap<String, Object> paramMap);
	ArrayList<BoardCategory> selectListBoardCategory(HashMap<String, Object> paramMap);
	ArrayList<BoardCategory> selectListBoardProjectCategory(HashMap<String, Object> paramMap);
	BoardCategory selectOneBoardCategory(HashMap<String, Object> paramMap);
    void updateBoardCategoryMissionCompleteCount(HashMap<String, Object> param);

//	<!--################################### BoardComment ###################################-->
	void updateBoardContent_CommentCount(HashMap<String, Object> param);
	BoardComment selectOneBoardComment(HashMap<String, Object> paramMap);
	int selectCountBoardComment(HashMap<String, Object> param);
	ArrayList<BoardComment> selectListBoardComment(HashMap<String, Object> param);
	void insertBoardComment(BoardComment data) throws SQLException;
	void updateBoardComment(BoardComment data) throws SQLException;
	void updateBoardCommentFavorite(BoardComment data) throws SQLException;

	//	<!--################################### BoardAttachFile ###################################-->
	ArrayList<BoardAttachFile> selectListBoardAttachFile(HashMap<String, Object> paramMap);
	BoardAttachFile selectOneBoardAttachFile(HashMap<String, Object> paramMap);
	void insertBoardAttachFile(BoardAttachFile baf);
	void deleteBoardAttachFile(BoardAttachFile baf);

	ArrayList<BoardCategory> selectMostMissionMonth(HashMap<String, Object> param);

    ArrayList<BoardContents> selectMostMissionUser(HashMap<String, Object> param);

    ArrayList<BoardContents> selectListWroteBoardContents(HashMap<String, Object> wroteBoardParam);


	//	<!--################################### menuConnectBoard ###################################-->
    MenuConnectBoard selectOneMenuConnectBoard(HashMap<String, Object> paramMap);

    ArrayList<BoardContents> selectListGreethingContents(HashMap<String, Object> param);

    int selectCountWroteBoardContents(HashMap<String, Object> paramMap);

	int selectCountWroteBoardComment(HashMap<String, Object> paramMap);

    BoardSetting selectOneBoardSettingTypeCode(HashMap<String, Object> wroteReviewViewParam);

    ArrayList<BoardSetting> selectListBoardSettingTypeCode(HashMap<String, Object> wroteBoardViewParam);

	int selectTotalCommentCount(HashMap<String, Object> paramMap);

    List<Map<String, Object>> selectListTotalComment(HashMap<String, Object> paramMap);

	void updateBoardComments(HashMap<String, Object> paramMap);

	void deleteBoardComments(HashMap<String, Object> paramMap);

	List<Map<String, Object>> selectCommentReply(HashMap<String, Object> paramMap);

    Map<String, Object> selectOneCommentReply(HashMap<String, Object> paramMap);

	int selectCommentReplyCount(HashMap<String, Object> paramMap);

	List<String> selectOneCommentMentions(HashMap<String, Object> paramMap);

    int isMissionAlreadyCheck(HashMap<String, Object> paramMap);

}
