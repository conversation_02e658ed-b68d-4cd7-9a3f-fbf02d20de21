package kr.co.wayplus.travel.service.manage;

import kr.co.wayplus.travel.mapper.manage.BannerManageMapper;
import kr.co.wayplus.travel.model.BannerCategory;
import kr.co.wayplus.travel.model.MainBannerImage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

@Service
public class BannerManageService {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final BannerManageMapper bannerManageMapper;

    public BannerManageService(BannerManageMapper bannerManageMapper) {
        this.bannerManageMapper = bannerManageMapper;
    }

    public int getMainBannerImageListCount(HashMap<String, Object> param) {
        return bannerManageMapper.selectMainBannerImageListCount(param);
    }

    public ArrayList<MainBannerImage> getMainBannerImageList(HashMap<String, Object> param) {
        return bannerManageMapper.selectMainBannerImageList(param);
    }

    public void writeMainBannerImage(MainBannerImage bannerImage) {
        bannerManageMapper.insertMainBannerImage(bannerImage);
    }

    public void modifyMainBannerDelete(HashMap<String, Object> param) {
        bannerManageMapper.updateMainBannerDelete(param);
    }

    public void modifyMainBannerOrder(HashMap<String, Object> param) {
        bannerManageMapper.updateMainBannerOrder(param);
    }

    public MainBannerImage getMainBannerImage(HashMap<String, Object> param) {
        return bannerManageMapper.selectMainBannerImage(param);
    }

    public void updateMainBannerImage(MainBannerImage bannerImage) {
        bannerManageMapper.updateMainBannerImage(bannerImage);
    }

    public ArrayList<MainBannerImage> getMainBannerPreviewList(HashMap<String, Object> param) {
        return  bannerManageMapper.selectMainBannerPreviewList(param);
    }

	public void updateMainBannerStatus(MainBannerImage bannerImage) {
		 bannerManageMapper.updateMainBannerStatus(bannerImage);
	}

    public ArrayList<BannerCategory> selectListBannerCategory(HashMap<String, Object> param) {
        return bannerManageMapper.selectListBannerCategory(param);
    }

	public BannerCategory selectOneBannerCategory(HashMap<String, Object> param) {
		return bannerManageMapper.selectOneBannerCategory(param);
	}
    public void saveBannerCategory(BannerCategory bc) throws SQLException {
        if(bc == null) {
			throw new SQLException("model is null");
		} else {
			if( bc.getBannerCategoryId() == null ) {
				bannerManageMapper.insertBannerCategory(bc);
			} else {
				bannerManageMapper.updateBannerCategory(bc);
			}
		}
    }

    public void updateBannerCategorySort(BannerCategory bannerCategory) {
        bannerManageMapper.updateBannerCategorySort(bannerCategory);
    }

    public void deleteBannerCategory(BannerCategory pc) {
        bannerManageMapper.deleteBannerCategory(pc);
    }

    public void updateBannerCategoryUseYn(BannerCategory pc) {
        bannerManageMapper.updateBannerCategoryUseYn(pc);
    }

}
