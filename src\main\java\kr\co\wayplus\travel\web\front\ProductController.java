package kr.co.wayplus.travel.web.front;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;

import org.apache.ibatis.annotations.Param;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.BoardAttachFile;
import kr.co.wayplus.travel.model.BoardContents;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.MainBannerImage;
import kr.co.wayplus.travel.model.MenuUser;
import kr.co.wayplus.travel.model.PagingDTO;
import kr.co.wayplus.travel.model.Policy;
import kr.co.wayplus.travel.model.ProductComment;
import kr.co.wayplus.travel.model.ProductDetailSchedule;
import kr.co.wayplus.travel.model.ProductInfo;
import kr.co.wayplus.travel.model.ProductPriceOption;
import kr.co.wayplus.travel.model.ProductPriceOption.DayList;
import kr.co.wayplus.travel.model.ProductPriceOption.FixPriceList;
import kr.co.wayplus.travel.model.ProductTourImages;
import kr.co.wayplus.travel.model.Reservation;
import kr.co.wayplus.travel.model.UserCustomerOrderList;
import kr.co.wayplus.travel.service.front.PageService;
import kr.co.wayplus.travel.service.front.ProductService;
import kr.co.wayplus.travel.util.FileInfoUtil;

@Controller
@RequestMapping("/product")
public class ProductController	extends BaseController {

	@Value("${cookie-set.domain}")
	private String cookieDomain;
	@Value("${cookie-set.prefix}")
	private String cookieName;

	private final Logger logger = LoggerFactory.getLogger(getClass());

	private final PageService pageService; /*common하게*/
	private final ProductService productService;

	@Autowired
	public ProductController(PageService pageService, ProductService service) {
		this.pageService = pageService;
		this.productService = service;
	}

	@GetMapping(value = {"/price-option"})
	@ResponseBody
	public HashMap<String, Object> pridce_option_ajax(
			HttpServletRequest request,
			@RequestParam(value="productSerial", required = true) String productSerial,
			@RequestParam(value="productTourId", required = true) int productTourId,
			@RequestParam(value="selectedDate", required = false) String selectedDate,
			@RequestParam(value="startDate", required = false) String startDate,
			@RequestParam(value="endDate", required = false) String endDate,
			@Param(value="isStay") Boolean isStay
		){
		HashMap<String,Object> retMap = new HashMap<String, Object>();

		try {
			HashMap<String,Object> paramMap = new HashMap<String, Object>();

			paramMap.put("productSerial", productSerial);
			paramMap.put("productTourId", productTourId);
			ProductInfo	productInfo = productService.selectOneProduct( paramMap );
			paramMap.put("selectedDate", selectedDate);

			retMap.put("optionPriceList", null);

			/*
			paramMap.put("travelDate", travelDate);
			paramMap.put("productTourId", productTourId);

			*/
			if ( startDate != null && endDate != null ) {
				paramMap.put("startDate", startDate);
				paramMap.put("endDate", endDate);
			}
			else {
				paramMap.put("selectedDate", selectedDate);
			}
			ArrayList<ProductPriceOption> list = null;

			if( !isStay ) {
				list = productService.selectListPriceOption(paramMap);
			} else {
				list = productService.selectListPriceOptionStayOnly(paramMap);
			}

			retMap.put("optionPriceList", list);

			/*
			if( productInfo.getPriceSetType() != null) {
				switch ( productInfo.getPriceSetType() ) {
				case "day": //상품 일자별 가격옵션
					ArrayList<DayList> optionPriceListDay	= productService.selectListProductDayPrice( paramMap );
					if( optionPriceListDay.size() > 0 ) {
						retMap.put("optionPriceList", optionPriceListDay);
					}
					break;
				case "fix": //상품 고정가격 옵션
					ArrayList<FixPriceList> optionPriceListFix = productService.selectListProductFixPrice( paramMap );
					if( optionPriceListFix.size() > 0) {
							retMap.put("optionPriceList", optionPriceListFix);
					}
					break;
				}
			}
			*/

			retMap.put("result","success");
			retMap.put("message","처리 성공");
		} catch (Exception e) {
			retMap.put("result","ERROR");
			retMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
		}

		return retMap;
	}

	@PostMapping("/price-option-list")
	@ResponseBody
	public HashMap<String, Object> productPriceOption_ajax_list(
			@RequestParam(value="travelDate", defaultValue = "null") String travelDate,
			@RequestParam(value="productTourId", defaultValue = "null") Integer productTourId) {
		HashMap<String,Object> resultMap = new HashMap<>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			HashMap<String, Object> paramMap = new HashMap<>();

			if ( travelDate == null || productTourId == null ) {
				resultMap.put("result","fail");
				resultMap.put("message","중요 파라미터 누락 입니다.");
			} else {
				paramMap.put("travelDate", travelDate);
				paramMap.put("productTourId", productTourId);

				ArrayList<ProductPriceOption> list = productService.selectListPriceOption(paramMap);

				resultMap.put("data",list);
				resultMap.put("result","success");
			resultMap.put("message","처리되었습니다.");
			}
		} catch (Exception e) {
			resultMap.put("result","error");
			resultMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			e.printStackTrace();
		}

		return resultMap;
	}
	//상품 구매 가능 수량 비교
	@GetMapping("/buy-possible-check")
	@ResponseBody
	public HashMap<String, Object> productPriceOptionBuyPossibleCheck(
			Reservation reservation) {
		HashMap<String,Object> resultMap = new HashMap<>();

		try {
			HashMap<String, Object> paramMap = new HashMap<>();

			if ( reservation.getProductTourId() == null ) {
				resultMap.put("result","fail");
				resultMap.put("message","중요 파라미터 누락 입니다.");
			} else {
				paramMap.put("productTourId", reservation.getProductTourId());

				ProductInfo product = productService.selectOneProduct(paramMap);

				reservation.setProductStayType( product.getProductStayType() );
				reservation.setPolicyInventory( product.getPolicyInventory() );
				
				HashMap<String, Object> possibleParam = pageService.calcStayPriceOption(reservation);

				resultMap.put("data", possibleParam);
				resultMap.put("result","success");
				resultMap.put("message","처리되었습니다.");
			}
		} catch (Exception e) {
			resultMap.put("result","error");
			resultMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			e.printStackTrace();
		}

		return resultMap;
	}

	@PostMapping("/save-comment")
    @ResponseBody
    public HashMap<String, Object> save_comment_ajax(
			ProductComment productComment
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

    		if(_user instanceof LoginUser) {
				productComment.setUserEmail( ((LoginUser)_user).getUserEmail() );
				productComment.setUserName( ((LoginUser)_user).getUserName() );
				productComment.setUserRole( ((LoginUser)_user).getUserRole() );

				productService.insertProductComment(productComment);
				retrunMap.put("result", "success");
				retrunMap.put("message", "등록되었습니다.");
				return retrunMap;
			}
			else {
				retrunMap.put("result", "error");
				retrunMap.put("message", "로그인 후 이용해주세요.");
				return retrunMap;
			}
		} catch (Exception e) {
			e.printStackTrace();
			retrunMap.put("result", "error");
			retrunMap.put("message", "등록중 에러가 발생하였습니다.");
			retrunMap.put("info", e.getMessage());
			logger.error(e.getCause().getMessage());
		}
        return retrunMap;
    }

	@GetMapping("/list-comment")
	@ResponseBody
	public HashMap<String, Object> getProductComments(
	        @RequestParam(value = "productSerial", required = true) String productSerial,
	        @RequestParam(value = "length", defaultValue = "10") int length,
	        @RequestParam(value = "start", defaultValue = "0") int start) {
	    HashMap<String, Object> resultMap = new HashMap<>();

	    try {
	        HashMap<String, Object> paramMap = new HashMap<>();
			Object object = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			if( object instanceof LoginUser _user) {
				paramMap.put("likeUserEmail", _user.getUsername());
			}

	        paramMap.put("productSerial", productSerial);
			int totalCount = productService.selectCountProductComment(paramMap);

	        paramMap.put("length", length);
	        paramMap.put("start", start);
	        ArrayList<ProductComment> comments = productService.selectProductComment(paramMap);

	        resultMap.put("result", "success");
	        resultMap.put("data", comments);
	        resultMap.put("recordsTotal", totalCount);
	        resultMap.put("message", "댓글을 성공적으로 조회했습니다.");
	    } catch (Exception e) {
	        resultMap.put("result", "error");
	        resultMap.put("message", "댓글 조회 중 오류가 발생했습니다.");
	        logger.error("댓글 조회 오류", e);
	    }

	    return resultMap;
	}

	//년-월을 바탕으로 최대정원기준 숙소상품 예약가능 리스트 정보
	@GetMapping("/ajax-stay-resv-possible-info")
	@ResponseBody
	public HashMap<String, Object> stayResvPossibleInfo(
			Reservation reservation) {
		HashMap<String,Object> resultMap = new HashMap<>();

		try {
			HashMap<String, Object> param = new HashMap<>();
			param.put("productUseYn", "Y");
			param.put("regacyYn", "N");
			param.put("deleteYn", "N");

			Object object = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			if( object instanceof LoginUser _user) {
				param.put("likeUserEmail", _user.getUsername());
			}

			param.put("productMenuId", reservation.getMenuId());
			ArrayList<ProductInfo> productStayList = productService.selectListProduct( param );
			for (ProductInfo product : productStayList) {
				//상품 이미지정보
				HashMap<String, Object> paramStay = new HashMap<>();
				paramStay.put("productTourId", product.getProductTourId());
				product.setStayImageList(productService.selectListProductImages(paramStay));
				ProductPriceOption productPriceOption = productService.selectOneEarlyBirdPrice(paramStay);
				if (productPriceOption != null) {
					product.setEarlyBirdPrice(productPriceOption.getEarlyBirdPrice());
				}
				//캘린더에서 예약 가능 여부 보여주기용
				product.setPickPeopleCount(reservation.getPickPeopleCount());
                product.setYear(reservation.getYear());
                product.setMonth(reservation.getMonth());
				
				product.setStayCalendarResvPossibleList(productService.getCalendarResvPossibleList(product));
			}

			resultMap.put("productStayList",productStayList);
			resultMap.put("result","success");
			resultMap.put("message","처리되었습니다.");
		} catch (Exception e) {
			resultMap.put("result","error");
			resultMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			e.printStackTrace();
		}

		return resultMap;
	}

	//년-월을 바탕으로 최대정원기준 숙소상품 예약가능 단일 숙소 정보
	@GetMapping("/ajax-one-stay-resv-possible-info")
	@ResponseBody
	public HashMap<String, Object> stayOneResvPossibleInfo(
			Reservation reservation) {
		HashMap<String,Object> resultMap = new HashMap<>();

		try {
			HashMap<String, Object> param = new HashMap<>();
			param.put("productUseYn", "Y");
			param.put("regacyYn", "N");
			param.put("deleteYn", "N");

			Object object = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			if( object instanceof LoginUser _user) {
				param.put("likeUserEmail", _user.getUsername());
			}

			param.put("productMenuId", reservation.getMenuId());
			param.put("productSerial", reservation.getProductSerial());
			ProductInfo productStay = productService.selectOneProduct( param );

			//상품 이미지정보
			HashMap<String, Object> paramStay = new HashMap<>();
			paramStay.put("productTourId", productStay.getProductTourId());
			productStay.setStayImageList(productService.selectListProductImages(paramStay));
			ProductPriceOption productPriceOption = productService.selectOneEarlyBirdPrice(paramStay);
			if (productPriceOption != null) {
				productStay.setEarlyBirdPrice(productPriceOption.getEarlyBirdPrice());
			}
			//캘린더에서 예약 가능 여부 보여주기용
			productStay.setPickPeopleCount(reservation.getPickPeopleCount());
			productStay.setYear(reservation.getYear());
			productStay.setMonth(reservation.getMonth());
			productStay.setStayCalendarResvPossibleList(productService.getCalendarResvPossibleList(productStay));

			resultMap.put("productStay",productStay);
			resultMap.put("result","success");
			resultMap.put("message","처리되었습니다.");
		} catch (Exception e) {
			resultMap.put("result","error");
			resultMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			e.printStackTrace();
		}

		return resultMap;
	}

	//년-월을 바탕으로 최대정원기준 프로그램상품 예약가능 리스트 정보
	@GetMapping("/ajax-program-resv-possible-info")
	@ResponseBody
	public HashMap<String, Object> programResvPossibleInfo(
			Reservation reservation) {
		HashMap<String,Object> resultMap = new HashMap<>();

		try {
			HashMap<String, Object> param = new HashMap<>();
			param.put("productUseYn", "Y");
			param.put("regacyYn", "N");
			param.put("deleteYn", "N");

			Object object = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			if( object instanceof LoginUser _user) {
				param.put("likeUserEmail", _user.getUsername());
			}
			MenuUser menuUser = pageService.getMenuUserFullMenuUrl( "/program" );
			param.put("programUpperMenuId", menuUser.getMenuId());
			ArrayList<ProductInfo> programList = productService.selectListProgram( param );
			for (ProductInfo product : programList) {
				//캘린더에서 예약 가능 여부 보여주기용
				product.setPickPeopleCount(reservation.getPickPeopleCount());
                product.setYear(reservation.getYear());
                product.setMonth(reservation.getMonth());

				product.setStayCalendarResvPossibleList(productService.getCalendarOneProgramResvPossibleDayList(product));
			}

			resultMap.put("programList",programList);
			resultMap.put("result","success");
			resultMap.put("message","처리되었습니다.");
		} catch (Exception e) {
			resultMap.put("result","error");
			resultMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			e.printStackTrace();
		}

		return resultMap;
	}

	//년-월을 바탕으로 최대정원기준 프로그램상품 예약가능 단일 정보
	@GetMapping("/ajax-one-program-resv-possible-info")
	@ResponseBody
	public HashMap<String, Object> oneProgramResvPossibleInfo(
			Reservation reservation) {
		HashMap<String,Object> resultMap = new HashMap<>();

		try {
			HashMap<String, Object> param = new HashMap<>();
			param.put("productUseYn", "Y");
			param.put("regacyYn", "N");
			param.put("deleteYn", "N");
			param.put("productSerial", reservation.getProductSerial());

			Object object = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			if( object instanceof LoginUser _user) {
				param.put("likeUserEmail", _user.getUsername());
			}
			ProductInfo program = productService.selectOneProduct( param );

			//캘린더에서 예약 가능 여부 보여주기용
			program.setPickPeopleCount(reservation.getPickPeopleCount());
			program.setYear(reservation.getYear());
			program.setMonth(reservation.getMonth());
			program.setProgramCalendarResvPossibleList(productService.getCalendarOneProgramResvPossibleDayList(program));

			resultMap.put("oneProgram", program);
			resultMap.put("result","success");
			resultMap.put("message","처리되었습니다.");
		} catch (Exception e) {
			resultMap.put("result","error");
			resultMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			e.printStackTrace();
		}

		return resultMap;
	}

	@GetMapping("/ajax-one-price-option-id-info")
	@ResponseBody
	public HashMap<String, Object> onePriceOptionIdInfo(
			@RequestParam(value="priceOptionId", required = true) String priceOptionId) {
		HashMap<String,Object> resultMap = new HashMap<>();

		try {
			HashMap<String, Object> param = new HashMap<>();
			param.put("priceOptionId", priceOptionId);

			resultMap.put("data", productService.selectOnePriceOptionById(param));
			resultMap.put("result","success");
			resultMap.put("message","처리되었습니다.");
		} catch (Exception e) {
			resultMap.put("result","error");
			resultMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			e.printStackTrace();
		}

		return resultMap;
	}



	/*
	@GetMapping(value = {"/{path1}"})
	public ModelAndView dynamicPath2(
			HttpServletRequest request
			, @PathVariable String path1
			) {
		ModelAndView mav = new ModelAndView();

		mav.setViewName( "/front/sub/product/list" );

		//commonPathFinder(mav, path1);

		return mav;
	}

	@GetMapping(value = {"/{path1}/{path2}"})
	public ModelAndView dynamicPath3(
			HttpServletRequest request
			, @PathVariable String path1
			, @PathVariable String path2
			) {
		ModelAndView mav = new ModelAndView();

		mav.setViewName( "/front/sub/product/list" );

		//commonPathFinder(mav, path1, path2);

		return mav;
	}

	private void commonPathFinder(ModelAndView mav, String ... arrPath) {
		HashMap<String, Object> param = new HashMap<>();

		String fullUrl = "";
		for (String string : arrPath) {
			fullUrl += "/"+string;
		}

		param.put("fullMenuUrl", fullUrl);

		MenuUser menu = pageService.selectOneMenuUser( param );

		mav.setViewName( "/front/sub/product/list" );
	}
	*/

//	@GetMapping(value = {"/{path1}"})
//	public ModelAndView dynamicPath2(HttpServletRequest request,
//			@PathVariable String path1,
//			@RequestParam(value="page", defaultValue="1") int page,
//			@RequestParam(value="pageSize",defaultValue = "9")int pageSize,
//			@RequestParam(value="category", defaultValue="ALL") String category,
//			@RequestParam(value="subCategory", defaultValue="ALL") String subCategory,
//			@RequestParam(value="searchKey", defaultValue="ALL") String searchKey,
//			@RequestParam(value="location", defaultValue="ALL") String location){
//		ModelAndView mav = new ModelAndView();
//
//		/*
//		 * 1. menuId 알아내기
//		 * 2. menuId통해 카태고리 표시용 정보 알아내기
//		 * 3. 제품목록 정보 알아내기
//		 */
//
//		HashMap<String, Object> param = new HashMap<>();
//		param.put("location", location);
//		param.put("category", category);
//		param.put("subCategory", subCategory);
//		param.put("searchKey", searchKey);
//
//		HashMap<String, Object> param1 = new HashMap<>();
//		HashMap<String, Object> param2 = new HashMap<>();
//		HashMap<String, Object> param3 = new HashMap<>();
//		//HashMap<String, Object> param4 = new HashMap<>();
//
//		param1.put("menuUrl", "/"+path1);
//		MenuUser menu = pageService.selectOneMenuUser( param1 );
//
//		if(menu != null) {
//			param2.put("upperMenuId", menu.getMenuId());
//			param2.put("useYn", "Y");
//			ArrayList<MenuUser> menuMenuList = pageService.selectListMenuUser( param2 );
//
//			param3.put("menuId", menu.getMenuId());
//			param3.put("bannerType", "menuBanner");
//			ArrayList< MainBannerImage > bannerList = pageService.selectListMainBannerImage(param3);
///*
//			if(!subCategory.equals("ALL")) {
//				param4.put("subCategory", subCategory);
//			}
//*/
//			param.put("menuId", menu.getMenuId());
//
//			int totalCount = productService.selectProductListCount(param);
//
//			PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
//			param.put("itemStartPosition", paging.getItemStartPosition());
//			param.put("pagePerSize", paging.getPagePerSize());
//
//			ArrayList< ProductInfo > productList = productService.selectProductList( param );
//
//			mav.addObject("menu", menu);
//			mav.addObject("menuCategory", menuMenuList);
//			mav.addObject("bannerList", bannerList);
//			mav.addObject("productList", productList);
//			mav.addObject("paging", paging);
//
//		} else {
//			//오~~류~~인~~데~~
//			//mav.setView(new RedirectView("/order/error"));
//		}
//
//		param.put("p1",param1);
//		param.put("p2",param2);
//		param.put("p3",param3);
//
//		mav.addObject("p", param);
//		mav.setViewName("/front/product/list");
//		return mav;
//	}
//
//	@GetMapping(value = {"/{path1}/{productSerial}"})
//	public ModelAndView dynamicPath2(HttpServletRequest request,
//			@PathVariable String path1,
//			@PathVariable String productSerial,
//			@RequestParam(value="page", defaultValue="1") int page,
//			@RequestParam(value="pageSize", defaultValue="10") int pageSize,
//			@RequestParam(value="searchKey", defaultValue="") String searchKey){
//		ModelAndView mav = new ModelAndView();
//
//		/*
//		 * 1. menuId 알아내기
//		 * 2. menuId통해 카태고리 표시용 정보 알아내기
//		 * 3. 제품목록 정보 알아내기
//		 */
//
//		HashMap<String, Object> paramMenu = new HashMap<>();
//		HashMap<String, Object> paramProduct = new HashMap<>();
//		HashMap<String, Object> paramPolicy = new HashMap<>();
//
//		paramMenu.put("menuUrl", "/"+path1);
//		MenuUser menu = pageService.selectOneMenuUser( paramMenu );
//
//		if(menu != null) {
//			paramProduct.put("productSerial", productSerial);
//
//			ProductInfo	ProductInfo = productService.selectOneProduct( paramProduct );
//
//			paramProduct.put("productTourId", ProductInfo.getProductTourId());
//
//			//상품 이미지정보
//			ArrayList<ProductTourImages> imageList = productService.selectListProductImages(paramProduct);
//			//상품 고정가격 옵션
//			ArrayList<FixPriceList> optionPriceListFix = productService.selectListProductFixPrice( paramProduct );
//			//상품 일자별 가격옵션
//			ArrayList<DayList> optionPriceListDay		= productService.selectListProductDayPrice( paramProduct );
//			//세부일정용
//			ArrayList<ProductDetailSchedule> productDetailScheduleList = productService.selectListProductDetailSchedule(paramProduct);
//
//			//HashMap<String, Object> mapDay = new HashMap<>();
//			//ArrayList<ProductDetailSchedule> listDay = new ArrayList<ProductDetailSchedule>();
//			HashMap<String, Object> detailParamMap = new HashMap<>();
//			ArrayList<String> listDay = new ArrayList<>();
//			ArrayList<String> detailIdList = new ArrayList<>();
//
//			if ( productDetailScheduleList.size() > 0 ) {
//				for ( ProductDetailSchedule productDetailSchedule : productDetailScheduleList ) {
//					detailIdList.add(String.valueOf(productDetailSchedule.getDetailId()));
//				}
//
//				for (ProductDetailSchedule pds : productDetailScheduleList) {
//					if( !detailParamMap.containsKey(pds.getDetailCategory()) ) {
//						detailParamMap.put( pds.getDetailCategory(), pds );
//						listDay.add( pds.getDetailCategory() );
//					}
//
//					detailParamMap.put("detailId", pds.getDetailId() );
//					pds.setListProductDetailScheduleImage( productService.selectListProductTourDetailImage(detailParamMap) );
//				}
//
//				mav.addObject("listDay", listDay);
//				mav.addObject("detailScheduleList", productDetailScheduleList);
//
//				/*
//				mav.addObject("detailScheduleList", productDetailScheduleList);
//				mav.addObject("detailScheduleImageList", productDetailScheduleImageList);
//				*/
//			}
//
//			paramPolicy.put("isLastPolicy", true);
//
//			paramPolicy.put("policyType", "4");
//			Policy policy4 = pageService.selectOnePolicy( paramPolicy );
//			paramPolicy.put("policyType", "9");
//			Policy policy9 = pageService.selectOnePolicy( paramPolicy );
//			paramPolicy.put("policyType", "10");
//			Policy policy10 = pageService.selectOnePolicy( paramPolicy );
//
//			mav.addObject("menu", menu);
//			mav.addObject("product", ProductInfo);
//			mav.addObject("imageList", imageList);
//			mav.addObject("optionPriceListFix", optionPriceListFix);
//			mav.addObject("optionPriceListDay", optionPriceListDay);
//
//			mav.addObject("listDay", listDay);
//			mav.addObject("detailScheduleList", productDetailScheduleList);
//
//			mav.addObject("policyPrivacy", policy4);
//			mav.addObject("policyRegulation", policy9);
//			mav.addObject("policyStipulation", policy10);
//
//		} else {
//			//오~~류~~인~~데~~
//			//mav.setView(new RedirectView("/order/error"));
//		}
//
//		mav.setViewName("/front/product/view");
//		return mav;
//	}
//
//	@GetMapping(value = {"/{path1}/{productSerial}/{date}"})
//	@ResponseBody
//	public HashMap<String, Object> pridce_option_ajax(HttpServletRequest request,
//			@PathVariable String path1,
//			@PathVariable String productSerial,
//			@PathVariable String date){
//		HashMap<String,Object> retMap = new HashMap<String, Object>();
//
//		try {
//			HashMap<String,Object> paramMap = new HashMap<String, Object>();
//
//			paramMap.put("productSerial", productSerial);
//
//			ProductInfo	ProductInfo = productService.selectOneProduct( paramMap );
//
//			paramMap.put("productTourId", ProductInfo.getProductTourId());
//			paramMap.put("priceSetDate", date);
//
//			//상품 일자별 가격옵션
//			ArrayList<DayList> optionPriceListDay		= productService.selectListProductDayPrice( paramMap );
//			//상품 고정가격 옵션
//			ArrayList<FixPriceList> optionPriceListFix = productService.selectListProductFixPrice( paramMap );
//
//			if( optionPriceListDay.size() > 0) {
//				retMap.put("optionPriceList", optionPriceListDay);
//			} else if( optionPriceListDay.size() == 0 && optionPriceListFix.size() > 0 ) {
//				retMap.put("optionPriceList", optionPriceListFix);
//			} else {
//				retMap.put("optionPriceList", null);
//			}
//
//			retMap.put("result","success");
//			retMap.put("message","처리 성공");
//		} catch (Exception e) {
//			retMap.put("result","ERROR");
//			retMap.put("message","처리중 에러 발생하였습니다.");
//			logger.info("------");
//			logger.debug(e.getMessage());
//		}
//
//		return retMap;
//	}
//
//	@GetMapping("/product-list")
//	@ResponseBody
//	public HashMap<String, Object> productList_ajax(
//			@RequestParam HashMap<String,Object> paramMap,
//			@RequestParam(value="start", defaultValue="0") int start,
//			@RequestParam(value="length", defaultValue="10") int length,
//			@Param(value="titleLike") String titleLike
//			){
//		HashMap<String, Object> resultMap = new HashMap<>();
//
//		try{
//			//메뉴 정보 불러오기
////			if( paramMap.containsKey("typeCode") ) {
////				String manageUrl = "/" + paramMap.get("typeCode");
////				MenuUser menu = productService.selectManageMenuByUrl(manageUrl);
////				int menu_id=menu.getMenuId().intValue();
////				paramMap.put("menuId",menu_id);
////			}
//			if(length >= 0) {
//				paramMap.put("itemStartPosition", start);
//				paramMap.put("pagePerSize", length);
//			}
//			paramMap.put("searchKey", titleLike);
//
//			if(!paramMap.containsKey("orderYn"))		 paramMap.put("orderYn", "Y");
//			if(!paramMap.containsKey("productStatus")) paramMap.put("productStatus", "ALL");
//
//			int productListCount = productService.selectProductListCount(paramMap);
//			List<ProductInfo> productList = productService.selectProductList(paramMap);
//
//			resultMap.put("recordsTotal", productList);
//			resultMap.put("recordsFiltered", productListCount);
//			resultMap.put("data", productList);
//
//			resultMap.put("result", "success");
//			resultMap.put("message", "처리되었습니다.");
//		} catch (Exception e){
//			logger.error(e.getMessage());
//			resultMap.put("result", "error");
//			resultMap.put("error", e.getMessage());
//			resultMap.put("message", e.getMessage());
//		}
//
//		return resultMap;
//	}
//

	/*
	 * spring boot 베이스로 작업할 예정이다,
	 * 사용자 프로필정보를 나중에 받는다. (연령,성별 등)
	 * 프로필 설정이 안된 사용자에게는 상품이 이용이 많은순, 좋아요 많은순으로 추천한다.
	 * 프로필이 설정된 사용자에게는 다른 사용자 프로필 정보를 통해 추천 데이터를 생성한다
	 * 프로필 없는 사용자가 구매이후 프로필 설정 할 경우 상품 추천결과에도 영향을 줄 수 있는 구조로 가려고 한다.
	 * */
	@PostMapping("/recom-list")
	@ResponseBody
	public HashMap<String, Object> recom_product_List_ajax(
			@RequestParam HashMap<String,Object> paramMap,
			@RequestParam(value="start", defaultValue="0") int start,
			@RequestParam(value="length", defaultValue="10") int length,
			@RequestParam(value="isLimit", defaultValue="true") Boolean isLimit,
			@Param(value="titleLike") String titleLike,
			@Param(value="menuType") String menuType
			){
		HashMap<String, Object> resultMap = new HashMap<>();

		try{
			paramMap.put("searchKey", titleLike);

			if(!paramMap.containsKey("orderYn"))		 paramMap.put("orderYn", "Y");
			if(!paramMap.containsKey("productStatus")) paramMap.put("productStatus", "ALL");

			paramMap.put("menuType", menuType);
			paramMap.put("productUseYn", "Y");
			paramMap.put("regcyYn", "N");
			paramMap.put("deleteYn", "N");

			LoginUser user = getBaseLoginUser();

			if( user.getAgeGroup() != null ||
				user.getUserGender() != null ||
				user.getJoinReasonType() != null ) {
				paramMap.put("isProfileRecom", true);

				paramMap.put("ageGroup", user.getAgeGroup());
				paramMap.put("gender", user.getUserGender());
				paramMap.put("purpose", user.getJoinReasonType());
			} else {
				paramMap.put("isDefualtRecom", true);
			}

			/*최근 구매 항목은 추천 제외 처리*/
			HashMap<String,Object> params =new HashMap<String,Object>();
			params.put("userEmail", getBaseUserEmail() );
			params.put("sort", "createDate" );
			params.put("sortOrder", "desc" );
			params.put("itemStartPosition", 0 );
			params.put("pagePerSize", 1 );

			ArrayList<UserCustomerOrderList> list = pageService.selectListUserCustomerOrderList(params);
			ArrayList<String> exceptList = new ArrayList<String>();
			for (UserCustomerOrderList ucol : list) {
				exceptList.add( ucol.getProductSerial() );
			}
			paramMap.put("exceptList", exceptList);

			if( isLimit ) {
				if(length >= 0) {
					paramMap.put("itemStartPosition", start);
					paramMap.put("pagePerSize", length);
				}

				int productListCount = productService.selectCountProductRecommend(paramMap);

				resultMap.put("recordsTotal", productListCount);
				resultMap.put("recordsFiltered", productListCount);
			}

			List<ProductInfo> productList = productService.selectListProductRecommend(paramMap);
			resultMap.put("data", productList);

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		} catch (Exception e){
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
			resultMap.put("message", e.getMessage());
		}

		return resultMap;
	}



}
