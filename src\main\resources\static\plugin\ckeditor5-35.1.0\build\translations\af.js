(function(e){const t=e["af"]=e["af"]||{};t.dictionary=Object.assign(t.dictionary||{},{"%0 of %1":"%0 van %1","Align center":"<PERSON><PERSON> in die middel","Align left":"<PERSON>yn links","Align right":"Belyn regs","Block quote":"Verwysingsaanhaling",Bold:"Vet",Cancel:"Kanselleer",Find:"Soek","Find and replace":"Soek en vervang","Find in text…":"Soek in teks …",Italic:"Kursief",Justify:"<PERSON>yn beide kante","Match case":"Hooflettersensitief","Next result":"Volgende resultaat","Previous result":"Vorige resultaat","Remove color":"Verwyder kleur","Remove Format":"Verwyder formatering",Replace:"Vervang","Replace all":"Vervang alles","Replace with…":"Vervang met ...","Restore default":"<PERSON>ste<PERSON> verstek",Save:"Stoor","Show more items":"Wys meer items","Show options":"Wys opsies",Strikethrough:"Deurstreep","Text alignment":"Teksbelyning","Text alignment toolbar":"Teksbelyning nutsbank","Text to find must not be empty.":"Soekteks mag nie leeg wees nie.","Tip: Find some text first in order to replace it.":"Wenk: Soek eers 'n bietjie teks om dit te vervang.",Underline:"Onderstreep","Whole words only":"Slegs hele woorde"});t.getPluralForm=function(e){return e!=1}})(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));