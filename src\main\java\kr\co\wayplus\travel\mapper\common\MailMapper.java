package kr.co.wayplus.travel.mapper.common;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import kr.co.wayplus.travel.model.*;

@Mapper
@Repository
public interface MailMapper {

	int selectCountMailLog(HashMap<String, Object> paramMap);
	ArrayList<MailLog> selectListMailLog(HashMap<String, Object> paramMap);
	MailLog selectOneMailLog(HashMap<String, Object> paramMap);
	void insertMailLog(MailLog mail) throws SQLException;
	void updateMailLog(MailLog bc) throws SQLException;

//	//	<!--################################### MailAttachs ###################################-->
//	ArrayList<MailAttachs> selectListMailAttachs(HashMap<String, Object> paramMap);
//	MailAttachs selectOneMailAttachs(HashMap<String, Object> paramMap);
//	void insertMailAttachs(MailAttachs baf) throws SQLException;
//	void deleteMailAttachs(MailAttachs baf) throws SQLException;

//	<!--################################### mailRecivedUser ###################################-->
	ArrayList<MailRecivedUser> selectListMailRecivedUser(HashMap<String, Object> paramMap);

	int selectCountMailRecivedUser(HashMap<String, Object> paramMap);
	void insertMailRecivedUser(MailRecivedUser mru) throws SQLException;
	void deleteMailRecivedUser(MailRecivedUser mru) throws SQLException;

}
