/*
Copyright (c) 2021 Kil Hyung-jin, with Reserved Font Name Pretendard.
https://github.com/orioncactus/pretendard

This Font Software is licensed under the SIL Open Font License, Version 1.1.
This license is copied below, and is also available with a FAQ at:
http://scripts.sil.org/OFL
*/

@font-face {
	font-family: 'Pretendard';
	font-weight: 900;
	font-display: swap;
	src: local('Pretendard Black'), url('./woff2-subset/Pretendard-Black.subset.woff2') format('woff2'), url('./woff-subset/Pretendard-Black.subset.woff') format('woff');
}

@font-face {
	font-family: 'Pretendard';
	font-weight: 800;
	font-display: swap;
	src: local('Pretendard ExtraBold'), url('./woff2-subset/Pretendard-ExtraBold.subset.woff2') format('woff2'), url('./woff-subset/Pretendard-ExtraBold.subset.woff') format('woff');
}

@font-face {
	font-family: 'Pretendard';
	font-weight: 700;
	font-display: swap;
	src: local('Pretendard Bold'), url('./woff2-subset/Pretendard-Bold.subset.woff2') format('woff2'), url('./woff-subset/Pretendard-Bold.subset.woff') format('woff');
}

@font-face {
	font-family: 'Pretendard';
	font-weight: 600;
	font-display: swap;
	src: local('Pretendard SemiBold'), url('./woff2-subset/Pretendard-SemiBold.subset.woff2') format('woff2'), url('./woff-subset/Pretendard-SemiBold.subset.woff') format('woff');
}

@font-face {
	font-family: 'Pretendard';
	font-weight: 500;
	font-display: swap;
	src: local('Pretendard Medium'), url('./woff2-subset/Pretendard-Medium.subset.woff2') format('woff2'), url('./woff-subset/Pretendard-Medium.subset.woff') format('woff');
}

@font-face {
	font-family: 'Pretendard';
	font-weight: 400;
	font-display: swap;
	src: local('Pretendard Regular'), url('./woff2-subset/Pretendard-Regular.subset.woff2') format('woff2'), url('./woff-subset/Pretendard-Regular.subset.woff') format('woff');
}

@font-face {
	font-family: 'Pretendard';
	font-weight: 300;
	font-display: swap;
	src: local('Pretendard Light'), url('./woff2-subset/Pretendard-Light.subset.woff2') format('woff2'), url('./woff-subset/Pretendard-Light.subset.woff') format('woff');
}

@font-face {
	font-family: 'Pretendard';
	font-weight: 200;
	font-display: swap;
	src: local('Pretendard ExtraLight'), url('./woff2-subset/Pretendard-ExtraLight.subset.woff2') format('woff2'), url('./woff-subset/Pretendard-ExtraLight.subset.woff') format('woff');
}

@font-face {
	font-family: 'Pretendard';
	font-weight: 100;
	font-display: swap;
	src: local('Pretendard Thin'), url('./woff2-subset/Pretendard-Thin.subset.woff2') format('woff2'), url('./woff-subset/Pretendard-Thin.subset.woff') format('woff');
}
