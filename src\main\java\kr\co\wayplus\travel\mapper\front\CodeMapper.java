package kr.co.wayplus.travel.mapper.front;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import kr.co.wayplus.travel.model.CodeItem;

@Mapper
@Repository
public interface CodeMapper {
	
//	<!--################################### CodeItem ###################################-->
	CodeItem selectOneCodeItem(HashMap<String, Object> paramMap);
}
