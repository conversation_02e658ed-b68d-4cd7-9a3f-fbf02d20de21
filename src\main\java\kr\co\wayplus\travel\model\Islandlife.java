package kr.co.wayplus.travel.model;

import java.util.ArrayList;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@Data
public class Islandlife extends CommonDataSet {
	private int rownum;

	private Integer id;
	private String title;	//섬살이유형
	private String subTitle;	//섬살이 서브제목
	private String content;	//섬살이유형내용
	private String selectColor; //선택한 섬살이 색깔
	private String islandlifeThumbnail;
	private String useYn;


}
