<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.front.ContentsMapper">
<!--
	 * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
-->
<!--################################### contents ###################################-->
	<select id="selectCountContents" parameterType="HashMap" resultType="Integer">
		SELECT count(content_id)
		  FROM contents a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)" >	and a.content_id=#{contentId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperContentId)" >	and upper_content_id=#{upperContentId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentName)" >	and content_name=#{contentName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(exposeType)" >	and expose_type=#{exposeType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentType)" >	and content_type=#{contentType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentSort)" >	and content_sort=#{contentSort}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentDesc)" >	and content_desc=#{contentDesc}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(hideYn)" >	and hide_yn=#{hideYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and a.create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and a.create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		 </where>
	</select>

	<select id="selectListContents" parameterType="HashMap" resultType="Contents">
		SELECT *
		  FROM(
			SELECT @rownum:=@rownum+1 AS rownum,
				   a.content_id, upper_content_id,
				   expose_type, content_name, content_type, content_sub_type, content_url, content_sort, content_desc, hide_yn, use_yn,
				   a.create_id, a.create_date, last_update_id, last_update_date, delete_yn, delete_id, delete_date
			  FROM contents a
			  join (SELECT @rownum:= 0) rnum
			<where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)" >	and a.content_id=#{contentId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperContentId)" >	and upper_content_id=#{upperContentId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentName)" >	and content_name=#{contentName}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(exposeType)" >	and expose_type=#{exposeType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentType)" >	and content_type=#{contentType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentSort)" >	and content_sort=#{contentSort}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentDesc)" >	and content_desc=#{contentDesc}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(hideYn)" >	and hide_yn=#{hideYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and a.create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and a.create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
			</where>

			 <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
				ORDER BY
				<choose>
					<when test="sort=='contentId'" >	content_id	</when>
					<when test="sort=='upperContentId'" >	upper_content_id	</when>
					<when test="sort=='contentName'" >	content_name	</when>
					<when test="sort=='contentType'" >	content_type	</when>
					<when test="sort=='contentSort'" >	content_sort	</when>
					<when test="sort=='contentDesc'" >	content_desc	</when>
					<when test="sort=='useYn'" >	use_yn	</when>
					<when test="sort=='hideYn'" >	hide_yn	</when>
					<when test="sort=='deleteYn'" >	delete_yn	</when>
					<when test="sort=='createId'" >	create_id	</when>
					<when test="sort=='createDate'" >	create_date	</when>
					<when test="sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="sort=='deleteId'" >	delete_id	</when>
					<when test="sort=='deleteDate'" >	delete_date	</when>
					<otherwise>rownum</otherwise>
				</choose>
				<choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
				ORDER BY <foreach item="item" index="index" collection="listSort" separator=",">
				<choose>
					<when test="item.sort=='contentId'" >	content_id	</when>
					<when test="item.sort=='upperContentId'" >	upper_content_id	</when>
					<when test="item.sort=='contentName'" >	content_name	</when>
					<when test="item.sort=='contentType'" >	content_type	</when>
					<when test="item.sort=='contentSort'" >	content_sort	</when>
					<when test="item.sort=='contentDesc'" >	content_desc	</when>
					<when test="item.sort=='useYn'" >	use_yn	</when>
					<when test="item.sort=='hideYn'" >	hide_yn	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="item.sort=='deleteId'" >	delete_id	</when>
					<when test="item.sort=='deleteDate'" >	delete_date	</when>
				</choose>
				<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>

			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
		 LIMIT #{itemStartPosition}, #{pagePerSize}
		 </if>
	</select>

	<select id="selectOneContents" parameterType="HashMap" resultType="Contents">
		SELECT a.content_id, upper_content_id,
			   expose_type, content_name, content_type,content_sub_type, content_url, content_sort, content_desc, hide_yn, use_yn,
			   a.create_id, a.create_date, last_update_id, last_update_date, delete_yn, delete_id, delete_date
		  FROM contents a
		 <where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)" >	and a.content_id=#{contentId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperContentId)" >	and upper_content_id=#{upperContentId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentName)" >	and content_name=#{contentName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(exposeType)" >	and expose_type=#{exposeType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentType)" >	and content_type=#{contentType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentSort)" >	and content_sort=#{contentSort}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentDesc)" >	and content_desc=#{contentDesc}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(hideYn)" >	and hide_yn=#{hideYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and a.create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and a.create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		 </where>
	</select>
<!--################################### contentsItem ###################################-->
	<select id="selectCountContentsItem" parameterType="HashMap" resultType="Integer">
		SELECT count(content_item_id) FROM contents_item
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentItemId)" >	and content_item_id=#{contentItemId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)" >	and content_id=#{contentId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentLangType)" >	and content_lang_type=#{contentLangType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentItemDetail)" >	and content_item_detail=#{contentItemDetail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentItemSort)" >	and content_item_sort=#{contentItemSort}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		 </where>
	</select>

	<select id="selectListContentsItem" parameterType="HashMap" resultType="ContentsItem">
		SELECT *
		  FROM(
			SELECT @rownum:=@rownum+1 AS rownum,
				   content_item_id, content_id, content_lang_type, REPLACE(content_item_detail,'contenteditable="true"', '') content_item_detail, content_item_sort, use_yn,
				   create_id, create_date, last_update_id, last_update_date, delete_yn, delete_id, delete_date
			  FROM contents_item a
			  join (SELECT @rownum:= 0) rnum
			<where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentItemId)" >	and content_item_id=#{contentItemId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)" >	and content_id=#{contentId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentLangType)" >	and content_lang_type=#{contentLangType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentItemDetail)" >	and content_item_detail=#{contentItemDetail}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentItemSort)" >	and content_item_sort=#{contentItemSort}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
			</where>

			 <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
				ORDER BY
				<choose>
					<when test="sort=='contentId'" >	content_id	</when>
					<when test="sort=='upperContentId'" >	upper_content_id	</when>
					<when test="sort=='contentName'" >	content_name	</when>
					<when test="sort=='contentType'" >	content_type	</when>
					<when test="sort=='contentSort'" >	content_sort	</when>
					<when test="sort=='contentDesc'" >	content_desc	</when>
					<when test="sort=='useYn'" >	use_yn	</when>
					<when test="sort=='hideYn'" >	hide_yn	</when>
					<when test="sort=='deleteYn'" >	delete_yn	</when>
					<when test="sort=='createId'" >	create_id	</when>
					<when test="sort=='createDate'" >	create_date	</when>
					<when test="sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="sort=='deleteId'" >	delete_id	</when>
					<when test="sort=='deleteDate'" >	delete_date	</when>
					<otherwise>rownum</otherwise>
				</choose>
				<choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
				ORDER BY <foreach item="item" index="index" collection="listSort" separator=",">
				<choose>
					<when test="item.sort=='contentId'" >	content_id	</when>
					<when test="item.sort=='upperContentId'" >	upper_content_id	</when>
					<when test="item.sort=='contentName'" >	content_name	</when>
					<when test="item.sort=='contentType'" >	content_type	</when>
					<when test="item.sort=='contentSort'" >	content_sort	</when>
					<when test="item.sort=='contentDesc'" >	content_desc	</when>
					<when test="item.sort=='useYn'" >	use_yn	</when>
					<when test="item.sort=='hideYn'" >	hide_yn	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="item.sort=='deleteId'" >	delete_id	</when>
					<when test="item.sort=='deleteDate'" >	delete_date	</when>
				</choose>
				<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a

		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isLast) and isLast">
		 order by last_update_date desc
		 </if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
		 LIMIT #{itemStartPosition}, #{pagePerSize}
		 </if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isLast) and isLast">
		 LIMIT 1
		 </if>
	</select>

	<select id="selectOneContentsItem" parameterType="HashMap" resultType="ContentsItem">
		SELECT content_item_id, content_id, content_lang_type, content_item_detail, content_item_sort, use_yn,
			   create_id, create_date, last_update_id, last_update_date, delete_yn, delete_id, delete_date
		  FROM contents_item a
		 <where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentItemId)" >	and content_item_id=#{contentItemId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentId)" >	and content_id=#{contentId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentLangType)" >	and content_lang_type=#{contentLangType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentItemDetail)" >	and content_item_detail=#{contentItemDetail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentItemSort)" >	and content_item_sort=#{contentItemSort}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		 </where>
	</select>
</mapper>