/**
 * 1. v1의 지번 주소 조회 없을경우
 * 2. v2의 주소 조회
 */

var apikey="EB6853D5-D0A8-3EC0-A0A6-F0A93700B8BE";
var domin="http://localhost:8080"
var isConsole = true;

function getGps(addr,isAlert,callbak){
	console.log(`getGps(${addr})`);
	var point = [x,y];
//	console.log(point);
	getV2JibunToGps(addr,isAlert,callbak);
}

function getV2JibunToGps(addr,isAlert,callbak){
	var data = "q="+addr+"&output=json&epsg=epsg:4326&apiKey="+apikey+"&domain="+domin;
	var addr="";
	$.ajax({
	    type: "get",
	    url: "http://apis.vworld.kr/jibun2coord.do",
	    data : data,
	    dataType: 'jsonp',
	    async: false,
	    success: function(data) {
	    	if(isConsole) console.log("---getV2JibunToGps---");
//	    	if(isConsole) console.log(point, (data.ADDR == undefined), data);
	    	if(data.JUSO == undefined){
	    		if(isConsole) console.log("NOT_FONUND", data);
//	    		addr = "fail";
	    		//getV2Info(point,isAlert,callbak);
	    	} else {
	    		if(isConsole) console.log("OK", data);

	    		var point = [data.EPSG_4326_X, data.EPSG_4326_Y];

	    		console.log("pnucode.getV2JibunToGps에서 결과 : ",point);
//	    		console.log("pnucode.js에서 알림, 수용가번호 : ["+point[2]+"], 좌표:["+point[0]+","+point[1]+"], getV1Jibun 결과 확인.",addr);
	    		callbak(point,addr,getPnucode(addr),isAlert);
	    	}
	    },
	    beforesend: function(){},
    	error:function(request,status,error) {
    		alert('처리중 시스템 장애입니다');
    		console.log("code:"+request.status+"\nerror:"+error);
    	}
	});
	return addr;
}

function getV2NewToGps(addr,isAlert,callbak){
	var data = "q="+addr+"&output=json&epsg=epsg:4326&apiKey="+apikey+"&domain="+domin;
	var addr="";
	$.ajax({
	    type: "get",
	    url: "http://apis.vworld.kr/new2coord.do",
	    data : data,
	    dataType: 'jsonp',
	    async: false,
	    success: function(data) {
	    	if(isConsole) console.log("---getV2NewToGps---");
//	    	if(isConsole) console.log(point, (data.ADDR == undefined), data);
	    	if(data.JUSO == undefined){
	    		if(isConsole) console.log("NOT_FONUND", data);
//	    		addr = "fail";
	    		//getV2Info(point,isAlert,callbak);
	    	} else {
	    		if(isConsole) console.log("OK", data);

	    		var point = [data.EPSG_4326_X, data.EPSG_4326_Y];

	    		console.log("pnucode.getV2NewToGps에서 결과 : ",point);
//	    		console.log("pnucode.js에서 알림, 수용가번호 : ["+point[2]+"], 좌표:["+point[0]+","+point[1]+"], getV1Jibun 결과 확인.",addr);
	    		//callbak(point,addr,getPnucode(addr),isAlert);
	    	}
	    },
	    beforesend: function(){},
    	error:function(request,status,error) {
    		alert('처리중 시스템 장애입니다');
    		console.log("code:"+request.status+"\nerror:"+error);
    	}
	});
	return addr;
}

function getJuso(x,y,no,isAlert,callbak){
//	console.log('getV1Jibun(x,y,no,callbak)');
	var point = [x,y,no];
//	console.log(point);
	getV1Jibun(point,isAlert,callbak);
}


function getV1Jibun(point,isAlert,callbak){
	var data = "x="+point[0]+"&y="+point[1]+"&output=json&epsg=epsg:4326&apiKey="+apikey+"&domain="+domin;
	var addr="";
	$.ajax({
	    type: "get",
	    url: "http://apis.vworld.kr/coord2jibun.do",
	    data : data,
	    dataType: 'jsonp',
	    async: false,
	    success: function(data) {
	    	if(isConsole) console.log("---getV1Jibun---");
//	    	if(isConsole) console.log(point, (data.ADDR == undefined), data);
	    	if(data.ADDR == undefined){
	    		if(isConsole) console.log("NOT_FONUND", data);
//	    		addr = "fail";
	    		getV2Info(point,isAlert,callbak);
	    	} else {
	    		if(isConsole) console.log("OK", data);
	    		addr = data.ADDR;
//	    		console.log(typeof(addr));
	    		if(addr.lastIndexOf('리 ') >= 0 ){
	    			addr = addr.substring(0, addr.lastIndexOf('리 ')+1);
	    		} else if(addr.lastIndexOf('동 ') >= 0){
	    			addr = addr.substring(0, addr.lastIndexOf('동 ')+1);
	    		} else if(addr.lastIndexOf('면 ') >= 0){
	    			addr = addr.substring(0, addr.lastIndexOf('면 ')+1);
	    		} else if(addr.lastIndexOf('읍 ') >= 0){
	    			addr = addr.substring(0, addr.lastIndexOf('읍 ')+1);
	    		} else if(addr.lastIndexOf('구 ') >= 0){
	    			addr = addr.substring(0, addr.lastIndexOf('구 ')+1);
	    		} else if(addr.lastIndexOf('군 ') >= 0){
	    			addr = addr.substring(0, addr.lastIndexOf('군 ')+1);
	    		} else if(addr.lastIndexOf('시 ') >= 0){
	    			/*여기는 시도, 시군구 ...*/
	    			addr = addr.substring(0, addr.lastIndexOf('시 ')+1);
	    		} else if(addr.lastIndexOf('시 ') >= 0){
	    			addr = addr.substring(0, addr.lastIndexOf('도 ')+1);
	    		}
//	    		console.log("pnucode.getV1Jibun에서 결과 : ",addr);
	    		console.log("pnucode.js에서 알림, 수용가번호 : ["+point[2]+"], 좌표:["+point[0]+","+point[1]+"], getV1Jibun 결과 확인.",addr);
	    		callbak(point,addr,getPnucode(addr),isAlert);
	    	}
	    },
	    beforesend: function(){},
    	error:function(request,status,error) {
    		alert('처리중 시스템 장애입니다');
    		console.log("code:"+request.status+"\nerror:"+error);
    	}
	});
	return addr;
}

function getV2Info(point,isAlert, callbak){
	var data = "key="+apikey+"&service=address&request=getAddress&version=2.0&crs=epsg%3A4326&version=2.0&format=json&type=road&zipcode=true&simple=false&point="+point[0]+","+point[1];
	var code;
	$.ajax({
	    type: "get",
	    url: "http://api.vworld.kr/req/address",
	    data : data,
	    dataType: 'jsonp',
	    async: false,
	    success: function(data) {
	    	if(isConsole) console.log("---getV2Info---");
	    	var response = data.response;
	    	if(isConsole) console.log(response.status, response);
	    	if(response.status == 'OK'){
	    		for(i in response.result){
	    			var data = response.result[i].structure;
	    			addr = data.level1 + " " + data.level2+ " " + data.level3;
//	    			console.log("pnucode.getV2Info에서 결과 : ",addr);
	    			console.log("pnucode.js에서 알림, 수용가번호 : ["+point[2]+"], 좌표:["+point[0]+","+point[1]+"], getV2Info 결과 확인.",addr);
	    			callbak(point,addr,getPnucode(addr),isAlert);
//	    			console.log(code);
	    		}
	    	} else {
	    		console.log("pnucode.js에서 알림, 수용가번호 : ["+point[2]+"], 좌표:["+point[0]+","+point[1]+"], getV1Jibun, getV2Info 결과 없었음.");
//	    		getV1New(point, callbak);
//	    		code = response.status;
	    	}
	    },
	    beforesend: function(){},
    	error:function(request,status,error) {
    		alert('처리중 시스템 장애입니다');
    		console.log("code:"+request.status+"\nerror:"+error);
    	}
	});
	return code;
}

function getV1New(point,isAlert,callbak){
	var data = "x="+point[0]+"&y="+point[1]+"&output=json&epsg=epsg:4326&apiKey="+apikey+"&domain="+domin;
	var addr="";
	$.ajax({
	    type: "get",
	    url: "http://apis.vworld.kr/coord2new.do",
	    data : data,
	    dataType: 'jsonp',
	    async: false,
	    success: function(data) {
	    	if(isConsole) console.log("---getV1new---");
	    	if(data.result ="검색결과가 없습니다."){
	    		if(isConsole) console.log("NOT_FONUND", data);
	    		getV1Jibun(point,isAlert,callbak);
	    	} else {
	    		if(isConsole) console.log("OK", data);
	    		addr = data.NEW_JUSO;
	    		callbak(point,addr,getPnucode(addr),isAlert);
	    	}
//	    	console.log(addr);
	    },
	    beforesend: function(){},
    	error:function(request,status,error) {
    		alert('처리중 시스템 장애입니다');
    		console.log("code:"+request.status+"\nerror:"+error);
    	}
	});

	return addr;
}



function getPnucode(addr){
	var code;
	$.ajax({
	    type: "post",
	    url: "/public/Pnucode/getPnucode.json",
	    data :  {"pnuAddr" : addr},
//	    dataType: 'jsonp',
	    dataType : "json",
	    contentType: "application/x-www-form-urlencoded; charset=UTF-8",
	    async: false,
	    success: function(res) {
	    	if ("success" == res.result) {
	    		if(res.data != null){
	    			code = res.data.pnucode;
	    		}
	    	}
//	    	console.log('----------');
	    },
	    beforesend: function(){},
    	error:function(request,status,error) {
    		alert('처리중 시스템 장애입니다');
    		console.log("code:"+request.status+"\nerror:"+error);
    	}
	});

	return code;
}


function getPoint(pnucode){
	console.log(pnucode);
}