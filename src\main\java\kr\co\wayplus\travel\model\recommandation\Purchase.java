package kr.co.wayplus.travel.model.recommandation;

import java.math.BigDecimal;
import java.time.LocalDateTime;


import kr.co.wayplus.travel.model.ProductInfo;
import lombok.Getter;
import lombok.Setter;


// 1. Purchase Entity
//@Entity
@Getter
@Setter
//@Table(name = "purchases")
public class Purchase {
//    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    //@ManyToOne(fetch = FetchType.LAZY)
    //@JoinColumn(name = "user_id")
    //private User user;

    //@ManyToOne(fetch = FetchType.LAZY)
    //@JoinColumn(name = "product_id")
    private ProductInfo product;

    private Double amount;  // 구매 금액
    private Integer quantity;   // 구매 수량
    private LocalDateTime purchaseDate;  // 구매 일시

    //@OneToOne(mappedBy = "purchase", cascade = CascadeType.ALL)
    //private Review review;      // 구매 후기

    private String orderStatus; // 주문 상태 (COMPLETED, CANCELLED 등)

    // 구매 이력 추적을 위한 메타데이터
    private String deviceType;  // 구매 디바이스 (WEB, MOBILE, APP 등)
    private String paymentMethod; // 결제 수단


}