package kr.co.wayplus.travel.mapper.manage;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import kr.co.wayplus.travel.model.ManageMenu;
import kr.co.wayplus.travel.model.MenuConnectBoard;
import kr.co.wayplus.travel.model.MenuConnectPlace;
import kr.co.wayplus.travel.model.MenuUser;

@Mapper
@Repository
public interface ManageMapper {
	ArrayList<ManageMenu> selectListManageMenu(HashMap<String, Object> paramMap);

	ManageMenu selectOneManageMenu(HashMap<String, Object> paramMap);

	void insertManageMenu(ManageMenu menu) throws SQLException;

	void updateManageMenu(ManageMenu menu) throws SQLException;

	void deleteManageMenu(ManageMenu menu) throws SQLException;

	/*################################################statistics################################################*/
	ArrayList<Map<String, Object>> selectListStatisticDate(Map<String, Object> paramMap);

	Map<String, Object> selectListStatisticInfo(Map<String, Object> paramMap);

	/*################################################statistics################################################*/

	int selectCountMenuConnectBoard(HashMap<String, Object> paramMap);

	MenuConnectBoard selectOneMenuConnectBoard(HashMap<String, Object> paramMap);

	void insertMenuConnectBoard(MenuConnectBoard data) throws SQLException;

	void deleteMenuConnectBoard(HashMap<String, Object> paramMap) throws SQLException;

	/*################################################menuConnectPlace################################################*/

	int selectCountMenuConnectPlace(HashMap<String, Object> paramMap);

	MenuConnectPlace selectOneMenuConnectPlace(HashMap<String, Object> paramMap);

	void insertMenuConnectPlace(MenuConnectPlace data) throws SQLException;

	void deleteMenuConnectPlace(HashMap<String, Object> paramMap) throws SQLException;
}