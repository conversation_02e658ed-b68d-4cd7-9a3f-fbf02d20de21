package kr.co.wayplus.travel.model;

import java.util.ArrayList;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@Data
public class SurveyQuestionAnswer extends CommonDataSet {
	private Integer id;	//고유 번호
	private Integer surveyId;	//설문ID
	private Integer islandlifeId;	//질문ID
	private Integer menuId;	//추천 메뉴ID
	private String answer;	//답변내역
	private Integer sortNumber;	//답변순서
}
