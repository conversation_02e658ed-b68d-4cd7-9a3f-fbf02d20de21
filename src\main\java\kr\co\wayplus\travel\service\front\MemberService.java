package kr.co.wayplus.travel.service.front;

import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import kr.co.wayplus.travel.mapper.user.UserMapper;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.util.CryptoUtil;
import kr.co.wayplus.travel.util.CustomBcryptPasswordEncoder;

@Service
public class MemberService {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final UserMapper userMapper;

    @Autowired
    public MemberService(UserMapper userMapper) {
        this.userMapper = userMapper;
    }

	public Boolean verifyUserInfo(LoginUser user)  throws InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {

        LoginUser storedUser = this.selectUserByUserid(user.getUserEmail());
        if (storedUser == null){
            return false;
        }

        CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();

        user.setUserPassword(CryptoUtil.aesDecode(user.getUserPassword(),user.getEncrypt(),user.getIv()));

        if (passwordEncoder.directMatches(user.getUserPassword(),storedUser.getPassword())){
            return true;
        }

        return false;
    }

	public LoginUser selectUserByUserid(String userEmail) {
		return userMapper.selectUserByUserid( userEmail );
	}

}
