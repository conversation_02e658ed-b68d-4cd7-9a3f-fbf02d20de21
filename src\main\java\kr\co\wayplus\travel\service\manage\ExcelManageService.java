package kr.co.wayplus.travel.service.manage;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import kr.co.wayplus.travel.builder.ExcelDataBuilder;
import kr.co.wayplus.travel.mapper.manage.PolicyManageMapper;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.Policy;
import kr.co.wayplus.travel.model.Reservation;
import kr.co.wayplus.travel.model.UserGroup;
import kr.co.wayplus.travel.model.excel.ExcelData;
import kr.co.wayplus.travel.model.excel.ExcelMetadata;
import kr.co.wayplus.travel.model.excel.HeaderInfo;
import kr.co.wayplus.travel.util.ExcelUtil;

@Service
public class ExcelManageService {
    private final Logger logger = LoggerFactory.getLogger(getClass());

	private final ReservationManageService reservationManageService;
	private final UserManageService userManageService;

    public ExcelManageService(ReservationManageService reservationManageService, UserManageService userManageService) {
        this.reservationManageService = reservationManageService;
				this.userManageService = userManageService;
    }

	// reservation-예약관리, paidList-결제내역조회
	//, userList-회원관리, groupList-단체관리
	public byte[] generateExcel(HashMap<String, Object> param) throws IOException, SQLException {
		ExcelMetadata metadata = createHeaderData(param.get("excelType").toString());
		List<ExcelData> dataList = createCellData(param);

		return generateExcelItem(metadata, dataList);
	}

	private ExcelMetadata createHeaderData(String excelType) {
        switch (excelType) {
            case "reservation":
                return ExcelMetadata.builder()
                    .sheetName("예약관리 리스트")
                    .addHeaderRow(Arrays.asList(
                        new HeaderInfo("예약번호", 1, 1),
                        new HeaderInfo("상태정보", 1, 1),
                        new HeaderInfo("여행일자", 1, 1),
                        new HeaderInfo("상품정보", 1, 1),
                        new HeaderInfo("예약자정보", 1, 1),
                        new HeaderInfo("총금액", 1, 1),
                        new HeaderInfo("총입금액", 1, 1),
                        new HeaderInfo("잔액", 1, 1),
                        new HeaderInfo("접수일자", 1, 1)
                    ))
                    .addColumnMapping("id", "예약번호")
                    .addColumnMapping("categoryTitle", "상태정보")
                    .addColumnMapping("travelScheduleDt", "여행일자")
                    .addColumnMapping("productInfo", "상품정보")
                    .addColumnMapping("userInfo", "예약자정보")
                    .addColumnMapping("amtT", "총금액")
                    .addColumnMapping("amtD", "총입금액")
                    .addColumnMapping("amtB", "잔액")
                    .addColumnMapping("createDate", "접수일자")
                    .startRow(0)
                    .startCol(0)
                    .build();
			case "paidList":
                return ExcelMetadata.builder()
                    .sheetName("결제내역조회")
                    .addHeaderRow(Arrays.asList(
                        new HeaderInfo("접수일시", 1, 1),
                        new HeaderInfo("고객ID(email)", 1, 1),
                        new HeaderInfo("고객명", 1, 1),
                        new HeaderInfo("연락처", 1, 1),
                        new HeaderInfo("발생비용", 1, 1),
                        new HeaderInfo("입금비용", 1, 1),
                        new HeaderInfo("내용", 1, 1),
                        new HeaderInfo("작성자", 1, 1),
                        new HeaderInfo("작성일", 1, 1)
                    ))
                    .addColumnMapping("payDate", "접수일시")
                    .addColumnMapping("userEmail", "고객ID(email)")
                    .addColumnMapping("userName", "고객명")
                    .addColumnMapping("userMobile", "연락처")
                    .addColumnMapping("amtG", "발생비용")
                    .addColumnMapping("amtD", "입금비용")
                    .addColumnMapping("payComment", "내용")
                    .addColumnMapping("createName", "작성자")
                    .addColumnMapping("createDt", "작성일")
                    .startRow(0)
                    .startCol(0)
                    .build();
			case "userList":
                return ExcelMetadata.builder()
                    .sheetName("회원관리")
                    .addHeaderRow(Arrays.asList(
                        new HeaderInfo("아이디", 1, 1),
                        new HeaderInfo("회원명", 1, 1),
                        new HeaderInfo("연락처", 1, 1),
                        new HeaderInfo("가입유형", 1, 1),
                        new HeaderInfo("회원상태", 1, 1),
                        new HeaderInfo("가입일", 1, 1)
                    ))
                    .addColumnMapping("userEmail", "아이디")
                    .addColumnMapping("userName", "회원명")
                    .addColumnMapping("userMobile", "연락처")
                    .addColumnMapping("userJoinType", "가입유형")
                    .addColumnMapping("accountStatus", "회원상태")
                    .addColumnMapping("userJoinDate", "가입일")
                    .startRow(0)
                    .startCol(0)
                    .build();
			case "groupList":
					return ExcelMetadata.builder()
						.sheetName("단체관리")
						.addHeaderRow(Arrays.asList(
							new HeaderInfo("순번", 1, 1),
							new HeaderInfo("단체명", 1, 1),
							new HeaderInfo("단체목적", 1, 1),
							new HeaderInfo("단체소속인원수", 1, 1),
							new HeaderInfo("예약건수", 1, 1),
							new HeaderInfo("등록일", 1, 1)
						))
						.addColumnMapping("rownum", "순번")
						.addColumnMapping("groupName", "단체명")
						.addColumnMapping("groupPurpose", "단체목적")
						.addColumnMapping("userCount", "단체소속인원수")
						.addColumnMapping("reservationCount", "예약건수")
						.addColumnMapping("createDate", "등록일")
						.addColumnMapping("createName", "작성자")
						.addColumnMapping("createDt", "작성일")
						.startRow(0)
						.startCol(0)
						.build();
            default:
                return ExcelMetadata.builder()
                    .sheetName("데이터 없음")
                    .addHeaderRow(Arrays.asList(
                        new HeaderInfo("데이터 없음", 1, 1)
                    ))
                    .addColumnMapping("noData", "데이터 없음")
                    .startRow(0)
                    .startCol(0)
                    .build();
        }
    }

    private List<ExcelData> createCellData(HashMap<String, Object> param) {
        List<ExcelData> dataList = new ArrayList<>();
        String excelType = param.get("excelType").toString();

        switch (excelType) {
            case "reservation":
                param.put("itemStartPosition", null);
				param.put("pagePerSize", null);
                List<Reservation> reservationList = reservationManageService.selectListReservation(param);
                for (Reservation reservation : reservationList) {
                    String categoryTitle = reservation.getCategoryTitle();
                    if (reservation.getGroupName() != null && !reservation.getGroupName().isEmpty()) {
                        categoryTitle += "단체명:" + reservation.getGroupName() + "\n";
                    }

                    // 예약 상태에 따른 스타일 정보
                    String backgroundColor = "";
                    String textColor = "";

                    if ("N".equals(reservation.getCancelYn())) {
                        switch (reservation.getReservationCode()) {
                            case "0": // 접수
                                backgroundColor = "BROWN";
                                break;
                            case "1": // 처리중
                                backgroundColor = "LIGHT_YELLOW";
                                break;
                            case "2": // 입금대기
                                backgroundColor = "LIGHT_ORANGE";
                                break;
                            case "3": // 확약
                                backgroundColor = "LIGHT_GREEN";
                                break;
                            case "4": // 완료
                                backgroundColor = "LIGHT_GREEN";
                                break;
                            case "5": // 취소요청
                                backgroundColor = "BLUE";
                                textColor = "WHITE";
                                break;
                            default:
                                break;
                        }
                        categoryTitle += " " + reservation.getReservationCodeName();
                    } else {
                        backgroundColor = "RED";
                        textColor = "WHITE";
                        categoryTitle += " 취소처리";
                    }

                    String ProductInfo = "";
                    if (reservation.getProductSerial() != null && reservation.getProductTourId() != null) {
                        ProductInfo = reservation.getProductSerial() + "(" + reservation.getProductTourId() + ")";
                        ProductInfo += "\n"+reservation.getProductTitle();
                    } else if (reservation.getProductSerial() != null && reservation.getProductTourId() == null) {
                        ProductInfo = reservation.getProductSerial() + "(last)";
                        ProductInfo += "\n"+reservation.getProductTitle();
                    } else {
                        ProductInfo = "~상품미선택~";
                    }
                    String userInfo = "";
                    if (reservation.getUserName() != null && !reservation.getUserName().isEmpty()) {
                        userInfo += reservation.getUserName();
                        userInfo += "\n" + reservation.getUserMobile();
                        if ("reservation".equals(reservation.getUserJoinType())) {
                            userInfo += "\n비회원";
                        } else {
                            userInfo += "\n" + reservation.getUserEmail();
                        }
                    } else {
                        userInfo += "~정보없음~";
                    }

                    // 잔액이 음수일 경우 빨간색 텍스트 적용
                    String balanceTextColor = reservation.getAmtB() < 0 ? "RED" : null;

                    ExcelDataBuilder builder = ExcelDataBuilder.create()
                        .id("reservation")
                        .value("id", reservation.getId())
                        .value("categoryTitle", categoryTitle)
                        .value("travelScheduleDt", reservation.getTravelScheduleDt())
                        .value("productInfo", ProductInfo)
                        .value("userInfo", userInfo)
                        .value("amtT", reservation.getAmtT())
                        .value("amtD", reservation.getAmtD())
                        .value("amtB", reservation.getAmtB())
                        .value("createDate", reservation.getCreateDate())
                        .format("completionRate", "#0.0%");

                        builder.cellStyle("categoryTitle", backgroundColor, textColor);
                        builder.cellStyle("amtB", null, balanceTextColor);

                    dataList.add(builder.build());
                }
            break;
			case "paidList":
			ArrayList<HashMap<String, Object>> userPaidList = userManageService.selectListUserCustomerPaymentVirtual(param);
				for (HashMap<String, Object> userPaid : userPaidList) {

					ExcelDataBuilder builder = ExcelDataBuilder.create()
						.id("paidList")
						.value("payDate", userPaid.get("pay_date"))
						.value("userEmail", userPaid.get("user_email"))
						.value("userName", userPaid.get("user_name"))
						.value("userMobile", userPaid.get("user_mobile"))
						.value("amtG", userPaid.get("amt_g"))
						.value("amtD", userPaid.get("amt_d"))
						.value("payComment", userPaid.get("pay_comment"))
						.value("createName", userPaid.get("create_name"))
						.value("createDt", userPaid.get("create_date"));

					dataList.add(builder.build());
				}
			break;
			case "userList":
				ArrayList<LoginUser> userList = userManageService.getUserList(param);
				for (LoginUser userPaid : userList) {

					String joinType = "";
					switch (userPaid.getUserJoinType()) {
						case "kakao":
							joinType = "카카오";
							break;
						case "naver":
							joinType = "네이버";
							break;
						case "google":
							joinType = "구글";
							break;
						case "facebook":
							joinType = "페이스북";
							break;
						case "offline":
							joinType = "오프라인";
							break;
						case "reservation":
							joinType = "예약";
							break;
						default:
							joinType = "홈페이지";
							break;
					}

					String accountStatus = "";
					switch (userPaid.getAccountStatus()) {
						case "active":
							accountStatus = "사용중";
							break;
						case "inactive":
							accountStatus = "정지중";
							break;
						case "black":
							accountStatus = "블랙리스트";
							break;
						case "withdraw":
							accountStatus = "탈퇴대기(사용자)";
							break;
						case "ban":
							accountStatus = "탈퇴자(관리자)";
							break;
						default:
							accountStatus = "정보없음";
							break;
					}

					ExcelDataBuilder builder = ExcelDataBuilder.create()
						.id("userList")
						.value("userEmail", userPaid.getUserEmail())
						.value("userName", userPaid.getUserName())
						.value("userMobile", userPaid.getUserMobile())
						.value("userJoinType", joinType)
						.value("accountStatus", accountStatus)
						.value("userJoinDate", userPaid.getUserJoinDate());

					dataList.add(builder.build());
				}
			break;
            case "groupList":
            param.put("deleteYn", "N");
			ArrayList<UserGroup> groupList = userManageService.selectListUserGroup(param);
				for (UserGroup group : groupList) {

					ExcelDataBuilder builder = ExcelDataBuilder.create()
						.id("groupList")
						.value("rownum", group.getRownum())
						.value("groupName", group.getGroupName())
						.value("groupPurpose", group.getGroupPurpose())
						.value("userCount", group.getUserCount())
						.value("reservationCount", group.getReservationCount())
						.value("createDate", group.getCreateDate());

					dataList.add(builder.build());
				}
			default:
                break;
        }

        return dataList;
    }

    private byte[] generateExcelItem(ExcelMetadata metadata, List<ExcelData> dataList) throws IOException {
        ExcelUtil excelUtil = new ExcelUtil();

        excelUtil.createHeader(metadata.getHeaders());
        excelUtil.addGridData(dataList, metadata.getHeaders().size());


        return excelUtil.generateExcelFile();
    }
}
