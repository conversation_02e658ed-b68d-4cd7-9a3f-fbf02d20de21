package kr.co.wayplus.travel.mapper.front;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import kr.co.wayplus.travel.model.ProductCategory;
import kr.co.wayplus.travel.model.ProductComment;
import kr.co.wayplus.travel.model.ProductDetailSchedule;
import kr.co.wayplus.travel.model.ProductDetailScheduleImage;
import kr.co.wayplus.travel.model.ProductInfo;
import kr.co.wayplus.travel.model.ProductInventory;
import kr.co.wayplus.travel.model.ProductPriceOption;
import kr.co.wayplus.travel.model.ProductPriceOption.DayList;
import kr.co.wayplus.travel.model.ProductPriceOption.FixPriceList;
import kr.co.wayplus.travel.model.ProductTourImages;

@Mapper
@Repository
public interface ProductMapper {

	/**
	 * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
	 */
	//	<!--################################### #{테이블명칭!!} ###################################-->
	int selectCountProduct(HashMap<String, Object> param);
	ArrayList<ProductInfo> selectListProduct(HashMap<String, Object> param);
	ArrayList<ProductInfo> selectListProgram(HashMap<String, Object> param);
	ProductInfo selectOneProduct(HashMap<String, Object> param);

	int selectCountProductRecommend(HashMap<String, Object> param);
	ArrayList<ProductInfo> selectListProductRecommend(HashMap<String, Object> param);

	FixPriceList selectOneProductFixPriceList(HashMap<String, Object> param);

	DayList selectOneProductDayPriceList(HashMap<String, Object> param);
	ArrayList<ProductTourImages> selectListProductImages(HashMap<String, Object> param);

	ArrayList<ProductPriceOption> selectListPriceOptionList(HashMap<String, Object> paramMap);
	ArrayList<ProductPriceOption> selectListPriceOption(HashMap<String, Object> paramMap);
	ArrayList<ProductPriceOption> selectListPriceOptionStayOnly(HashMap<String, Object> paramMap);
	ProductPriceOption selectOnePriceOption(HashMap<String, Object> paramMap);
	ProductPriceOption selectOnePriceOptionById(HashMap<String, Object> paramMap);
	ArrayList<ProductPriceOption> selectListPriceOptionCalendarPossible(HashMap<String, Object> paramMap);
	ArrayList<ProductPriceOption> selectListPriceOptionProgramCalendarPossible(HashMap<String, Object> paramMap);

	ArrayList<FixPriceList> selectListProductFixPrice(HashMap<String, Object> param);
	ArrayList<DayList> selectListProductDayPrice(HashMap<String, Object> param);
	ArrayList<ProductDetailSchedule> selectListProductDetailSchedule(HashMap<String, Object> param);

	ArrayList<ProductDetailScheduleImage> selectListProductTourDetailImage(HashMap<String, Object> paramMap);

	ArrayList<ProductCategory> selectListProductCategory(HashMap<String, Object> param);

	ProductInfo selectOneProductTourId(HashMap<String, Object> paramProduct);

	ArrayList<DayList> selectEarlyBirdPrice(HashMap<String, Object> paramProduct);
	ProductPriceOption selectOneEarlyBirdPrice(HashMap<String, Object> paramProduct);
	ArrayList<DayList> selectOneProductOperationDay(HashMap<String, Object> paramProduct);

	//	<!--################################### ProductInventory ###################################-->
	ProductInventory selectSummaryProductInventory(HashMap<String, Object> param);
	void insertProductInventory(ProductInventory data) throws SQLException;

	//	<!--################################### ScheduleInfo ###################################-->
    void insertProductComment(kr.co.wayplus.travel.model.ProductComment productComment);
    ArrayList<ProductComment> selectListProductComment(HashMap<String, Object> paramProduct);

	ArrayList<ProductComment> selectProductComment(HashMap<String, Object> paramMap);
    int selectCountProductComment(HashMap<String, Object> paramMap);
    void updateProductCommentFavorite(ProductComment pc);


    void updateProductViewCount(HashMap<String, Object> paramProduct) throws SQLException;


    int selectProductPaymentTypeCount(HashMap<String, Object> param);



}
