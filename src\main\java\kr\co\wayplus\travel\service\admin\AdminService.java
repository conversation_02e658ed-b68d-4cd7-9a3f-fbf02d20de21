package kr.co.wayplus.travel.service.admin;

import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import kr.co.wayplus.travel.mapper.admin.AdminMapper;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.ManageMenuAuth;
import kr.co.wayplus.travel.model.ManageMenuConnectAuth;
import kr.co.wayplus.travel.model.SmsRecivedUser;
import kr.co.wayplus.travel.util.CryptoUtil;
import kr.co.wayplus.travel.util.CustomBcryptPasswordEncoder;

@Service
public class AdminService {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final AdminMapper adminMapper;

    public AdminService(AdminMapper adminMapper) {
        this.adminMapper = adminMapper;
    }

    public int getAdministratorUserListCount() {
        return adminMapper.selectAdministratorUserListCount();
    }

    public ArrayList<LoginUser> getAdministratorUserList(HashMap<String, Object> param) {
        return adminMapper.selectAdministratorUserList(param);
    }

    public LoginUser getAdministratorInfo(String userEmail) {
        return adminMapper.selectAdministratorInfo(userEmail);
    }

    public void createAdministrator(LoginUser user, boolean encrypted) throws InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        if(encrypted){
            user.setUserPassword(CryptoUtil.aesDecode(user.getUserPassword(), user.getEncrypt(), user.getIv()));
        }
        CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();
        user.setUserPassword(passwordEncoder.encode(user.getPassword()));
        adminMapper.insertAdministrator(user);
    }

    public void withdrawalAdministrator(LoginUser user) {
        adminMapper.insertWithdrawalAdministrator(user);
        adminMapper.deleteAdministrator(user);
    }

    public void updateAdministrator(LoginUser user, boolean encrypted) throws InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
        if(user.getUserPassword() != null && !user.getUserPassword().isEmpty()) {
            if (encrypted) {
                user.setUserPassword(CryptoUtil.aesDecode(user.getUserPassword(), user.getEncrypt(), user.getIv()));
            }
            CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();
            user.setUserPassword(passwordEncoder.encode(user.getUserPassword()));
        }
        adminMapper.updateAdministrator(user);
    }
//	<!--################################### manageMenuAuth ###################################-->
	public int selectCountManageMenuAuth(HashMap<String, Object> paramMap) {
		return adminMapper.selectCountManageMenuAuth(paramMap);
	}
	public ArrayList<ManageMenuAuth> selectListManageMenuAuth(HashMap<String, Object> paramMap) {
		return adminMapper.selectListManageMenuAuth(paramMap);
	}
	public ManageMenuAuth selectOneManageMenuAuth(HashMap<String, Object> paramMap) {
		return adminMapper.selectOneManageMenuAuth(paramMap);
	}

	public void insertManageMenuAuth(ManageMenuAuth bc) throws SQLException {
		adminMapper.insertManageMenuAuth(bc);
	}

	public void updateManageMenuAuth(ManageMenuAuth bc) throws SQLException {
		adminMapper.updateManageMenuAuth(bc);
	}

	public void deleteManageMenuAuth(ManageMenuAuth bc) throws SQLException {
		adminMapper.deleteManageMenuAuth(bc);
	}
	public void restoreManageMenuAuth(ManageMenuAuth bc) throws SQLException {
		adminMapper.restoreManageMenuAuth(bc);
	}

//	<!--################################### SmsRecivedUser ###################################-->
	public int selectCountSmsRecivedUser(HashMap<String, Object> paramMap) {
		return adminMapper.selectCountSmsRecivedUser(paramMap);
	}

	public ArrayList<SmsRecivedUser> selectListSmsRecivedUser(HashMap<String, Object> paramMap) {
		return adminMapper.selectListSmsRecivedUser(paramMap);
	}
	public SmsRecivedUser selectOneSmsRecivedUser(HashMap<String, Object> paramMap) {
		return adminMapper.selectOneSmsRecivedUser(paramMap);
	}

	public void insertSmsRecivedUser(SmsRecivedUser bc) throws SQLException {
		adminMapper.insertSmsRecivedUser(bc);
	}

	public void updateSmsRecivedUser(SmsRecivedUser bc) throws SQLException {
		adminMapper.updateSmsRecivedUser(bc);
	}

	public void deleteSmsRecivedUser(SmsRecivedUser bc) throws SQLException {
		adminMapper.deleteSmsRecivedUser(bc);
	}
	public void restoreSmsRecivedUser(ManageMenuAuth bc) throws SQLException {
		adminMapper.restoreManageMenuAuth(bc);
	}

//	<!--################################### manageMenuAuth ###################################-->
	public int selectCountManageMenuConnectAuth(HashMap<String, Object> paramMap) {
		return adminMapper.selectCountManageMenuConnectAuth(paramMap);
	}
	public ArrayList<ManageMenuConnectAuth> selectListManageMenuConnectAuth(HashMap<String, Object> paramMap) {
		return adminMapper.selectListManageMenuConnectAuth(paramMap);
	}
	public ArrayList<ManageMenuConnectAuth> selectListManageMenuConnectAuth(Long authId) {
		HashMap<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("authId", authId);

		return this.selectListManageMenuConnectAuth(paramMap);
	}
	public ManageMenuConnectAuth selectOneManageMenuConnectAuth(HashMap<String, Object> paramMap) {
		return adminMapper.selectOneManageMenuConnectAuth(paramMap);
	}

	public void insertManageMenuConnectAuth(ManageMenuConnectAuth bc) throws SQLException {
		adminMapper.insertManageMenuConnectAuth(bc);
	}

	public void updateManageMenuConnectAuth(ManageMenuConnectAuth bc) throws SQLException {
		adminMapper.updateManageMenuConnectAuth(bc);
	}

	public void deleteManageMenuConnectAuth(ManageMenuConnectAuth bc) throws SQLException {
		adminMapper.deleteManageMenuConnectAuth(bc);
	}


}
