package kr.co.wayplus.travel.web.front;

import java.util.ArrayList;
import java.util.HashMap;

import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.InquiryContent;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.MenuUser;
import kr.co.wayplus.travel.model.PagingDTO;
import kr.co.wayplus.travel.model.ProductInfo;
import kr.co.wayplus.travel.model.Reservation;
import kr.co.wayplus.travel.service.common.MessageSenderService;
import kr.co.wayplus.travel.service.front.MemberService;
import kr.co.wayplus.travel.service.front.PageService;
import kr.co.wayplus.travel.service.front.ProductService;
import kr.co.wayplus.travel.util.CryptoUtil;

@Controller
@RequestMapping("/member")
public class MemberController extends BaseController {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final MemberService memberService;
    private final PageService pageService;
    private final MessageSenderService messageSenderService;
    private final ProductService productService;

    @Value("${key.crypto.encrypt}")
    private String encrypt;
    @Value("${key.crypto.iv}")
    private String iv;

    public MemberController(
    		MemberService memberService,
    		PageService pageService,
    		MessageSenderService messageSenderService,
    		ProductService productService) {
        this.memberService = memberService;
        this.pageService = pageService;
        this.messageSenderService = messageSenderService;
        this.productService = productService;
    }

    @PostMapping(value="/verify-user")
    @ResponseBody
    public HashMap<String,Object> verifyUser(
    	HttpServletRequest request,
		HttpSession session,
		@ModelAttribute LoginUser user,
		BindingResult bindingResult,
		HttpServletResponse response){

        HashMap<String,Object> resultMap = new HashMap<>();
        try{
            appendBindingErrorLog(bindingResult);

            if( getCookies( request, "user_verify" ) == null ) {
	            if(session.getAttribute("encrypt") != null) encrypt = (String) session.getAttribute("encrypt");
	            if(session.getAttribute("iv") != null) iv = (String) session.getAttribute("iv");
	            user.setEncrypt(encrypt);
	            user.setIv(iv);
	            Boolean verifyUserPassword = memberService.verifyUserInfo(user);
	            if (verifyUserPassword){

	            	setCookies(response, "user_verify", "success", (5 * 60 * 60));

	                resultMap.put("result","success");
	            }else{
	                resultMap.put("result","error");
	                resultMap.put("message","회원정보가 일치하지 않습니다.");
	            }
            }

        }catch (Exception e){
            logger.debug(e.getMessage());
        }

        return resultMap;
    }
//
//    @GetMapping("/myPage")
//    public ModelAndView myPage(){
//        ModelAndView mav = new ModelAndView();
//
//        mav.setViewName("/front/member/myPage");
//        return mav;
//    }
//
//    @GetMapping("/info")
//    public ModelAndView info(HttpSession session){
//        ModelAndView mav = new ModelAndView();
//
//        if(session.getAttribute("encrypt") == null || session.getAttribute("iv") == null){
//            CryptoUtil cryptoUtil = new CryptoUtil();
//            session.setAttribute("encrypt", cryptoUtil.generateRandomEncryptKey(""));
//            session.setAttribute("iv", cryptoUtil.generateRandomIv(""));
//        }
//
//        mav.setViewName("/front/member/info");
//        return mav;
//    }
//
//    @GetMapping("/reservation")
//    public ModelAndView reservation(
//    		@RequestParam(value="page", defaultValue="1") int page,
//            @RequestParam(value="pageSize", defaultValue="10") int pageSize,
//            @RequestParam(value="category", defaultValue="0") int category,
//            @RequestParam(value="searchKey", defaultValue="") String searchKey){
//        ModelAndView mav = new ModelAndView();
//
//        try {
//        	Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
//        	if(_user instanceof LoginUser) {
//				LoginUser user = (LoginUser)_user;
//
//				HashMap<String, Object> paramMap = new HashMap<>();
//				paramMap.put("userEmail",user.getUserEmail());
//
//				int totalContentCount = pageService.selectCountReservation(paramMap);
//		        PagingDTO paging = new PagingDTO(totalContentCount, page, 0, pageSize);
//		        paramMap.put("itemStartPosition", paging.getItemStartPosition());
//		        paramMap.put("pagePerSize", paging.getPagePerSize());
//
//		        mav.addObject("totalCount", totalContentCount);
//		        mav.addObject("paging", paging);
//
//		        ArrayList<Reservation> contents = pageService.selectListReservation(paramMap);
//		        mav.addObject("list", contents);
//		        mav.addObject("menuType","member");
//		        mav.addObject("menuUrl","/reservation");
//        	}
//        } catch (Exception e) {
//        }
//
//        mav.setViewName("front/member/reservation");
//        return mav;
//    }
//
//    @GetMapping("/reservation/form")
//    public ModelAndView reservation(
//    		HttpServletRequest request,
//    		@RequestParam(value="mode", defaultValue="I") String mode){
//        ModelAndView mav = new ModelAndView();
//
//        if(mode.equals("I")) {
//	        mav.addObject("reservation",  new Reservation());
//	    	mav.addObject("user",         new LoginUser());
//	    	mav.addObject("product",      new ProductInfo());
//        } else {
//
//        }
//
//        mav.addObject("mode", mode);
//        mav.setViewName("front/member/reservation-form");
//        return mav;
//    }
//
//    @GetMapping("/reservation/{id}")
//    public ModelAndView reservation(
//    		HttpServletRequest request,
//    		@RequestParam(value="mode", defaultValue="U") String mode,
//    		@PathVariable String id){
//        ModelAndView mav = new ModelAndView();
//
//        HashMap<String, Object> paramMap = new HashMap<>();
//
//        paramMap.put("id", id);
//    	Reservation data = pageService.selectOneReservation(paramMap);
//
//    	mav.addObject("reservation",  data);
//
//    	if(data.getProductSerial() != null && data.getProductTourId() != null) {
//    		paramMap.put( "productSerialOld", data.getProductSerial() );
//    		paramMap.put( "productTourId", data.getProductTourId() );
//    		mav.addObject("product", productService.selectOneProduct(paramMap));
//    	} else {
//    		mav.addObject("product",      new ProductInfo());
//    	}
//    	mav.addObject("mode", mode);
//        mav.setViewName("front/member/reservation-form");
//
//        return mav;
//    }
//
//    @GetMapping("/question")
//    public ModelAndView question(
//    		@RequestParam(value="page", defaultValue="1") int page,
//            @RequestParam(value="pageSize", defaultValue="10") int pageSize,
//            @RequestParam(value="category", defaultValue="0") int category,
//            @RequestParam(value="searchKey", defaultValue="") String searchKey){
//        ModelAndView mav = new ModelAndView();
//
//        try {
//        	Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
//        	if(_user instanceof LoginUser) {
//				LoginUser user = (LoginUser)_user;
//
//				HashMap<String, Object> paramMap = new HashMap<>();
//				paramMap.put("customerEmail",user.getUserEmail());
//				paramMap.put("categoryId","1");
//
//				int totalContentCount = pageService.selectCountInquiryContent(paramMap);
//		        PagingDTO paging = new PagingDTO(totalContentCount, page, 0, pageSize);
//		        paramMap.put("itemStartPosition", paging.getItemStartPosition());
//		        paramMap.put("pagePerSize", paging.getPagePerSize());
//
//		        mav.addObject("totalCount", totalContentCount);
//		        mav.addObject("paging", paging);
//
//		        ArrayList<InquiryContent> contents = pageService.selectListInquiryContent(paramMap);
//		        mav.addObject("list", contents);
//		        mav.addObject("menuType","crm");
//		        mav.addObject("menuUrl","/inquiry");
//        	}
//        } catch (Exception e) {
//        }
//
//        mav.setViewName("front/member/question");
//        return mav;
//    }
//    @GetMapping("/estimate")
//    public ModelAndView estimate(
//    		@RequestParam(value="page", defaultValue="1") int page,
//            @RequestParam(value="pageSize", defaultValue="10") int pageSize,
//            @RequestParam(value="category", defaultValue="0") int category,
//            @RequestParam(value="searchKey", defaultValue="") String searchKey){
//    	ModelAndView mav = new ModelAndView();
//        try {
//        	Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
//        	if(_user instanceof LoginUser) {
//				LoginUser user = (LoginUser)_user;
//
//				HashMap<String, Object> paramMap = new HashMap<>();
//				paramMap.put("customerEmail",user.getUserEmail());
//				paramMap.put("categoryId","5");
//
//				int totalContentCount = pageService.selectCountInquiryContent(paramMap);
//		        PagingDTO paging = new PagingDTO(totalContentCount, page, 0, pageSize);
//		        paramMap.put("itemStartPosition", paging.getItemStartPosition());
//		        paramMap.put("pagePerSize", paging.getPagePerSize());
//
//		        mav.addObject("totalCount", totalContentCount);
//		        mav.addObject("paging", paging);
//
//		        ArrayList<InquiryContent> contents = pageService.selectListInquiryContent(paramMap);
//		        mav.addObject("list", contents);
//		        mav.addObject("menuType","crm");
//		        mav.addObject("menuUrl","/inquiry");
//        	}
//        } catch (Exception e) {
//        }
//    	mav.setViewName("front/member/estimate");
//    	return mav;
//    }
//
//    @GetMapping(value="/{url}/form")
//    public ModelAndView crm_form(
//    		@PathVariable String url,
//    		@RequestParam(value="mode", defaultValue="I") String mode,
//    		@RequestParam(value="id", defaultValue="0") String id,
//    		@Param(value="onlyCategory")  String onlyCategory
//    		) {
//    	ModelAndView mav = new ModelAndView();
//
//    	HashMap<String, Object> paramMenu = new HashMap<>();
//    	String menuUrl = "/"+url;;
//    	paramMenu.put("menuType", "crm");
//        paramMenu.put("menuUrl", menuUrl);
//        MenuUser menu = pageService.selectOneMenuUser( paramMenu ); // -> menu_id=>upper_menu_id
//        mav.addObject("menu", menu);
//
//        if( url.equals("question") ) {
//        	onlyCategory = "1";
//        } else if( url.equals("estimate") ) {
//        	onlyCategory = "5";
//        } else {
//        	onlyCategory = null;
//        }
//
//    	HashMap<String, Object> paramCategory = new HashMap<>();
//    	paramCategory.put("useYn","Y");
//        paramCategory.put("deleteYn","N");
//        paramCategory.put("groupYn","N");
//        paramCategory.put("sort","orderNum");
//        paramCategory.put("sortOrder","ASC");
//        if( onlyCategory != null )
//        	paramCategory.put("id",onlyCategory);
//
//        mav.addObject("categorys",  pageService.selectListInquiryCategory( paramCategory ));
//
//        if(mode.equals("I")) {
//        	mav.addObject("content",  new InquiryContent());
//        } else {
//        	HashMap<String, Object> paramMap = new HashMap<>();
//        	paramMap.put("id", id);
//        	mav.addObject("content",  pageService.selectOneInquiryContent(paramMap));
//        }
//
//        mav.addObject("menuUrl", menuUrl);
//        mav.addObject("mode", mode);
//
//    	mav.setViewName("/front/crm/inquiry/form");
//		return mav;
//	}
//    @GetMapping(value="/{url}/{id}")
//    public ModelAndView crm_view(
//    		HttpServletRequest request,
//    		@RequestParam(value="mode", defaultValue="U") String mode,
//    		@PathVariable String url,
//    		@PathVariable String id){
//        ModelAndView mav = new ModelAndView();
//
//        String onlyCategory;
//        if( url.equals("question") ) {
//        	onlyCategory = "1";
//        } else if( url.equals("estimate") ) {
//        	onlyCategory = "5";
//        } else {
//        	onlyCategory = null;
//        }
//
//        HashMap<String, Object> paramCategory = new HashMap<>();
//    	paramCategory.put("useYn","Y");
//        paramCategory.put("deleteYn","N");
//        paramCategory.put("groupYn","N");
//        paramCategory.put("sort","orderNum");
//        paramCategory.put("sortOrder","ASC");
//        if( onlyCategory != null )
//        	paramCategory.put("id",onlyCategory);
//
//        mav.addObject("categorys",  pageService.selectListInquiryCategory( paramCategory ));
//
//        HashMap<String, Object> paramMenu = new HashMap<>();
//        String menuUrl = "/"+url;;
//        paramMenu.put("menuUrl", menuUrl);
//        paramMenu.put("menuType", "crm");
//        MenuUser menu = pageService.selectOneMenuUser( paramMenu ); // -> menu_id=>upper_menu_id
//        mav.addObject("menu", menu);
//
//        try {
//        	Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
//
////	        if( url.equals("inquiry") ) {
////	        } else if( url.equals("reservation") ) {
////	        }
//        	HashMap<String, Object> paramMap = new HashMap<>();
//        	paramMap.put("id", id);
//
//	        InquiryContent content = pageService.selectOneInquiryContent(paramMap);
//	        mav.addObject("content", content);
///*
//	        HashMap<String, Object> paramMapBoard = new HashMap<>();
//	        paramMapBoard.put("boardId", id);
//
//	        mav.addObject("listAttach",  pageService.selectListBoardAttachFile(paramMapBoard));
//*/
//	        HttpSession session = request.getSession();
//
//	        boolean secretPass = false;
//
//	        Object sessionSecretPass = session.getAttribute( "secretPass"+id);
//
//	        if(content.getSecretYn().equals("Y")) {
//	        	if(_user instanceof LoginUser) {
//					LoginUser user = (LoginUser)_user;
//
//					 if( user.getUserRole().equals("manage".toUpperCase()) ||
//			        	 user.getUserRole().equals("admin".toUpperCase())) {
//			        	/*권한으로 패스*/
//			        	secretPass = true;
//			        } else if( user.getUserEmail().equals( content.getCreateId()) ||
//			        		   user.getUserEmail().equals( content.getCustomerEmail()) )  {
//			        	secretPass = true;
//			        }
//	        	} else if ( sessionSecretPass!=null && (boolean)sessionSecretPass ){
//	        		secretPass = true;
//	        		//session.setAttribute( "secretPass"+id, null);
//	        	}
//	        } else {
//	        	secretPass = true;
//	        }
//	        mav.addObject("secretPass", secretPass);
//
//	        mav.addObject("menuUrl", menuUrl);
//	        mav.addObject("mode", mode);
//	        mav.addObject("id", id);
//
////	        if(mode.equals("V")){
////				mav.setViewName("/front/crm/inquiry/view");
////			} else {
//			if(mode.equals("I")){
//				mav.setViewName("/front/crm/inquiry/form");
//			} else {
//				mav.setViewName("/front/crm/inquiry/form");
//			}
////			}
//
//        } catch (Exception e) {
//
//        }
//        return mav;
//    }
//
//
//
//    @GetMapping("/mail-test")
//    @ResponseBody
//    public HashMap<String, Object> mailTest(){
//        HashMap<String, Object> retrunMap = new HashMap<>();
//
//        try {
//            messageSenderService.sendMailFromSet("inquiry", 1, "", "");
//
//            retrunMap.put("result", "success");
//            retrunMap.put("message", "처리가 완료 되었습니다.");
//        } catch (Exception e) {
//            retrunMap.put("result", "error");
//            retrunMap.put("message", "처리중 문제가 발생했습니다.");
//            e.printStackTrace();
//            logger.error(e.getMessage());
//        }
//        return retrunMap;
//    }
//
//    @GetMapping("/message-test")
//    @ResponseBody
//    public HashMap<String, Object> messageTest(){
//        HashMap<String, Object> retrunMap = new HashMap<>();
//
//        try {
//            messageSenderService.pushMessageQueueFromSet("inquiry", 1);
//
//            retrunMap.put("result", "success");
//            retrunMap.put("message", "처리가 완료 되었습니다.");
//        } catch (Exception e) {
//            retrunMap.put("result", "error");
//            retrunMap.put("message", "처리중 문제가 발생했습니다.");
//            e.printStackTrace();
//            logger.error(e.getMessage());
//        }
//        return retrunMap;
//    }
}
