
/* 개편 후 */
.footer{
    background-color:#111;
    padding:35px 0 45px 0;
}
.footer-wrap{
    display:flex;
    justify-content: space-between;
}
.footer-wrap .footer-left{
    display:flex;
}
.footer-ci h5{
    color:#fff;
    font-size:20px;
    font-weight:600;
    margin-right:85px;
}
.footer-fnb{
    margin-right:50px;
}
.footer-fnb ul li{
    color:#fff;
    margin-bottom:10px;
    font-weight:500;
}
.footer-info ul li{
    color:#fff;
    margin-bottom:10px;
    font-weight:300;
}
.insta-subscription{
    display:flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom:45px;
}
.insta-subscription .insta{
    margin-right:20px;
    width:38px;
    height:38px;
    cursor: pointer;
}
.subscription-wrap a{
    padding:5px 10px 5px 10px;
     background:none;
     border:1px solid #888;
     color:#FFC960;
     display:flex;
     align-items: center;
     justify-content: center;
     cursor: pointer;
}
.subscription-wrap a img{
    margin-left:7px;
}
.footer-copy{
    color:#a0a0a0;
    font-weight:300;
}

@media screen and (max-width:1280px) {
    .footer-ci h5{
        margin-right:20px;
    }
    .footer-fnb{
        margin-right:20px;
    }
    .footer-copy{
        font-size:14px;
    }
}
@media screen and (max-width:1024px) {
    .footer-wrap {
        flex-direction: column;
    }
    .footer-left{
        margin-bottom:25px;
    }
    .footer-ci h5{
        margin-right:50px;
    }
    .footer-fnb{
        margin-right:50px;
    }
    .insta-subscription{
        justify-content: normal;
        margin-bottom:10px;
    }
}
@media screen and (max-width:768px) {
    .footer{
        padding:50px 0;
    }
    .footer-left{
        flex-direction: column;
        margin-bottom:0;
    }
    .footer-ci h5{
        margin-bottom:15px;
        margin-right:0;
    }
    .footer-fnb{
        margin-right:0;
        margin-bottom:10px;
    }
    .footer-info {
        margin-bottom:15px;
    }
    .footer-info ul li{
        font-size:15px;
        line-height:20px;
    }
    
}