package kr.co.wayplus.travel.model;

import java.util.ArrayList;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@Data
public class Survey extends CommonDataSet {
	private Integer id;	//고유 번호
	private String surveyVersion;	//설문 버전
	private String surveyTitle;	//설문 이름
	private String surveyAnswer;	//문답유형//enum('2','3','4','5','6','7')

	private int questionCount;
}
