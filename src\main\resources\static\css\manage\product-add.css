th { border-bottom: none !important; }
tr { background: #FFFFFF;}
td { height: 57px; font-weight: 400; font-size: 15px; color: #444444; }
button { background: none; border: none; }
p { margin: 0; padding: 0 }
label { margin-bottom: 0;/*display:unset;*/}
button {font-family: unset;font-size: unset;line-height: unset;}
img {vertical-align: unset;}

.info-product-basic{display:flex;justify-content: space-between;align-items: center;}
#drop-down{background-size:cover;background-repeat:no-repeat;background-position:center;width:6px;height:17px;}
.schedule-modal-box, .schedule-add-modal-box, .schedule-modify-modal-box, .add-car-option-modal-box {display: none; justify-content: center; align-items: center;position: fixed; width: 100%;height: 100%; background-color: #0000009A; top: 0; z-index: 1038;}
.schedule-add-modal-inner-box {width: 1000px; height: 755px; background: #FFFFFF;}
.schedule-add-modal-top {display:flex; justify-content: space-between; padding: 21px 30px; border-bottom: 1px solid #222222;}
.schedule-add-modal-top-text {font: 600 24px Pretendard; color: #222222;}
.schedule-p-text {min-width: 73px; height: 20px; font: 600 15px Pretendard; color:#222222;}
.schedule-p-text.group {height: 48px; font: 600 15px Pretendard; color:#222222;}
.schedule-price-text {width: 100%; font: 400 15px Pretendard; color:#444444;}
.calendar-text {position: relative; width: 100%; height: 35px; padding: 8px 12px; font: 400 15px Pretendard; color: #444444;background-color: #FFFFFF; border: 1px solid #CCCCCC;}
.start-day-box {display: flex; align-items: center; margin-bottom: 15px;}
.end-day-box {display: flex; align-items: center; margin-bottom: 15px;}
.schedule-hr-15 {margin: 15px 0 15px 0; border-top: 1px solid #666666 !important;}
.schedule-hr {margin: 30px 0 30px 0; border-top: 1px solid #666666;}
.schedule-add-modal-scroll-box {padding: 30px 0 20px 30px; padding-right: 20px; background-color: #F9F9F9;}
.schedule-add-modal-scroll-inner-box {height: 500px; overflow-y: scroll}
.schedule-add-modal-price-select-box {padding-right: 17px; padding-bottom: 10px;}
.add-option-btn {display: flex; justify-content: center; align-items: center; width: 100%; height: 35px; margin-top: 7px; background-color: #383838; cursor: pointer;}
.add-option-btn-text {margin-right: 10px; font: 500 15px Pretendard; color: #FFFFFF;}
.price-basic-option-title {width: 100%; height: 60px; padding: 5px 11px; border: 1px solid #CCCCCC; background-color: #FFFFFF}
.price-option-group {width: 180px; height: 60px; margin-right: 10px; margin-bottom: 8px; padding: 5px 11px; border: 1px solid #CCCCCC; background-color: #FFFFFF}
.price-option-title {width: 100%; height: 60px; margin-right: 10px; margin-bottom: 8px; padding: 5px 11px; border: 1px solid #CCCCCC; background-color: #FFFFFF}
.price-option-type {width: 50px; height: 60px; margin-right: 11px; padding: 10px 11px; border: 1px solid #CCCCCC; background-color: #FFFFFF;}
.price-option-capacity {width: 95px; height: 60px; margin-right: 11px; padding: 10px 11px; border: 1px solid #CCCCCC; background-color: #FFFFFF;}
.price-option-quantity {width: 95px; height: 60px; margin-right: 11px; padding: 10px 11px; border: 1px solid #CCCCCC; background-color: #FFFFFF;}
.price-option-capacity .schedule-p-text,
.price-option-quantity .schedule-p-text {font-size:12px;}
.option-btx-box {display: flex; justify-content: center; align-items: center;}
.delete-option-btn {display:flex; align-items: center; padding-bottom: 8px;}
.day-checkbox, .modify-day-checkbox {min-width: 50px; margin-right: 10px;}
.by-day-price-option-box {display: none; margin-top: 25px;}
.by-day-price-option-item-box {display: flex; margin-right: 10px;margin-bottom: 17px;flex-wrap: wrap;flex-direction: column;align-items: center;max-width: 195px; width: calc(80vw / 7);}
.schedule-option-item-box {display: flex; margin-bottom: 6px;}
.schedule-option-item-label {display: flex; height: fit-content; margin-bottom:10px;}
.by-day-price-option-item-box-div {display: none;}
.product-add-btn-group {display: flex;justify-content: center;margin-top: 15px;}
.product-modify-btn {width: 75px; height: 35px; margin-right: 10px; font: 600 14px Pretendard; border-radius: 5px; color: #FFFFFF; background-color: #0062D4}
.product-delete-btn {width: 75px; height: 35px; margin-right: 10px; font: 600 14px Pretendard; border-radius: 5px; color: #FFFFFF; background-color: #D3455D}
.product-close-btn {width: 75px; height: 35px; font: 600 14px Pretendard; border-radius: 5px; color: #FFFFFF; background-color: #383838}
.radio-check{display :none;}
.product-input-wrapper{background-color: #EEEEEE;margin-bottom: 20px;padding: 23px 23px 0;opacity: 0.68;filter: drop-shadow(2px 2px 6px rgba(0, 0, 0, 0.25));}
.product-input-wrapper.active{background-color:#FFFFFF;opacity:1;}
.product-input-wrapper.active .basic-info,.product-input-wrapper.active .price-info,.product-input-wrapper.active .schedule-info,.product-input-wrapper.active .template-info,.product-input-wrapper.active .car-info,.product-input-wrapper.active .stay-info{display:block;border-top:solid 1px rgb(34,34,34);padding-top:20px;}
.product-input-wrapper .basic-info,.product-input-wrapper .price-info,.product-input-wrapper .schedule-info,.product-input-wrapper .template-info,.product-input-wrapper .golf-option-info,.product-input-wrapper .car-info,.product-input-wrapper .stay-info{display:none;}

/*
.golf-option-info {border-top: solid 1px #222222;}
#product-util-buttons{text-align: center;}
#product-util-buttons button{border-stye:none;background-color:transparent;width:102px;height:35px;line-height:35px;border-radius:5px;margin-right:10px;}
#product-util-buttons button:last-child{margin-right:0;}
#product-util-buttons button.preview,#product-util-buttons button.flag,#product-util-buttons button.cancel{background-color:#383838;color:#FFFFFF;}
#product-util-buttons button.save{background-color:#0062D4;color:#FFFFFF;}

.product-input-wrapper .buttons{text-align: center;margin-bottom:0;padding-bottom:40px;padding-top:40px;}
.product-input-wrapper .buttons li{display:inline-block;width:100px;height:35px;border-radius:5px;margin-right:10px;text-align: center;line-height:35px;}
.product-input-wrapper .buttons li button{border-style:none;background-color:transparent;color:#FFFFFF;text-align: center;}
.product-input-wrapper .buttons li:last-child{margin-right:0;}
.product-input-wrapper .buttons li.cancel{background-color:#383838;}
.product-input-wrapper .buttons li.save{background-color:#0062D4;color:#FFFFFF;}
*/
.switch-box{position:relative;}
.switch-box > ul.status{padding-left:0;list-style:none;}
.switch-box input{}
.switch-box ul{margin-bottom:0;}
.switch-box ul li{display:none;}
.switch-box ul li.active{display:block;padding-left:0;list-style:none;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);}
.title-div{margin-top:-23px;padding-top:23px;padding-bottom:20px;display:flex;justify-content: space-between;align-items: center;cursor:pointer}
.title-div h3{font-size:20px;}
.quad-div{display:inline-block;width:calc(25% - 5px)}
.quad-div div{display:inline-block;}
.tri-div{display:inline-block;width:calc(33.333% - 5px);}
.row-div{margin-bottom:15px}
.row-div .dotted{margin: 30px 0px;border: dashed 1px #CCCCCC;}
.row-div-double{margin-bottom:20px}
.row-div h5{display:inline-block;font-size:15px;width: 90px;}
.full-div{width:calc(100% - 10px)}
.full-div .form-control{width:calc(100% - 26px)}
.full-div h5{font-size:15px;}
.meta-tag-container {display: flex}
.meta-tag-container h5 {width: 76px;}
.meta-tag-container .meta-tag {display: flex; width: 100%; margin-bottom: 5px;}
.meta-tag-container .meta-tag input:first-of-type{width: 200px; margin-right: 10px;}
.meta-tag-container .meta-tag-delete-btn {width : 70px; color: red}
.tri-div-double{display:inline-block;width:calc(75% - 5px);}
.tri-div-double h5{font-size:15px;font-weight:600;display:inline-block;}
.remain-div{width: calc(100% - 101px);padding-left: 4px;}
.tri-div-double .form-control{display: inline-block;width: calc(100% - 105px);/*margin-left:15px;*/height: 34px;line-height:34px;padding: 0.375rem 0.75rem;font-size: 1rem;font-weight: 400;line-height: 1.5;color: #495057;background-color: #fff;background-clip: padding-box;border: 1px solid #ced4da;border-radius: 0.25rem;box-shadow: inset 0 0 0 rgba(0, 0, 0, 0);transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}
.text-area .form-control,.exclude-info .form-control{display: inline-block;width: calc(100% - 75px);resize: none;}
.exclude-info{display:flex;justify-content: space-between;align-items: center;}
.text-area{display:flex;justify-content: space-between;align-items: flex-start;}
.hashtag{display:flex;justify-content: space-between;align-items: center;}
.link-item,.link-addr,.link-url{display:flex;justify-content: space-between;align-items: center;}
.link-item h5,.link-addr h5,.link-url h5{width:70px;}
.link-item input,.link-addr input,.link-url input{width:calc(100% - 70px);margin-left:10px;}
.hashtag .form-control{display: inline-block;width: calc(100% - 75px);/*margin-left:30px;*/}
.include-info{display:flex;justify-content: space-between;align-items: center;}
.include-info .row{width: calc(100% - 62px);}
.include-info .form-control{display: inline-block;width: calc(100% - 75px);margin-left:5px;}
.include-info .row .form-control{width: calc(100% - 11px);}
.full-div .form-control.is-invalid,.half-div .form-control.is-invalid,.penta-div .form-control.is-invalid,.tri-div .form-control.is-invalid,.quad-div .form-control.is-invalid{width:calc(100% - 50px)}
.full-div .form-floating,.half-div{margin-right:5px}
.half-div{display:inline-block;width:calc(50% - 10px)}
/* form-control*/
.quad-div .form-control.tri-div .form-control,.penta-div .form-control,.half-div .form-control{display: inline-block;width: calc(100% - 100px);height: calc(2.25rem + 2px);padding: 0.375rem 0.75rem;font-size: 1rem;font-weight: 400;line-height: 1.5;color: #495057;background-color: #fff;background-clip: padding-box;border: 1px solid #ced4da;border-radius: 0.25rem;box-shadow: inset 0 0 0 rgba(0, 0, 0, 0);transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}
.tri-div h5,.quad-div h5,.half-div h5{font-size:15px;font-weight:600;display:inline-block;}
.tri-div .form-toggle,.quad-div .form-toggle{outline: none;width: calc(100% - 105px);}
.tri-div .form-select,.quad-div .form-select{outline: none;width: calc(100% - 105px);/*margin-left: 10px;*/padding: 7px 5px;font: 400 15px Pretendard;color: #444444;border: 1px solid #CCCCCC;border-radius: 0.25rem;}
.form-select:disabled {background: #CCCCCC;}
.tri-div > .toggle,.quad-div > .toggle{	float:right;	width:calc(100% - 100px);height:33px;}
.tri-div > .select-box,.quad-div > .select-box{	position: relative;	/*float:right;*//*top:-5px;*/width:calc(100% - 100px);height:33px;line-height:33px;cursor:pointer;border-radius:5px;}
.select-box > .form-select{	width: calc(100% - 5px );	margin-left: 0;}
.tri-div .switch-box,.quad-div .switch-box{/*display:inline-block;*/float:right;top:-5px;width:calc(100% - 100px);height:33px;line-height:33px;border:solid 1px #CCCCCC;cursor:pointer;border-radius:5px;}
.tri-div .switch-box{margin-left:10px;}
.toggle + div.switch-box .toggleSwitch{display:block;position:relative;background-color:#FFFFFF;width:100%;}
.toggle + div.switch-box .toggleButton{position:absolute;top:2px;left:6px;width:4px;height:27px;border-radius:3px;background-color:#17E;}
.toggle:checked + div.switch-box{background-color:#17E;transition:all ease-in .3s;}
.toggle:checked + div.switch-box .toggleSwitch{}
.toggle:checked + div.switch-box .toggleButton{left:97%;background-color:#FFFFFF;transition:.3s;}
.toggle:checked + div.switch-box .status li.active{color:#FFFFFF;}
/*.info-toggle button{display:none;}*/
/*.info-toggle button{display:block;width:20px;height:20px;background-repeat:no-repeat;background-size:cover;background-position:center;border-style:none;background-color:transparent;}*/
.upload-wrapper{border:solid 1px #CCCCCC;width:calc(100%);/*margin-left:10px;*/height:35px;line-height:35px;position:relative;display:inline-block;border-radius: 0.25rem;}
.upload-wrapper .upload_btn{background-color:#383838;display:flex;justify-content: center;align-items: center;width:120px;margin:0 0 0 calc(89% - 5px);cursor:pointer;position: absolute;right: 0;}
.upload-wrapper .upload_btn li{margin-right:8px;}
.upload-wrapper .upload_btn li:last-child{margin-right:0;}
.upload-wrapper .upload_btn li.icon{width:20px;height:20px;background-repeat:no-repeat;background-position:center;background-color:#FFFFFF;border-radius:2px;}
.upload-wrapper .upload_btn li.text{color:#FFFFFF;}
/* content.css로 이관
.notification-icon{margin-left:30px;color: #666666;}
.notification-icon::before{content:"";background:url("/images/icon/info_icon.svg") no-repeat 0px 0px;height:20px;width:20px;position:absolute;margin-left:-20px;margin-top:5px;}
*/
#product-images{display: inline-block;width: calc(100%);height: 235px;border: solid 1px #CCCCCC;/*margin-left: 65px;*/margin-top: 10px;border-radius: 0.25rem;}
.product-image-item{width: fit-content;overflow:hidden;}
.product-image-item img{width:auto;height:230px;width: 100%;aspect-ratio: 4 / 4;object-fit: cover;}
.product-thumbnail-view img{width:auto;height:310px;aspect-ratio: 4 / 4;}
.product-image-delete-button{position:absolute;top:10px;right:15px;border: solid 1px #ff1111;width: 17px;height: 17px;line-height: 15px;color: #ff1111; background: #fff; padding: 0px 3px;}
.product-image-item.thumbnail .product-image-delete-button{display:none;}
#product-thumbnail-view{width: calc(100%);height: 312px;border: solid 1px #CCCCCC;text-align: center;overflow:hidden;}

.product-image-pagination{position: absolute;width: 100%;top:50%;}
/* content.css로 이관
.picky{position:relative;}
.picky .calendar{width:21px;height:22px;background-repeat:no-repeat;background-size:cover;background-position:center;}
.picky .form-control{display:inline-block;width:calc(100% - 20px);text-align: center;}
.picky button[type='button']{position: absolute;top: 50%;left:10px;transform: translateY(-50%);height: 22px;}
.picky span{margin-left:5px;}
*/
button {margin: 0;padding: 0;background: none;border: none;}
p, hr {margin: 0;padding: 0;}
input {border: none;}
[type="checkbox"] {appearance: none;position: relative;border: 1px solid #0062D4;border-radius: 5px;width: 50px;height: 20px;}
[type="checkbox"]::before {content: "";position: absolute;top: 1px;left: 1px;width: 16px;height: 16px;border-radius: 3px;background-color: #0062D4;transition: left 150ms linear;}
[type="checkbox"]:checked {background-color: #0062D4;border-color: #0062D4;}
[type="checkbox"]:checked::before {background-color: white;left: 31px;}
[type="checkbox"]:disabled {border-color: lightgray;opacity: 0.7;cursor: not-allowed;}
[type="checkbox"]:disabled:before {background-color: lightgray;}
[type="checkbox"]:disabled + span {opacity: 0.7;cursor: not-allowed;}
/*스크롤바*/.schedule-add-modal-scroll-inner-box::-webkit-scrollbar, .connect-place-select-modal-scroll-inner-box::-webkit-scrollbar {width: 29px;}
.schedule-add-modal-scroll-inner-box::-webkit-scrollbar-thumb, .connect-place-select-modal-scroll-inner-box::-webkit-scrollbar-thumb {background-color: #848484;background-clip : padding-box;border: 11px solid transparent;}
.schedule-add-modal-scroll-inner-box::-webkit-scrollbar-track, .connect-place-select-modal-scroll-inner-box::-webkit-scrollbar-track {background-color: #EEEEEE;border: 9px solid transparent;background-clip: padding-box;margin-left: 5px;margin-right: 5px;}
/* common으로 이관select {-o-appearance: none;-webkit-appearance: none;-moz-appearance: none;appearance: none;}
select {width: calc(100% - 74px);background: #FFFFFF url('/images/icon/full-arrow-down.svg') no-repeat 98% 50%;padding: 7px 0 7px 5px;font: 400 15px Pretendard;color: #444444;border: 1px solid #CCCCCC;}
select option {font-family: 'Pretendard', serif;font-weight: 500;font-size: 16px;color: #333333;border: 1px solid #D9D9D9;filter: drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.1));}
*/.sector-sub-category-add-select, .sector-sub-category-modify-select {width: 100%;height: 40px;background: url('/images/icon/full-arrow-down.svg') no-repeat 97% 50%;color: #333333;font-weight: 400;font-size: 16px;padding-left: 20px;border: 1px solid #8F8F8F;border-radius: 5px;}
.detail-schedule-select, .detail-stay-select {position: relative; width: 440px; height: 35px; padding: 8px 12px; font: 400 15px Pretendard; color: #444444;border: 1px solid #CCCCCC; background: #FFFFFF url('/images/icon/full-arrow-down.svg') no-repeat 98% 50%;}
.detail-schedule-caution-text {font-weight: 400;font-size: 15px;color: #666666;}
.detail-schedule-text-box {display: flex;align-items: center;    padding: 30px 0;}
.detail-schedule-text-box h5 {margin-right: 10px;margin-bottom: 0;}
.detail-schedule-box, .detail-stay-box {padding-top: 25px;padding-left: 70px;padding-bottom: 31px;border-top:solid 1px #CCCCCC;}
.detail-schedule-box.border-btm-none {border-bottom: none;}
.detail-schedule-content-box,.detail-link-content-box {display: none;width: 970px;margin-top: 27px;padding: 25px;border: 1px solid #CCCCCC;background: rgba(238, 238, 238, 0.93);}
.detail-schedule-content-box ul > li:last-child,.detail-link-content-box ul > li:last-child {margin-bottom: 0;}
.add-schedule-btn {display: flex; justify-content: center; align-items: center; max-width: 920px; width: 100%; height: 50px; margin-top: 20px;background-color: #222222; cursor: pointer;}
.add-schedule-btn-text {font: 600 18px Pretendard;color: #FFFFFF;}
.delete-schedule-btn {display:flex;height: 100%;align-items: start;}
.detail-schedule-order-text, .detail-stay-order-text {min-width: 66px;font: 600 16px Pretendard;color: #222222;}
.detail-schedule-item-box,.detail-link-item-box {width: 790px;margin-right: 11px;}
.detail-schedule-content-inner-box,.detail-link-content-inner-box, .detail-stay-content-inner-box {display:flex; width: 920px; height: 275px; margin-bottom: 20px;padding: 25px 25px 25px 20px; border: 1px solid #CCCCCC; background: #FFFFFF;}
.detail-schedule-first-line {display:flex; margin-bottom: 8px;}
.detail-schedule-second-line {width: 100%; height: 70px; margin-right: 2px; margin-bottom: 8px;padding-top: 13px; padding-right: 20px; padding-left: 20px; border: 1px solid #CCCCCC}
.detail-schedule-third-line {display:flex; justify-content: space-between;padding-bottom: 8px;}
.detail-schedule-name-box {width: 468px; height: 70px; margin-right: 2px;padding-top: 13px; padding-right: 20px; padding-left: 20px; border: 1px solid #CCCCCC}
.detail-schedule-name-label {display: grid; height: 49px;font-weight: 600; font-size: 15px; color: #222222}
.detail-schedule-name-input {height: 19px;font-weight: 400; font-size: 15px; color: #666666}
.detail-schedule-address-box {width: 320px; height: 70px;padding-top: 13px; padding-right: 20px; padding-left: 20px; border: 1px solid #CCCCCC}
.detail-schedule-address-label {display: grid; height: 49px;font-weight: 600; font-size: 15px; color: #222222}
.detail-schedule-address-input, .detail-stay-address-input {height: 19px;font-weight: 400; font-size: 15px; color: #666666}
.detail-schedule-title-label {display: grid; height: 49px;font-weight: 600; font-size: 15px; color: #222222}
.detail-schedule-title-input {height: 19px;font-weight: 400; font-size: 15px; color: #666666}
.detail-schedule-item-image-list, .detail-stay-item-image-list {position: relative;display: flex; max-width: 315px; width: 100%;overflow: hidden;}
.detail-schedule-item-image-box, .detail-stay-item-image-box {position: relative; width: 70px; height: 70px;}
.detail-schedule-item-image-box img, .detail-stay-item-image-box img {width: 70px; height: 70px;}
.detail-stay-item-image-box, .detail-stay-item-image-box {position: relative; width: 70px; height: 70px;}
.detail-stay-item-image-box img, .detail-stay-item-image-box img {width: 70px; height: 70px;}
.detail-schedule-item-image-delete-btn {position: absolute; top: 2px; right: 5px;width: 15px; height: 15px;}
.detail-schedule-item-image-delete-btn img {width: 15px; height: 15px;}
.connect-place-box {width: 258px; height: 70px;padding-top: 13px; padding-right: 20px; padding-left: 20px; border: 1px solid #CCCCCC}
.connect-place-box-label {display: grid; width: 100%; height: 49px;font-weight: 600; font-size: 15px; color: #222222}
.connect-place-box-input {height: 19px;font-weight: 400; font-size: 15px; color: #666666}
.choose-place-btn {width: 140px; font-weight: 600; font-size: 15px; color: #FFFFFF; background: #222222;}
.schedule-swiper-button-next::after,.schedule-swiper-button-prev::after {display: none;}
.schedule-swiper-button-prev {position: absolute;top: 37%;width: 25px; height: 25px;background: url('/images/icon/left-arrow-gray-6x12.svg') no-repeat;background-size: 25px 25px;text-indent: -9999px;z-index: 1;}
.schedule-swiper-button-next {position: absolute;top: 37%;right: 0;width: 25px; height: 25px;background: url('/images/icon/right-arrow-gray-6x12.svg') no-repeat;background-size: 25px 25px;text-indent: -9999px;z-index: 1;}
.connect-place-select-modal-box,.connect-place-product-select-modal-box {display: none; justify-content: center; align-items: center;position: fixed; width: 100%;height: 100%; background-color: #0000009A; top: 0; z-index: 1038;}
.connect-place-select-modal-inner-box {width: 500px; height: 645px; background: #FFFFFF}
.connect-place-select-modal-scroll-inner-box {height: 450px;overflow-y: scroll;}
.connect-place-select-modal-scroll-box {height: 505px; padding: 30px 0 20px 30px; padding-right: 20px; background-color: #F9F9F9;}
.place-hr {margin: 20px 0; border-top: 1px solid #666666;}
.place-item-box {width: 403px; height: 70px;margin-bottom: 10px; padding: 13px 20px; border: 1px solid #CCCCCC;background: #FFFFFF; cursor: pointer;}
.place-item-box.active {border: 2px solid #0062D4;}
.place-item-name {font-weight: 600; font-size: 15px; color: #222222;}
.place-item-address {font-weight: 400; font-size: 15px; color: #666666;}
.add-detail-schedule-item-btn-img {width: 15px; height: 15px;margin-right: 9px;}
.region-type-select {width: 100%; height: 35px;}
.place-search-box {display:flex; align-items: center; height: 35px;}
.place-search-name {min-width: 73px;font-weight: 600; font-size: 15px; color: #222222;}
.place-search-name-input,.product-search-name-input {width: 100%; height: 35px;margin-right: 10px; padding-left: 10px;border: 1px solid #CCCCCC;}
.place-search-btn,.product-search-btn {min-width: 70px; height: 35px;font: 500 15px Pretendard; color: #FFFFFF; background-color: #383838;}
.place-choose-btn-box {display: flex; justify-content: center; margin-top: 15px;}
.place-choose-btn {width: 75px; height: 35px;margin-right: 10px;font: 600 14px Pretendard; color: #FFFFFF;border-radius: 5px; background-color: #0062D4;}
.place-choose-close-btn {width: 75px; height: 35px;font: 600 14px Pretendard;  color: #FFFFFF;border-radius: 5px; background-color: #383838}
.detail-schedule-day-tab-box ,.detail-link-tab-box {display: flex;flex-wrap: wrap;}
.schedule-buttons {text-align: center;margin-bottom:0;padding-top: 40px;padding-bottom: 30px;}
.schedule-add-btn {width: 102px; height: 35px;margin-bottom: 15px;font-weight: 600; font-size: 16px;color: #FFFFFF; border-radius: 5px; background: #0062D4;}
.option-add-btn {min-width: 102px; height: 35px;padding: 0 15px;font-weight: 600; font-size: 16px;color: #FFFFFF; border-radius: 5px; background: #383838;}
.text-info-image {margin-left: 21px; margin-right: 5px;}
/* .calendar-box {display: none;} */
.fix-price-box {display: flex;position: relative;}
.ui-sortable .price-option-id-box .fix-price-option-sort {display: flex;justify-content: center;align-items: center;min-width: 20px;height: 60px;}
.fix-price-option-group {min-width: 250px; height: 60px;margin-left: 10px; margin-right: 10px; margin-bottom: 8px; padding: 5px 11px;border: 1px solid #CCCCCC; background-color: #FFFFFF}
.fix-price-option-title {width: 100%; height: 60px;margin-left: 10px; margin-right: 10px; margin-bottom: 8px; padding: 5px 11px;border: 1px solid #CCCCCC; background-color: #FFFFFF}
.fix-price-option-date {min-width: 250px; height: 60px;margin-right: 11px; padding: 10px 11px;border: 1px solid #CCCCCC; background-color: #FFFFFF;}
.fix-price-option-price {min-width: 120px; height: 60px;margin-right: 11px; padding: 10px 11px;border: 1px solid #CCCCCC; background-color: #FFFFFF;}
.fix-price-option-capacity,.fix-price-option-quantity {min-width: 150px; height: 60px;margin-right: 11px; padding: 10px 11px;border: 1px solid #CCCCCC; background-color: #FFFFFF;}
.price-option-id-box {display:flex;width: 100%;}
.fix-add-option-btn {display: flex; justify-content: center; align-items: center;width: calc(100% - 10px); height: 35px;margin-top: 7px; margin-left: 10px;background-color: #383838; cursor: pointer;}
.price-cancel {width: 102px; height: 35px;margin-right: 10px;font: 600 14px Pretendard; color: #FFFFFF;border-radius: 5px; background-color: #383838;}
.price-save {width: 102px; height: 35px;font: 600 14px Pretendard; color: #FFFFFF;border-radius: 5px; background-color: #0062D4;}
.price-select {margin-left: 10px;background: #FFFFFF url("/images/icon/full-arrow-down.svg") no-repeat 99% 50%;}
.component-item table{border-collapse:collapse;width:100%;}
.component-item thead{border-bottom:solid 1px #FFFFFF;}
.component-item thead > tr > th{background: rgba(85, 85, 85, 0.9)}
.component-item tr:first-child th:first-child,.component-item tr:first-child td:first-child{border-right:solid 1px #FFFFFF;}
.component-item thead tr:first-child th{font-family: "Pretendard-Regular";font-size:18px;color:#FFFFFF;}
.component-item th, .component-item td{padding:10px;}
.component-item td{background-color:#CCCCCC;}
.component-item tr:last-child > td{border-bottom:solid 1px #CCCCCC;}
.pagination-box {display: flex; justify-content: center;}
.day-tab-btn { width: 120px; height: 42px; margin-right: 7px; margin-bottom: 5px; font-weight: 400; font-size: 18px; color:#888888; border: 1px solid #CCCCCC; background: #FFFFFF; }
.day-tab-btn.active { font-weight: 600; font-size: 18px; color: #1177EE; border-top: 2px solid #1177EE; }

.car-info input {height: 35px;padding: 0 12px;border: solid 1px #CCCCCC;}
.car-info {padding: 30px 0 40px 0;border-top: solid 1px #222222;}
.car-half-hr {margin: 30px 0;border-top: dashed 1px #CCCCCC;}
.fuel-select {width: 240px;}
.car-first-line {display: flex; justify-content: space-between; align-items: center;margin-bottom: 14px;font-weight: 400; font-size: 15px; color: #444444;}
.car-first-line span {margin-right: 25px;font-weight: 500; font-size: 15px;color: #222222;}
.car-first-line input {width: 240px;}
.insurance-select {width: 200px;}
.car-second-line {display: flex; align-items: center;font-weight: 400; font-size: 15px; color: #444444;}
.car-second-line span {margin-right: 7px;font-weight: 500; font-size: 15px;color: #222222;}
.insurance-label input {width: 511px;}
.insurance-label span {margin-right: 17px;}
.car-option-box {display: flex;}
.car-option-title {min-width: 73px;padding-top: 8px;font-weight: 600; font-size: 15px; color: #222222;}
.car-option-button-box {display: flex; flex-wrap: wrap;}
.car-option-button-box-item {position: relative; display:flex; justify-content: center; align-items: center;min-width: 180px; height: 35px;margin-right: 10px; margin-bottom: 10px;padding: 0 10px;border: solid 1px #CCCCCC; background-color: #EEEEEE;}
.add-car-option {display: flex; justify-content: center; align-items: center;width: 180px; height: 35px;color: #FFFFFF;background-color: #383838;}
.delete-car-option-btn {position: absolute; right: 10px;height: 17px;}
.car-option-add-modal {width: 500px; height: 236px; background: #FFFFFF;}
.car-option-add-modal-inner-box {width: 500px; height: 100%; background: #FFFFFF;}
.car-option-btn-group-box{display: flex; justify-content: center; margin-top: 15px;}

.product-option-add-btn-box { width: 100%; text-align: end; }
.product-option-modify-btn {width: 60px; height: 30px; margin-right: 7px; font-weight: 500; font-size: 14px;color: #333333; border: 1px solid #B7B7B7;  border-radius: 5px; background: #EEEEEE;}
.product-option-delete-btn { width: 60px; height: 30px; font-weight: 500; font-size: 14px; color: #FFFFFF; border-radius: 5px; background: #383838; }
.w-80 { width: 80px; }
.product-option-add-modal-inner-box { width: 500px; height: 375px; background: #FFFFFF; }
.product-option-add-modal-top { display: flex; justify-content: space-between; align-items: center; height: 65px; padding: 0 30px; border-bottom: 1px solid #222222; }
.product-option-add-text { align-items: center; display: flex; min-width: 60px; margin-right: 11px; font-weight: 600; font-size: 20px; color: #222222; }
.category-text-main-box { padding: 30px; background: #F9F9F9; }
.product-option-category-text { min-width: 73px; font-weight: 600; font-size: 15px; color: #222222 }
.product-option-category-text-box { display: flex; align-items: center; margin-bottom: 15px; }
.btn-group-box { display: flex; justify-content: center; margin-top: 30px; }
.option-save-btn { width: 75px; height: 35px; margin-right: 10px; font: 600 14px Pretendard; border-radius: 5px; color: #FFFFFF; background-color: #0062D4 }
.option-close-btn { width: 75px; height: 35px; font: 600 14px Pretendard; border-radius: 5px; color: #FFFFFF; background-color: #383838 }
.option-delete-btn { width: 75px; height: 35px; margin-right: 10px; font: 600 14px Pretendard; border-radius: 5px; color: #FFFFFF; background-color: #D4445C }
.product-option-category-select {position: relative; width: 100%; height: 35px; padding: 8px 12px; font: 400 15px Pretendard; color: #444444;border: 1px solid #CCCCCC; background: #FFFFFF url('/images/icon/full-arrow-down.svg') no-repeat 98% 50%;}
.product-option-category-input {position: relative; width: 100%; height: 35px; padding: 8px 12px; font: 400 15px Pretendard; color: #444444;border: 1px solid #CCCCCC;}
.product-option-add-modal-box {display: none; justify-content: center; align-items: center;position: fixed; width: 100%;height: 100%; background-color: #0000009A; top: 0; z-index: 1038;}
.product-option-add-modal { width: 500px; height: 375px; background: #FFFFFF; }
.order-modify-btn { width: 102px; height: 35px; font-weight: 600; font-size: 14px; color: #FFFFFF; border: none; border-radius: 5px; background: #383838 }
.product-option-change-box { padding: 21px 30px; }
.product-option-order-change-modal { width: 437px; height: 594px;background: #FFFFFF; }
.product-option-order-change-modal-scroll-inner-box { height: 331px; overflow-y: scroll; }
.product-option-order-change-modal-inner-box { height: 375px; background: #FFFFFF; }
.product-option-order-change-modal-scroll-box { height: 370px; padding: 20px 0 30px 30px; padding-right: 20px; background-color: #F9F9F9; }
.move-box-text { font-weight: 600; font-size: 15px; color: #222222; text-align: center; }
.option-sort-item {position: relative; display: flex; justify-content: center; align-items: center; width: 343px; height: 60px;margin-bottom: 15px; background: #FFFFFF; filter: drop-shadow(2px 2px 2px rgba(0, 0, 0, 0.3)); opacity: 0.5}
.option-sort-item.active { opacity: 1; }
.product-option-change-btn-group-box { display: flex; justify-content: center; margin-top: 20px;}

.stay-name-box {width: 100%; height: 70px;padding-top: 13px; padding-right: 20px; padding-left: 20px;border: 1px solid #CCCCCC;}
.stay-option-box {display:flex;margin-bottom: 8px;}
.stay-option-box label {width: 170px;margin-right: 10px;}
.stay-option-box label:first-child {width: 480px;margin-right: 10px;}
.stay-option-box label:last-child {margin-right: 0;}
.stay-option-title-label {display: grid;height: 70px;padding: 13px 20px;font-weight: 600; font-size: 15px; color: #222222;border: solid 1px #CCCCCC;}
.stay-title-input {width: 100%; height: 19px;font-weight: 400; font-size: 15px; color: #666666}
.stay-content-inner-box {display:flex; width: 920px; margin-bottom: 20px;padding: 25px 25px 25px 20px; border: 1px solid #CCCCCC; background: #FFFFFF;}

/*wayplus product editor*/
@font-face {font-family: 'NanumSquareNeo-Variable';src: url('https://cdn.jsdelivr.net/gh/projectnoonnu/noonfonts_11-01@1.0/NanumSquareNeo-Variable.woff2') format('woff2');font-weight: normal;font-style: normal;}
@font-face {font-family: 'Pretendard-Regular';src: url('https://cdn.jsdelivr.net/gh/Project-Noonnu/noonfonts_2107@1.1/Pretendard-Regular.woff') format('woff');font-weight: 400;font-style: normal;}
@font-face {font-family: 'GmarketSansMedium';src: url('https://cdn.jsdelivr.net/gh/projectnoonnu/noonfonts_2001@1.1/GmarketSansMedium.woff') format('woff');font-weight: normal;font-style: normal;}
/* editor frame style 용*/
*{margin:0;padding:0;}
ul{list-style: none;padding-left:0;}
body{margin:0;padding:0;}
input:disabled, :disabled input{cursor:not-allowed;}
.template-container{position:relative;}
#library-section{width:320px;/*height:100%;*/position:relative;display:flex;/* float:left; */border-top:solid 1px #CCCCCC;background-color:#FFFFFF;margin-left:30px;margin-top:40px;}
#editor-section{width:calc(100% - 320px);height:100%;/* float:left; */background-color:#efefef;overflow-y:scroll;position:relative;}
#editor-section header{/*visibility: hidden;*/position:fixed;top:240px;left:45%;z-index:9999;background-color:rgba(255,255,255,0.7);border-radius:5px;border:solid 1px #CCCCCC;user-select:none;}
#editor-section header div{display:inline-block;width:95px;height:30px;line-height:30px;margin-right:15px;text-align: center;}
#editor-section header div.zoom-out,#editor-section header div.zoom-in{cursor:pointer;}
#editor-section header div:last-child{margin-right:0;}
#editor-section header div.slide-width-height{margin-left:0;margin-right:10px;}
#editor-section header div.slide-width-height::before{content:"";width:2px;height:15px;background-color:#CCCCCC;position:absolute;top:9px;right:120px;}
#transform{display:flex;position:absolute;top:0;right:0%;}
#transform li{margin-right:30px;cursor:pointer;}
#transform li:last-child{margin-right:0;}
#component-design{width:100%;text-align: center;margin-top:25px;/* width:55px;border-right:solid 1px #CCCCCC; */}
#component-design li{cursor:pointer;display:inline-block;width:240px;height:50px;margin-right:15px;line-height:50px;text-align: center;background-color:#383838;color:#FFFFFF;}
#component-design li.edit-insert-template img{vertical-align: middle;margin-right:7px;}
#component-design li:last-child{margin-right:0;}
#component-design li.active{background-color:#0062D4;color:#FFFFFF;}
#library-section #button{position:absolute;top:50%;right:-29px;transform:translate(-50%,-50%);}
#font li{display:inline-block;width:100%;margin-bottom:15px;}
/*#align-components{display:none;}#align-components.active{display:block;}*/
#font li:last-child{margin-bottom:0;}
#text-radio-chk{display:flex;justify-content: flex-start;align-items: flex-start;}
.clear-fix{clear:both;}
.library{display:none;}
.library .notification-icon{margin-left: 20px;font-size: 13px;margin-bottom: 10px;}
.library.active{display:block;background-color:#FFFFFF;}
.library.active h3{text-align: center;background-color:#333333;color:#FFFFFF;height:40px;line-height:40px;font-size:18px;}
.library.active h4{width:100%;font-size:16px;font-family: 'Pretendard-Regular';margin-bottom:10px;}
.library.active select{display: block;width: 100%;padding: 0.375rem 2.25rem 0.375rem 0.75rem;-moz-padding-start: calc(0.75rem - 3px);font-size: 1rem;font-weight: 400;line-height: 1.5;color: #212529;background-color: #fff;background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");background-repeat: no-repeat;background-position: right 0.75rem center;background-size: 16px 12px;border: 1px solid #ced4da;/* border-radius: 0.375rem; */transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;-webkit-appearance: none;-moz-appearance: none;appearance: none;}
.library.active input[type='text']{display: block;width: 100%;/* padding: 0.375rem 0.75rem; */font-size: 1rem;font-weight: 400;line-height: 1.5;color: #212529;background-color: #fff;background-clip: padding-box;border: 1px solid #ced4da;-webkit-appearance: none;-moz-appearance: none;appearance: none;border-radius: 0.375rem;transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}
.library.active div.input-wrapper{width:100%;height:35px;line-height:35px;border:solid 1px #CCCCCC;margin-bottom:10px;overflow: hidden;}
.library.active input[type='range']{/* range input custom */position:relative;width:100%;}
.library.active div.hex-value{width:100%;height:35px;line-height:35px;text-align: center;border:solid 1px #CCCCCC;}
.library.active div.input-wrapper input{width:100%;height:100%;border-style:none;outline: none;}
.library.active div.input-wrapper input[type='number']{background-color:#FFFFFF;padding-left:15px;}
.library.active input:focus{color: #212529;background-color: #fff;border-color: #86b7fe;outline: 0;box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);}
.library.active select:focus{border-color: #86b7fe;outline: 0;box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);}
.library.active ul{padding:20px 15px;}
.library.active ul li{margin-bottom:10px;}
.library.active .insert-button li{display:inline-block;width:130px;height:40px;line-height:40px;margin-right:17px;text-align: center;}
.library.active .insert-button li button{background-color:transparent;border-style:none;color:#FFFFFF;font-size:18px;cursor:pointer;}
.library.active .insert-button li:first-child{background-color:#0062D4;}
.library.active .insert-button li:last-child{background-color:#383838;margin-right:0;}
.library.active .insert-button li.text-create,.library.active .insert-button li.text-apply{background-color:#0062D4;}
.section-wrapper{width:100%;display:flex;flex-direction:row;/* 아래 margin-top은 추후 지워야 함 */margin-top:55px;background-color:#efefef;height:777px;}
.section-wrapper.ck{display:block;}
.library-wrapper{width:100%;background-color:#efefef;}
.image-wrapper{display:flex;justify-content: flex-start;align-items: flex-start;}
.image-block{background-size:contain;background-repeat:no-repeat;background-position:center;overflow: hidden;/* width:320px;height:280px; */}
.image-block img{vertical-align: middle;}
.image-wrapper img{width:100%;}
.upload-btn{position: relative;display: inline-block;width:100%;height:45px;line-height:45px;overflow: hidden;border: 2px solid #ccc;border-radius: 4px;/*padding: 8px 0px;*/cursor: pointer;background-color: #f1f1f1;}
.upload-btn input[type="file"] {position: absolute;left: 0;top: 0;opacity: 0;cursor: pointer;height: 100%;width: 100%;}
.upload-btn label {display:flex;justify-content: center;align-items: center;/* display: block;white-space: nowrap;overflow: hidden;text-overflow: ellipsis; */}
.upload-btn label .icon{background-repeat:no-repeat;background-size:cover;background-position:center;width:18px;height:18px;margin-right:8px;}
.upload-btn:hover {background-color: #e1e1e1;}
.upload-btn:active {background-color: #d1d1d1;}
.preview-container{max-height:250px;overflow-y: scroll;}
.preview-image{height:150px;overflow: hidden;position:relative;}
.preview-image img{width:100%;height:100%;}
.component-item.text{font-family:'Pretendard-Regular';font-size:20px;font-weight:600;}
.component-item.image{width:350px;height:fit-content;}
.component-item.bgImage{width:100%;height:200px;}
.component-item p{text-align: center;}
#align-components > .align-types{padding:0px;}
#align-components > .align-types li{display: inline-block;width: 50px;height: 30px;cursor:pointer;}
#align-components > .align-types li div.align-img{background-size:cover;background-repeat:no-repeat;background-position: center;width:20px;height:20px;margin:auto;}
#align-components > .align-types li div.align-text{font-size: 12px;text-wrap: nowrap;text-align: center;}

.ck-editor__editable:not(.ck-editor__nested-editable) {min-height: 738px;max-height: 738px;overflow-y: scroll;}
.ck-source-editing-area{min-height: 738px;max-height: 738px;}
.ck-source-editing-area textarea{max-height: 738px;overflow-y: scroll;}
/* 해당 css는 반드시 editor에 적용되어야할 element스타일 */#content-container {position: relative;max-width: 900px;margin: 0 auto;background-size: cover;background-repeat: no-repeat;background-position: center;height: 100%;/* height: 2000px; *//* transform-origin: 50% 10%; *//* transform: scale(0.65); *//*margin-bottom: 100px;*/margin-top: 80px;overflow: hidden;}
#content-container .slides {position: relative;width: 100%}
#container-wrapper{position:relative;}
#content-container .slides .slide {/*height: 660px;*//*height:1000px;*//*TODO.. 상품 에디터 수정 필요*/height:1000px;background-size: cover;background-repeat: no-repeat;background-position: center;background-color: #FFFFFF;border-bottom: solid 1px #CCCCCC;position: relative;}
#content-container .slides .slide.edit-style {box-shadow: inset 0px 0px 0px 4px #10FE0C;}
#content-container li.slide:last-child {border-style: none;}
#title-area {position: relative;}
#content-area {position: relative;}
#bottom-area {position: relative;}
p {white-space: nowrap;user-select: none;}
.component-item {pointer-events: auto;overflow: hidden;}
[contenteditable] {outline: 0px solid transparent;}
/* .component-item.edit-content {border: dashed 1px red;padding: 12px 0%;}*/
.component-item.edit-style {box-shadow: inset 0px 0px 0px 4px #10FE0C;}
.component-item.edit-style[contenteditable=true] {box-shadow: inset 0px 0px 0px 4px #FE0E0C;}
.component-item.dragging {border: dashed 1px #CCCCCC;/* transition: top 0.18s, left 0.18s; *//* 이동할 때 0.3초의 애니메이션 효과 적용 */}
.component-item.active {pointer-events: none;}
/*.component-item:hover {cursor: move;}*/
.component-item img{width:100%;height:100%;}
/*   ///// card-container //// 아래 .card-container는 추후 수정 필요 */
.component-item div.card-container{width:100%;display:flex;flex-wrap:wrap;flex-direction: column;align-items: center;}
.component-item div.card-container .image-box{width:inherit;height:230px;background-size:cover;background-repeat:no-repeat;background-position:center;background-color:transparent;}
.component-item div.card-container .text{font-family: "Pretendard-Regular";font-weight:500;font-size:20px;text-align: center;paddig:8px 12px;}
/*   /////////// */
#edit-utility-item {display: none;}
#edit-utility-item.active {display: block;width: 280px;height: fit-content;}
.bg-wrapper {transform: translate(-50%, -50%);background-color: #efefef;}
#util-buttons {position: fixed;top: 50%;left: 27%;transform: translate(-50%, -50%);margin:0;border-radius:5px;/* bottom: 50px;left: 50%;transform: translate(-50%,-50%); */}
#util-buttons li{cursor:pointer;margin-bottom:14px;text-align: center;width:30px;height:30px;line-height:30px;border-radius:5px;}
#util-buttons li:hover{background-color:rgba(255,255,255,1);transition:all ease-in-out .35s;}
#util-buttons li:last-child{margin-bottom:0px;}
#util-buttons li.slide-add{background-repeat:no-repeat;background-size:cover;background-position:center;padding:2px 4px;}
#util-buttons li.slide-remove{background-repeat:no-repeat;background-size:cover;background-position:center;padding:2px 4px;}
#util-buttons li img{width:100%;height:100%;}
#buttons li {/* display:inline-block; */width: 35px;height: 30px;line-height: 30px;text-align: center;border: solid 2px #0062D4;border-bottom-left-radius: 20px;border-bottom-right-radius: 20px;background-color: #0062D4;}
#buttons li:last-child {/* display:inline-block; */width: 35px;height: 30px;line-height: 30px;text-align: center;border: solid 2px #383838;border-bottom-left-radius: 20px;border-bottom-right-radius: 20px;background-color: #383838;}
#buttons button {border-style: none;background-color: transparent;width: 100%;height: 100%;cursor: pointer;color: #FFFFFF;}
#buttons li:hover {border: solid 1px #FFFFFF;transition: all ease-in-out .35s;}
.show-modal {display: none;}
.show-modal.active {display: block;position: fixed;top: 0;left: 0;z-index:9999;width: 100%;height: 200%;background-color: rgba(75, 75, 75, 0.7);overflow-x: hidden;overflow-y: auto;}
.show-modal.active .data-wrapper {width: 960px;/*height: 500px;*/margin: 0 auto;position: fixed;top: 50%;left: 50%;transform: translate(-50%, -50%);background-color: #FFFFFF;}
.show-modal.active .data-wrapper header {display: flex;justify-content: space-between;align-items: center;background-color: #FFFFFF;border-bottom: solid 1px #222222;margin-bottom: 20px;padding: 20px 30px;}
.show-modal.active .data-wrapper .data {/* height:313px; */width: 100%;padding:10px 70px;background-color: #F9F9F9;}
.show-modal.active .data-wrapper .data .template{}
.show-modal.active .data-wrapper .data .template li.selected{border:solid 1px #0a58ca;}
.show-modal.active .data-wrapper .data .template li:hover{box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;cursor:pointer;top:-10px;transition:all ease-in-out .35s;}
.show-modal.active .data-wrapper .data .template li .template-thumbnail{height:260px;}
.show-modal.active .data-wrapper .data .template li .template-thumbnail img{width:100%;height:100%;aspect-ratio: 4 /3;}
.template-title{font-family:"Pretendard-Regular";font-size:18px;text-align: center;padding-top:10px;padding-bottom:10px;}
.show-modal.active .data-wrapper .template li{overflow: hidden;}
.show-modal.active .data-wrapper header .cancel-btn {cursor: pointer;}
.show-modal.active .data-wrapper .select-button {text-align: center;margin-top: 20px;}
.show-modal.active .data-wrapper .select-button li {display: inline-block;width: 75px;height: 35px;line-height: 35px;margin-right: 10px;cursor: pointer;}
.show-modal.active .data-wrapper .select-button li.add {background-color: #0062D4;border-radius: 5px;color: #FFFFFF;}
.show-modal.active .data-wrapper .select-button li.cancel {background-color: #383838;border-radius: 5px;color: #FFFFFF;}
.show-modal.active .data-wrapper .select-button li:last-child {margin-right: 0;}
#editor-section .toolbox{position: absolute;top: 0;left: 0;}
#editor-section .toolbox-items{display: flex;flex-direction: row;align-items: center;}
.swiper-button-next, .swiper-button-prev {color: #0045f9; opacity: 1;background:#ffffff99;border-radius:4px;/*border:1px;*/}
.equipment-service-container .title-p-box,
.template-info .title-p-box{margin-bottom: 20px;}
.template-info .title-p-box{margin-top: 50px;}
.equipment-service-container .title-p,
.template-info .title-p{font-size: 16px; font-weight: 600; margin-bottom: 5px;}

/* 숙소상품 프로그램 재고캘린더 */
.calendar-box  .btn-area .floating-button{
    width:180px;
    background-color:#05A54B;
    color:#fff;
    border-radius: 5px;
    height:40px;
    margin-right:10px;
}
@media (hover:hover) {
    .calendar-box  .btn-area .floating-button:hover{
        background-color:#158b48;
    }
}


