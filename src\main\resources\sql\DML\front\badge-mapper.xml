<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.front.BadgeMapper">
<!--
	 * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
-->
<!--################################### badgeContents ###################################-->
	<select id="selectOneBadgeContents" parameterType="HashMap" resultType="BadgeContents">
		SELECT *
		FROM badge_contents
		WHERE delete_yn = 'N'
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(badgeId)">
			AND badge_id = #{badgeId}
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(badgeCategoryId)">
			AND badge_category_id = #{badgeCategoryId}
		</if>
	</select>

	<insert id="insertUserBagdeContents">
		INSERT INTO badge_contents
		SET badge_type = #{badgeType},
			badge_desc = #{badgeDesc},
			badge_name = #{badgeName},
			badge_image_file_id = #{badgeImageFileId},
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(badgeCategoryId)">
			badge_category_id = #{badgeCategoryId},
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)">
			use_yn = #{useYn},
			</if>
			create_id = #{createId},
			create_date = now()
	</insert>

<!--################################### #{badgeAcquireHistory} ################################### -->
	<insert id="insertUserBadgeAcquireHistory" parameterType="HashMap" useGeneratedKeys="true" keyProperty="badgeHistoryId">
		INSERT INTO badge_acquire_history
		SET badge_id = #{badgeId},
			user_email = #{userEmail},
			create_id = #{createId},
			create_date = now()
	</insert>
</mapper>