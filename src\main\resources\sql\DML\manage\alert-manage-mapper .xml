<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.manage.AlertManageMapper">
	<!-- ######################### sms_policy ######################### -->
	<insert id="insertSmsPolicy" parameterType="SmsPolicy" useGeneratedKeys="true" keyProperty="smsPolicyId">
		INSERT INTO sms_policy
			(
				policy_send_schedule_time, policy_send_schedule_type, user_send_yn
				, admin_send_yn, use_yn, create_id, create_date)
		VALUES
			(
				#{policySendScheduleTime}, #{policySendScheduleType}, #{userSendYn}
				, #{adminSendYn}, #{useYn}, #{createId}, now())
	</insert>

	<select id="selectOneSmsPolicy" parameterType="HashMap" resultType="SmsPolicy">
		SELECT *
		  FROM sms_policy
		 WHERE delete_yn = 'N'
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(smsPolicyId)"> AND sms_policy_id=#{smsPolicyId} </if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)"> AND use_yn = #{useYn} </if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(policySendScheduleType)"> AND policy_send_schedule_type=#{policySendScheduleType} </if>
	</select>

	<select id="selectCountListSmsPolicy" parameterType="HashMap" resultType="int">
		SELECT COUNT(sms_policy_id)
		  FROM sms_policy
		 WHERE delete_yn = 'N'
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(policySendScheduleType)"> AND policy_send_schedule_type=#{policySendScheduleType} </if>
	</select>

	<select id="selectListSmsPolicy" parameterType="HashMap" resultType="SmsPolicy">
		SELECT *,  row_number() over(order by sms_policy_id asc) as row_num 
		  FROM sms_policy
		 WHERE delete_yn = 'N'
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(policySendScheduleType)"> AND policy_send_schedule_type=#{policySendScheduleType} </if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
			ORDER BY
			<choose>
				<when test="sort=='sms_policy_id'" >	sms_policy_id	</when>
				<when test="sort=='createId'" >	create_id	</when>
				<when test="sort=='createDate'" >	create_date	</when>
				<when test="sort=='lastUpdateId'" >	last_update_id	</when>
				<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
				<when test="sort=='deleteYn'" >	delete_yn	</when>
				<when test="sort=='deleteId'" >	delete_id	</when>
				<when test="sort=='deleteDate'" >	delete_date	</when>

				<otherwise>row_num</otherwise>
			</choose>
			<choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
        </if>
	</select>

	<update id="updateSmsPolicy" parameterType="SmsPolicy">
		UPDATE sms_policy
		   SET policy_send_schedule_time = #{policySendScheduleTime}, policy_send_schedule_type = #{policySendScheduleType}
				, user_send_yn = #{userSendYn}, admin_send_yn = #{adminSendYn}, use_yn = #{useYn}
				, last_update_id = #{lastUpdateId}, last_update_date = now()
		 WHERE sms_policy_id = #{smsPolicyId}
	</update>

	<update id="deleteSmsPolicy" parameterType="SmsPolicy">
		UPDATE sms_policy
		   SET delete_yn = 'Y', use_yn = 'N'
				, last_update_id = #{lastUpdateId}, last_update_date = now()
		 WHERE sms_policy_id = #{smsPolicyId}
	</update>

	<update id="updateSmsPolicyYn" parameterType="SmsPolicy">
		UPDATE sms_policy
		   SET last_update_id = #{lastUpdateId},
		   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userSendYn)"> user_send_yn = #{userSendYn}, </if>
		   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(adminSendYn)"> admin_send_yn = #{adminSendYn}, </if>	
		   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)"> use_yn = #{useYn},</if>	
		   last_update_date = now()
		 WHERE sms_policy_id = #{smsPolicyId}
	</update>
</mapper>