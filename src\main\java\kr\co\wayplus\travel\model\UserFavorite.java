package kr.co.wayplus.travel.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserFavorite extends PlaceSpot {
    private Integer id
                , menuId
                , productTourId
                , commentId
                , productCategoryId
                , productCommentId
                , userFavoriteId
                , tsId
                , boardId
                , favoriteCount;
    private String userEmail	//사용자ID(이메일ID)
                , userRole
                , userName
                , targetUserEmail	//좋아요 받은 사용자ID(이메일ID)
                , productSerial	//상품번호
                , intoTime
                , productTitle
                , productThumbnail
                , productPrice
                , fullMenuUrl
                , type
                , productFavoriteType
                , boardFavoriteType
                , favoriteType
                , favoriteCalcType;
}
