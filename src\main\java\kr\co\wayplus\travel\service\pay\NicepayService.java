package kr.co.wayplus.travel.service.pay;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class NicepayService {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    public JsonNode netCancel (
    		String nicePayUrl,
    		String clientID,
    		String secretKey,

    		String orderId
//    		,String mallReserved
//    		,String ediDate
//    		,String signData
//    		,String returnCharSet
    		) throws JsonProcessingException {

    	HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Basic " + Base64.getEncoder().encodeToString((clientID + ":" + secretKey).getBytes()));
        headers.setContentType(MediaType.APPLICATION_JSON);

        Map<String, Object> AuthenticationMap = new HashMap<>();
        AuthenticationMap.put("orderId", String.valueOf(orderId));

        HttpEntity<String> request = new HttpEntity<>(objectMapper.writeValueAsString(AuthenticationMap), headers);
        ResponseEntity<JsonNode> responseEntity = restTemplate.postForEntity(
        	nicePayUrl+ "/netcancel/", request, JsonNode.class);

        JsonNode responseNode = responseEntity.getBody();
        String resultCode = responseNode.get("resultCode").asText();

        //System.out.println(responseNode.toPrettyString());

    	return responseNode;
    }


}
