package kr.co.wayplus.travel.web.manage;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.AlertMessageLog;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.Policy;
import kr.co.wayplus.travel.model.SmsPolicy;
import kr.co.wayplus.travel.model.SortData;
import kr.co.wayplus.travel.service.api.NCloudApiService;
import kr.co.wayplus.travel.service.manage.AlertManageService;

@Controller
@RequestMapping("/manage/alert")
public class AlertManageController extends BaseController {
    private final Logger logger = LoggerFactory.getLogger(getClass());

	@Value("${upload.file.path}")
	String externalImageUploadPath;

    @Value("${upload.file.max-size}")
    int maxFileSize;

    private final AlertManageService svcAlert;
    private final NCloudApiService svcNcloud;
    public AlertManageController(
    		AlertManageService svc1,
    		NCloudApiService svc2) {
        this.svcAlert = svc1;
        this.svcNcloud = svc2;
    }
	////<!--################################### policy ###################################-->
    @GetMapping("/list")
    public ModelAndView alert(
    		@RequestParam(value="page", defaultValue="1") int page,
            @RequestParam(value="pageSize", defaultValue="10") int pageSize,
		    @RequestParam(value="searchType", defaultValue="") String searchType,
		    @RequestParam(value="searchKey", defaultValue="") String searchKey){
        ModelAndView mav = new ModelAndView();

        HashMap<String, Object> paramMap = new HashMap<String, Object>();

    	mav.setViewName("manage/sub/alert/list");
    	return mav;
    }

    @GetMapping("/form")
    public ModelAndView alert_form(
    		@RequestParam(value="mode", defaultValue="I") String mode,
    		@RequestParam(value="id", defaultValue="0") String id){
        ModelAndView mav = new ModelAndView();
/*
        if(mode.equals("U")) {
        	HashMap<String, Object> paramMap = new HashMap<>();
        	paramMap.put("id", id);
        	mav.addObject("policy",  svcPolicy.selectOnePolicy(paramMap));
        	paramMap.clear();
        	mav.addObject("p", paramMap);
        } else {
        	mav.addObject("policy",  new Policy());
        }
        mav.addObject("mode", mode);
        mav.addObject("policyCategory",  svcPolicy.selectListPolicyCategory(null));
*/
        mav.setViewName("manage/sub/alert/form");
        return mav;
    }
    @PostMapping("/list")
    @ResponseBody
   	public HashMap<String, Object> alert_list(
   			HttpServletRequest request,
   			@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length,
   			@Param(value="titleLike") String titleLike
   		){
   		HashMap<String, Object> resultMap = new HashMap<>();

   		try{
   			HashMap<String, Object> paramMap = new HashMap<>();

   			List<SortData> listSort = getListOrder(request);
   			paramMap.put("listSort", listSort);
   			paramMap.put("titleLike", titleLike);

   			if(length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
    		}

   			paramMap.put("sort", "requestTime");
    		paramMap.put("sortOrder", "desc");

   			int totalCount = svcNcloud.selectCountLogMessageSend(paramMap);
   			ArrayList<AlertMessageLog> lists = svcNcloud.selectListLogMessageSend(paramMap);

	        resultMap.put("recordsTotal", totalCount);
	  		resultMap.put("recordsFiltered", totalCount);
	  		resultMap.put("data", lists);

   			resultMap.put("result", "success");
   			resultMap.put("message", "처리되었습니다.");
   		}catch (Exception e){
   			logger.error(e.getMessage());
   			resultMap.put("result", "error");
//   			resultMap.put("message", "처리중 오류가 발생하였습니다.");
   			resultMap.put("error", e.getMessage());
   			resultMap.put("message", e.getMessage());
   		}

   		return resultMap;
   	}

    @PostMapping("/save")
	@ResponseBody
	public HashMap<String, Object> alert_save(Policy ts){
		HashMap<String, Object> resultMap = new HashMap<>();
		try{
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser user = (LoginUser)_user;
        		/*
				if(ts.getId() == null) {
					ts.setCreateId( user.getUserEmail() );
					svcPolicy.insertPolicy(ts);
				} else {
					ts.setLastUpdateId( user.getUserEmail() );
					svcPolicy.updatePolicy(ts);
				}
				*/

				resultMap.put("result", "success");
	    		resultMap.put("message", "처리가 완료 되었습니다.");
	    	} else {
	    		resultMap.put("result", "fail");
	    		resultMap.put("message", "로그인 문제가 발생되었습니다.");
	    	}
		}catch (Exception e){
			logger.error(e.getCause().getLocalizedMessage());
			resultMap.put("result", "error");
			resultMap.put("message", e.getMessage());

		}

		return resultMap;
	}

    @PostMapping("/delete")
	@ResponseBody
	public HashMap<String, Object> alert_delete(Policy ts){
		HashMap<String, Object> resultMap = new HashMap<>();
		try{
			//svcPolicy.deletePolicy(ts);
			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		}catch (Exception e){
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}

	////<!--################################### sms policy ###################################-->
	@GetMapping("/sms-policy-list")
    public ModelAndView smsPolicyList(){
        ModelAndView mav = new ModelAndView();

    	mav.setViewName("manage/sub/alert/sms-policy-list");
    	return mav;
    }

    @GetMapping("/sms-policy-form")
    public ModelAndView smsPolicyForm(
    		@RequestParam(value="mode", defaultValue="I") String mode,
    		@RequestParam(value="smsPolicyId", defaultValue="0") String smsPolicyId){
        ModelAndView mav = new ModelAndView();
        if(mode.equals("U")) {
        	HashMap<String, Object> paramMap = new HashMap<>();
        	paramMap.put("smsPolicyId", smsPolicyId);
        	mav.addObject("smsPolicy",  svcAlert.selectOneSmsPolicy(paramMap));
        } else {
        	mav.addObject("smsPolicy",  null);
        }
        mav.addObject("mode", mode);
        mav.setViewName("manage/sub/alert/sms-policy-form");
        return mav;
    }
    @PostMapping("/ajax-sms-policy-list")
    @ResponseBody
   	public HashMap<String, Object> ajaxSmsPolicyList(
   			@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length,
   			@Param(value="titleLike") String titleLike
   		){
   		HashMap<String, Object> resultMap = new HashMap<>();

   		try{
   			HashMap<String, Object> paramMap = new HashMap<>();

   			paramMap.put("titleLike", titleLike);
   			if(length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
    		}

   			paramMap.put("sort", "sms_policy_id");
    		paramMap.put("sortOrder", "desc");

   			int totalCount = svcAlert.selectCountListSmsPolicy(paramMap);
   			ArrayList<SmsPolicy> lists = svcAlert.selectListSmsPolicy(paramMap);

	        resultMap.put("recordsTotal", totalCount);
	  		resultMap.put("recordsFiltered", totalCount);
	  		resultMap.put("data", lists);

   			resultMap.put("result", "success");
   			resultMap.put("message", "처리되었습니다.");
   		}catch (Exception e){
   			logger.error(e.getMessage());
   			resultMap.put("result", "error");
   			resultMap.put("error", e.getMessage());
   			resultMap.put("message", e.getMessage());
   		}

   		return resultMap;
   	}

    @PostMapping("/sms-policy-save")
	@ResponseBody
	public HashMap<String, Object> smsPolicySave(SmsPolicy smsPolicy){
		HashMap<String, Object> resultMap = new HashMap<>();
		try{
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser user = (LoginUser)_user;
				
				if(smsPolicy.getPolicySendScheduleTime() == null) {
					smsPolicy.setPolicySendScheduleTime( "0" );
				}

				HashMap<String, Object> paramMap = new HashMap<>();
				paramMap.put("policySendScheduleType", smsPolicy.getPolicySendScheduleType());

				if ( svcAlert.selectListSmsPolicy(paramMap).size() == 0) {
					smsPolicy.setCreateId( user.getUserEmail() );
					svcAlert.insertSmsPolicy(smsPolicy);
	
					resultMap.put("result", "success");
					resultMap.put("message", "등록되었습니다.");
				} else {
					resultMap.put("result", "fail");
					resultMap.put("message", "이미 같은 값으로 등록되어있는 발신 시기가 존재합니다.");
				}
				
	    	} else {
	    		resultMap.put("result", "fail");
	    		resultMap.put("message", "로그인 문제가 발생되었습니다.");
	    	}
		}catch (Exception e){
			e.printStackTrace();
			logger.error(e.getCause().getLocalizedMessage());
			resultMap.put("result", "error");
			resultMap.put("message", e.getMessage());

		}

		return resultMap;
	}

	@PutMapping("/sms-policy-update")
	@ResponseBody
	public HashMap<String, Object> smsPolicyUpdate(SmsPolicy smsPolicy){
		HashMap<String, Object> resultMap = new HashMap<>();
		try{
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser user = (LoginUser)_user;

					if(smsPolicy.getPolicySendScheduleTime() == null) {
						smsPolicy.setPolicySendScheduleTime( "0" );
					}

					smsPolicy.setLastUpdateId( user.getUserEmail() );
					svcAlert.updateSmsPolicy(smsPolicy);
	
					resultMap.put("result", "success");
					resultMap.put("message", "수정되었습니다.");
	    	} else {
	    		resultMap.put("result", "fail");
	    		resultMap.put("message", "로그인 문제가 발생되었습니다.");
	    	}
		}catch (Exception e){
			e.printStackTrace();
			logger.error(e.getCause().getLocalizedMessage());
			resultMap.put("result", "error");
			resultMap.put("message", e.getMessage());

		}

		return resultMap;
	}

    @PostMapping("/sms-policy-delete")
	@ResponseBody
	public HashMap<String, Object> smsPolicyDelete(SmsPolicy smsPolicy){
		HashMap<String, Object> resultMap = new HashMap<>();
		try{
			svcAlert.deleteSmsPolicy(smsPolicy);
			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		}catch (Exception e){
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}

	@PutMapping("/sms-policy-yn-update")
	@ResponseBody
	public HashMap<String, Object> smsPolicyYnUpdate(SmsPolicy smsPolicy){
		HashMap<String, Object> resultMap = new HashMap<>();
		try{
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser user = (LoginUser)_user;

					smsPolicy.setLastUpdateId( user.getUserEmail() );
					svcAlert.updateSmsPolicyYn(smsPolicy);
	
					resultMap.put("result", "success");
					resultMap.put("message", "수정되었습니다.");
	    	} else {
	    		resultMap.put("result", "fail");
	    		resultMap.put("message", "로그인 문제가 발생되었습니다.");
	    	}
		}catch (Exception e){
			e.printStackTrace();
			logger.error(e.getCause().getLocalizedMessage());
			resultMap.put("result", "error");
			resultMap.put("message", e.getMessage());

		}

		return resultMap;
	}
}
