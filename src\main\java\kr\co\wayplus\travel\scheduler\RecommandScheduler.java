package kr.co.wayplus.travel.scheduler;

import kr.co.wayplus.travel.service.common.MessageSenderService;
import kr.co.wayplus.travel.service.recommand.RecommandationManageService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class RecommandScheduler {
    private Logger logger = LoggerFactory.getLogger(getClass());
    protected static String ACTIVE_PROFILE;

    private final RecommandationManageService rcmdService;

    public RecommandScheduler(RecommandationManageService rcmdService) {
        this.rcmdService = rcmdService;
        ACTIVE_PROFILE = System.getProperty("spring.profiles.active");
    }


    /**
     * 매일 새벽 1시에 실행
     */
    @Scheduled(cron = "0 0 1 * * *")
    public void recommandDataGen(){
    	if( ACTIVE_PROFILE.equals("server") ) {
	        logger.debug("Send Recommand Data Scheduled Start");
	        rcmdService.updateRecommendations();
	        logger.debug("Send Recommand Data Scheduled End");
    	}
    }
}
