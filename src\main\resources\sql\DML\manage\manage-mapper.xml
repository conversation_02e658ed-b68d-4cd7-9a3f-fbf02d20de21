<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.manage.ManageMapper">
	<!--################################### manageMenu ###################################-->
	<select id="selectCountManageMenu" parameterType="HashMap" resultType="Integer">
		SELECT count(menu_id)
		  FROM manage_menu
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)" >	and menu_type=#{menuType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperMenuId)" >	and upper_menu_id=#{upperMenuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuName)" >	and menu_name=#{menuName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuNameEn)" >	and menu_name_en=#{menuNameEn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuNameCh)" >	and menu_name_ch=#{menuNameCh}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuUrl)" >	and menu_url=#{menuUrl}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuIcon)" >	and menu_icon=#{menuIcon}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSort)" >	and menu_sort=#{menuSort}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuDesc)" >	and menu_desc=#{menuDesc}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
		 </where>
	</select>

	<select id="selectListManageMenu" parameterType="HashMap" resultType="ManageMenu">
		SELECT *
		  FROM(
			SELECT @rownum:=@rownum+1 AS rownum,
				   a.menu_id, a.upper_menu_id, a.menu_type, a.menu_name,
				   a.menu_url, a.menu_icon, a.menu_sort, a.menu_desc, a.menu_permission,
				   a.create_id, a.create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.use_yn
			  FROM manage_menu a
			  <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authId)" >join manage_menu_connect_auth b on a.menu_id = b.menu_id </if>
			  join (SELECT @rownum:= 0) rnum
			 <where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isAdmin) and !isAdmin" >	and a.menu_permission != 'ADMIN'	</if>

			 	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and a.menu_id=#{menuId}	</if>
			 	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)" >	and a.menu_type=#{menuType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperMenuId)" >	and a.upper_menu_id=#{upperMenuId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuName)" >	and a.menu_name=#{menuName}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuUrl)" >	and a.menu_url=#{menuUrl}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuIcon)" >	and a.menu_icon=#{menuIcon}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSort)" >	and a.menu_sort=#{menuSort}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuDesc)" >	and a.menu_desc=#{menuDesc}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and a.create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and a.create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and a.last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and a.last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and a.use_yn=#{useYn}	</if>
				and a.delete_yn='N'
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(hideYn)" >	and a.hide_yn=#{hideYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(authId)" >and b.auth_id = #{authId} </if>
			 </where>

			 <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
				ORDER BY
				<choose>
					<when test="sort=='menuId'" >	menu_id	</when>
					<when test="sort=='upperMenuId'" >	upper_menu_id	</when>
					<when test="sort=='menuName'" >	menu_name	</when>
					<when test="sort=='menuUrl'" >	menu_url	</when>
					<when test="sort=='menuIcon'" >	menu_icon	</when>
					<when test="sort=='menuSort'" >	menu_sort	</when>
					<when test="sort=='menuDesc'" >	menu_desc	</when>
					<when test="sort=='createId'" >	create_id	</when>
					<when test="sort=='createDate'" >	create_date	</when>
					<when test="sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="sort=='deleteYn'" >	delete_yn	</when>
					<when test="sort=='useYn'" >	use_yn	</when>
					<otherwise>rownum</otherwise>
				</choose>
				<choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
				ORDER BY <foreach item="item" index="index" collection="listSort" separator=",">
				<choose>
					<when test="item.sort=='menuId'" >	menu_id	</when>
					<when test="item.sort=='upperMenuId'" >	upper_menu_id	</when>
					<when test="item.sort=='menuName'" >	menu_name	</when>
					<when test="item.sort=='menuUrl'" >		menu_url	</when>
					<when test="item.sort=='menuIcon'" >	menu_icon	</when>
					<when test="item.sort=='menuSort'" >	menu_sort	</when>
					<when test="item.sort=='menuDesc'" >	menu_desc	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='useYn'" >	use_yn	</when>
				</choose>
				<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>

			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
		 LIMIT #{itemStartPosition}, #{pagePerSize}
		 </if>
	</select>

	<select id="selectOneManageMenu" parameterType="HashMap" resultType="ManageMenu">
		SELECT menu_id, upper_menu_id, menu_type, menu_name,
			   menu_url, menu_icon, menu_sort, menu_desc, menu_permission,
			   create_id, create_date, last_update_id, last_update_date, delete_yn, use_yn
		  FROM manage_menu a
		  join (SELECT @rownum:= 0) rnum
		 <where>
		 	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperMenuId)" >	and upper_menu_id=#{upperMenuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)" >	and menu_type=#{menuType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuName)" >	and menu_name=#{menuName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuUrl)" >	and menu_url=#{menuUrl}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuIcon)" >	and menu_icon=#{menuIcon}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSort)" >	and menu_sort=#{menuSort}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuDesc)" >	and menu_desc=#{menuDesc}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			and delete_yn='N'
		 </where>
	</select>

	<insert id="insertManageMenu" parameterType="ManageMenu" useGeneratedKeys="true" keyProperty="menuId">
		INSERT INTO manage_menu
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)" >	menu_type=#{menuType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperMenuId)" >	upper_menu_id=#{upperMenuId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuName)" >	menu_name=#{menuName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuUrl)" >	menu_url=#{menuUrl},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuIcon)" >	menu_icon=#{menuIcon},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSort)" >	menu_sort=#{menuSort},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuDesc)" >	menu_desc=#{menuDesc},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuPermission)" >	menu_permission=#{menuPermission}, </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	create_id=#{createId},	</if>
			create_date = now(),delete_yn='N'
		</set>
	</insert>

	<update id="updateManageMenu" parameterType="ManageMenu">
		UPDATE manage_menu
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)" >	menu_type=#{menuType},	</if>
			<!--
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperMenuId) and isMenuUpperId" >	upper_menu_id=#{upperMenuId},	</if>
			<if test="!isMenuUpperId" >	upper_menu_id=null,	</if>
			 -->
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperMenuId) and isMenuUpperId" >	upper_menu_id=#{upperMenuId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isMenuUpperId) and !isMenuUpperId" >	upper_menu_id=null,	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuName)" >	menu_name=#{menuName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuUrl)" >	menu_url=#{menuUrl},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuIcon)" >	menu_icon=#{menuIcon},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSort)" >	menu_sort=#{menuSort},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuDesc)" >	menu_desc=#{menuDesc},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuPermission)" >	menu_permission=#{menuPermission}, </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	delete_yn=#{deleteYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
		</where>
	</update>

	<update id="deleteManageMenu" parameterType="ManageMenu">
		UPDATE manage_menu
		<set>
			delete_yn='Y',last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
		</where>
	</update>
	<!--################################### Statistic ###################################-->
	<select id="selectListStatisticDate" parameterType="HashMap" resultType="HashMap">
		select
		<choose>
			<when test="chartType == 'dateRange'">time, </when>
			<when test="chartType == 'weekRange'">WEEKDAY(STR_TO_DATE( time, '%Y-%m-%d')) week, </when>
		</choose>
		 <choose>
			<when test="chartType == 'weekRange'">case WEEKDAY(STR_TO_DATE( time, '%Y-%m-%d')) when 0 then '월' when 1 then '화' when 2 then '수' when 3 then '목' when 4 then '금' when 5 then '토' else '일' end  weekname, </when>
		</choose>

		count(session_id) cnt
		  from (
			select session_id, SUBSTRING(min(request_time),1,10) time
			  from webservice_log
			 group by session_id) a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">time between #{startDate} and #{endDate}</if>
		</where>
		group by
		<choose>
			<when test="chartType == 'dateRange'">time </when>
			<when test="chartType == 'weekRange'">week </when>
		</choose>

		<choose>
			<when test="chartType == 'dateRange'">order by time desc </when>
		</choose>
	</select>

	<select id="selectListStatisticInfo" parameterType="HashMap" resultType="HashMap">
	 select sum(day_cnt) to_sum_cnt
	 		, round(avg(day_cnt),1) to_avg_cnt
	 		, max(case when time = date(now()) then day_cnt else 0 end) to_day_count
	 		, max(case when time = date(adddate(now(), interval -1 day)) then day_cnt else 0 end) to_yuesterday_count
	   from(
		 select time
		 		,count(session_id) day_cnt
		  from (
			select session_id, SUBSTRING(min(request_time),1,10) time
			  from webservice_log
			 group by session_id) a
		 group by time
		 order by time desc ) a
	</select>

	<!--################################### manageMenu ###################################-->
	<select id="selectCountMenuConnectBoard" parameterType="HashMap" resultType="Integer">
		SELECT count(create_date) FROM menu_connect_board
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	and board_id=#{boardId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
		 </where>
	</select>

	<select id="selectOneMenuConnectBoard" parameterType="HashMap" resultType="MenuConnectBoard">
		SELECT menu_id, board_id, create_date
		  FROM menu_connect_board
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	and board_id=#{boardId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
		 </where>
	</select>

	<insert id="insertMenuConnectBoard" parameterType="MenuConnectBoard" useGeneratedKeys="true" keyProperty="menuId">
		INSERT INTO menu_connect_board
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	menu_id=#{menuId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	board_id=#{boardId},	</if>
			create_date = now()
		</set>
	</insert>

	<delete id="deleteMenuConnectBoard" parameterType="HashMap">
        DELETE FROM menu_connect_board
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	and board_id=#{boardId}	</if>
		</where>
    </delete>

    <!--################################### MenuConnectPlace ###################################-->
    <select id="selectCountMenuConnectPlace" parameterType="HashMap" resultType="Integer">
		SELECT count(create_date) FROM menu_connect_place
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(placeCode)" >	and place_code=#{placeCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
		 </where>
	</select>

	<select id="selectOneMenuConnectPlace" parameterType="HashMap" resultType="MenuConnectPlace">
		SELECT menu_id, place_code, create_date
		  FROM menu_connect_place
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(placeCode)" >	and place_code=#{placeCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
		 </where>
	</select>

	<insert id="insertMenuConnectPlace" parameterType="MenuConnectPlace" useGeneratedKeys="true" keyProperty="menuId">
		INSERT INTO menu_connect_place
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	menu_id=#{menuId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(placeCode)" >	place_code=#{placeCode},	</if>
			create_date = now()
		</set>
	</insert>

	<delete id="deleteMenuConnectPlace" parameterType="HashMap">
        DELETE FROM menu_connect_place
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(placeCode)" >	and place_code=#{placeCode}	</if>
		</where>
    </delete>
</mapper>