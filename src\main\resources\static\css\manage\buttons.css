@charset "UTF-8";
:root{
	--black-background-color:#333333;
	--black-border-color:#333333;
	--black-hover-background-color:#454545;
	--black-hover-border-color:#454545;
	--black-disable-background-color:#666666;
	--black-disable-border-color:#666666;

	--gray-background-color:#E3E3E3;
	--gray-border-color:#B7B7B7;
	--gray-hover-background-color:#E3E3E3;
	--gray-hover-border-color:#B7B7B7;
	--gray-disable-background-color:#E3E3E3;
	--gray-disable-border-color:#B7B7B7;

	--wgray-color:#777;
	--wgray-background-color:#F9F9F9;
	--wgray-border-color:#CCCCCC;
	--wgray-hover-color:#666;
	--wgray-hover-background-color:#F9F9F9;
	--wgray-hover-border-color:#CCCCCC;
	--wgray-disable-background-color:#F9F9F9;
	--wgray-disable-border-color:#DCDCDC;

	--way-font-color-black:#333333;
	--way-font-color-white:#ffffff;

	--way-color-blue:#0062D4;
	--way-color-blue-hover:#004697;

	--way-color-red:#D4445C;
	--way-color-red-hover:#B33853;

	--way-color-black:#333333;
	--way-color-black-hover:#000000;

	--way-color-lightgray:#E3E3E3;
	--way-color-lightgray-hover:#CCCCCC;
	--way-color-lightgray-border:#B7B7B7;

	--way-color-excel:#13753d;
	--way-color-excel-hover:#094724;
}

.menu .btn{height: 35px !important;border-radius: 0.3125rem;}

.btn-blue {color: #fff;background-color: var(--way-color-blue);border-color: var(--way-color-blue);box-shadow: none;}
.btn-blue:hover {color: #fff;background-color: var(--way-color-blue-hover);border-color: var(--way-color-blue-hover);}
.btn-blue:focus, .btn-blue.focus {color: #fff;background-color: var(--way-color-blue-hover);border-color: var(--way-color-blue-hover);box-shadow: 0 0 0 0 rgba(72, 180, 97, 0.5);}
.btn-blue.disabled, .btn-blue:disabled {color: #fff;background-color: var(--way-color-blue);border-color: var(--way-color-blue);}
.btn-blue:not(:disabled):not(.disabled):active, .btn-blue:not(:disabled):not(.disabled).active
/*,.show > .btn-blue.dropdown-toggle*/ {color: #fff;background-color: var(--black-disable-background-color);border-color: var(--black-disable-border-color);}
.btn-blue:not(:disabled):not(.disabled):active:focus, .btn-blue:not(:disabled):not(.disabled).active:focus
/*,.show > .btn-blue.dropdown-toggle:focus*/ {box-shadow: 0 0 0 0 rgba(72, 180, 97, 0.5);}

.btn-outline-blue {color: var(--way-color-blue);border-color: var(--way-color-blue);display: inline-flex;align-items: center;}
.btn-outline-blue:hover {color: #fff;background-color: var(--way-color-blue);border-color: var(--way-color-blue);}
.btn-outline-blue:focus, .btn-outline-blue.focus {box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.5);}
.btn-outline-blue.disabled, .btn-outline-blue:disabled {color: #007bff;background-color: transparent;}
.btn-outline-blue:not(:disabled):not(.disabled):active, .btn-outline-blue:not(:disabled):not(.disabled).active,
.show > .btn-outline-blue.dropdown-toggle {color: #fff;background-color: #007bff;border-color: #007bff;}
.btn-outline-blue:not(:disabled):not(.disabled):active:focus, .btn-outline-blue:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-blue.dropdown-toggle:focus {box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.5);}

.btn-red {color: #fff;background-color: var(--way-color-red);border-color: var(--way-color-red);box-shadow: none;}
.btn-red:hover {color: #fff;background-color: var(--way-color-red-hover);border-color: var(--way-color-red-hover);}
.btn-red:focus, .btn-red.focus {color: #fff;background-color: var(--way-color-red-hover);border-color: var(--way-color-red-hover);box-shadow: 0 0 0 0 rgba(72, 180, 97, 0.5);}
.btn-red.disabled, .btn-red:disabled {color: #fff;background-color: var(--way-color-red);border-color: var(--way-color-red);}
.btn-red:not(:disabled):not(.disabled):active, .btn-red:not(:disabled):not(.disabled).active
/*,.show > .btn-red.dropdown-toggle*/ {color: #fff;background-color: var(--black-disable-background-color);border-color: var(--black-disable-border-color);}
.btn-red:not(:disabled):not(.disabled):active:focus, .btn-red:not(:disabled):not(.disabled).active:focus
/*,.show > .btn-red.dropdown-toggle:focus*/ {box-shadow: 0 0 0 0 rgba(72, 180, 97, 0.5);}

.btn-outline-red {color: var(--way-color-red-hover);border-color: var(--way-color-red-hover);display: inline-flex;align-items: center;}}
.btn-outline-red:hover {color: #fff;background-color: var(--way-color-red);border-color: var(--way-color-red);}
.btn-outline-red:focus, .btn-outline-red.focus {box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.5);}
.btn-outline-red.disabled, .btn-outline-red:disabled {color: var(--way-color-red);background-color: transparent;}
.btn-outline-red:not(:disabled):not(.disabled):active, .btn-outline-red:not(:disabled):not(.disabled).active,
.show > .btn-outline-red.dropdown-toggle {color: #fff;background-color: var(--way-color-red);border-color: var(--way-color-red);}
.btn-outline-red:not(:disabled):not(.disabled):active:focus, .btn-outline-red:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-red.dropdown-toggle:focus {box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.5);}

.btn-black {color: #fff;background-color: var(--way-color-black);border-color: var(--black-border-color);box-shadow: none;}
.btn-black:hover {color: #fff;background-color: var(--way-color-black-hover);border-color: var(--way-color-black-hover);}
.btn-black:focus, .btn-black.focus {color: #fff;background-color: var(--way-color-black-hover);border-color: var(--way-color-black-hover);box-shadow: 0 0 0 0 rgba(72, 180, 97, 0.5);}
.btn-black.disabled, .btn-black:disabled {color: #fff;background-color: var(--way-color-black);border-color: var(--way-color-black);}
.btn-black:not(:disabled):not(.disabled):active, .btn-black:not(:disabled):not(.disabled).active
/*,.show > .btn-black.dropdown-toggle*/ {color: #fff;background-color: var(--way-color-black);border-color: var(--way-color-black);}
.btn-black:not(:disabled):not(.disabled):active:focus, .btn-black:not(:disabled):not(.disabled).active:focus
/*,.show > .btn-black.dropdown-toggle:focus*/ {box-shadow: 0 0 0 0 rgba(72, 180, 97, 0.5);}

.btn-outline-black {color: var(--way-font-color-black);border-color: var(--way-color-black);display: inline-flex;align-items: center;}}
.btn-outline-black:hover {color: #fff;background-color: var(--way-color-black-hover);border-color: var(--way-color-black-hover);}
.btn-outline-black:focus, .btn-outline-black.focus {box-shadow: 0 0 0 0 rgba(52, 58, 64, 0.5);}
.btn-outline-black.disabled, .btn-outline-black:disabled {color: var(--way-font-color-black);background-color: transparent;}
.btn-outline-black:not(:disabled):not(.disabled):active, .btn-outline-black:not(:disabled):not(.disabled).active,
.show > .btn-outline-black.dropdown-toggle {color: #fff;background-color: var(--way-color-black);border-color: var(--way-color-black);}
.btn-outline-black:not(:disabled):not(.disabled):active:focus, .btn-outline-black:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-black.dropdown-toggle:focus {box-shadow: 0 0 0 0 rgba(52, 58, 64, 0.5);}

.btn-gray {color: var(--way-font-color-black);background-color: var(--way-color-lightgray);border-color: var(--way-color-lightgray-border);box-shadow: none;}
.btn-gray:hover {color: var(--way-font-color-black);background-color: var(--way-color-lightgray-hover);border-color: var(--way-color-lightgray-border);}
.btn-gray:focus, .btn-gray.focus {color: var(--way-font-color-black);background-color: var(--way-color-lightgray-hover);border-color: var(--way-color-lightgray-border);box-shadow: 0 0 0 0 rgba(72, 180, 97, 0.5);}
.btn-gray.disabled, .btn-gray:disabled {color: var(--way-font-color-black);background-color: var(--way-color-lightgray);border-color: var(--way-color-lightgray-border);}
.btn-gray:not(:disabled):not(.disabled):active, .btn-gray:not(:disabled):not(.disabled).active
/*,.show > .btn-gray.dropdown-toggle*/ {color: var(--way-font-color-black);background-color: var(--way-color-lightgray);border-color: var(--way-color-lightgray-border);}
.btn-gray:not(:disabled):not(.disabled):active:focus, .btn-gray:not(:disabled):not(.disabled).active:focus
/*,.show > .btn-gray.dropdown-toggle:focus*/ {box-shadow: 0 0 0 0 rgba(72, 180, 97, 0.5);}

.btn-outline-gray {color: var(--way-font-color-black);border-color: var(--way-color-lightgray);display: inline-flex;align-items: center;}}
.btn-outline-gray:hover {color: var(--way-font-color-black);background-color: var(--way-color-lightgray-hover);border-color: var(--way-color-lightgray-border);}
.btn-outline-gray:focus, .btn-outline-gray.focus {box-shadow: 0 0 0 0 rgba(52, 58, 64, 0.5);}
.btn-outline-gray.disabled, .btn-outline-gray:disabled {color: var(--way-font-color-black);background-color: transparent;}
.btn-outline-gray:not(:disabled):not(.disabled):active, .btn-outline-gray:not(:disabled):not(.disabled).active,
.show > .btn-outline-gray.dropdown-toggle {color: var(--way-font-color-black);background-color: var(--way-color-lightgray);border-color: var(--way-color-lightgray);}
.btn-outline-gray:not(:disabled):not(.disabled):active:focus, .btn-outline-gray:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-gray.dropdown-toggle:focus {box-shadow: 0 0 0 0 rgba(52, 58, 64, 0.5);}

.btn-excel {color: #fff;background-color: var(--way-color-excel);border-color: var(--way-color-excel);box-shadow: none;}
.btn-excel:hover {color: #fff;background-color: var(--way-color-excel-hover);border-color: var(--way-color-excel-hover);}
.btn-excel:focus, .btn-excel.focus {color: #fff;background-color: var(--way-color-excel-hover);border-color: var(--way-color-excel-hover);box-shadow: 0 0 0 0 rgba(72, 180, 97, 0.5);}
.btn-excel.disabled, .btn-excel:disabled {color: #fff;background-color: var(--way-color-excel);border-color: var(--way-color-excel);}
.btn-excel:not(:disabled):not(.disabled):active, .btn-excel:not(:disabled):not(.disabled).active
/*,.show > .btn-excel.dropdown-toggle*/ {color: #fff;background-color: var(--way-color-excel);border-color: var(--way-color-excel);}
.btn-excel:not(:disabled):not(.disabled):active:focus, .btn-excel:not(:disabled):not(.disabled).active:focus
/*,.show > .btn-excel.dropdown-toggle:focus*/ {box-shadow: 0 0 0 0 rgba(72, 180, 97, 0.5);}

.btn-outline-excel {color: var(--way-color-excel);border-color: var(--way-color-excel);display: inline-flex;align-items: center;}}
.btn-outline-excel:hover {color: #fff;background-color: var(--way-color-excel-hover);border-color: var(--way-color-excel-hover);}
.btn-outline-excel:focus, .btn-outline-excel.focus {box-shadow: 0 0 0 0 rgba(52, 58, 64, 0.5);}
.btn-outline-excel.disabled, .btn-outline-excel:disabled {color: var(--way-color-excel);background-color: transparent;}
.btn-outline-excel:not(:disabled):not(.disabled):active, .btn-outline-excel:not(:disabled):not(.disabled).active,
.show > .btn-outline-excel.dropdown-toggle {color: #fff;background-color: var(--way-color-excel);border-color: var(--way-color-excel);}
.btn-outline-excel:not(:disabled):not(.disabled):active:focus, .btn-outline-excel:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-excel.dropdown-toggle:focus {box-shadow: 0 0 0 0 rgba(52, 58, 64, 0.5);}







.btn-outline-primary {color: var(--way-color-blue);border-color: var(--way-color-blue);display: inline-flex;}
.btn-outline-primary:hover {color: #fff;background-color: var(--way-color-blue);border-color: var(--way-color-blue);}
.btn-outline-primary:focus, .btn-outline-primary.focus {box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.5);}
.btn-outline-primary.disabled, .btn-outline-primary:disabled {color: #007bff;background-color: transparent;}
.btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active,
.show > .btn-outline-primary.dropdown-toggle {color: #fff;background-color: #007bff;border-color: #007bff;}
.btn-outline-primary:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-primary.dropdown-toggle:focus {box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.5);}

.btn-outline-secondary {color: #6c757d;border-color: #6c757d;}
.btn-outline-secondary:hover {color: #fff;background-color: #6c757d;border-color: #6c757d;}
.btn-outline-secondary:focus, .btn-outline-secondary.focus {box-shadow: 0 0 0 0 rgba(108, 117, 125, 0.5);}
.btn-outline-secondary.disabled, .btn-outline-secondary:disabled {color: #6c757d;background-color: transparent;}
.btn-outline-secondary:not(:disabled):not(.disabled):active, .btn-outline-secondary:not(:disabled):not(.disabled).active,
.show > .btn-outline-secondary.dropdown-toggle {color: #fff;background-color: #6c757d;border-color: #6c757d;}
.btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-secondary.dropdown-toggle:focus {box-shadow: 0 0 0 0 rgba(108, 117, 125, 0.5);}

.btn-outline-success {color: #28a745;border-color: #28a745;}
.btn-outline-success:hover {color: #fff;background-color: #28a745;border-color: #28a745;}
.btn-outline-success:focus, .btn-outline-success.focus {box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.5);}
.btn-outline-success.disabled, .btn-outline-success:disabled {color: #28a745;background-color: transparent;}
.btn-outline-success:not(:disabled):not(.disabled):active, .btn-outline-success:not(:disabled):not(.disabled).active,
.show > .btn-outline-success.dropdown-toggle {color: #fff;background-color: #28a745;border-color: #28a745;}
.btn-outline-success:not(:disabled):not(.disabled):active:focus, .btn-outline-success:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-success.dropdown-toggle:focus {box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.5);}

.btn-outline-info {color: #17a2b8;border-color: #17a2b8;}
.btn-outline-info:hover {color: #fff;background-color: #17a2b8;border-color: #17a2b8;}
.btn-outline-info:focus, .btn-outline-info.focus {box-shadow: 0 0 0 0 rgba(23, 162, 184, 0.5);}
.btn-outline-info.disabled, .btn-outline-info:disabled {color: #17a2b8;background-color: transparent;}
.btn-outline-info:not(:disabled):not(.disabled):active, .btn-outline-info:not(:disabled):not(.disabled).active,
.show > .btn-outline-info.dropdown-toggle {color: #fff;background-color: #17a2b8;border-color: #17a2b8;}
.btn-outline-info:not(:disabled):not(.disabled):active:focus, .btn-outline-info:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-info.dropdown-toggle:focus {box-shadow: 0 0 0 0 rgba(23, 162, 184, 0.5);}

.btn-outline-warning {color: #ffc107;border-color: #ffc107;}
.btn-outline-warning:hover {color: #1f2d3d;background-color: #ffc107;border-color: #ffc107;}
.btn-outline-warning:focus, .btn-outline-warning.focus {box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.5);}
.btn-outline-warning.disabled, .btn-outline-warning:disabled {color: #ffc107;background-color: transparent;}
.btn-outline-warning:not(:disabled):not(.disabled):active, .btn-outline-warning:not(:disabled):not(.disabled).active,
.show > .btn-outline-warning.dropdown-toggle {color: #1f2d3d;background-color: #ffc107;border-color: #ffc107;}
.btn-outline-warning:not(:disabled):not(.disabled):active:focus, .btn-outline-warning:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-warning.dropdown-toggle:focus {box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.5);}

.btn-outline-danger {color: #dc3545;border-color: #dc3545;}
.btn-outline-danger:hover {color: #fff;background-color: #dc3545;border-color: #dc3545;}
.btn-outline-danger:focus, .btn-outline-danger.focus {box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.5);}
.btn-outline-danger.disabled, .btn-outline-danger:disabled {color: #dc3545;background-color: transparent;}
.btn-outline-danger:not(:disabled):not(.disabled):active, .btn-outline-danger:not(:disabled):not(.disabled).active,
.show > .btn-outline-danger.dropdown-toggle {color: #fff;background-color: #dc3545;border-color: #dc3545;}
.btn-outline-danger:not(:disabled):not(.disabled):active:focus, .btn-outline-danger:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-danger.dropdown-toggle:focus {box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.5);}

.btn-outline-light {color: #f8f9fa;border-color: #f8f9fa;}
.btn-outline-light:hover {color: #1f2d3d;background-color: #f8f9fa;border-color: #f8f9fa;}
.btn-outline-light:focus, .btn-outline-light.focus {box-shadow: 0 0 0 0 rgba(248, 249, 250, 0.5);}
.btn-outline-light.disabled, .btn-outline-light:disabled {color: #f8f9fa;background-color: transparent;}
.btn-outline-light:not(:disabled):not(.disabled):active, .btn-outline-light:not(:disabled):not(.disabled).active,
.show > .btn-outline-light.dropdown-toggle {color: #1f2d3d;background-color: #f8f9fa;border-color: #f8f9fa;}
.btn-outline-light:not(:disabled):not(.disabled):active:focus, .btn-outline-light:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-light.dropdown-toggle:focus {box-shadow: 0 0 0 0 rgba(248, 249, 250, 0.5);}

.btn-outline-dark {color: #343a40;border-color: #343a40;}
.btn-outline-dark:hover {color: #fff;background-color: #343a40;border-color: #343a40;}
.btn-outline-dark:focus, .btn-outline-dark.focus {box-shadow: 0 0 0 0 rgba(52, 58, 64, 0.5);}
.btn-outline-dark.disabled, .btn-outline-dark:disabled {color: #343a40;background-color: transparent;}
.btn-outline-dark:not(:disabled):not(.disabled):active, .btn-outline-dark:not(:disabled):not(.disabled).active,
.show > .btn-outline-dark.dropdown-toggle {color: #fff;background-color: #343a40;border-color: #343a40;}
.btn-outline-dark:not(:disabled):not(.disabled):active:focus, .btn-outline-dark:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-dark.dropdown-toggle:focus {box-shadow: 0 0 0 0 rgba(52, 58, 64, 0.5);}

