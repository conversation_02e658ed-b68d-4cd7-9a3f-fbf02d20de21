package kr.co.wayplus.travel.service.manage;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import kr.co.wayplus.travel.mapper.manage.IslandlifeManageMapper;
import kr.co.wayplus.travel.model.BoardAttachFile;
import kr.co.wayplus.travel.model.Islandlife;
import kr.co.wayplus.travel.model.IslandlifeAttachFile;

@Service
public class IslandlifeManageService {

	@Value("${upload.file.path}")
	private String imageUploadPath;
    private final IslandlifeManageMapper mapper;
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    public IslandlifeManageService(IslandlifeManageMapper mapper) {
        this.mapper = mapper;
    }

    public int selectCountIslandlife(HashMap<String, Object> paramMap) {
    	return mapper.selectCountIslandlife(paramMap);
    }
	public ArrayList<Islandlife> selectListIslandlife(HashMap<String, Object> paramMap) {
		return mapper.selectListIslandlife(paramMap);
	}
	public Islandlife selectOneIslandlife(HashMap<String, Object> paramMap) {
		return mapper.selectOneIslandlife(paramMap);
	}

	public void insertIslandlife(Islandlife cnt) throws SQLException {
		mapper.insertIslandlife(cnt);
	}

	public void updateIslandlife(Islandlife cnt) throws SQLException {
		mapper.updateIslandlife(cnt);
	}

	public void deleteIslandlife(Islandlife cnt) throws SQLException {
		mapper.deleteIslandlife(cnt);
	}


	public ArrayList<IslandlifeAttachFile> selectListIslandlifeAttachFile(HashMap<String, Object> paramMap) {
		return mapper.selectListIslandlifeAttachFile(paramMap);
	}
	public IslandlifeAttachFile selectOneIslandlifeAttachFile(HashMap<String, Object> paramMap) {
		return mapper.selectOneIslandlifeAttachFile(paramMap);
	}
	public void insertIslandlifeAttachFile(IslandlifeAttachFile baf) throws SQLException {
		mapper.insertIslandlifeAttachFile(baf);
	}

	public void deleteIslandlifeAttachFile(IslandlifeAttachFile baf) throws SQLException {
		mapper.deleteIslandlifeAttachFile(baf);
	}
}
