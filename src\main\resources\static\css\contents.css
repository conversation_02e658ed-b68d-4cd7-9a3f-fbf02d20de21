@charset "UTF-8";
/*!
 * Wayplus Contents Page StyleSheet
 */

.fit-cover {object-fit: cover;width:100%;height:100%;}
figure.image {margin:0;}
figure.image img {max-width:100%;}
.board-title {margin:30px 0 0 0;}
.board-subtitle {margin:0;}
.board-table {margin-top:20px; width: 100%; border-collapse: collapse; }
.table-header-tr {height: 60px;padding:10px;border-bottom:1px solid #ddd;}
.table-header-tr th{padding:5px;}
.table-body-tr{height:50px;padding:10px;border-bottom: 1px solid #ddd;}
.table-body-tr td{padding:5px;}
.cell-left {text-indent: 10px;}
.cell-center {text-align: center;vertical-align: middle;}
.table-pagination {margin:50px 0;}
.tr-link:hover {cursor:pointer;background-color:#fafafa;}
.print-page {page-break-inside: initial;}


.board-content {margin-top:50px;padding-bottom:80px;color:#282828;}
.board-content-title {padding:15px 20px;height:30px;line-height:30px;font-size:20px;font-weight:600;background-color:#f8f8f8;border:1px solid transparent;}
.board-content-writer {padding:10px 20px;height:25px;line-height:25px;border:1px solid #eee;font-size:14px;}
.board-content-category {font-weight:600;color:#444444;}
.board-content-create {color:#777777;float:right;}
.board-content-text {padding:10px 20px;border:1px solid #eee;}
.board-content-button {padding:10px 20px;text-align: center;margin-top:30px;}
.button-back {padding:5px 20px;height:30px;line-height:20px;background-color:#f8f8f8;border:1px solid #eee;}
.button-back:hover {cursor: pointer;background-color:#eee;}
.print-button {background-color:#3e3a39;color:white;border: 1px solid #3e3a39;}
.print-button:hover {background-color:#4e4a49;}

.main-banner {width:100%;max-height:400px;margin-top:90px;overflow:hidden;}
.main-banner-list {width:100%;max-height:400px;list-style:none;padding:0;margin:0;}
.main-banner-item {display:none;width:100%;max-height:400px;overflow:hidden;z-index: 5;}
.main-banner-item.active {display: block;}
.main-banner-image {position:relative;min-width:100%;min-height:250px;max-height:400px;}
.main-banner-gradient {position:relative;height:0;width:100%;background: transparent linear-gradient(90deg, #5F7DFF 0%, #5F7DFFB3 10%, #5F7DFF1A 38%, #5F7DFF00 100%) 0 0 no-repeat padding-box;z-index:10;}

.main-banner-guide {list-style:none;padding:0 12%;height:36px;position:relative;z-index:100;}
.main-banner-guide-left {float:left;height:36px;width:36px;}
.main-banner-guide-right {float:right;height:36px;width:36px;}

.main-banner-page {display:flex;overflow:hidden;list-style: none;padding:0 20%;width:60%;height:45px;position:relative;top:-80px;z-index:100;}
.main-banner-page-button {line-height:45px;background: #0000004D 0 0 no-repeat padding-box;color:white;text-align:center;margin-right:1px;font-size:15px;}
.main-banner-page-button.active {background: #5F7DFF 0 0 no-repeat padding-box;}
.main-banner-page-button:last-child {margin-right:0;}
.main-banner-page-button:hover {background-color: rgba(95, 124, 255, 0.8);cursor:pointer;}

.main-banner-plaintext {position: relative; top:-280px; left:20%; color:white;z-index:15;word-break: keep-all;}
.main-banner-text {text-shadow:4px 4px 4px #353535; font-size: 52px; line-height:60px;font-family: SEBANG_Gothic_Bold;}
.main-banner-subtext {font-size: 16px;margin-top:10px;}
.main-banner-link:hover {cursor:pointer;}

.svg-guide-icon {height:35px;width:35px;opacity:0.8;}
.svg-guide-icon:hover {cursor:pointer;}


.main-notice {width:100%;background-color: #ffffff;}
.main-notice-board {width: 1200px; max-width: calc(100% - 40px); margin: 0 auto; padding:30px 20px;}
.main-notice-title {font-family:'SEBANG_Gothic_Bold';font-size:36px;color:#191919;}
.main-notice-desc {margin-top:5px;font-size:18px;color:#00000099;font-weight:500;height:25px;overflow:hidden;}
.main-notice-desc-text {float:left; }
.main-notice-more {float:right; font-size:14px;line-height:30px;color:#777;}
.main-notice-list {padding:40px 0;list-style:none;color:#00000099;margin:0;}
.main-notice-list li {display:block;height:50px;line-height:50px;border-bottom:1px solid #cbcbcb;overflow:hidden;}
.main-notice-list li:hover {color: rgba(95, 125, 255, 1);cursor: pointer;}
.main-notice-list li:first-child {border-top:1px solid #cbcbcb;}
.notice-board-title-text {float:left;}
.notice-board-date {float:right;font-size:14px;color:#00000066;}

.main-expose-list-menu {width:100%;background-color:#ffffff;}
.main-list-menu {padding:20px;width:1200px;max-width:calc(100% - 40px);margin:0 auto;height:380px;}
.main-list-menu:first-child {margin-top:30px;}
.main-list-menu:last-child {margin-bottom:30px;}
.list-menu-title {font-family: 'SEBANG_Gothic_Bold';font-size: 36px;}
.list-menu-title:hover {color: rgba(95, 125, 255, 1);cursor: pointer;}
.list-menu-title a {text-decoration:none;color:inherit;}
.list-menu-item {padding:0;margin:30px 0 0 0;list-style:none;height:280px;overflow:hidden;}
.list-menu-product {display:inline-block;max-width:380px;width:31.6%;margin-left:2%;overflow:hidden;}
.list-menu-product:first-child {margin-left:0;}
.list-menu-product-image {width:100%;height:200px;background-color:rgba(0,0,0,0.1);}
.list-menu-product-link {color:inherit; text-decoration:none;}
.list-menu-product-link:hover {color:#5B7CE8;cursor:pointer;}
.list-menu-product-title {font-size:18px;font-weight:500;margin:10px 0;height:26px;overflow: hidden;}
.list-menu-product-price {font-size:20px;font-weight:600;color: rgba(95, 125, 255, 1);margin-bottom: 5px;}
.list-menu-guide {list-style:none;padding:0;height:36px;position:relative;z-index:100;top:-200px;opacity:0.8;display:none;}
.list-menu-guide-left {float:left;height:36px;width:36px;}
.list-menu-guide-right {float:right;height:36px;width:36px;margin-right:10px;}

.main-expose-icon-menu{width:100%;background-color:#F6F6F6;}
.icon-menu-catch {padding-top:50px;width:1200px;max-width:calc(100% - 40px);margin:0 auto;}
.icon-menu-catch-title {font-family: 'SEBANG_Gothic_Bold';font-size: 36px;}
.icon-menu-catch-subtext {font-size: 18px;margin-top:5px;color:#00000099;word-break: keep-all;}
.main-icon-menu {padding:30px 20px;width:1200px;max-width:calc(100% - 40px);margin:0 auto 0;}
.icon-menu-item {padding:0;margin:0;list-style:none;}
.icon-menu-product {display:inline-block;height:420px;width:31.6%;margin-right:2%;overflow:hidden;}
.icon-menu-product:nth-child(3n) {margin-right:0;}
.icon-menu-thumbnail{width:100%;height:420px;}
.icon-menu-shadow {position:relative;width:100%;height:420px;top:-120px;background-color:rgba(0,0,0,0.3);color:white;transition: all 0.3s;}
.icon-menu-title {text-align:center;font-size:20px;font-weight:500;padding-top:30px;}
.icon-menu-more {width:115px;text-align:center;border:1px solid #fff;margin:5px auto;font-size:14px;}
.icon-menu-product:hover .icon-menu-shadow {top:-420px;background-color: rgba(95, 125, 255, 0.9);cursor:pointer;}
.icon-menu-product:hover .icon-menu-title {padding-top:160px;}

.typea-banner-area {display:flex;width:1200px;height:250px;max-width:calc(100% - 40px);overflow:hidden;margin: 0 auto;}
.typea-top-list-area, .typea-product-area {width:1200px;max-width:calc(100% - 40px);overflow:hidden;margin: 70px auto 0;}
.typea-margin-hr {margin:70px auto;width:1200px;height:0px;border-top:none;border-left:none;border-right:none;border-bottom:1px solid #ddd;}
.typea-margin-hr-30 {margin:30px auto;width:1200px;height:0px;border-top:none;border-left:none;border-right:none;border-bottom:1px solid #ddd;}
.typea-margint-hr {margin:70px auto 30px;width:1200px;height:0px;border-top:none;border-left:none;border-right:none;border-bottom:1px solid #ddd;}
.typeb-marginb-hr {margin:0px auto 70px;width:1200px;height:0px;border-top:none;border-left:none;border-right:none;border-bottom:1px solid #ddd;}
.typea-pagination {width:1200px;max-width:calc(100% - 40px);margin:70px auto;}

.typea-large-banner {width:900px;height:250px;overflow:hidden;border-radius:15px;margin-right:30px;}
.typea-large-banner.full-banner {width:1200px;margin-right:0;}
.typea-large-banner.full-banner .typea-large-banner-image {height:inherit;}
.typea-large-banner-list {list-style:none;padding:0;margin:0;width:100%;height:100%;overflow:hidden;}
.typea-large-banner-item {display:none;}
.typea-large-banner-item.active {display:block;}
.typea-large-banner-image {width:100%; height: 250px; object-fit: cover;}

.typea-small-banner {width:350px;height:250px;overflow:hidden;border-radius:15px;}
.typea-small-banner-list {list-style:none;padding:0;margin:0;width:100%;height:100%;overflow:hidden;}
.typea-small-banner-item {display:none;}
.typea-small-banner-item.active {display:block;}
.typea-small-banner-image {width:100%; height: 250px; object-fit: cover;}

.typea-indicator {width:100%;height:25px;text-align:center;line-height:25px;position:relative;top:-35px;opacity:0.8;}
.typea-indicator-button {width:14px;height:14px;background-color: #C2C2C2;border:none;border-radius:10px;margin-right:8px;}
.typea-indicator-button.active {background-color:#5F7DFF;}
.typea-indicator-button:hover {cursor:pointer;background-color:#5F7DFF;}
.typea-indicator-button:last-child {margin-right: 0;}

.typea-best-product {display: flex; width:100%;}
.title-left {width:25%;margin-right:20px;}
.typea-best-product-title {width:60%;padding:50px 20% 40px; background-color:#0000000D;}
.typea-best-product-title h1 {margin:0;font-size:36px;font-family: 'SEBANG_Gothic_Bold';}
.typea-best-product-title p {font-size:20px;color:#00000099;margin:10px 0;word-break:keep-all;}
.title-paging {justify-content:center;margin-top:50px;}
.typea-best-product-list {width: calc(75% - 20px);list-style:none;padding:0;margin:0;overflow:hidden;height:550px;}
.typea-best-product-item {width: calc(46% - 3px);overflow:hidden;display:inline-block;margin-left:4%;}
.typea-best-product-item a {text-decoration:none;color:inherit;}
.typea-best-product-item .product-item-image {height:400px;width:100%;object-fit:cover;}
.typea-best-product-item .product-item-text {height:150px;margin:0;padding:0;width:100%;}
.typea-best-product-item .product-item-text h3 {margin:10px 0;font-size:20px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
.typea-best-product-item .product-item-text p {font-size:18px;margin:0;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
.typea-best-product-item .product-item-text p.product-item-price {margin-top: 10px;font-size: 21px;font-weight: 600;color:#5F7DFF;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}

.typea-product-list {overflow: hidden;margin: 60px auto 0;list-style:none;padding:0;}
.typea-product-item {width: calc(30% - 4px);margin-right:5%;display:inline-flex;overflow:hidden;margin-bottom:70px;}
.typea-product-item a {text-decoration:none;color:inherit;}
.typea-product-list li:nth-child(3n) {margin-right:0;}
.typea-product-item-image {width:100%;height:350px;object-fit:cover;}
.typea-product-item .product-item-text {height:150px;margin:0;padding:0;width:100%;}
.typea-product-item .product-item-text h3 {margin:10px 0;font-size:20px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
.typea-product-item .product-item-text p {font-size:18px;margin:0;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
.typea-product-item .product-item-text p.product-item-price {margin-top: 10px;font-size: 21px;font-weight: 600;color:#5F7DFF;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}

.typea-product-category {}
.typea-product-category-list {list-style:none;padding:0;color:#747474;}
.typea-product-category-list a {text-decoration:none;color:inherit;}
.typea-product-category-list li {display:inline-block; border:1px solid #bbb; border-radius:20px;padding:5px 10px;width:calc(10% - 20px);min-width:100px;margin-right:15px;text-align:center;margin-bottom:10px;}
.typea-product-category-list li:last-child {margin-right:0;}
.typea-product-category-list li:hover {cursor:pointer;background-color: #5F7DFF;color:white;}
.typea-product-category-list li.active {background-color: #5F7DFF;color:white;}
.typea-product-category-list li.search {width:300px;border:none;float:right;padding:0;}
.typea-product-category-list li.search:hover {background-color:inherit;color:inherit;}
.typea-category-search {height:18px;width:calc(100% - 62px);border-radius:10px;border:1px solid #bbb;padding:8px 40px 8px 20px;}
.typea-category-search-button {float:right;position:relative;top:-33px;right:5px;height:30px;width:30px; border:none; background: rgba(255,255,255,1) url("../images/icon/search.svg") 0 50% /25px no-repeat;cursor:pointer;}
.typea-product-view-box {overflow:hidden;display:flex;}
.typea-product-view-box-left {width: 45%;margin-right:5%;}
.typea-product-view-box-right {width: 50%;}
.typea-product-view-thumbnails {list-style:none;padding:0;margin:0;overflow:hidden;}
.typea-product-view-image {height:500px; width:100%;object-fit:cover;}
.typea-product-view-gallery {width:100%;height:80px;margin-top:10px;overflow:hidden;}
.typea-product-view-gallery-list {height:80px;}
.typea-product-view-gallery-item {display:inline-block;cursor:pointer;}
.typea-product-gallery-image {height:80px;width:80px;object-fit:cover;}


.typea-product-view-title {margin:20px 0;font-size:24px;font-weight:600;color:#5f7dff;}
.typea-product-view-subtext {font-size:20px;margin-top:5px;}
.typea-product-view-info {margin-top:50px;}
.typea-product-view-info-item {padding:15px 0;line-height:25px;border-bottom:1px solid #dddddd;}
.typea-product-view-info-item .info-item-title {display:inline-block;vertical-align:top; width:100px; color:#5f7dff;font-weight:500;}
.typea-product-view-info-item .info-item-text {display:inline-block;width: calc(100% - 105px);}
.typea-product-view-info-item:last-child {border:none;}
.typea-product-view-sns {margin-top:75px;}
.sns-icon {display:inline-block;margin-right:10px;width:50px;height:50px;}
.sns-icon img {border-radius:8px;width:50px !important;height:50px !important;}
.sns-icon:hover {cursor: pointer;}
.sns-icon:last-child {margin-right: 0;}

.typea-product-detail{overflow:hidden;display:flex;}
.typea-product-detail-content {display:inline-block;width:calc(100% - 400px);margin-right:60px;}
.typea-product-detail-tab {display:flex;}
.typea-product-detail-tab .product-tab-item {width:calc(100% / 6);text-align:center;border:1px solid #eee;padding:10px 0;}
.typea-product-detail-tab .product-tab-item:hover {cursor:pointer;background-color: #5f7dff;color:white;}
.typea-product-detail-tab .product-tab-item.active {background-color: #5f7dff;color:white;-webkit-print-color-adjust:exact;}
.typea-product-detail-data {margin-bottom:60px;}
.typea-product-detail-data .product-tab-content {display:none;padding:30px 10px;word-break: keep-all;border:1px solid #f4f4f4;}


#product-detail-regulation,#product-detail-stipulation {line-height:25px;white-space: pre-wrap;}
#product-detail-regulation,#product-detail-stipulation pre{font-family:"Noto Sans KR", sans-serif;}
.product-schedule-image {width:100%;}
.product-detail-link {text-decoration:none;color:inherit;}
.product-detail-day-title {padding: 10px;border-bottom: 1px solid #BBBBBB;font-size: 23px;font-weight: 600;color: #007822;cursor: pointer;}
.product-detail-day-title i{display: inline-block;width:1.5rem;height:30px;background-repeat:no-repeat;background-position:50%; background-size: 100% 100%;vertical-align:middle;float: right;background-image: url("/images/icon/icon-schedule-close.svg");opacity: 1;}
.product-detail-day-title.acrive i{background-image: url("/images/icon/icon-schedule-open.svg");}
.product-detail-day-title-list {list-style:none;padding:0;margin:0;}
.product-detail-day-title-list li {display:inline-block;padding:5px 15px;border-radius:8px;cursor:pointer;box-shadow:2px 2px 2px 2px rgba(0, 0, 0, 0.2);color:#ccc;font-size:14px;font-weight:600;margin-right:10px;}
.product-detail-day-title-list li.active {color:#5F7DFF;}
.product-detail-day-title-list li:last-child {margin-right:0;}
.product-detail-day-content {margin-top:50px;}
.product-detail-day-list {list-style:none;padding-left:70px;margin:0;background:url("/images/icon/line.svg") -10px 0px /30px repeat-y;-webkit-print-color-adjust:exact;}
.product-detail-day-list li {margin: 20px 0;padding: 1em 0 1.5em;display: block;position: relative;counter-increment: inst;}
.product-detail-day-list li:last-child {padding-bottom:1em;}
.product-detail-day-list li::before {
    content: counter(inst); background: #5f7dff; color: #fff;
    position: absolute; left:-70px; top:18px;
    font-size: 14px; line-height:24px; font-weight:500;
    border-radius: 0 12px 12px 0;
    padding-left: 20px; height: 25px; width: 30px;
    transition: all 0.2s ease-in-out;
    z-index: 1;
    -webkit-print-color-adjust:exact;
}
.product-detail-address {background:url("/images/icon/pin.svg") 0 4px/18px no-repeat;padding-left:20px;}

.typea-product-detail-cart {display:inline-block;width:335px;vertical-align:top;}
.typea-product-detail-calendar {height:420px;}
.product-inquiry {font-size:18px;font-family: "Noto Sans KR";}
.product-inquiry .calendar-button {font-size:18px !important;font-weight:500 !important;}
.calendar-picker {position:relative;top:355px; width:calc(100% - 45px);padding:10px 20px;border:1px solid #eee;}
.calendar-clear {}
.calendar-button {width:calc(100% - 3px);padding:10px 20px;height:50px;font-size:18px;font-weight:500;}
.calendar-button:hover {cursor:pointer;}
.calendar-button.clear {background-color:#353535;color:white;border:1px solid #353535;}
.calendar-button.inquiry {background-color:#5f7dff;color:white;border:1px solid #5f7dff;}
.calendar-button.smartstore {background-color:#00c73c;color:white;border:1px solid #00c73c;}

.product-picker {width:calc(100% - 43px);padding:3px 20px;margin-top:10px;border: 1px solid #ddd;}
.product-picker-numbers {display:inline-block;width:105px;height:30px;overflow:hidden;}
.product-picker-count-input {width:30px;height:15px;border:1px solid #f2f2f2;padding:5px;text-align:center;}
.product-picker-count-input::-webkit-outer-spin-button,
.product-picker-count-input::-webkit-inner-spin-button {appearance:none;-webkit-appearance:none;-moz-appearance:none;}
.product-picker-calc {display:inline-block;height:30px;width: calc(100% - 120px);color:#e10101cc;text-align:right;line-height:27px;margin-left:5px;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;}
.product-picker-label {width:100%;white-space:nowrap;line-height:30px;text-overflow:ellipsis;overflow:hidden;margin-bottom:5px;}
.product-total-price{width:calc(100% - 45px);margin-top:10px;padding:20px;height:30px;line-height:30px;font-size:1.3em;font-weight:700;}
.product-count-control {width:25px;height:25px;border:none;background-color:transparent;font-size:18px;color:#444;position:relative;top:1px;}
.product-count-control:hover {cursor:pointer;}
.product-count-control.minus { }
.product-count-control.plus { }
.total-price-tag {float:right;color:#E10101CC;}

.calendar-cs-center {width:calc(100% - 63px);padding:20px 30px;margin:20px 0 50px;background-color:#f2f2f2;}
.calendar-cs-title {display: inline-block;font-weight:500;margin-bottom:10px;}
.calendar-cs-info {display: block;font-size:14px;color:#333333;}
.calendar-cs-info a {text-decoration:none;color:inherit;}

.estimate-privacy-agree {margin-bottom:30px;}
.estimate-content {border:1px solid #eee;padding:10px;}
.estimate-content .product-picker {width:calc(100% - 47px);padding:3px 20px;margin-top:10px;border: 1px solid #ddd;}
.privacy-agree-button {margin-top:10px;}
.estimate-dialog-input {padding:10px; margin-top:10px;font-size:14px;line-height:20px;border:1px solid #eee;}
.estimate-dialog-input.full {width:calc(100% - 27px);}
.estimate-dialog-input.half {width:calc(50% - 27px);}
.estimate-dialog-input.price {width: calc(100% - 20px);text-align: right;margin: 0;padding: 0;line-height: 30px;border:none;background:none;color:#e10101cc;}
.estimate-notice {width: calc(100% - 27px);padding:20px 10px; border:1px solid #eee;background-color:#f8f8f8;}


.typea-tour-category-box {overflow: hidden;width:1200px;max-width:calc(100% - 40px);margin: 70px auto 0;}
.typea-tour-category-list {list-style:none;padding:0;display:flex;}
.typea-category-select {width:25%;height:45px;padding:10px 20px 10px 10px;border: 1px solid #ccc; color: #707070;margin-right:2%;}
.typea-category-select > option {padding:5px;}
.typea-category-input {width:30%;height:23px; padding:10px 40px 10px 10px;border: 1px solid #ccc; color: #707070;}
.typea-input-overlay-button {float:right;position:relative;right:40px;height:45px;width:40px;padding:0;border:none;background: url('/images/icon/search.svg') 5px 50%/25px no-repeat}
.typea-input-overlay-button:hover {cursor:pointer;}
.typea-input-search-button {width:25%;height:45px;padding:10px;border: 1px solid #5f7dff;background-color:#5f7dff;color:white;}
.typea-input-search-button:hover {cursor:pointer;}
.typea-input-search-button.small-rent {display: none;}

.typea-category-input-rent {display:flex;margin-right: 10px;}
.typea-category-input-day {width:50%;height:23px; padding:10px;border: 1px solid #ccc; color: #707070;margin-right:1%;}
.typea-category-select-time {width:50%; height:45px;padding:10px 20px 10px 10px;border: 1px solid #ccc; color: #707070;margin-right:2%;}
.typea-category-select-time > option {line-height:150%;}



.typea-list-total-count {}
.typea-tourspot-area {width:1200px;max-width:calc(100% - 40px);margin:0 auto;}
.typea-item-count {color:#5f7dff; margin-left:5px;}
.typea-tourspot-list {overflow: hidden;margin: 60px auto 0;list-style:none;padding:0;}
.typea-tourspot-list-item {width: calc(24% - 1px);margin-right:1%;display:inline-flex;overflow:hidden;margin-bottom:70px;}
.typea-tourspot-list li:nth-child(4n) {margin-right:0;}
.typea-tourspot-item-image {object-fit:cover;width:100%;height:284px;}
.typea-tourspot-item-title {margin-top:10px;font-size:20px;font-weight:500;}
.typea-tourspot-item-title:hover {cursor:pointer;color: #5f7dff;}
.typea-tourspot-item-address {font-size:14px;margin-top:10px;}

.typea-tourspot-title {font-size:24px;font-weight:600;margin:50px 0 20px;}
.typea-tourspot-address {font-size:18px;font-weight:500;}
.typea-tourspot-address a {text-decoration:none;color:inherit;}
.typea-tourspot-description {font-size:18px;line-height:27px;text-indent:5px;color:#646464;word-break:keep-all;}
.typea-tourspot-gallery {list-style:none;padding:0;margin-top:50px;display:flex;}
.typea-tourspot-gallery-list {list-style:none;padding:0;min-width:50%;margin:0 2% 0 0;overflow:hidden;}
.typea-tourspot-gallery-list li {width:100%;}

.typea-tourspot-map {height:500px;width:100%;margin-bottom:50px;}
.typea-tourspot-map-info {padding:10px 20px 10px 10px;font-size:14px;}
.typea-tourspot-map-tooltip {margin-bottom:10px;}
.typea-tourspot-map-info .phone {color:#288756;}
.typea-tourspot-map-info .address {text-overflow: ellipsis;white-space: nowrap;}

.typea-rentacar-area {width:1200px;max-width:calc(100% - 40px);margin:0 auto;min-height:200px;}
.typea-rentacar-list {overflow: hidden;margin: 60px auto 0;list-style: none;padding:0;}
.typea-rentacar-item {width: calc(33% - 6px);margin-right:1%;display:inline-flex;overflow:hidden;margin-bottom:70px;border:1px solid #eee;}
.typea-rentacar-list li:nth-child(3n) {margin-right:0;}
.vehicle-image {height:250px;border-bottom:1px solid #eee;padding:20px 10px;}
.vehicle-image img {height:100%;width:100%;object-fit:contain;}
.vehicle-name {padding:10px 5%;font-size:21px;text-align:center;font-weight:600;}
.vehicle-option {padding:0 5%;font-size:13px;color:#646464; overflow:hidden;text-overflow:ellipsis;white-space: nowrap;}
.vehicle-type-detail {padding:5px 5%;display: flex;justify-content: space-around;margin-top:10px;background-color: #eee;}
.vehicle-reserve-status {padding:5px 5%;color:white;height:30px;line-height:30px;text-align: center;}
.vehicle-reserve-status.available {background-color: #5f7dff;cursor:pointer;}
.vehicle-reserve-status.unavailable {background-color: #444444;}

.print-product-area-selector {display:flex;line-height:50px;border-top:1px solid #ddd;border-bottom:1px solid #ddd;text-align:center;overflow:hidden;}
.print-product-area-name {background-color: #0000001A;width:100px;vertical-align: middle;}
.print-product-area-text {width:calc(100% - 100px);text-align:left;}
.print-product-area-check {display:inline-block;margin-left: 25px;}
.typea-print-product-box {display:block;margin:50px 0;}
.typea-print-product-title {margin:50px auto;font-size:24px;font-weight:600;color:#5f7dff;}
.typea-print-product-info {display:flex;}
.typea-print-product-info .typea-product-detail-calendar {height:445px;}
.typea-print-product-info .calendar-picker {top:385px;}
.typea-print-product-detail {}
.typea-print-product-detail .typea-product-detail-content {width:100%;display:block;}
.typea-print-product-detail .product-tab-content {display:block;}
.typea-print-product-info .product-picker:first-child{margin-top:0;}
.typea-print-product-itinerary {margin-bottom:10px;}

.typeb-product-category {color:#888888;font-size:1em !important;}
.typeb-product-item-image {width:100%;height:250px;object-fit:cover;}

.typeb-product-view-box {overflow:hidden;}
.typeb-product-view-title {margin:20px 0;font-size:30px;font-weight:600;color:#5f7dff;word-break: keep-all;}
.typeb-product-view-subtitle {margin:20px 0 10px;font-size:20px;word-break: keep-all;}
.typeb-product-view-info {display:flex;justify-content:space-between;width:100%;line-height:40px;padding:10px 0;}
.typeb-product-info-text {position:relative;top:4px; display:inline-block; font-family: "Noto Sans KR"; }
.typeb-stay-product-info-list {margin:0;padding:0;list-style:none;}
.typeb-stay-product-info-list li {display:inline-block;}
.typeb-stay-product-label {padding:5px 15px;text-align:center;border-radius:20px;color:#666666E6;border:1px solid #666666E6;margin-right:10px;}
.stay-product-label-value {margin-right:30px;}
.product-label-marker {position:relative;top:3px;}
.typeb-product-view-sns {}
.typeb-product-view-sns .sns-icon {display:inline-block;margin-right:10px;width:40px;height:40px;}
.typeb-product-view-sns .sns-icon img {border-radius:8px;width:40px !important;height:40px !important;}
.sns-icon:hover {cursor: pointer;}
.sns-icon:last-child {margin-right: 0;}

.typeb-product-view-img {height:42.8vw;max-height:514px; width:100%;object-fit:cover;}
.typeb-product-view-gallery {padding:20px 80px;}
.typeb-product-view-gallery-item {height:85px;width:150px;overflow:hidden;cursor:pointer;}
.typeb-product-view-gallery-item img {height:85px;width:150px;object-fit:cover;}
.swiper-arrow {position:relative;top:-110px;height:85px;width:80px;cursor:pointer;}
.swiper-arrow img {height:85px;width:80px;}
.swiper-arrow-left {float:left;}
.swiper-arrow-right {float:right;}

.typeb-print-product-detail-calendar {}
.typeb-print-product-view-image {}
.typeb-print-product-view-image .swiper-arrow {position:relative;top:-90px;height:65px;width:60px;cursor:pointer;}
.typeb-print-product-view-image .swiper-arrow img {width:60px;height:65px;}
.typeb-print-product-view-img {height:42.8vw;max-height:257px; width:100%;object-fit:cover;}
.typeb-print-product-view-gallery {padding:20px 60px;}
.typeb-print-product-view-gallery-item {height:68px;width:120px;overflow:hidden;cursor:pointer;}
.typeb-print-product-view-gallery-item img {height:68px;width:120px;object-fit:cover;}


.typeb-tourspot-map {height:500px;width:100%;max-width:800px;margin-bottom:50px;}
.typeb-tourspot-map-info {padding:10px 20px 10px 10px;font-size:14px;background-color: #fff;}
.typeb-tourspot-map-tooltip {margin-bottom:10px;}
.typeb-tourspot-map-info .phone {color:#288756;}
.typeb-tourspot-map-info .address {text-overflow: ellipsis;white-space: nowrap;}

.typeb-product-option {display:block;width:100%;margin-bottom:50px;}
.typeb-product-option-title {margin:10px 0;font-size:21px;line-height:21px;font-weight:600;}
.typeb-product-option-info {display:flex;}
.typeb-product-option-image {width:360px;height:200px;overflow:hidden;}
.typeb-product-option-images {display:inline-block;width:360px;height:200px;overflow:hidden;}
.typeb-product-option-images img {width:360px;height:200px;object-fit: cover;}
.typeb-product-option-image-guide {width:360px;height:40px;position:relative;top:-120px;}
.typeb-product-option-desc {display:inline-block;width:calc(100% - 380px);min-height:200px;margin-left:15px;}
.product-option-occupancy {color:#999;margin-bottom:20px;}
.product-option-include {margin-bottom:20px;}
.product-option-notice pre {padding:0;background:none;border:none;line-height:1.2;}
.product-option-include .option-item {display:inline-block;padding:5px 10px;border:1px solid #ddd;}
.product-detail-option {padding:10px;border:1px solid #eee;margin-top:15px;}
.product-detail-search {width:20px;height:20px;position:relative;top:3px;}
.option-item-info {min-height:30px;}
.option-item-sub {font-size: 14px;color:#777;}
.option-item-title {display: inline-block;font-weight:600;font-size:18px;width:80px;vertical-align: top;}
.option-item-text {display:inline-block;width:225px;}
.option-item-select {height:26px;border:1px solid #ddd;border-radius:5px;padding:4px 10px;min-width:70px;max-width:230px}
.option-price-input {border:none;text-align:right;color:#E40303;font-size:18px;width:calc(100% - 30px);padding:5px 15px;}

.info-greeting {width:100%;object-fit: scale-down;}
.info-certification {border: 1px solid #eee;width:392px;}
#find-us {padding-top:90px;}
.info-location-title {height:50px;background-color: #5F7DFF;color: white;text-align: center;line-height: 50px;font-size: 20px;}
.kakao-map {height:500px;margin:50px auto 100px;width:1000px;max-width:calc(100% - 240px);padding:0;}
.kakao-map-info {padding:10px 20px 10px 10px;font-size:15px;}
.kakao-map-info .head_tooltip {margin-bottom:10px;}
.kakao-map-info a {text-decoration:none;color:#000;}
.kakao-map-info p {margin:0;font-size:13px;}
.kakao-map-info .addInfoAddr {color:#919191;font-size:12px;}
.kakao-map-info .zipcode {margin-right:10px;}
.kakao-map-info .phone {color:#288756;}

.is-invalid {border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}
.calendar-picker.is-invalid {width:calc(100% - 65px);padding-right:40px;}
#estimate-user-name.is-invalid {width:calc(50% - 49px);}
#estimate-user-tel.is-invalid {width:calc(50% - 49px);}

.login-form-div {width:360px; margin:0 auto;}
.login-input {width:320px;height:23px;display:block;margin:10px auto; padding:10px 20px;border: 1px solid #ccc;border-radius:5px;color: #707070;}
.login-button {width:360px;height:45px;display:block;margin:20px auto; padding:10px 20px;border: 1px solid #ccc;border-radius:5px;color: white;background-color:#5f7dff;font-size:18px;}
.login-button:hover {cursor: pointer; background-color: #5B7CE8;}

@media screen and (max-width: 960px) {
    .main-banner {margin-top:140px;}
    .main-banner-page {top:-55px;height:30px;}
    .main-banner-page-button {line-height:30px;font-size:12px;}
    .list-menu-title {font-size:32px;}
    .icon-menu-catch-title {font-size:32px;}
    .main-notice-title {font-size: 32px;}
    .kakao-map {max-width:calc(100% - 120px);}

    .typea-banner-area {height:210px;}
    .typea-large-banner {height:210px;}
    .typea-large-banner-image {height: 210px;}
    .typea-small-banner {height:210px;}
    .typea-small-banner-image {height: 210px;}


    .typea-best-product-list {height:480px}
    .typea-best-product-item .product-item-image {height:330px;}
    .typea-product-item-image {height:300px;}
    .typeb-product-item-image {height:200px;}

    .typea-tourspot-list-item {width: 32%;}
    .typea-tourspot-list li:nth-child(4n) {margin-right:1%;}
    .typea-tourspot-list li:nth-child(3n) {margin-right:0;}

    .typea-product-view-image {height:450px;width:100%;object-fit:cover;}
    .typea-product-view-info {margin-top:40px;}
    .typea-product-view-info-item {padding:10px 0;}
    .typea-product-view-sns {margin-top:65px;}

    .typea-product-detail {display:flex;flex-direction:column-reverse;}
    .typea-product-detail-content {display: block;width: 100%;}
    .typea-product-detail-cart {width:100%;display:flex;}
    .typea-product-detail-calendar {width:330px;position:relative;top:3px;}

    .typea-product-detail-option {width:calc(100% - 350px);margin-left:20px;}
    .product-picker:first-child {margin-top:0;}
    .product-picker-label {font-size:15px;}

    .typea-print-product-info {display:block;}
    .typea-print-product-info .typea-product-view-box-left{width:100%;margin:0;}
    .typea-print-product-info .typea-product-view-box-right{width:100%;}

    .typeb-product-view-title {font-size:28px;}
    .typeb-product-view-subtitle {margin:20px 0 10px;font-size:19px;}
    .typeb-stay-product-label {font-size:15px;margin-right:10px;}
    .stay-product-label-value {margin-right:25px;font-size:15px;}
}

@media screen and (max-width: 720px) {
    .wrap {min-height:calc(100vh - 120px); margin-top:120px;}
    .main-banner {margin-top:75px;}
    .main-banner-guide {display: none;}
    .main-banner-subtext {display:none;}
    .main-banner-page {top:-45px;height:20px;}
    .main-banner-page-button {line-height:20px;font-size:11px;}
    .main-banner-plaintext {left:15%;width:70%;}
    .list-menu-item {height:275px;}
    .list-menu-title {font-size:30px;}
    .list-menu-product-title {font-size:16px;}
    .list-menu-product-price {font-size:18px;}
    .icon-menu-catch-title {font-size:30px;}
    .icon-menu-catch-subtext {font-size:16px;}

    .main-notice-title {font-size: 30px;}
    .main-notice-desc-text{font-size:16px;line-height:28px;}
    .main-notice-list {padding:30px 0;}
    .main-notice-list li {height:40px;line-height:40px;}
    .notice-board-title-text {font-size: 14px;}
    .notice-board-date {font-size:12px;}

    .list-menu-product {width:48.5%;margin-left:2%;}
    .list-menu-guide {display:block;}
    .icon-menu-product {width:48.5%;margin-right:2%;}
    .icon-menu-product:nth-child(3n) {margin-right:2%;}
    .icon-menu-product:nth-child(2n) {margin-right:0;}
    .list-menu-guide-right {margin-right:0;}
    .kakao-map {max-width:calc(100% - 80px);}

    .typea-tour-category-list {display:block;}
    .typea-category-select {width:48%;margin-right:2%;}
    .typea-category-select:nth-child(2n) {margin-right:0;}
    .typea-category-input {width:calc(98% - 49px);margin-top:10px;}
    .typea-input-overlay-button {right:10px;top:-45px;}
    .typea-tourspot-list-item {width: 49%;}
    .typea-tourspot-list li:nth-child(3n) {margin-right:1%;}
    .typea-tourspot-list li:nth-child(2n) {margin-right:0;}
    .typea-tourspot-gallery {display: block;}
    .typea-tourspot-description {margin-top:10px;}

    .typea-rentacar-item {width: calc(49% - 2px);margin-right:2%;}
    .typea-rentacar-list li:nth-child(3n) {margin-right:2%;}
    .typea-rentacar-list li:nth-child(2n) {margin-right:0;}
    .typea-input-search-button.small-rent {display: inline-block; width:48%;position:relative;top:2px;}
    .typea-input-search-button.large-rent {display: none;}
    .typea-product-category-list li.search {width:100%;}
    .typea-category-input-rent {display:inline-block;width:48%;margin:10px 2% 0 0;}
    .typea-category-input-rent:nth-child(2n) {margin-right:0;}

    .typea-category-input-day {width:calc(50% - 13px);margin-right:0;}
    .typea-category-select-time {width:calc(50% - 13px);margin-right:0;}
    .typeb-product-view-info {display: block;}
    .typeb-product-view-sns {margin-top:10px;text-align:right;}

    .typea-large-banner {margin-right:0;height: auto;}
    .typea-small-banner {display: none;}

    .typea-best-product {display:block;}
    .title-left {height:auto;width:100%;margin-bottom:20px;}
    .typea-best-product-title {padding: 30px 20px 20px 20px;width: calc(100% - 40px);}
    .typea-best-product-title h1 {font-size:30px;}
    .typea-best-product-title p {font-size:18px;}
    .typea-best-product-list {width:100%;}
    .typea-best-product-item {width:calc(48% - 2px)}
    .typea-best-product-item:first-child {margin-left: 0;}

    .typea-product-list {overflow: hidden;margin: 60px auto 0;list-style:none;padding:0;}
    .typea-product-item {width: calc(48% - 3px);margin-right:4%;display:inline-flex;overflow:hidden;margin-bottom:70px;}
    .typea-product-list li:nth-child(3n) {margin-right:4%;}
    .typea-product-list li:nth-child(2n) {margin-right:0;}
    .typea-product-item .product-item-text h3 {font-size:18px;}
    .typea-product-item .product-item-text p {font-size:16px;}

    .typea-product-view-box {overflow:hidden;display:block;}
    .typea-product-view-box-left {width: 100%;margin-right:0%;}
    .typea-product-view-box-right {width: 100%;}

    .typea-product-detail-cart {display: block;}
    .typea-product-detail-calendar {display:block;width:100%;justify-content:center;}
    .typea-product-detail-option {display:block;width:100%;margin:20px 0;}

    .typeb-product-option-info {display:block;}
    .typeb-product-option-image {display:block;width:100%;}
    .typeb-product-option-images {display:block;width:100%;height:200px;overflow:hidden;}
    .typeb-product-option-images img {width:360px;height:200px;object-fit: cover;}
    .typeb-product-option-image-guide {width:360px;height:40px;position:relative;top:-120px;}

    .typeb-product-option-desc {display:block;width:100%;margin-left:0;}
    .stay-product-label-value {margin-right:20px;}

    .typeb-product-view-gallery {padding:20px 50px;}
    .typeb-product-view-gallery-item {height:70px;width:125px;overflow:hidden;cursor:pointer;}
    .typeb-product-view-gallery-item img {height:70px;width:125px;object-fit:cover;}
    .swiper-arrow {position:relative;top:-90px;height:55px;width:50px;cursor:pointer;}
    .swiper-arrow img {height:55px;width:50px;}

    .typeb-print-product-view-gallery {padding:20px 50px;}
    .typeb-print-product-view-image .swiper-arrow {position:relative;top:-90px;height:55px;width:50px;cursor:pointer;}
    .typeb-print-product-view-image .swiper-arrow img {width:50px;height:55px;}

    .typeb-product-view-title {font-size:25px;}
    .typeb-product-view-subtitle {margin:15px 0 10px;font-size:18px;}
    .typeb-stay-product-label {font-size:14px;}
    .stay-product-label-value {margin-right:20px;font-size:14px;}
}

@media screen and (max-width: 480px) {
    .wrap {min-height:calc(100vh - 100px); margin-top:100px;}
    .contents {max-width:calc(100% - 20px);}
    .main-banner {margin-top:65px;}
    .main-banner-plaintext {left:10%;width:80%;}
    .main-banner-page {height:10px;width:20%;padding:0 40%;}
    .main-banner-page-button {border-radius:20px;color:transparent;}
    .list-menu-title {font-size:28px;}
    .icon-menu-catch-title {font-size:28px;}
    .main-notice-title {font-size: 28px;}
    .notice-board-date {display: none;}
    .list-menu-product {width:100%;margin-left:2%;max-width:100%;}
    .icon-menu-product {width:100%;margin-right:0;}
    .kakao-map {max-width:calc(100% - 20px);}
    .typea-tourspot-list-item {width: 100%;margin:0;}

    .typea-rentacar-item {width: calc(100% - 2px);margin-right:0;}
    .typea-rentacar-list li:nth-child(3n) {margin-right:0;}
    .typea-rentacar-list li:nth-child(2n) {margin-right:0;}
    .typea-product-category-list li {margin-right:0;}
    .typea-category-input-rent {width:100%;margin-right:0;}

    .typea-banner-area {height:180px;}
    .typea-large-banner {height:180px;}
    .typea-large-banner-image {height: 180px;}

    .typea-best-product {display:block;}
    .title-left {height:auto;width:100%;margin-bottom:20px;}
    .typea-best-product-title {padding: 30px 20px 20px 20px;width: calc(100% - 40px);}
    .typea-best-product-title h1 {font-size:25px;}
    .typea-best-product-title p {font-size:16px;}
    .typea-best-product-list {width:100%;}
    .typea-best-product-item {width:100%}
    .typea-best-product-item {margin: 0;}
    .typea-best-product-item .product-item-text h3 {font-size:18px;}
    .typea-best-product-item .product-item-text p {font-size:16px;}

    .typea-product-list {overflow: hidden;margin: 40px auto 0;list-style:none;padding:0;}
    .typea-product-item {width:100%; margin-right:0;display:block;overflow:hidden;margin-bottom:40px;}
    .typea-product-list li:nth-child(3n) {margin-right:0;}


    .typea-product-view-title {font-size:21px;}
    .typea-product-view-subtext {font-size:18px;}
    .typea-product-view-image {height:400px;width:100%;object-fit:cover;}
    .typea-product-view-info {margin-top:50px;}
    .typea-product-view-sns {margin-top:50px;}

    .product-picker-label {font-size:14px;}
    .estimate-dialog-input.half {width:calc(100% - 27px);}
    .estimate-privacy-agree, .estimate-content {font-size:15px;}

    .typeb-product-option-images img {width:100%;height:200px;object-fit: cover;}
    .typeb-product-option-image-guide {width:100%;height:40px;position:relative;top:-120px;}

    .typeb-product-view-gallery {padding:20px 40px;}
    .typeb-product-view-gallery-item {height:60px;width:105px;}
    .typeb-product-view-gallery-item img {height:60px;width:105px;}
    .swiper-arrow {top:-80px;height:45px;width:40px;}
    .swiper-arrow img {height:45px;width:40px;}

    .typeb-print-product-view-gallery {padding:20px 40px;}
    .typeb-print-product-view-gallery-item {height:60px;width:105px;overflow:hidden;cursor:pointer;}
    .typeb-print-product-view-gallery-item img {height:60px;width:105px;object-fit:cover;}

    .typeb-print-product-view-gallery {padding:20px 40px;}
    .typeb-print-product-view-image .swiper-arrow {position:relative;top:-80px;height:45px;width:40px;cursor:pointer;}
    .typeb-print-product-view-image .swiper-arrow img {width:40px;height:45px;}

    .typeb-product-view-title {font-size:24px;}
    .typeb-product-view-subtitle {margin:10px 0;font-size:16px;}
    .typeb-stay-product-label {border:none;padding:5px;font-weight:bold;color: #5F7DFF;}
}

@media print {
    .typea-print-product-info .typea-product-detail-calendar {height:auto;}
    .typea-print-product-info .calendar-picker {top:0;}
    .typea-print-product-info .typea-product-view-image {height:250px;}

}