package kr.co.wayplus.travel.model;

import lombok.Data;
import lombok.ToString;

// 중기육상예보
@ToString
@Data
public class WeatherShtWithMidFcstVO {
	private	String fcstDate;
	private	String skyam;
	private	String ptyam;
	private	String wfAm;

	private	String skypm;
	private	String ptypm;
	private	String wfPm;

	private	String tmpmin;
	private	String tmpmax;

	private	String weatherTypeAm;
	private	String weatherImageAm;
	private	String weatherNormalImageAm;

	private	String weatherTypePm;
	private	String weatherImagePm;
	private	String weatherNormalImagePm;

	private	String dayOfWeek;	// 관측시간
	private	String datef1;	// 관측시간
	
	private double visitRatingNormal; // 방문객예측레이팅
	private double visitRatingStudent; // 방문객예측레이팅

	public void setAmWeatherImg(String[] data) {
		this.weatherTypeAm = data[0];
		this.weatherImageAm = data[1];
		this.weatherNormalImageAm = data[2];
	}
	public void setPmWeatherImg(String[] data) {
		this.weatherTypePm = data[0];
		this.weatherImagePm = data[1];
		this.weatherNormalImagePm = data[2];
	}


}
