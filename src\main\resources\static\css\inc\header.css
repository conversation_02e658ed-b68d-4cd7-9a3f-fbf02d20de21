
/* 개편 후 */


.header{
    height:93px;
    border-bottom:1px solid #000;
}
.header .header-inner{
    width:calc(100% - 200px);
    margin:0 auto;
    display:flex;
    align-items: center;
    justify-content: space-between;
    height:inherit;
}
.header .header-inner .gnb-area{
    margin-left:5%;
}

.gnb{
    display:flex;
    align-items: center;
}
.gnb > li{
    margin-right:40px;
    font-size:20px;
    font-weight:600;
    color:#000;
    text-align: center;
    height:93px;
    display:flex;
    align-items: center;
}

.gnb > li:last-child{
    margin-right:0;
}
.gnb li .lnb {
    display:none;
    align-items: center;
    justify-content: center;
    position:absolute;
    top:93px;
    left:0;
    width:100%;
    padding:15px 0;
    border-bottom:1px solid #000;
    z-index: 99;
    background-color:#fff;
}
.gnb .lnb li{
    padding:11px 30px;
    color:#444;
    font-size:16px;
    border:1px solid #ccc;
    border-radius: 40px;
    margin-right:15px;
    cursor: pointer;
    font-weight:400;
}
.gnb .lnb li:last-child{
    margin-right:0;
}

.header .header-inner .header-link-area{
    display:flex;
    align-items: center;
}
.header .header-inner .header-link-area > div{
    margin-right:15px;
    cursor: pointer;
    text-align: center;
}

.header .header-inner .ham-btn{
    margin-left:15px;
    cursor: pointer;
}
@media (hover : hover) {
    .gnb > li:hover{
        color:#056FB8;

    }
    .gnb > li:hover .lnb{
        display:block;
        display:flex;
    }
    .gnb li .lnb li:hover {
        background-color:#056FB8;
        font-weight:700;
        color:#fff;
        border:1px solid #000;
    }
    .header .header-inner .header-link-area > div:hover{
        color:#056FB8;
    }
}




/* 햄버거 on */
.navigation-area{
    position: fixed;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100vh;
    z-index: 9999999;
    transition: all 0.3s;
    overflow-y: scroll;
    background: #000;
    color: #fff;
    padding-top:80px;
    display:none;
}
.navigation-area.on{
    display:block;
    right: 0;
}
.nav-title{
    display:flex;
    margin-bottom:60px;
    position:relative;
}
.nav-title h5{
    word-break:keep-all ;
    width:800px;
    font-family: 'Tenada';
    font-size: 42px;
    text-align: center;
    margin: 0 auto;
    line-height: 60px;
}
.nav-title h5 br{
    display:none;
}
.ham-closed-btn{
    position:absolute;
    top:-20px;
    right:0;
    cursor: pointer;
}
.pc_nav_con{
    display:flex;
    width:100%;
    justify-content: space-between;
}
.pc_nav_con .pc_nav_con_item{
    width:20%;
    text-align: center;
    padding:20px 30px;
    box-sizing: border-box;
}
.pc_nav_con_item .head-link{
    font-size:20px;
    font-weight:600;
    line-height:24px;
    word-break: keep-all;
    display:block;
    margin-bottom:40px;
}
.pc_nav_con_item{
    border-right:1px solid #4A4A4A;
}
.pc_nav_con_item:first-child{
    border-left:1px solid #4A4A4A;
}
.pc_nav_lnb li{
    margin-bottom:15px;

}
.pc_nav_lnb li:last-child{
    margin-bottom:0;
}
.pc_nav_lnb li a{
    color:#B1B1B1;
    font-size:18px;
    line-height:21px;
    word-break: keep-all;
}
.pc_nav_lnb li a:hover{
    color:#05A54B;
}
@media (hover : hover) {
    .pc_nav_con_item .head-link:hover{
        color:#05A54B;
    }
}
.pc_nav_button_area{
    display: flex;
    justify-content: center;
    position:absolute;
    bottom:100px;
    left:50%;
    transform: translateX(-50%);
    width:100%;
}
.pc_nav_button_area button{
    padding:10px 28px;
    border:none;
    color:#222;
    background-color:#fff;
    border-radius: 50px;
    font-size:15px;
    font-weight:600;
    cursor: pointer;
    margin-right:10px;
}
.pc_nav_button_area button:hover{
    background-color:#EDEDED;
    text-decoration: underline;
}
.pc_nav_button_area .language_btn_area button:hover{
    text-decoration: underline;
}
.pc_nav_button_area .language_btn_area{
    margin:0 20px;
    display:flex;
    flex-direction: column;
    background-color:#fff;
    border-radius: 20px;
}
.pc_nav_button_area .guide_btn{
    background-color:#05A54B;
    border:none;
    border-radius: 50px;
    padding:10px 28px;
    cursor: pointer;
    font-size:15px;
    font-weight:600;
}
.pc_nav_button_area .guide_btn:hover{
    background-color:#fff;
    color:#05A54B;
}


@media screen and (max-width:1500px) {
    .header .header-inner .gnb-area{
        margin-left:3%;
    }
    .gnb > li{
        margin-right:30px;
    }
}


@media screen and (max-width:1400px) {
    .header .header-inner{
        width:calc(100% - 20px);
        margin:0 auto;
    }
    .header .header-inner .gnb-area{
        margin-left:2%;
    }
    .gnb > li{
        margin-right:25px;
        font-size:18px;
    }
}
@media screen and (max-width:1280px) {
    .header .header-inner .gnb-area{
        margin-left:2%;
    }
    .gnb > li{
        margin-right:20px;
        font-size:16px;
    }
    .header .header-inner .header-link-area > div{
        margin-right:10px;
        font-size:15px;
    }
}
@media screen and (max-width:1024px) {
    .header .header-inner .gnb-area{
        display:none;
    }
    .header .header-inner .header-link-area > div{
        display:none;
    }
    .header .header-inner .header-link-area > div.ham-btn{
        display:block;
    }
    .pc_nav_con .pc_nav_con_item{
        padding:20px 15px;
    }
    .nav-title h5{
        font-size:36px;
        width:550px;
    }		
    .pc_nav_button_area button{
        padding:10px 15px;
    }
}


@media screen and (max-width:768px) {
    .header{
        height:68px;
    }

    .navigation-area{
        position:fixed;
        height:100%;
        padding-bottom:40px;
    }
    .navigation-area .contetns{
        overflow-y: scroll;
    }
    .nav-title h5{
        font-size:28px;
        word-break: keep-all;
        line-height:1.4;
    }
    .nav-title h5 br{
        display:block;
    }
    .ham-closed-btn{
        top:-60px;
        width:40px;
        height:40px;
    }
    .ham-closed-btn img{
        width:100%;
    }

    .nav-title{
        margin-bottom:20px;
    }
    .pc_nav_con{
        flex-direction: column;
    }
    .pc_nav_con .pc_nav_con_item{
        width:100%;
        border-right:none;
    }
    .pc_nav_con_item:first-child{
        border-left:none;
    }
    .pc_nav_con_item .head-link{
        margin-bottom:10px;
    }

    .pc_nav_button_area{
        flex-direction: column;
        position:unset;
        transform: unset;
        margin-top:50px;
    }


    .pc_nav_button_area > button{
        width:100%;
        margin:0 0;
        margin-bottom:10px;
    }
    .pc_nav_button_area .nav-login-buttons{
        display:flex;
        justify-content: space-between;
        margin-bottom:10px;
    }
    .pc_nav_button_area .nav-login-buttons button{
        width:calc(50% - 5px);
        margin:0;
    }
    .pc_nav_button_area .nav-lang-buttons {
        display:flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom:10px;
    }
    .pc_nav_button_area .nav-lang-buttons button{
        width: calc(50% - 5px);
        margin: 0;
    }


    .pc_nav_button_area .guide_btn{
        width:100%;

    }
}