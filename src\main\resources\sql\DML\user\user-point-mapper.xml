<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.user.UserPointMapper">

    <select id="selectUserPointSettingListCount" parameterType="HashMap" resultType="Integer">
        SELECT count(ups.id) FROM user_point_set ups LEFT JOIN code_item ci ON ups.accrued_code = ci.code and ci.upper_code ='pointType'
        WHERE ups.delete_yn = 'N'
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)">
            AND use_yn = #{useYn}
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(accruedCode)">
            AND accrued_code = #{accruedCode}
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
            AND expire_date &gt; #{startDate}
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
            AND expire_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
        </if>
    </select>

    <select id="selectUserPointSettingList" parameterType="HashMap" resultType="UserPointSet">
        SELECT ups.id, accrued_code, ci.name,
               accrued_reason, accrued_type, accrued_point,
               duplicate_yn, duplicate_period, duplicate_count, expired_day,
               start_date, expire_date,
               ups.use_yn, ups.use_type, ups.create_id, ups.create_date,
               ups.last_update_id, ups.last_update_date
          FROM user_point_set ups
          LEFT JOIN code_item ci ON ups.accrued_code = ci.code and ci.upper_code ='pointType'
         WHERE ups.delete_yn = 'N'
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)">
            AND use_yn = #{useYn}
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(accruedCode)">
            AND accrued_code = #{accruedCode}
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
            AND start_date &gt; #{startDate}
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
            AND expire_date &lt; #{endDate}
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useType)">
            AND use_type = #{useType}
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperCode)">
            AND ci.upper_code = #{upperCode}
        </if>
        ORDER BY id DESC
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
            LIMIT #{itemStartPosition}, #{pagePerSize}
        </if>
    </select>

    <select id="selectUserPointSettingById" parameterType="String" resultType="UserPointSet">
        SELECT ups.id, ups.accrued_code,
               ups.accrued_reason, ups.accrued_type, ups.accrued_point,
               ups.duplicate_yn, ups.duplicate_period, ups.duplicate_count, ups.expired_day,
               ups.start_date, ups.expire_date,
               ups.use_yn, ups.use_type, ups.create_id, ups.create_date,
               ups.last_update_id, ups.last_update_date, ups.delete_yn,
               ci.name
          FROM user_point_set ups
          LEFT JOIN code_item ci ON ups.accrued_code = ci.code and ci.upper_code ='pointType'
         WHERE ups.id = #{value} AND ups.delete_yn = 'N'
    </select>

    <insert id="insertUserPointSetting" parameterType="UserPointSet">
        INSERT INTO user_point_set
           SET accrued_code = #{accruedCode},
               accrued_reason = #{accruedReason},
               accrued_type = #{accruedType},
               accrued_point = #{accruedPoint},
               duplicate_yn = #{duplicateYn},
               duplicate_period = #{duplicatePeriod},
               duplicate_count = #{duplicateCount},
               expired_day = #{expiredDay},
               start_date = #{startDate},
               expire_date = #{expireDate},
               use_yn = #{useYn},
               use_type = #{useType},
               create_id = #{createId},
               create_date = now()
    </insert>

    <update id="updateUserPointSettingDeleteYn" parameterType="UserPointSet">
        UPDATE user_point_set
           SET delete_yn = 'Y', delete_id = #{deleteId}, delete_date = now()
         WHERE id = #{id}
    </update>

    <update id="updateUserPointSetting" parameterType="UserPointSet">
        UPDATE user_point_set
           SET accrued_code = #{accruedCode},
               accrued_reason = #{accruedReason},
               accrued_point = #{accruedPoint},
               duplicate_yn = #{duplicateYn},
               duplicate_period = #{duplicatePeriod},
               duplicate_count = #{duplicateCount},
               expired_day = #{expiredDay},
               start_date = #{startDate},
               expire_date = #{expireDate},
               use_yn = #{useYn},
               use_type = #{useType},
               last_update_id = #{lastUpdateId},
               last_update_date = now()
         WHERE id = #{id} AND delete_yn = 'N'
    </update>

    <select id="selectUserPointRecordListCount" parameterType="HashMap" resultType="Integer">
        SELECT count(*) FROM user_point_record
         WHERE delete_yn = 'N'
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(accruedType)">
                <if test="accruedType != 'ALL'">
                   AND accrued_type = #{accruedType}
                </if>
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">
                   AND user_email = #{userEmail}
               </if>
               <choose>
                    <when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(accruedType)">
                    AND accrued_type = #{accruedType}
                    </when>
                    <otherwise> AND accrued_type != 'giftExchange'</otherwise>
               </choose>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType)">
                   <choose>
                       <when test="searchType == 'accrued'">
                           <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                                AND accrued_date &gt; #{startDate}
                           </if>
                           <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                               AND accrued_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                           </if>
                       </when>
                       <when test="searchType == 'expire'">
                           <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                               AND expire_date &gt; #{startDate}
                           </if>
                           <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                               AND expire_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                           </if>
                       </when>
                   </choose>
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
                   AND (
                        user_email LIKE CONCAT('%', #{searchKey}, '%')
                        OR user_email IN (SELECT user_email FROM user WHERE user.user_name LIKE CONCAT('%', #{searchKey}, '%'))
                       )
               </if>
    </select>

    <select id="selectUserPointRecordList" parameterType="HashMap" resultType="UserPointRecord">
        SELECT id, upr.user_email, user.user_name,
               accrued_id, accrued_type, accrued_reason,
               accrued_date, ifnull(expire_date,'') expire_date,
               point_remain, point_accrued, point_used, point_expired,
               create_id, create_date, last_update_id, last_update_date
               , DATE_FORMAT(accrued_date, '%Y-%m-%d') createDateFormat
          FROM user_point_record upr LEFT JOIN user ON upr.user_email = user.user_email
         WHERE delete_yn = 'N'
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">
            AND upr.user_email = #{userEmail}
        </if>
        <choose>
            <when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(accruedType) and accruedType == 'notGiftExchange'">
              AND accrued_type != 'giftExchange'
            </when>
             <when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(accruedType) and accruedType != 'ALL'">
              AND accrued_type = #{accruedType}
            </when>
             <when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(accruedType)">
              AND accrued_type = #{accruedType}
            </when>
        </choose>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(used) and used == 'true'">
           AND upr.point_remain > 0 AND (upr.expire_date IS NULL OR upr.expire_date > now())
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType)">
            <choose>
                <when test="searchType == 'accrued'">
                    <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                        AND accrued_date &gt; #{startDate}
                    </if>
                    <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                        AND accrued_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                    </if>
                </when>
                <when test="searchType == 'expire'">
                    <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                        AND expire_date &gt; #{startDate}
                    </if>
                    <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                        AND expire_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                    </if>
                </when>
            </choose>
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
            AND (
                    upr.user_email LIKE CONCAT('%', #{searchKey}, '%')
                    OR user.user_name LIKE CONCAT('%', #{searchKey}, '%')
                )
        </if>
        <choose>
            <when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(used) and used == 'true'">
                ORDER BY CASE WHEN expire_date IS NULL THEN STR_TO_DATE('9999-12-31', '%Y-%m-%d') ELSE expire_date END
            </when>
            <when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDateDesc) and createDateDesc == 'true'">
                ORDER BY create_date DESC
            </when>
            <otherwise>
                ORDER BY id DESC
            </otherwise>
        </choose>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
            LIMIT #{itemStartPosition}, #{pagePerSize}
        </if>
    </select>

    <select id="selectUserPointAccruedListCount" parameterType="HashMap" resultType="Integer">
        SELECT count(*) FROM user_point_accrued_log upal
        LEFT JOIN user ON upal.user_email = user.user_email
         <where>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType)">
                <choose>
                    <when test="searchType == 'accrued'">
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                            AND accrued_date &gt;= #{startDate}
                        </if>
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                            AND accrued_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                        </if>
                    </when>
                    <when test="searchType == 'expire'">
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                            AND expire_date &gt;= #{startDate}
                        </if>
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                            AND expire_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                        </if>
                    </when>
                </choose>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
                    AND (
                            user.user_email LIKE CONCAT('%', #{searchKey}, '%')
                            OR user.user_name LIKE CONCAT('%', #{searchKey}, '%')
                        )
                </if>
            </if>
         </where>
    </select>

    <select id="selectUserPointAccruedList" parameterType="HashMap" resultType="UserPointAccrued">
        SELECT id, upal.user_email, user.user_name,
               accrued_date, accrued_code, accrued_type, accrued_reason,
               expire_date, point_accrued,
               create_id, create_date, cancel_code, cancel_id, cancel_date
          FROM user_point_accrued_log upal LEFT JOIN user ON upal.user_email = user.user_email
         <where>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType)">
                <choose>
                    <when test="searchType == 'accrued'">
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                            AND accrued_date &gt;= #{startDate}
                        </if>
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                            AND accrued_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                        </if>
                    </when>
                    <when test="searchType == 'expire'">
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                            AND expire_date &gt;= #{startDate}
                        </if>
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                            AND expire_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                        </if>
                    </when>
                </choose>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
                    AND (
                            user.user_email LIKE CONCAT('%', #{searchKey}, '%')
                            OR user.user_name LIKE CONCAT('%', #{searchKey}, '%')
                        )
                </if>
            </if>
         </where>
        ORDER BY id DESC
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
            LIMIT #{itemStartPosition}, #{pagePerSize}
        </if>
    </select>

    <select id="selectUserPointAccruedById" parameterType="HashMap" resultType="UserPointAccrued">
        SELECT * FROM user_point_accrued_log
        <where>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)"> AND id = #{id} </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardContentsId)"> AND board_contents_id = #{boardContentsId} </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardCategoryId)"> AND board_category_id = #{boardCategoryId} </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardCommentId)"> AND board_comment_id = #{boardCommentId} </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userFavoriteId)"> AND user_favorite_id = #{userFavoriteId} </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)"> AND product_serial = #{productSerial} </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCommentId)"> AND product_comment_id = #{productCommentId} </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(paymentMoid)"> AND payment_moid = #{paymentMoid} </if>
        </where>
    </select>

    <select id="selectUserPointUsedListCount" parameterType="HashMap" resultType="Integer">
        SELECT count(*) FROM user_point_used_log
         <where>
           <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType)">
               <choose>
                   <when test="searchType == 'used'">
                       <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                           AND used_date &gt; #{startDate}
                       </if>
                       <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                           AND used_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                       </if>
                   </when>
                   <when test="searchType == 'cancel'">
                       <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                           AND cancel_date &gt; #{startDate}
                       </if>
                       <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                           AND cancel_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                       </if>
                   </when>
               </choose>
           </if>
           <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
               AND (
                    user_email LIKE CONCAT('%', #{searchKey}, '%')
                    OR user_email IN (SELECT user_email FROM user WHERE user.user_name LIKE CONCAT('%', #{searchKey}, '%'))
                   )
           </if>
         </where>
    </select>

    <select id="selectUserPointUsedList" parameterType="HashMap" resultType="UserPointUsed">
        SELECT id, upul.user_email, user.user_name,
               used_date, used_type,
               point_used, detail_record,
               create_id, create_date, cancel_code, cancel_id, cancel_date
          FROM user_point_used_log upul LEFT JOIN user ON upul.user_email = user.user_email
         <where>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType)">
                <choose>
                    <when test="searchType == 'used'">
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                            AND used_date &gt; #{startDate}
                        </if>
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                            AND used_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                        </if>
                    </when>
                    <when test="searchType == 'cancel'">
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                            AND cancel_date &gt; #{startDate}
                        </if>
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                            AND cancel_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                        </if>
                    </when>
                </choose>
            </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
                AND (
                        upul.user_email LIKE CONCAT('%', #{searchKey}, '%')
                        OR user.user_name LIKE CONCAT('%', #{searchKey}, '%')
                    )
            </if>
         </where>
         <choose>
            <when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDateDesc) and createDateDesc == 'true'">
                    ORDER BY create_date DESC
            </when>
            <otherwise>
                    ORDER BY id DESC
            </otherwise>
        </choose>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
            LIMIT #{itemStartPosition}, #{pagePerSize}
        </if>
    </select>

    <select id="selectOneUserPointUsedLog" parameterType="HashMap" resultType="UserPointUsed">
        SELECT id, upul.user_email, user.user_name,
               used_date, used_type,
               point_used, detail_record,
               create_id, create_date, cancel_code, cancel_id, cancel_date
          FROM user_point_used_log upul LEFT JOIN user ON upul.user_email = user.user_email
         <where>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPointUsedLogId)">
                AND id = #{userPointUsedLogId}
            </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType)">
                <choose>
                    <when test="searchType == 'used'">
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                            AND used_date &gt; #{startDate}
                        </if>
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                            AND used_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                        </if>
                    </when>
                    <when test="searchType == 'cancel'">
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                            AND cancel_date &gt; #{startDate}
                        </if>
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                            AND cancel_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                        </if>
                    </when>
                </choose>
            </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
                AND (
                        upul.user_email LIKE CONCAT('%', #{searchKey}, '%')
                        OR user.user_name LIKE CONCAT('%', #{searchKey}, '%')
                    )
            </if>
         </where>
    </select>

    <select id="selectUserPointSetByTypeCode" parameterType="String" resultType="UserPointSet">
        SELECT id, accrued_code,
               accrued_reason, accrued_type, accrued_point,
               duplicate_yn, duplicate_period, duplicate_count, expired_day,
               start_date, expire_date, use_yn
          FROM user_point_set
         WHERE delete_yn = 'N' AND use_yn = 'Y' AND accrued_code = #{value}
           AND (start_date IS NULL OR start_date &lt; now())
           AND (expire_date IS NULL OR expire_date &gt; now())
    </select>

    <select id="selectUserPointCreateLogCount" parameterType="UserPointAccrued" resultType="Integer">
        SELECT count(*) FROM user_point_accrued_log
         WHERE user_email = #{userEmail}
           <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(accruedCode)">
           AND accrued_code = #{accruedCode}
           </if>
           <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(accruedReason)">
           AND accrued_reason = #{accruedReason}
           </if>
           <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(duplicatePeriod)">
           AND DATE_FORMAT(accrued_date, '%Y-%m-%d') &gt; #{duplicatePeriod}
           </if>
    </select>

    <insert id="insertPointAccruedLog" parameterType="UserPointAccrued" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_point_accrued_log
           SET user_email = #{userEmail},
               accrued_date = now(),
               accrued_type = #{accruedType},
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(accruedCode)">
               accrued_code = #{accruedCode},
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(accruedReason)">
               accrued_reason = #{accruedReason},
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)">
               expire_date = #{expireDate},
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardContentsId)">
               board_contents_id = #{boardContentsId},
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardCategoryId)">
               board_category_id = #{boardCategoryId},
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardCommentId)">
               board_comment_id = #{boardCommentId},
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userFavoriteId)">
               user_favorite_id = #{userFavoriteId},
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(paymentMoid)">
               payment_moid = #{paymentMoid},
               </if>
               point_accrued = #{pointAccrued},
               create_id = #{createId},
               create_date = now()
    </insert>

    <insert id="insertPointRecord" parameterType="UserPointRecord">
        INSERT INTO user_point_record
           SET user_email = #{userEmail},
               accrued_id = #{accruedId},
               accrued_type = #{accruedType},
               accrued_reason= #{accruedReason},
               accrued_date = now(),
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)">
                   expire_date = #{expireDate},
               </if>
               point_remain = #{pointRemain},
               point_accrued = #{pointAccrued},
               create_id = #{createId},
               create_date = now()
    </insert>

    <update id="updateUserPointAccruedCancel" parameterType="UserPointAccrued">
        UPDATE user_point_accrued_log
           SET cancel_code = #{cancelCode}, cancel_id = #{cancelId}, cancel_date = now()
         WHERE id = #{id}
    </update>

    <update id="updateUserPointRecordDelete" parameterType="UserPointRecord">
        UPDATE user_point_record
           SET delete_yn = #{deleteYn}, delete_code = #{deleteCode},
               delete_id = #{deleteId}, delete_date = now()
         WHERE id = #{id}
            <if test="accruedId &gt; 0">
                AND accrued_id = #{accruedId}
            </if>
    </update>

    <select id="selectCodeItemList" parameterType="String" resultType="CodeItem">
        SELECT code, upper_code, name, code, upper_code, name, code_desc, code_depth, code_acronym, code_sort, use_yn
          FROM code_item
         WHERE upper_code = #{value} AND use_yn = 'Y'
         ORDER BY code_sort
    </select>

    <select id="selectUserPointRecord" parameterType="HashMap" resultType="UserPointRecord">
        SELECT user_email, point_accrued, point_used, accrued_date, point_expired,
        DATE_FORMAT(accrued_date, '%Y-%m-%d') as calc_accrued_date
        FROM user_point_record
        WHERE user_email = #{userEmail} AND delete_yn = 'N'
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(accruedType)">
            AND accrued_type = #{accruedType}
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(currentMonth)">
            AND MONTH(accrued_date) = #{currentMonth}
        </if>
    </select>

    <select id="selectOneUserPointRecord" parameterType="HashMap" resultType="UserPointRecord">
        SELECT id, user_email, point_remain, point_accrued, point_used, accrued_date, point_expired,
        DATE_FORMAT(accrued_date, '%Y-%m-%d') as calc_accrued_date, CASE WHEN expire_date &lt;= NOW() THEN 'Y' ELSE 'N' END as is_expired
        FROM user_point_record
        WHERE user_email = #{userEmail} AND delete_yn = 'N'
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(accruedId)">
            AND accrued_id = #{accruedId}
        </if>
    </select>

    <select id="selectUserPointRecordSummary" parameterType="HashMap" resultType="UserPointRecord">
        SELECT user_email,
               SUM(point_remain) point_remain,
               SUM(point_accrued) point_accrued,
               SUM(point_used) point_used,
               SUM(point_expired) point_expired
          FROM user_point_record
         WHERE user_email = #{userEmail} AND delete_yn = 'N'
         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(excludeExpired) and excludeExpired == 'true'">
            AND (expire_date IS NULL OR expire_date >= NOW())
         </if>
         GROUP BY user_email
    </select>

    <select id="selectUserExpiringPointRecordSummary" parameterType="HashMap" resultType="UserPointRecord">
        <![CDATA[
        SELECT user_email, expire_date, SUM(point_accrued) point_expired
        FROM user_point_record
        WHERE user_email = #{userEmail} AND expire_date >= CURDATE() AND expire_date <= DATE_ADD(CURDATE(), INTERVAL 1 MONTH) AND delete_yn = 'N'
        ]]>
    </select>

    <update id="updateUserPointRecordUsed" parameterType="UserPointRecord">
        UPDATE user_point_record
           SET point_remain = #{pointRemain}, point_used = #{pointUsed},
               last_update_id = #{lastUpdateId}, last_update_date = now()
         WHERE user_email = #{userEmail} AND id = #{id} AND accrued_id = #{accruedId}
    </update>

    <insert id="insertPointUsedLog" parameterType="UserPointUsed" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_point_used_log
           SET user_email = #{userEmail},
               used_type = #{usedType},
               used_date = now(),
               point_used = #{pointUsed},
               detail_record = #{detailRecord},
               create_id = #{createId},
               create_date = now()
    </insert>

    <select id="selectUserPointIdByBoardId" parameterType="HashMap" resultType="UserPointRecord">
        SELECT *
        FROM user_point_record
        WHERE board_id = #{boardId} AND delete_yn = 'N'
    </select>

    <select id="selectUserPointExchangeList" parameterType="HashMap" resultType="UserPointExchange">
        SELECT *, row_number() over (order by create_date asc) as rownum
        FROM user_point_exchange_log as upel
        LEFT JOIN user as user ON upel.user_email = user.user_email
        WHERE delete_yn = 'N'
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType)">
            <choose>
                <when test="searchType == 'exchangeDate'">
                    <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                        AND exchange_date &gt;= #{startDate}
                    </if>
                    <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                        AND exchange_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                    </if>
                </when>
            </choose>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
                AND (
                        upel.user_email LIKE CONCAT('%', #{searchKey}, '%')
                        OR user.user_name LIKE CONCAT('%', #{searchKey}, '%')
                    )
            </if>
        </if>

        ORDER BY rownum DESC
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
            LIMIT #{itemStartPosition}, #{pagePerSize}
        </if>
    </select>

     <select id="selectCountUserPointExchangeList" parameterType="HashMap" resultType="Integer">
        SELECT COUNT(*)
        FROM user_point_exchange_log as upel
        LEFT JOIN user as user ON upel.user_email = user.user_email
        WHERE delete_yn = 'N'
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
            AND (
                    upel.user_email LIKE CONCAT('%', #{searchKey}, '%')
                    OR user.user_name LIKE CONCAT('%', #{searchKey}, '%')
                )
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
            LIMIT #{itemStartPosition}, #{pagePerSize}
        </if>
    </select>

    <insert id="insertUserPointExchange" parameterType="UserPointExchange" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_point_exchange_log
           SET user_email = #{createId},
               user_point_used_log_id = #{userPointUsedLogId},
               exchange_date = now(),
               exchange_type = #{exchangeType},
               exchange_point = #{exchangePoint},
               exchange_code = #{exchangeCode},
               point_exchange_reason = #{pointExchangeReason},
               point_exchange_count = #{pointExchangeCount},
               point_exchange_confirm_yn = #{pointExchangeConfirmYn},
               create_id = #{createId},
               create_date = now()
    </insert>

    <update id="updateUserPointExchange" parameterType="UserPointExchange">
        UPDATE user_point_exchange_log
           SET last_update_date = now()
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pointExchangeConfirmYn)">
               , point_exchange_confirm_yn = #{pointExchangeConfirmYn}
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)">
               , last_update_id = #{lastUpdateId}
               </if>
         WHERE id = #{id}
    </update>

    <select id="selectOneUserPointAccruedLog" parameterType="hashMap" resultType="UserPointAccrued">
        SELECT *
        FROM user_point_accrued_log
    </select>

    <!-- 청풍용 -->
    <select id="selectUserPointRecordUsedListCount" parameterType="HashMap" resultType="UserPointRecord">
        COUNT(*)
        FROM (
            SELECT id, upr.user_email, user.user_name,
                accrued_id, accrued_type, accrued_reason,
                accrued_date, expire_date,
                point_remain, point_accrued, point_used, point_expired,
                create_id, create_date, last_update_id, last_update_date
                , DATE_FORMAT(accrued_date, '%Y-%m-%d') createDateFormat
            FROM user_point_record upr LEFT JOIN user ON upr.user_email = user.user_email
            WHERE delete_yn = 'N'
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(accruedType)">
                <if test="accruedType != 'ALL'">
                    AND accrued_type = #{accruedType}
                </if>
            </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">
                AND upr.user_email = #{userEmail}
            </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(used) and used == 'true'">
            AND upr.point_remain > 0 AND (upr.expire_date IS NULL OR upr.expire_date > now())
            </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType)">
                <choose>
                    <when test="searchType == 'accrued'">
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                            AND accrued_date &gt; #{startDate}
                        </if>
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                            AND accrued_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                        </if>
                    </when>
                    <when test="searchType == 'expire'">
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                            AND expire_date &gt; #{startDate}
                        </if>
                        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                            AND expire_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                        </if>
                    </when>
                </choose>
            </if>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
                AND (
                        upr.user_email LIKE CONCAT('%', #{searchKey}, '%')
                        OR user.user_name LIKE CONCAT('%', #{searchKey}, '%')
                    )
            </if>
            UNION
            SELECT upul.id, upul.user_email, null, null, upul.used_type, upul.used_type,
                null, null, null, null, point_used, null, create_id, create_date, null, null,
                DATE_FORMAT(create_date , '%Y-%m-%d') createDateFormat
            FROM user_point_used_log upul
            <where>
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">
                upul.user_email = #{userEmail}
            </if>
            </where>
            <choose>
                <when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(used) and used == 'true'">
                    ORDER BY CASE WHEN expire_date IS NULL THEN STR_TO_DATE('9999-12-31', '%Y-%m-%d') ELSE expire_date END
                </when>
                <when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDateDesc) and createDateDesc == 'true'">
                    ORDER BY create_date DESC
                </when>
                <otherwise>
                    ORDER BY id DESC
                </otherwise>
            </choose>
        )
    </select>
     <!-- 청풍용 -->
    <select id="selectUserPointRecordUsedCancelList" parameterType="HashMap" resultType="UserPointRecord">
        SELECT id, upr.user_email, user.user_name,
               accrued_id, accrued_type, accrued_reason,
               accrued_date, expire_date,
               point_remain, point_accrued, point_used, point_expired,
               create_id, create_date, last_update_id, last_update_date
               , DATE_FORMAT(accrued_date, '%Y-%m-%d') createDateFormat
               , null as cancel_date
        FROM user_point_record upr LEFT JOIN user ON upr.user_email = user.user_email
        WHERE delete_yn = 'N'
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(accruedType)">
            <if test="accruedType != 'ALL'">
                AND accrued_type = #{accruedType}
            </if>
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">
            AND upr.user_email = #{userEmail}
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(used) and used == 'true'">
           AND upr.point_remain > 0 AND (upr.expire_date IS NULL OR upr.expire_date > now())
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType)">
            <choose>
                <when test="searchType == 'accrued'">
                    <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                        AND accrued_date &gt; #{startDate}
                    </if>
                    <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                        AND accrued_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                    </if>
                </when>
                <when test="searchType == 'expire'">
                    <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">
                        AND expire_date &gt; #{startDate}
                    </if>
                    <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
                        AND expire_date &lt; DATE_ADD(STR_TO_DATE(#{endDate}, '%Y-%m-%d'), INTERVAL 1 DAY)
                    </if>
                </when>
            </choose>
        </if>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
            AND (
                    upr.user_email LIKE CONCAT('%', #{searchKey}, '%')
                    OR user.user_name LIKE CONCAT('%', #{searchKey}, '%')
                )
        </if>
        UNION
        SELECT upul.id, upul.user_email, null, null, upul.used_type, upul.used_type,
               null, null, null, null, point_used, null, create_id, create_date, null, null,
               DATE_FORMAT(create_date , '%Y-%m-%d') createDateFormat, cancel_date as cancel_date
        FROM user_point_used_log upul
        <where>
         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">
            upul.user_email = #{userEmail}
         </if>
        </where>
        UNION
        SELECT upcl.id, upcl.user_email, null,null, upcl.cancel_code, upcl.cancel_code,
            null, null, point_cancel as point_remain, null, null,  null,
            create_id, create_date, null, null, DATE_FORMAT(create_date , '%Y-%m-%d') createDateFormat,
            cancel_date as cancel_date
        FROM
            user_point_cancel_log upcl
        <where>
         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">
            upcl.user_email = #{userEmail}
         </if>
        </where>
        <choose>
            <when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(used) and used == 'true'">
                ORDER BY CASE WHEN expire_date IS NULL THEN STR_TO_DATE('9999-12-31', '%Y-%m-%d') ELSE expire_date END, id DESC
            </when>
            <when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDateDesc) and createDateDesc == 'true'">
                ORDER BY create_date DESC, id DESC
            </when>
            <otherwise>
                ORDER BY id DESC
            </otherwise>
        </choose>
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
            LIMIT #{itemStartPosition}, #{pagePerSize}
        </if>
    </select>

    <!-- 청풍용 -->
    <update id="updateUserPointUsedLog" parameterType="UserPointUsed">
        UPDATE user_point_used_log
           SET cancel_date = now()
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelId)">
               , cancel_id = #{cancelId}
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelCode)">
               , cancel_code = #{cancelCode}
               </if>
         WHERE id = #{id}
    </update>

    <select id="selectUserPointRecordGroupListCount" parameterType="HashMap" resultType="Integer">
    SELECT COUNT(*) FROM (
        SELECT user_email
          FROM user_point_record
         WHERE delete_yn = 'N'
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
                AND (
                user_email LIKE CONCAT('%', #{searchKey}, '%')
                OR user_email IN (SELECT user_email FROM user WHERE user.user_name LIKE CONCAT('%', #{searchKey}, '%'))
                )
            </if>
         GROUP BY user_email
    ) a
    </select>
    <select id="selectUserPointRecordGroupList" parameterType="HashMap" resultType="UserPointRecord">
        SELECT user_email,
               (SELECT user_name FROM user us WHERE us.user_email = upr.user_email) userName,
               SUM(point_remain) point_remain,
               SUM(point_accrued) point_accrued,
               SUM(point_used) point_used,
               SUM(point_expired) point_expired
          FROM user_point_record upr
         WHERE delete_yn = 'N'
            <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
                AND (
                user_email LIKE CONCAT('%', #{searchKey}, '%')
                OR user_email IN (SELECT user_email FROM user WHERE user.user_name LIKE CONCAT('%', #{searchKey}, '%'))
                )
            </if>
         GROUP BY user_email
         ORDER BY point_remain DESC
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
            LIMIT #{itemStartPosition}, #{pagePerSize}
        </if>
    </select>

    <select id="selectUserPointExchangeCodeByUseId" parameterType="Integer" resultType="String">
        SELECT exchange_code
          FROM user_point_exchange_log
         WHERE user_point_used_log_id = #{value}
    </select>

    <insert id="insertUserPointCancelLog" parameterType="UserPointCancel">
        INSERT INTO user_point_cancel_log
           SET user_point_used_log_id = #{userPointUsedLogId},
               user_email = #{userEmail},
               point_cancel = #{pointCancel},
               detail_refund_record = #{detailRefundRecord},
               cancel_id = #{cancelId},
               cancel_code = #{cancelCode},
               cancel_date = now(),
               create_id = #{createId},
               create_date = now()
    </insert>

    <insert id="insertUserPointExpiredLog" parameterType="UserPointExpired" useGeneratedKeys="true" keyProperty="userPointExpiredId">
        INSERT INTO user_point_expired_log
        SET user_point_recode_id = #{userPointRecodeId},
            user_email = #{userEmail},
            point_expired = #{pointExpired},
            expired_date = #{expiredDate},
            create_id = #{createId},    
            create_date = NOW()
    </insert>

    <select id="selectExpiredPointRecords" resultType="UserPointRecord">
        SELECT id, user_email, accrued_id, accrued_type, accrued_reason,
               accrued_date, expire_date, point_remain, point_accrued, point_used, point_expired
          FROM user_point_record
         WHERE delete_yn = 'N'
           AND expire_date IS NOT NULL
           AND expire_date &lt;= NOW()
         ORDER BY expire_date ASC
    </select>

    <update id="updatePointRecordExpired" parameterType="UserPointRecord">
        UPDATE user_point_record
           SET point_remain = #{pointRemain},
               point_expired = #{pointExpired},
               last_update_id = #{lastUpdateId},
               last_update_date = NOW()
         WHERE id = #{id}
           AND delete_yn = 'N'
    </update>

    <select id="isPointRecordAlreadyExpired" parameterType="int" resultType="Boolean">
        SELECT EXISTS (
            SELECT 1 
            FROM user_point_expired_log 
            WHERE user_point_recode_id = #{value}
        ) as is_expired
    </select>

</mapper>