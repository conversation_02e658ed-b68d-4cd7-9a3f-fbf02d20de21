<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.front.CodeMapper">
<!--
	 * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
-->
	<!--################################### CodeItem ###################################-->
	<select id="selectOneCodeItem" parameterType="HashMap" resultType="CodeItem">
		SELECT id, code, upper_code, name, code_acronym
		 FROM code_item a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(code)" >	and code=#{code}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperCode)" >	and upper_code=#{upperCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(name)" >	and name=#{name}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
		</where>
	</select>
</mapper>