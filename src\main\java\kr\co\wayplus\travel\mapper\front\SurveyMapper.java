package kr.co.wayplus.travel.mapper.front;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import kr.co.wayplus.travel.model.CoachInfo;
import kr.co.wayplus.travel.model.CodeItem;
import kr.co.wayplus.travel.model.InquiryCategory;
import kr.co.wayplus.travel.model.InquiryContent;
import kr.co.wayplus.travel.model.MainBannerImage;
import kr.co.wayplus.travel.model.MainNoticePopup;
import kr.co.wayplus.travel.model.MenuUser;
import kr.co.wayplus.travel.model.PlaceSpot;
import kr.co.wayplus.travel.model.Policy;
import kr.co.wayplus.travel.model.Reservation;
import kr.co.wayplus.travel.model.SettingAllianceContents;
import kr.co.wayplus.travel.model.SettingAwardsContents;
import kr.co.wayplus.travel.model.SettingCompanyInfo;
import kr.co.wayplus.travel.model.SettingNavbar;
import kr.co.wayplus.travel.model.Survey;
import kr.co.wayplus.travel.model.SurveyImage;
import kr.co.wayplus.travel.model.SurveyQuestion;
import kr.co.wayplus.travel.model.SurveyQuestionAnswer;
import kr.co.wayplus.travel.model.SurveyRecommand;
import kr.co.wayplus.travel.model.SurveyResult;
import kr.co.wayplus.travel.model.UserCustomerCart;
import kr.co.wayplus.travel.model.UserCustomerOrder;
import kr.co.wayplus.travel.model.UserCustomerOrderHistory;
import kr.co.wayplus.travel.model.UserCustomerOrderList;
import kr.co.wayplus.travel.model.UserCustomerPayment;

@Mapper
@Repository
public interface SurveyMapper {
//	<!--################################### Survey ###################################-->
	Survey selectOneLastSurvey(HashMap<String, Object> paramMap);
//	<!--################################### SurveyQuestion###################################-->
	int selectCountLastSurveyQuestion(HashMap<String, Object> paramMap);
	ArrayList<SurveyQuestion> selectListLastSurveyQuestion(HashMap<String, Object> paramMap);
//	<!--################################### SurveyQuestionAnswer ###################################-->
	int selectCountSurveyQuestionAnswer(HashMap<String, Object> paramMap);
	ArrayList<SurveyQuestionAnswer> selectListSurveyQuestionAnswer(HashMap<String, Object> paramMap);
	SurveyQuestionAnswer selectOneSurveyQuestionAnswer(HashMap<String, Object> paramMap);
//	<!--################################### SurveyResult###################################-->
	int selectCountSurveyResult(HashMap<String, Object> paramMap);
	ArrayList<SurveyResult> selectListSurveyResult(HashMap<String, Object> paramMap);
	SurveyResult selectOneSurveyResult(HashMap<String, Object> paramMap);
	void insertSurveyResult(SurveyResult param) throws SQLException;
//	<!--################################### SurveyResult###################################-->
	int selectCountSurveyRecommand(HashMap<String, Object> paramMap);
	ArrayList<SurveyRecommand> selectListSurveyRecommand(HashMap<String, Object> paramMap);
	SurveyRecommand selectOneSurveyRecommand(HashMap<String, Object> paramMap);
//	<!--################################### SurveyImage ###################################-->
	int selectCountSurveyImage(HashMap<String, Object> paramMap);
	ArrayList<PlaceSpot> selectListSurveyImage(HashMap<String, Object> paramMap);
	SurveyImage selectOneSurveyImage(HashMap<String, Object> paramMap);
}
