<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.manage.ReservationManageMapper">

<!--################################### Reservation ###################################-->
	<select id="selectListCountReservationContentByCalendar" parameterType="HashMap" resultType="HashMap">
		with list as (
			select *
			  from(
				select a.id,
					   a.user_email,
					   IFNULL(a.user_name, u.user_name) user_name,
					   IFNULL(IFNULL(a.user_mobile, u.user_mobile), '~연락처없음~') user_mobile,
					   a.product_serial,
					   pt.product_title,
					   pt.menu_sub_type,
					   travel_schedule_json,
				       @travel := json_value(travel_schedule_json,'$.data.travelSchedule') travel_dt,
				       @travelF := DATE_FORMAT(substring_index(@travel, ' ~ ',1), '%Y-%m-%d') as travel_from_dt,
					   @travelT := DATE_FORMAT(substring_index(@travel, ' ~ ',-1),'%Y-%m-%d') as travel_to_dt,
				       a.cancel_yn,
				       a.apply_code, b.name apply_code_name,
				       a.reservation_code, c.name reservation_code_name,
				       a.cancel_code, d.name cancel_code_name,
				       a.meeting_time,a.meeting_place,a.guide_name,a.guide_tel,
				       a.flag_departure_yn,
				       a.flag_price_yn,a.flag_hotel_yn,a.flag_airline_yn,
				       a.flag_schedule,a.flag_leader_yn,a.flag_guide_yn,
				       a.flag_close_yn
				  from reservation a
				  left join `user` u on a.user_email = u.user_email
				  left join (select pt.product_serial, pt.product_title, mu.menu_type, mu.menu_sub_type from product_tour pt join menu_user mu on pt.product_menu_id = mu.menu_id where regacy_yn = 'N') pt on a.product_serial = pt.product_serial
				  left join code_item b on a.apply_code = b.code and b.upper_code = 'statusType'
				  left join code_item c on a.reservation_code = c.code and c.upper_code = 'reservationType'
				  left join code_item d on a.cancel_code = d.code and d.upper_code = 'cancelType'
				 where a.delete_yn = 'N') a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fromDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(toDate)" >
				and travel_from_dt between DATE_FORMAT(#{fromDate},'%Y-%m-%d') and DATE_FORMAT(#{toDate},'%Y-%m-%d')
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(targetDate)" >
				and travel_from_dt = #{date}
			</if>
		</where>)
		select *
		  from (
		<!--
		    select travel_from_dt,travel_to_dt,menu_sub_type, 'apply' code,       '접수' name, sum(case when cancel_yn = 'N' and reservation_code = '0' then 1 else 0 end) cnt
		      from list group by travel_from_dt,menu_sub_type
			 union all
		 -->
			select travel_from_dt,travel_to_dt,menu_sub_type,  menu_sub_type code, '예약' name, sum(case when cancel_yn = 'N' and reservation_code = '2' then 1 else 0 end) cnt
			  from list group by travel_from_dt,menu_sub_type
			 union all
			select travel_from_dt,travel_to_dt,menu_sub_type,  menu_sub_type code,     '이용' name, sum(case when cancel_yn = 'N' and reservation_code = '3' then 1 else 0 end) cnt
			  from list group by travel_from_dt,menu_sub_type
			 union all
			select travel_from_dt,travel_to_dt,menu_sub_type,  'cancel' code,      '취소접수' name, sum(case when cancel_yn = 'Y' and cancel_code = '0'      then 1 else 0 end) cnt
			  from list group by travel_from_dt,menu_sub_type
		<!--
			 union all
			select travel_from_dt,travel_to_dt,menu_sub_type,  'canceling' code,   '취소진행' name, sum(case when cancel_yn = 'Y' and cancel_code = '1'      then 1 else 0 end) cnt
			  from list group by travel_from_dt,menu_sub_type
		-->
			) a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isGtZero)" > and cnt > 0 </if>
		</where>
		 order by travel_from_dt
		<where>
         </where>
    </select>

    <select id="selectListReservationContentByCheckList" parameterType="HashMap" resultType="HashMap">
		with list as (
			select *
			  from(
				select a.id, a.user_email,
				       IFNULL(a.user_name, u.user_name) user_name,
				       IFNULL(IFNULL(a.user_mobile, u.user_mobile),'~연락처없음~') user_mobile,
				       a.product_serial,
				       pt.product_title,
					   pt.menu_sub_type,
				       travel_schedule_json,
				       @travel := json_value(travel_schedule_json,'$.data.travelSchedule') travel_dt,
				       @travelF := DATE_FORMAT(substring_index(@travel, ' ~ ',1), '%Y-%m-%d') as travel_from_dt,
					   @travelT := DATE_FORMAT(substring_index(@travel, ' ~ ',-1), '%Y-%m-%d') as travel_to_dt,
				       a.cancel_yn,
				       a.apply_code, b.name apply_code_name,
				       a.reservation_code, c.name reservation_code_name,
				       a.cancel_code, d.name cancel_code_name,
				       a.meeting_time,a.meeting_place,a.guide_name,a.guide_tel,
				       a.flag_departure_yn,
				       a.flag_price_yn,a.flag_hotel_yn,a.flag_airline_yn,
				       a.flag_schedule,a.flag_leader_yn,a.flag_guide_yn,
				       a.flag_close_yn
				  from reservation a
				  left join code_item b on a.apply_code = b.code and b.upper_code = 'statusType'
				  left join code_item c on a.reservation_code = c.code and c.upper_code = 'reservationType'
				  left join code_item d on a.cancel_code = d.code and d.upper_code = 'cancelType'
				  left join `user` u on a.user_email = u.user_email
				  left join (select pt.product_serial, pt.product_title, mu.menu_type, mu.menu_sub_type from product_tour pt join menu_user mu on pt.product_menu_id = mu.menu_id where regacy_yn = 'N') pt on a.product_serial = pt.product_serial
				 where a.delete_yn = 'N') a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fromDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(toDate)" >
				and travel_from_dt between #{fromDate} and #{toDate}
			</if>
		<!--
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(targetDate)" >
				and #{targetDate} between DATE_FORMAT(travel_from_dt,'%Y-%m-%d') and DATE_FORMAT(travel_to_dt,'%Y-%m-%d')
			</if>
		-->
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(targetDate)" >
				and travel_from_dt = #{targetDate}
			</if>
		</where>)
		select *
		  from (
		<!--
		    select travel_from_dt,'apply' code,       '접수' name, id, user_email, user_name, user_mobile
		      from list where cancel_yn = 'N' and reservation_code = '0'
			union all
			select travel_from_dt, 'reservation' code, '예약' name, id, user_email, user_name, user_mobile
			  from list where cancel_yn = 'N' and reservation_code = '2'
			union all
		 -->
			select travel_from_dt, 'package' code,     '프로그램' name, id, user_email, user_name, user_mobile
			  from list where cancel_yn = 'N' and reservation_code = '3' and menu_sub_type = 'package'
			union all
			select travel_from_dt, 'stay' code,     '숙소' name, id, user_email, user_name, user_mobile
			  from list where cancel_yn = 'N' and reservation_code = '3' and menu_sub_type = 'stay'
			union all
			select travel_from_dt, 'cancel' code,      '취소접수' name, id, user_email, user_name, user_mobile
			  from list where cancel_yn = 'Y' and cancel_code = '0'
			union all
			select travel_from_dt, 'canceling' code,   '취소진행' name, id, user_email, user_name, user_mobile
			  from list where cancel_yn = 'Y' and cancel_code = '1'
			) a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isGtZero)" > and cnt > 0 </if>
		</where>
		 order by travel_from_dt
    </select>

	<select id="selectListReservationCountStatusType" parameterType="HashMap" resultType="HashMap">
		select #{cancelYn} cancelYn,
			<if test='cancelYn eq "N"'>
		       ifnull(sum( case when reservation_code = '0' then 1 else 0 end ),0) code0,
		       ifnull(sum( case when reservation_code = '1' then 1 else 0 end ),0) code1,
		       ifnull(sum( case when reservation_code = '2' then 1 else 0 end ),0) code2,
		       ifnull(sum( case when reservation_code = '3' then 1 else 0 end ),0) code3,
		       ifnull(sum( case when reservation_code = '4' then 1 else 0 end ),0) code4,
			</if>
			<if test='cancelYn eq "Y"'>
		       ifnull(sum( case when cancel_code = '0' then 1 else 0 end ),0) code0,
		       ifnull(sum( case when cancel_code = '1' then 1 else 0 end ),0) code1,
		       ifnull(sum( case when cancel_code = '2' then 1 else 0 end ),0) code2,
			</if>
		       count(*) tcnt
		  from reservation a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	and a.cancel_yn=#{cancelYn}	</if>
			and a.delete_yn = 'N'
		</where>
	</select>
	
	<select id="selectOneIsHaveSpecialPrice" parameterType="HashMap" resultType="String">
		WITH RECURSIVE 
			price as (
				select pt.product_serial
				       , pt.product_tour_id
				       , po.price_option_id
				       , po.option_name
				       , po.option_one_code
				       , pf.start_date
				  from product_tour pt
				  left join product_tour_price_option po on pt.product_tour_id = po.product_tour_id
				  left join product_tour_price_fix_set pf on po.price_option_id = pf.price_option_id 
				 where product_serial = #{productSerial} )
		select case when spical_haved >= 1 then 'Y' else 'N' end isHaveSpecialPrice
		  from(
			 select product_serial, sum(case when start_date is not null then 1 else 0 end) spical_haved
			   from price p
			  where p.product_tour_id = (select max(product_tour_id) a from price)
			  group by product_serial) p
	</select>

	<select id="selectListCalcReservation" parameterType="HashMap" resultType="Reservation">
		WITH RECURSIVE
			DateRange AS (
				SELECT DATE_FORMAT(#{startDate}, '%Y-%m-%d') AS date
				UNION ALL
				SELECT DATE_ADD(date, INTERVAL 1 DAY)
				FROM DateRange
				WHERE DATE_ADD(date, INTERVAL 1 DAY) &lt;= DATE_FORMAT(#{endDate}, '%Y-%m-%d') ),
			productPeriod as (
				select pr.product_serial, date rest_date, pr.id
					from DateRange dr, 
						product_tour_rest_period pr
					where dr.date between pr.product_rest_start_date and pr.product_rest_end_date
					and pr.product_serial = #{productSerial} 
					and pr.delete_yn = 'N'),
			daycheck as(
				select
					pt.product_serial,
					case when count(*) > 0 then 'day' else 'fix' end isDay
				from product_tour_price_option po
				JOIN product_tour pt on po.product_tour_id = pt.product_tour_id and pt.regacy_yn = 'N'
				join product_tour_price_set pd on po.price_option_id = pd.price_option_id
				WHERE pt.product_serial = #{productSerial} ) ,
			mix as (
				select
					pt.product_serial
					, pt.policy_inventory
					, pt.product_use_yn
					, pt.delete_yn product_delete_yn
					, pt.regacy_yn product_regacy_yn
					, mix.price_id
					, mix.gubn
					, mix.isDay
					, po.*
					, mix.product_price
					, mix.price_set_date
					, mix.start_date
					, mix.end_date
					, mix.consecurive_discount_amount
					, mix.extra_person_defualt_charge
					, mix.extra_person_consecurive_charge
				from product_tour_price_option po
				JOIN product_tour pt on po.product_tour_id = pt.product_tour_id /*and pt.regacy_yn = 'N'*/
				join (
					select
						gubn
						, price_option_id
						, price_id
						, price_set_date
						, start_date
						, end_date
						, product_price
						, d.isDay
						, consecurive_discount_amount
						, extra_person_defualt_charge
						, extra_person_consecurive_charge
					from(
						select
							'day' gubn
							, price_option_id
							, price_id
							, price_set_date
							, null start_date
							, null end_date
							, (CASE WHEN price_sale != 0 THEN price_sale ELSE price_normal END ) AS product_price
							, null consecurive_discount_amount
							, null extra_person_defualt_charge
							, null extra_person_consecurive_charge
						from product_tour_price_set
						/*where delete_yn = 'N' and use_yn = 'Y'*/
					union all
						select
							'fix' gubn
							, price_option_id
							, price_id
							, null price_set_date
							, start_date
							, end_date
							, (CASE WHEN price_sale != 0 THEN price_sale ELSE price_normal END ) AS product_price
							, consecurive_discount_amount
							, extra_person_defualt_charge
							, extra_person_consecurive_charge
						from product_tour_price_fix_set
						/*where delete_yn = 'N' and use_yn = 'Y' */) as b
					join daycheck d on b.gubn = d.isDay ) mix ON po.price_option_id = mix.price_option_id
				WHERE pt.product_serial = #{productSerial}
					/*and po.use_yn = 'Y'*/),
			RankedPrices AS (
				SELECT
					dr.date
					, po.product_serial
					, po.product_tour_id
					, po.price_option_id
					, po.price_id
					, po.policy_inventory
					, po.gubn
					, (CASE WHEN po.start_date IS NOT NULL THEN 'S' ELSE 'N' END) gubn2
					, ROW_NUMBER() OVER ( PARTITION BY dr.date, CASE WHEN po.start_date IS NOT NULL THEN 1 ELSE 0 END order by po.price_option_id /*ORDER BY CASE WHEN po.start_date IS NOT NULL THEN 1 ELSE 0 END*/ ) as rn
					, po.max_quantity
					, po.max_capacity
					, po.product_price
					, po.price_set_date
					, po.start_date
					, po.end_date
					, po.option_one_code
					, po.option_name
					, po.product_delete_yn
					, po.product_regacy_yn
					, po.use_yn
					, po.delete_yn
					, consecurive_discount_amount
					, extra_person_defualt_charge
					, extra_person_consecurive_charge
				FROM DateRange dr
				JOIN mix po ON
					( (po.gubn = 'fix'
						AND ((po.start_date IS NOT NULL
							AND dr.date BETWEEN po.start_date AND po.end_date)
						OR po.start_date IS NULL))
					OR (po.gubn = 'day'
						AND po.price_set_date = dr.date
						AND po.start_date is null
						AND po.end_date is null ) ) ) ,
			resvDates AS (
				SELECT
					id,
					product_serial,
					seq,
					DATE_ADD(start_date, INTERVAL seq -1 DAY) as date,
					start_date,
					end_date,
					checkout_date,
					option_id,
					order_count,
					order_one_code,
					pickPeople
				FROM(
					SELECT id, product_serial, ROW_NUMBER() OVER (PARTITION BY id ORDER BY t.option_key) as seq,
						JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.travelSchedule')) as travel_schedule,
						STR_TO_DATE( JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.travelSchedule')) , '%Y-%m-%d') as start_date,
						DATE_ADD(STR_TO_DATE(SUBSTRING_INDEX( JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.travelSchedule')) , ' ~ ', -1), '%Y-%m-%d'), INTERVAL -1 DAY) as end_date,
						STR_TO_DATE(SUBSTRING_INDEX( JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.travelSchedule')) , ' ~ ', -1), '%Y-%m-%d') as checkout_date,
						JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.pickPeople')) as pickPeople,
						t.option_key,
						JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.0'))) as option_id,
						JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.1'))) as price,
						JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.2'))) as value2,
						JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.3'))) as order_count,
						JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.4'))) as order_one_code
					FROM
						reservation
					CROSS JOIN JSON_TABLE(
							JSON_KEYS(JSON_EXTRACT(price_option_json, '$.data')),
							'$[*]' COLUMNS(option_key VARCHAR(50) PATH '$') ) AS t
					where
						cancel_yn = 'N'
						and delete_yn = 'N'
						and is_group = 'N'
						and t.option_key like 'option-%'
						and product_serial = #{productSerial} ) as base ) ,
			aggResvTable as (
				select
					date
					, option_id
					, order_one_code
					, sum(order_count) order_count
					, sum(pickPeople) pick_people
				from resvDates
				group by date, option_id, order_one_code),
			aggData as (
				select st.*,
			       rd.order_one_code, rd.option_id, rd.order_count, rd.pick_people
			  from RankedPrices st
			  left join aggResvTable rd on st.date = rd.date and st.option_one_code = rd.order_one_code	and st.price_option_id = rd.option_id	),
			lastRnData as (
				select date,gubn2,option_one_code, max(rn) last_rn
				  from RankedPrices
				 group by date,gubn2,option_one_code),
			lastData as (
				select *
					   , (order_count + order_count2) oc
				  from (
					select date
						, MAX(CASE WHEN gubn2 = 'N' THEN option_id END) as option_id
						, MAX(CASE WHEN gubn2 = 'N' THEN max_quantity END) as max_quantity
						, MAX(CASE WHEN gubn2 = 'N' THEN max_capacity END) as max_capacity
						, MAX(CASE WHEN gubn2 = 'N' THEN option_one_code END) as option_one_code
						, MAX(CASE WHEN gubn2 = 'N' THEN product_price END) as product_price
						, MAX(CASE WHEN gubn2 = 'N' THEN option_name END) as option_name
						, MAX(CASE WHEN gubn2 = 'N' THEN consecurive_discount_amount END) as consecurive_discount_amount
						, MAX(CASE WHEN gubn2 = 'N' THEN extra_person_defualt_charge END) as extra_person_defualt_charge
						, MAX(CASE WHEN gubn2 = 'N' THEN extra_person_consecurive_charge END) as extra_person_consecurive_charge
						, MAX(CASE WHEN gubn2 = 'N' THEN order_count END) as order_count
						, MAX(CASE WHEN gubn2 = 'N' THEN remain_count END) as remain_count
						, MAX(CASE WHEN gubn2 = 'N' THEN pick_people END) as pick_people
						, MAX(CASE WHEN gubn2 = 'S' THEN option_id END) as option_id2
						, MAX(CASE WHEN gubn2 = 'S' THEN max_quantity END) as max_quantity2
						, MAX(CASE WHEN gubn2 = 'S' THEN max_capacity END) as max_capacity2
						, MAX(CASE WHEN gubn2 = 'S' THEN option_one_code END) as option_one_code2
						, MAX(CASE WHEN gubn2 = 'S' THEN product_price END) as product_price2
						, MAX(CASE WHEN gubn2 = 'S' THEN start_date END) as start_date
						, MAX(CASE WHEN gubn2 = 'S' THEN end_date END) as end_date
						, MAX(CASE WHEN gubn2 = 'S' THEN option_name END) as option_name2
						, MAX(CASE WHEN gubn2 = 'S' THEN consecurive_discount_amount END) as consecurive_discount_amount2
						, MAX(CASE WHEN gubn2 = 'S' THEN extra_person_defualt_charge END) as extra_person_defualt_charge2
						, MAX(CASE WHEN gubn2 = 'S' THEN extra_person_consecurive_charge END) as extra_person_consecurive_charge2
						, MAX(CASE WHEN gubn2 = 'S' THEN order_count END) as order_count2
						, MAX(CASE WHEN gubn2 = 'S' THEN remain_count END) as remain_count2
						, MAX(CASE WHEN gubn2 = 'S' THEN pick_people END) as pick_people2
					from (
						SELECT
							ag.date
							, ag.gubn2
							, max(ag.rn) rn
							, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.price_option_id END) as option_id
							, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.max_quantity END) as max_quantity
							, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.max_capacity END) as max_capacity
							, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.option_one_code END) as option_one_code
							, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.product_price END) as product_price
							, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.start_date END) as start_date
							, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.end_date END) as end_date
							, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.option_name END) as option_name
							, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.consecurive_discount_amount END) as consecurive_discount_amount
							, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.extra_person_defualt_charge END) as extra_person_defualt_charge
							, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.extra_person_consecurive_charge END) as extra_person_consecurive_charge
							, ifnull(sum(order_count),0) order_count
							, max(case when ag.rn = ld.last_rn then ag.max_capacity else null end) - ifnull(sum(order_count),0) remain_count
							, ifnull(sum(pick_people),0) pick_people
						FROM aggData ag
						left join lastRnData ld on ag.date = ld.date and ag.rn = ld.last_rn and ag.option_one_code = ld.option_one_code and ag.rn = ld.last_rn
						GROUP BY ag.date, ag.gubn2 ) a
					GROUP BY date
					ORDER BY date
					) a)
			select
				date,
				date as travel_date ,
				IFNULL(max_quantity,0) as max_quantity,
				IFNULL(max_capacity,0) as max_capacity ,
				IFNULL(max_quantity2,0) as special_quantity ,
				IFNULL(max_capacity2,0) as special_capacity ,
				IFNULL(order_count,0) as order_count ,
				IFNULL(order_count2,0) as special_order_count ,
				IFNULL(oc,0) as total_order_count ,
				case
					when max_capacity is null then 'can\' t sell'
					when order_count > max_capacity then ' over '
					when order_count = max_capacity then 'sold out' else 'remain' end 'rsv_possible' ,
				case when max_capacity2 is null then ' can\'t sell'
					when order_count2 > max_capacity2 then 'over'
					when order_count2 = max_capacity2 then 'sold out'
					else 'remain'
				end 'special_rsv_possible' ,
				product_price as product_price_param ,
				product_price2 as special_price ,
				option_id as price_option_id,
				option_id2 as special_option_id ,
				option_name ,
				option_name2 as special_option_name ,
				option_one_code ,
				option_one_code2 as special_option_one_code ,
				consecurive_discount_amount ,
				extra_person_defualt_charge ,
				extra_person_consecurive_charge ,
				consecurive_discount_amount2 as special_consecurive_discount_amount ,
				extra_person_defualt_charge2 as special_extra_person_defualt_charge ,
				extra_person_consecurive_charge2 as special_extra_person_consecurive_charge,
				case when pp.id is null then 0 else 1 end is_rest_date
			from lastData a
			left join productPeriod pp on pp.rest_date = a.date
	</select>

	<select id="selectCountReservation" parameterType="HashMap" resultType="Integer">
        SELECT count(id)
          FROM (
            SELECT *
              FROM (
				SELECT
					a.id,
					@travel := json_value(travel_schedule_json,'$.data.travelSchedule') travel_schedule_dt,
					@travelF := substring_index(@travel, ' ~ ',1) as datef,
					@travelT :=substring_index(@travel, ' ~ ',-1) as datet,
					ifNUll(p.product_title, '~상품미선택~') product_title,
					a.reservation_code,
					a.create_date,
					a.user_email,
					u.user_name,
					IFNULL(IFNULL(a.user_mobile, u.user_mobile), '~연락처없음~') user_mobile,
					json_value(a.group_json, '$.data.groupId') group_id,
					IFNULL( json_value(group_json, '$.data.groupName') , '') group_name,
					a.delete_yn, a.cancel_yn
				  FROM reservation a
				  left join `user` u on a.user_email = u.user_email
				  left join product_tour p on a.product_serial = p.product_serial and a.product_tour_id = p.product_tour_id
				  <where>
				  			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	and apply_code=#{applyCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationCode)" >	and reservation_code=#{reservationCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	and cancel_yn=#{cancelYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelCode)" >	and cancel_code=#{cancelCode}	</if>
				  </where> ) a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and concat(a.product_title,a.user_name,a.group_name,a.user_mobile,a.user_email) like concat('%',#{searchKey},'%')</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateType)">
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateFrom) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateTo)">
				<choose>
					<when test='dateType eq "travelFrom"'>and datef between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d %H:%i:%s') </when>
					<when test='dateType eq "travelTo"'>and datet between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d %H:%i:%s') </when>
					<when test='dateType eq "receipt"'>and a.create_date between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d %H:%i:%s')</when>
				</choose>
				</if>
			</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and a.user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)" >	and user_name=#{userName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPhone)" >	and user_mobile=#{userMobile}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupType)" >	and group_type=#{groupType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupId)" >	and group_id=#{groupId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	and berth_type=#{berthType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_serial=#{productSerial}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	and berth_json=#{berthJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	and travel_schedule_json=#{travelScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	and air_type_request=#{airTypeRequest}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	and air_schedule_json=#{airScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	and vehicle_type=#{vehicleType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	and vehicle_count=#{vehicleCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	and vehicle_json=#{vehicleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(totalAmount)" >	and total_amount=#{totalAmount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(meetingTime)" >	and meeting_time=#{meetingTime}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(meetingPlace)" >	and meeting_place=#{meetingPlace}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guideName)" >	and guide_name=#{guideName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guideTel)" >	and guide_tel=#{guideTel}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagDepartureYn)" >	and flag_departure_yn=#{flagDepartureYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagPriceYn)" >	and flag_price_yn=#{flagPriceYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagHotelYn)" >	and flag_hotel_yn=#{flagHotelYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagAirlineYn)" >	and flag_airline_yn=#{flagAirlineYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagSchedule)" >	and flag_schedule=#{flagSchedule}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagLeaderYn)" >	and flag_leader_yn=#{flagLeaderYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagGuideYn)" >	and flag_guide_yn=#{flagGuideYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagCloseYn)" >	and flag_close_yn=#{flagCloseYn}	</if>
         </where>) b
    </select>

	<select id="selectListReservation" parameterType="HashMap" resultType="Reservation">
		SELECT *
		  FROM(
			SELECT *
			  FROM(
		        SELECT @rownum:=@rownum+1 AS rownum,
		        		a.id, a.user_email,
		        		IFNULL(a.user_name, u.user_name) user_name,
		        		IFNULL(IFNULL(a.user_mobile, u.user_mobile),'~연락처없음~') user_mobile,
		        		IFNULL(@travel := json_value(travel_schedule_json,'$.data.travelSchedule'), '~일정정보없음~') travel_schedule_dt,
		        		@travelF := substring_index(@travel, ' ~ ',1) as datef,
						@travelT :=substring_index(@travel, ' ~ ',-1) as datet,
						concat(date_format(@travelF, '%Y%m%d'), date_format(@travelT, '%Y%m%d')) travel_schedule_dt_sort,
		        		apply_code,
		        		ifNUll(a.product_serial, '') product_serial,
						a.product_tour_id,
		        		ifNUll(p.product_title, '~상품미선택~') product_title,
		        		reservation_code,
		        		b.name reservation_code_name,
		        		cancel_yn,
		        		cancel_code,
		        		b2.name cancel_code_name,
		        		group_json,
		        		json_value(group_json,'$.data.groupId') group_id,
		        		IFNULL( json_value(group_json, '$.data.groupName') , '') group_name,
		        		berth_type,
		        		berth_json,
						price_option_json,
		        		travel_schedule_json,
		        		air_type_request,
		        		air_schedule_json,
		        		vehicle_type, vehicle_count, vehicle_json, total_amount,
		        		a.create_id, a.file_id, a.create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.delete_id, a.delete_date,
		        		ifNUll(a.category_id,'') category_id,
		        		ifNUll(c.title,'') category_title,
		        		a.meeting_time,a.meeting_place,a.guide_name,a.guide_tel,
						a.flag_price_yn,
						a.flag_hotel_yn,
						a.flag_airline_yn,
						a.flag_schedule,
						a.flag_leader_yn,
						a.flag_guide_yn,
						a.flag_close_yn,
						a.flag_settlement1_yn,
						a.flag_settlement2_yn,
						u.user_join_type,
						IFNULL( pay.amt_t, 0) amt_t,
						IFNULL( pay.amt_g, 0) amt_g,
						IFNULL( pay.amt_a, 0) amt_a,
						IFNULL( pay.amt_d, 0) amt_d,
						IFNULL( pay.amt_b, 0) amt_b,
						m.menu_type,
						m.menu_sub_type
		          FROM reservation a
		          left join product_tour p on a.product_serial = p.product_serial and a.product_tour_id = p.product_tour_id
		          left join menu_user m on p.product_menu_id = m.menu_id
		          left join code_item b on a.reservation_code = b.code and b.upper_code = 'reservationType'
		          left join code_item b2 on a.cancel_code = b2.code and b2.upper_code = 'cancelType'
		          left join inquiry_category c on a.category_id = c.id
		          left join `user` u on a.user_email = u.user_email
		          left join
					( select reservation_id,(amt_g + amt_a) amt_t,  amt_g, amt_a, amt_d, (amt_g + amt_a) - amt_d amt_b
					  from (
						SELECT
						    reservation_id,
							sum(case when pay_division = 'G' then pay_amount else 0 end) amt_g,
							sum(case when pay_division = 'A' then pay_amount else 0 end) amt_A,
							sum(case when pay_division = 'D' then pay_amount else 0 end) amt_d
				  		FROM user_customer_payment
				  		group by reservation_id) a ) pay on pay.reservation_id = a.id
		          join (SELECT @rownum:= 0) rnum ) a
	         <where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and concat(a.product_title,a.user_name,a.group_name,a.user_mobile,a.user_email) like concat('%',#{searchKey},'%')</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateType)">
	         		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateFrom) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateTo)">
					<choose>
						<when test="dateType eq 'travelFrom'">and datef between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d %H:%i:%s') </when>
						<when test="dateType eq 'travelTo'">and datet between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d %H:%i:%s') </when>
						<when test="dateType eq 'receipt'">and a.create_date between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d %H:%i:%s')</when>
					</choose>
					</if>
				</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(capacityCheck)">
				CASE
					WHEN travel_schedule_dt LIKE '%~%' THEN
						DATE_SUB(a.datet, INTERVAL 1 day) >= #{rsvDateT}
					ELSE
						a.datet >= #{rsvDateT}
					END
				</if>

	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and a.user_email=#{userEmail}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)" >	and user_name=#{userName}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPhone)" >	and user_mobile=#{userMobile}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupType)" >	and group_type=#{groupType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupId)" >	and group_id=#{groupId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	and berth_type=#{berthType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_serial=#{productSerial}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	and apply_code=#{applyCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationCode)" >	and reservation_code=#{reservationCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	and cancel_yn=#{cancelYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelCode)" >	and cancel_code=#{cancelCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	and berth_json=#{berthJson}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	and travel_schedule_json=#{travelScheduleJson}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	and air_type_request=#{airTypeRequest}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	and air_schedule_json=#{airScheduleJson}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	and vehicle_type=#{vehicleType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	and vehicle_count=#{vehicleCount}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	and vehicle_json=#{vehicleJson}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(totalAmount)" >	and total_amount=#{totalAmount}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and a.create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and a.create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and a.last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and a.last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and a.delete_date=#{deleteDate}	</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(meetingTime)" >	and meeting_time=#{meetingTime}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(meetingPlace)" >	and meeting_place=#{meetingPlace}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guideName)" >	and guide_name=#{guideName}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guideTel)" >	and guide_tel=#{guideTel}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagDepartureYn)" >	and flag_departure_yn=#{flagDepartureYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagPriceYn)" >	and flag_price_yn=#{flagPriceYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagHotelYn)" >	and flag_hotel_yn=#{flagHotelYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagAirlineYn)" >	and flag_airline_yn=#{flagAirlineYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagSchedule)" >	and flag_schedule=#{flagSchedule}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagLeaderYn)" >	and flag_leader_yn=#{flagLeaderYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagGuideYn)" >	and flag_guide_yn=#{flagGuideYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagCloseYn)" >	and flag_close_yn=#{flagCloseYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagSettlement1Yn)" >	and flag_settlement1_yn=#{flagSettlement1Yn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagSettlement2Yn)" >	and flag_settlement2_yn=#{flagSettlement2Yn}	</if>
	         </where>) b

	         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort=='id'" >	id	</when>
		            <when test="sort=='travelScheduleDt'" >	travel_schedule_dt_sort	</when>
		            <when test="sort=='productTitle'" >	product_title	</when>
		            <when test="sort=='productSerial'" >	product_serial	</when>
		            <when test="sort=='amtT'" >	amt_t	</when>
		            <when test="sort=='amtG'" >	amt_g	</when>
		            <when test="sort=='amtA'" >	amt_a	</when>
		            <when test="sort=='amtD'" >	amt_d	</when>
		            <when test="sort=='amtB'" >	amt_b	</when>
		            <when test="sort=='id'" >	id	</when>
					<when test="sort=='userEmail'" >	user_email	</when>
					<when test="sort=='userName'" >	user_name	</when>
					<when test="sort=='categoryTitle'" >	category_title	</when>
					<when test="sort=='userMobile'" >	user_mobile	</when>
					<when test="sort=='groupJson'" >	group_json	</when>
					<when test="sort=='berthType'" >	berth_type	</when>
					<when test="sort=='productSerial'" >	product_serial	</when>
					<when test="sort=='applyCode'" >	apply_code	</when>
					<when test="sort=='reservationCode'" >	reservation_code	</when>
					<when test="sort=='cancelYn'" >	cancel_yn	</when>
					<when test="sort=='cancelCode'" >	cancel_code	</when>
					<when test="sort=='berthJson'" >	berth_json	</when>
					<when test="sort=='travelScheduleJson'" >	travel_schedule_json	</when>
					<when test="sort=='airTypeRequest'" >	air_type_request	</when>
					<when test="sort=='airScheduleJson'" >	air_schedule_json	</when>
					<when test="sort=='vehicleType'" >	vehicle_type	</when>
					<when test="sort=='vehicleCount'" >	vehicle_count	</when>
					<when test="sort=='vehicleJson'" >	vehicle_json	</when>
					<when test="sort=='totalAmount'" >	total_amount	</when>
					<when test="sort=='deleteId'" >	delete_id	</when>
					<when test="sort=='menuType'" >	menu_type	</when>
					<when test="sort=='menuSubType'" >	menu_sub_type	</when>
					<when test="sort=='createDate'" >	create_date	</when>
					<when test="sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="sort=='deleteYn'" >	delete_yn	</when>
					<when test="sort=='deleteId'" >	delete_id	</when>
					<when test="sort=='deleteDate'" >	delete_date	</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		    		<when test="item.sort=='id'" >	id	</when>
		            <when test="item.sort=='travelScheduleDt'" >	travel_schedule_dt_sort	</when>
		            <when test="item.sort=='productTitle'" >	product_title	</when>
		            <when test="item.sort=='productSerial'" >	product_serial	</when>
		            <when test="item.sort=='amtT'" >	amt_t	</when>
		            <when test="item.sort=='amtG'" >	amt_g	</when>
		            <when test="item.sort=='amtA'" >	amt_a	</when>
		            <when test="item.sort=='amtD'" >	amt_d	</when>
		            <when test="item.sort=='amtB'" >	amt_b	</when>
		            <when test="item.sort=='id'" >	id	</when>
					<when test="item.sort=='userEmail'" >	user_email	</when>
					<when test="item.sort=='userName'" >	user_name	</when>
					<when test="item.sort=='categoryTitle'" >	category_title	</when>
					<when test="item.sort=='userMobile'" >	user_mobile	</when>
					<when test="item.sort=='groupJson'" >	group_json	</when>
					<when test="item.sort=='berthType'" >	berth_type	</when>
					<when test="item.sort=='productSerial'" >	product_serial	</when>
					<when test="item.sort=='applyCode'" >	apply_code	</when>
					<when test="item.sort=='reservationCode'" >	reservation_code	</when>
					<when test="item.sort=='cancelYn'" >	cancel_yn	</when>
					<when test="item.sort=='cancelCode'" >	cancel_code	</when>
					<when test="item.sort=='berthJson'" >	berth_json	</when>
					<when test="item.sort=='travelScheduleJson'" >	travel_schedule_json	</when>
					<when test="item.sort=='airTypeRequest'" >	air_type_request	</when>
					<when test="item.sort=='airScheduleJson'" >	air_schedule_json	</when>
					<when test="item.sort=='vehicleType'" >	vehicle_type	</when>
					<when test="item.sort=='vehicleCount'" >	vehicle_count	</when>
					<when test="item.sort=='vehicleJson'" >	vehicle_json	</when>
					<when test="item.sort=='totalAmount'" >	total_amount	</when>
					<when test="item.sort=='menuType'" >	menu_type	</when>
					<when test="item.sort=='menuSubType'" >	menu_sub_type	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='deleteId'" >	delete_id	</when>
					<when test="item.sort=='deleteDate'" >	delete_date	</when>

		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>

		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>
	<select id="selectOneReservation" parameterType="HashMap" resultType="Reservation">
         SELECT @rownum:=@rownum+1 AS rownum,
        		a.id, a.user_email, CASE WHEN uco.tid IS NULL THEN 'none' ELSE uco.tid END as tid,
        		IFNULL(a.user_name, u.user_name) user_name,
        		IFNULL(a.user_mobile, u.user_mobile) user_mobile,
        		ifnull(@travel := json_value(travel_schedule_json,'$.data.travelSchedule'),'~일정정보없음~') travel_schedule_dt,
        		@travelF := substring_index(@travel, ' ~ ',1) as datef,
				@travelT :=substring_index(@travel, ' ~ ',-1) as datet,
        		group_json,berth_type, a.product_serial,a.product_tour_id,ifNUll(p.product_title, '~상품미선택~') product_title, apply_code,
        		reservation_code, b.name reservation_code_name, cancel_yn, cancel_code, berth_json, price_option_json,
        		travel_schedule_json, air_type_request, air_schedule_json,
        		vehicle_type, vehicle_count, vehicle_json, use_point, user_point_used_log_id, total_amount, a.pay_moid,
        		a.create_id, a.file_id, a.create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.delete_id, a.delete_date,
        		a.category_id,a.meeting_time,a.meeting_place,a.guide_name,a.guide_tel,
        		a.flag_departure_yn,
				a.flag_price_yn,a.flag_hotel_yn,a.flag_airline_yn,
				a.flag_schedule,a.flag_leader_yn,a.flag_guide_yn,
				a.flag_close_yn,a.flag_settlement1_yn,a.flag_settlement2_yn, a.is_group
          FROM reservation a
          left join product_tour p on a.product_serial = p.product_serial and a.product_tour_id = p.product_tour_id
          left join code_item b on a.reservation_code = b.code and b.upper_code = 'reservationType'
          left join `user` u on a.user_email = u.user_email
          left join user_customer_order uco on a.id = uco.reservation_id
          join (SELECT @rownum:= 0) rnum
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and a.id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)" >	and user_name=#{userName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPhone)" >	and user_mobile=#{userMobile}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupJson)" >	and group_json=#{groupJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	and berth_json=#{berthJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	and berth_type=#{berthType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_serial=#{productSerial}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	and apply_code=#{applyCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationCode)" >	and reservation_code=#{reservationCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	and cancel_yn=#{cancelYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelCode)" >	and cancel_code=#{cancelCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	and travel_schedule_json=#{travelScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	and air_type_request=#{airTypeRequest}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	and air_schedule_json=#{airScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	and vehicle_type=#{vehicleType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	and vehicle_count=#{vehicleCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	and vehicle_json=#{vehicleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(totalAmount)" >	and total_amount=#{totalAmount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(meetingTime)" >	and meeting_time=#{meetingTime}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(meetingPlace)" >	and meeting_place=#{meetingPlace}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guideName)" >	and guide_name=#{guideName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guideTel)" >	and guide_tel=#{guideTel}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagDepartureYn)" >	and flag_departure_yn=#{flagDepartureYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagPriceYn)" >	and flag_price_yn=#{flagPriceYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagHotelYn)" >	and flag_hotel_yn=#{flagHotelYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagAirlineYn)" >	and flag_airline_yn=#{flagAirlineYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagSchedule)" >	and flag_schedule=#{flagSchedule}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagLeaderYn)" >	and flag_leader_yn=#{flagLeaderYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagGuideYn)" >	and flag_guide_yn=#{flagGuideYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagCloseYn)" >	and flag_close_yn=#{flagCloseYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagSettlement1Yn)" >	and flag_settlement1_yn=#{flagSettlement1Yn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagSettlement2Yn)" >	and flag_settlement2_yn=#{flagSettlement2Yn}	</if>
         </where>
	</select>

	<insert id="insertReservation" parameterType="Reservation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO reservation
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)" >	user_name=#{userName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userMobile)" >	user_mobile=#{userMobile},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	product_serial=#{productSerial},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	product_tour_id=#{productTourId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	apply_code=#{applyCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationCode)" >	reservation_code=#{reservationCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupJson)" >	group_json=#{groupJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	berth_type=#{berthType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	berth_json=#{berthJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	cancel_yn=#{cancelYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelCode)" >	cancel_code=#{cancelCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(priceOptionJson)" >	price_option_json=#{priceOptionJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	travel_schedule_json=#{travelScheduleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	air_type_request=#{airTypeRequest},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	air_schedule_json=#{airScheduleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	vehicle_type=#{vehicleType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	vehicle_count=#{vehicleCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	vehicle_json=#{vehicleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(totalAmount)" >	total_amount=#{totalAmount},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderType)" >	order_type=#{orderType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMethod)" >	pay_method=#{payMethod},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	pay_moid=#{payMoid},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)" >	category_id=#{categoryId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	file_id=#{fileId},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(meetingTime)" >	meeting_time=#{meetingTime},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(meetingPlace)" >	meeting_place=#{meetingPlace},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guideName)" >	guide_name=#{guideName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guideTel)" >	guide_tel=#{guideTel},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagDepartureYn)" >	flag_departure_yn=#{flagDepartureYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagPriceYn)" >	flag_price_yn=#{flagPriceYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagHotelYn)" >	flag_hotel_yn=#{flagHotelYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagAirlineYn)" >	flag_airline_yn=#{flagAirlineYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagSchedule)" >	flag_schedule=#{flagSchedule},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagLeaderYn)" >	flag_leader_yn=#{flagLeaderYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagGuideYn)" >	flag_guide_yn=#{flagGuideYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagCloseYn)" >	flag_close_yn=#{flagCloseYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagSettlement1Yn)" >	flag_settlement1_yn=#{flagSettlement1Yn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagSettlement2Yn)" >	flag_settlement2_yn=#{flagSettlement2Yn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isGroup)" >	is_group=#{isGroup},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			create_date = now()
		</set>
    </insert>

    <update id="updateReservation" parameterType="Reservation">
        UPDATE reservation
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)" >	user_name=#{userName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userMobile)" >	user_mobile=#{userMobile},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	product_serial=#{productSerial},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	product_tour_id=#{productTourId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	apply_code=#{applyCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationCode)" >	reservation_code=#{reservationCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	cancel_yn=#{cancelYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelCode)" >	cancel_code=#{cancelCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupJson)" >	group_json=#{groupJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	berth_type=#{berthType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	berth_json=#{berthJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(priceOptionJson)" >	price_option_json=#{priceOptionJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	travel_schedule_json=#{travelScheduleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	air_type_request=#{airTypeRequest},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	air_schedule_json=#{airScheduleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	vehicle_type=#{vehicleType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	vehicle_count=#{vehicleCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	vehicle_json=#{vehicleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(totalAmount)" >	total_amount=#{totalAmount},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderType)" >	order_type=#{orderType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMethod)" >	pay_method=#{payMethod},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	pay_moid=#{payMoid},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)" >	category_id=#{categoryId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	file_id=#{fileId},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(meetingTime)" >	meeting_time=#{meetingTime},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(meetingPlace)" >	meeting_place=#{meetingPlace},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guideName)" >	guide_name=#{guideName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guideTel)" >	guide_tel=#{guideTel},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagDepartureYn)" >	flag_departure_yn=#{flagDepartureYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagPriceYn)" >	flag_price_yn=#{flagPriceYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagHotelYn)" >	flag_hotel_yn=#{flagHotelYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagAirlineYn)" >	flag_airline_yn=#{flagAirlineYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagSchedule)" >	flag_schedule=#{flagSchedule},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagLeaderYn)" >	flag_leader_yn=#{flagLeaderYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagGuideYn)" >	flag_guide_yn=#{flagGuideYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagCloseYn)" >	flag_close_yn=#{flagCloseYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagSettlement1Yn)" >	flag_settlement1_yn=#{flagSettlement1Yn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flagSettlement2Yn)" >	flag_settlement2_yn=#{flagSettlement2Yn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isGroup)" >	is_group=#{isGroup},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
		</where>
    </update>

    <update id="restoreReservation" parameterType="Reservation">
        UPDATE reservation
           SET delete_yn='N'
        WHERE id = #{id}
    </update>

    <update id="deleteReservation" parameterType="Reservation">
        UPDATE reservation
           SET delete_yn='Y'
        WHERE id = #{id}
    </update>


<!--################################### ReservationQna ###################################-->
	 <select id="selectCountReservationQna" parameterType="HashMap" resultType="Integer">
        SELECT count(id)
          FROM (
			SELECT id,
			   reservation_id,
			   apply_code,
			   request_date,
			   request_text,
			   response_date,
			   response_name,
			   response_text,
			   response_category,
			   response_subcategory,
			   create_id,
			   create_date,
			   last_update_id,
			   last_update_date,
			   delete_yn,
			   delete_id,
			   delete_date
		  FROM reservation_qna a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	and apply_code=#{applyCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestDate)" >	and request_date=#{requestDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestText)" >	and request_text=#{requestText}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseDate)" >	and response_date=#{responseDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseName)" >	and response_name=#{responseName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseText)" >	and response_text=#{responseText}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseCategory)" >	and response_category=#{responseCategory}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseSubcategory)" >	and response_subcategory=#{responseSubcategory}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
         </where>) a
    </select>

	<select id="selectListReservationQna" parameterType="HashMap" resultType="ReservationQna">
		SELECT *
		  FROM(
			SELECT id,
				   reservation_id,
				   apply_code,
				   request_date,
				   request_text,
				   ifnull(response_date, '-') response_date,
				   ifnull(response_name, '-') response_name,
				   ifnull(response_text, '-') response_text,
				   response_category,
				   response_subcategory,
				   create_id,
				   create_date,
				   last_update_id,
				   last_update_date,
				   delete_yn,
				   delete_id,
				   delete_date
			  FROM reservation_qna
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	and apply_code=#{applyCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestDate)" >	and request_date=#{requestDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestText)" >	and request_text=#{requestText}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseDate)" >	and response_date=#{responseDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseName)" >	and response_name=#{responseName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseText)" >	and response_text=#{responseText}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseCategory)" >	and response_category=#{responseCategory}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseSubcategory)" >	and response_subcategory=#{responseSubcategory}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
         </where> ) a

         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
	    	ORDER BY
	        <choose>
				<when test="sort=='id'" >	id	</when>
				<when test="sort=='reservationId'" >	reservation_id	</when>
				<when test="sort=='applyCode'" >	apply_code	</when>
				<when test="sort=='requestDate'" >	request_date	</when>
				<when test="sort=='requestText'" >	request_text	</when>
				<when test="sort=='responseDate'" >	response_date	</when>
				<when test="sort=='responseName'" >	response_name	</when>
				<when test="sort=='responseText'" >	response_text	</when>
				<when test="sort=='responseCategory'" >	response_category	</when>
				<when test="sort=='responseSubcategory'" >	response_subcategory	</when>
				<when test="sort=='createId'" >	create_id	</when>
				<when test="sort=='createDate'" >	create_date	</when>
				<when test="sort=='lastUpdateId'" >	last_update_id	</when>
				<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
				<when test="sort=='deleteYn'" >	delete_yn	</when>
				<when test="sort=='deleteId'" >	delete_id	</when>
				<when test="sort=='deleteDate'" >	delete_date	</when>
	            <otherwise>rownum</otherwise>
	        </choose>
	        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
	    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
	    	<choose>
				<when test="item.sort=='id'" >	id	</when>
				<when test="item.sort=='reservationId'" >	reservation_id	</when>
				<when test="item.sort=='applyCode'" >	apply_code	</when>
				<when test="item.sort=='requestDate'" >	request_date	</when>
				<when test="item.sort=='requestText'" >	request_text	</when>
				<when test="item.sort=='responseDate'" >	response_date	</when>
				<when test="item.sort=='responseName'" >	response_name	</when>
				<when test="item.sort=='responseText'" >	response_text	</when>
				<when test="item.sort=='responseCategory'" >	response_category	</when>
				<when test="item.sort=='responseSubcategory'" >	response_subcategory	</when>
				<when test="item.sort=='createId'" >	create_id	</when>
				<when test="item.sort=='createDate'" >	create_date	</when>
				<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
				<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
				<when test="item.sort=='deleteYn'" >	delete_yn	</when>
				<when test="item.sort=='deleteId'" >	delete_id	</when>
				<when test="item.sort=='deleteDate'" >	delete_date	</when>
	        </choose>
	    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
		</if>

		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneReservationQna" parameterType="HashMap" resultType="ReservationQna">
         SELECT id,
			   reservation_id,
			   apply_code,
			   request_date,
			   request_text,
			   response_date,
			   response_name,
			   response_text,
			   response_category,
			   response_subcategory,
			   create_id,
			   create_date,
			   last_update_id,
			   last_update_date,
			   delete_yn,
			   delete_id,
			   delete_date
		  FROM reservation_qna
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	and apply_code=#{applyCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestDate)" >	and request_date=#{requestDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestText)" >	and request_text=#{requestText}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseDate)" >	and response_date=#{responseDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseName)" >	and response_name=#{responseName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseText)" >	and response_text=#{responseText}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseCategory)" >	and response_category=#{responseCategory}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseSubcategory)" >	and response_subcategory=#{responseSubcategory}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
         </where>
	</select>

	<insert id="insertReservationQna" parameterType="ReservationQna" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO reservation_qna
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	id=#{id},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	reservation_id=#{reservationId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	apply_code=#{applyCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestDate)" >	request_date=#{requestDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestText)" >	request_text=#{requestText},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseDate)" >	response_date=#{responseDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseName)" >	response_name=#{responseName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseText)" >	response_text=#{responseText},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseCategory)" >	response_category=#{responseCategory},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseSubcategory)" >	response_subcategory=#{responseSubcategory},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			create_date = now()
		</set>
    </insert>

    <update id="updateReservationQna" parameterType="ReservationQna">
        UPDATE reservation_qna
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	id=#{id},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	reservation_id=#{reservationId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	apply_code=#{applyCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestDate)" >	request_date=#{requestDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestText)" >	request_text=#{requestText},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseDate)" >	response_date=#{responseDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseName)" >	response_name=#{responseName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseText)" >	response_text=#{responseText},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseCategory)" >	response_category=#{responseCategory},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseSubcategory)" >	response_subcategory=#{responseSubcategory},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
		</where>
    </update>

    <update id="restoreReservationQna" parameterType="ReservationQna">
        UPDATE reservation_qna
           SET delete_yn='N'
        WHERE id = #{id}
    </update>

    <update id="deleteReservationQna" parameterType="ReservationQna">
        UPDATE reservation_qna
           SET delete_yn='Y'
        WHERE id = #{id}
    </update>

    <!--################################### ReservationUserConnect ###################################-->
	<select id="selectCountReservationUserConnect" parameterType="HashMap" resultType="Integer">
		SELECT count(reservation_id)
		  FROM reservation_user_connect
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
		</where>
	</select>

	<select id="selectListReservationUserConnect" parameterType="HashMap" resultType="LoginUser">
        SELECT @rownum:=@rownum+1 AS rownum,
               a.user_email, user_join_type, user_name,
               user_nationality, ifnull(user_mobile,'') user_mobile, user_tel,
               user_addr_zipcode, user_addr_extra,
               user_addr_jibun, user_addr_road, user_addr_detail,
               user_role, user_class_name, user_class_code,
               user_group_code,
               user_token_id, user_grade_id, user_grade,
               user_grade_start, user_grade_end,
               user_membership_id, user_membership_grade,
               user_membership_start, user_membership_end,
               user_verified_email, user_verified_mobile,
               user_ci, user_di, user_di_corp,
               user_join_date, user_modify_date,
               last_login_date, last_password_date,
               last_login_fail_count, account_status,
               naver_token, naver_email, naver_join_date,
               kakao_token, kakao_email, kakao_join_date,
               google_token, google_email, google_join_date,
               facebook_token, facebook_email, facebook_join_date,
               secondary_email, privacy_retention_days, mailing_yn,
               b.reservation_id, b.create_id, b.create_date
          FROM user a
          inner join reservation_user_connect b on a.user_email = b.user_email
          join (SELECT @rownum:= 0) rnum
         WHERE user_role = 'USER'
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and b.reservation_id=#{reservationId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and b.user_email=#{userEmail}	</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(type)">
                   <if test="type != 'ALL'">AND account_status = #{type}</if>
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
                   AND (
                           user_email LIKE CONCAT('%', #{searchKey}, '%')
                           OR user_name LIKE CONCAT('%', #{searchKey}, '%')
                           OR REPLACE(user_mobile, '-', '') LIKE CONCAT('%', REPLACE(#{searchKey}, '-', ''), '%')
                       )
               </if>
         ORDER BY user_join_date DESC
         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
    </select>

	<select id="selectOneReservationUserConnect" parameterType="HashMap" resultType="ReservationUserConnect">
		SELECT user_email, group_id, create_id, create_date
		  FROM reservation_user_connect
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
		</where>
	</select>

    <insert id="insertReservationUserConnect" parameterType="ReservationUserConnect">
        INSERT INTO reservation_user_connect SET
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	reservation_id=#{reservationId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			create_date=now()
    </insert>

    <delete id="deleteReservationUserConnect" parameterType="ReservationUserConnect">
		delete from reservation_user_connect
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
		</where>
	</delete>
</mapper>
