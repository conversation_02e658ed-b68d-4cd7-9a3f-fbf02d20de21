/*공통(중복제거)
* {margin: 0; padding: 0;}
:not(.ck-content *) > li {list-style: none;}
.ck-content ol,.ck-content ul {margin-left:20px}
a {text-decoration: none; color: inherit; cursor: pointer;}
body {font-family: "Pretendard"; overflow-x: hidden;}
.link{cursor: pointer}
.nolink{cursor: default}
.nolinked{cursor: default}
.nolinked:hover{color: #FFFFFF !important; font-weight: 300 !important;}
*/
/* 웹킷 기반 브라우저용 스크롤바 스타일링 */


/* 스크롤이 필요한 컨테이너에 적용할 클래스 */

/*font는 다른쪽에서*/

/*콘텐츠*/
.contents {
    width: 1400px;
    margin: 0 auto;
}

.contents_s_box {
    display: flex;
    justify-content: space-between;
}

.contents_s {
    width: 1020px;
}

.contents_side {
    width: calc(100% - 283px - 96px);
}

.side_menu {
    width: 283px;
}

.side_menu_ver {
    width: 265px;
}

.contents_side_ver {
    width: calc(100% - 265px - 114px);
}

#google_translate_element .goog-te-gadget-simple {border:none;}
#google_translate_element .goog-te-gadget-icon {display:none;}
#google_translate_element a {color:inherit;text-decoration:none;}
#google_translate_element img {margin-top:2px;}

/*반응형쿼리*/
@media screen and (max-width:1600px) {
    .contents {
        width: calc(100% - 40px);
    }
}

@media screen and (max-width:1024px) {
    .contents_s {
        width: 100%;
    }

    .contents_side_ver {
        width: calc(100% - 265px - 50px);
    }
}

@media screen and (max-width:768px) {
    .contents_s_box {
        flex-wrap: wrap;
    }

    .contents_side {
        width: 100%;
    }

    .side_menu {
        width: 100%;
        margin-top: 50px;
    }

    .side_menu_ver {
        width: 100%;
    }

    .contents_side_ver {
        width: 100%;
        margin-top: 50px;
    }
}

/* 개편 후 */
/*공통*/
* {margin: 0; padding: 0; box-sizing: border-box; font-family: 'Pretendard';}
:not(.ck-content *) > li {list-style: none;}
a {text-decoration: none; color: inherit; cursor: pointer;}
body {overflow-x: hidden;}
em{ font-style: normal;}
input, select, textarea{font-size:16px; outline: none;resize: none; border-radius: 0;}
input::placeholder, textarea::placeholder {color:#aaa;}
.link{cursor: pointer}
.nolink{cursor: default}
.nolinked{cursor: default}
.nolinked:hover{color: #FFFFFF !important; font-weight: 300 !important;}
figure img { width: 100%; height: unset;}
/*font*/
/*
@font-face {
    font-family: 'Pretendard';
    src: url('../../font/Pretendard-Thin.otf') format('woff2');
    font-weight: 100;
}

@font-face {
    font-family: 'Pretendard';
    src: url('../../font/Pretendard-ExtraLight.otf') format('woff2');
    font-weight: 200;
}

@font-face {
    font-family: 'Pretendard';
    src: url('../../font/Pretendard-Light.otf') format('woff2');
    font-weight: 300;
}

@font-face {
    font-family: 'Pretendard';
    src: url('../../font/Pretendard-Regular.otf') format('woff2');
    font-weight: 400;
}

@font-face {
    font-family: 'Pretendard';
    src: url('../../font/Pretendard-Medium.otf') format('woff2');
    font-weight: 500;
}

@font-face {
    font-family: 'Pretendard';
    src: url('../../font/Pretendard-SemiBold.otf') format('woff2');
    font-weight: 600;
}

@font-face {
    font-family: 'Pretendard';
    src: url('../../font/Pretendard-Bold.otf') format('woff2');
    font-weight: 700;
}

@font-face {
    font-family: 'Pretendard';
    src: url('../../font/Pretendard-ExtraBold.otf') format('woff2');
    font-weight: 800;
}

@font-face {
    font-family: 'Pretendard';
    src: url('../../font/Pretendard-Black.otf') format('woff2');
    font-weight: 900;
}

@font-face {
    font-family: 'Tenada';
    src: url('../../font/Tenada.ttf') format('woff2');
}
*/

/*콘텐츠*/
.contents {
    width: 1400px;
    margin: 0 auto;
}
.short-contents{
    width:1024px;
    margin:0 auto;
}
@media screen and (max-width:1400px) {
    .contents {
        width:calc(100% - 40px);
    }
}
@media screen and (max-width:1024px) {
    .short-contents{
        width:calc(100% - 40px);
    }
}


/*서브컨텐츠 최소 높이 설정*/
.sub-wrap{
    min-height:calc(100vh - 650px);
}
@media screen and (max-width:768px) {
    .sub-wrap{
        min-height:200px;
    }
}


/*반응형 블럭 처리*/
.pc-block{
    display:block;
}
.mo-block{
    display:none;
}
@media screen and (max-width:768px) {
    .pc-block{
        display:none;
    }
    .mo-block{
        display:block;
    }
}


/*버튼*/
.btn-black03,.btn-white01,.btn-black02 {
    text-align: center;
    margin-top:55px;
}
.btn-black03 button{
    padding:14px 90px;
    border-radius: 50px;
    color:#fff;
    font-size:20px;
    font-weight:600;
    background-color:#222;
    border:1px solid #000;
    cursor: pointer;
}
.btn-white01 button{
    padding:14px 30px;
    border:1px solid #444;
    background-color:#fff;
    color:#222;
    border-radius: 50px;
    cursor: pointer;
    font-size:16px;
    font-weight:400;
}
.btn-black02 button{
    padding:15px 90px;
    background-color:#222;
    color:#fff;
    text-align: center;
    font-size:18px;
    cursor: pointer;
    border:none;
    font-weight:600;
}
@media (hover : hover) {
    .btn-black03 button:hover{
        background-color:#fff;
        color:#222;
    }
    .btn-white01 button:hover{
        background-color:#ededed;
        color:#444;
        text-decoration: underline;
    }

}
@media screen and (max-width:768px) {
    .btn-black03 button,
    .btn-white01 button{
        width:100%;
        font-size:16px;
        padding:14px 10px;
    }
}

/*반응형쿼리*/
@media screen and (max-width:1600px) {

}

/*  플로팅메뉴  */
.floating-area{
    position:fixed;
    bottom:50px;
    right:0;
    z-index: 99;
}
.floating-area a{
    padding:10px 25px 10px 30px;
    font-size:15px;
    font-weight:600;
    line-height:19px;
    border-top-left-radius: 50px;
    border-bottom-left-radius: 50px;
    display:flex;
    flex-direction: column;
    align-items: center;
    border:1px solid #444;
    margin-bottom:6px;
    border-right:unset;
}
.floating-area a.apply-btn{
    background-color:#05A54B;
    color:#fff;
}
.floating-area a.certify-btn{
    background-color:#FFC960;
    color:#333;
}
@media (hover : hover) {
    .floating-area a:hover{
        background-color:#f0f0f0;
        color:#444;
    }
}
@media screen and (max-width:768px) {
    .floating-area{
        bottom:20px;
    }
    .floating-area a p{
        display:none;
    }
    .floating-area a{
        width:55px;
        height:48px;
    }
    .floating-area .apply-btn {
        background:url(/images/icon/floating-icon-03.svg) center center no-repeat;
    }
    .floating-area .certify-btn {
        background:url(/images/icon/floating-icon-02.svg) center center no-repeat;
    }
}