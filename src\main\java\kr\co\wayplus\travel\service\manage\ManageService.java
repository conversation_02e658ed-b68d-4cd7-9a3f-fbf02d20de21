package kr.co.wayplus.travel.service.manage;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import kr.co.wayplus.travel.mapper.manage.ManageMapper;
import kr.co.wayplus.travel.model.ManageMenu;
import kr.co.wayplus.travel.model.MenuConnectBoard;
import kr.co.wayplus.travel.model.MenuConnectPlace;

@Service
public class ManageService {

	@Value("${upload.file.path}")
	private String imageUploadPath;
    private final ManageMapper mapper;
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    public ManageService(ManageMapper mapper) {
        this.mapper = mapper;
    }

	public ArrayList<ManageMenu> selectListManageMenu(HashMap<String, Object> paramMap) {
		return mapper.selectListManageMenu(paramMap);
	}
	public ManageMenu selectOneManageMenu(HashMap<String, Object> paramMap) {
		return mapper.selectOneManageMenu(paramMap);
	}

	public void insertManageMenu(ManageMenu menu) throws SQLException {
		mapper.insertManageMenu(menu);
	}

	public void updateManageMenu(ManageMenu menu) throws SQLException {
		mapper.updateManageMenu(menu);
	}

	public void deleteManageMenu(ManageMenu menu) throws SQLException {
		mapper.deleteManageMenu(menu);
	}

	public void selectList(HashMap<String, Object> paramMap, HashMap<String, Object> retMap, Integer userAuthId) {
		ArrayList<ManageMenu> listTopMenu = new ArrayList<ManageMenu>(); /*최상위 메뉴 뽑기*/
		HashMap<String,ManageMenu> _map = new HashMap<String, ManageMenu>(); /*색인용 Map*/
		HashMap<String,ArrayList<ManageMenu>> _mapUpper = new HashMap<String, ArrayList<ManageMenu>>(); /*상위 메뉴 색인용 Map*/

		if( userAuthId != null ) {
			paramMap.put("authId", userAuthId);
		}

		ArrayList<ManageMenu> list = this.selectListManageMenu(paramMap);

		for (ManageMenu _menu : list) {
			_map.put(_menu.getMenuId().toString(), _menu);

			if(_menu.getUpperMenuId() != null) {
				ArrayList<ManageMenu> subList = null;
				if( _mapUpper.containsKey(_menu.getUpperMenuId().toString()) ) {
					subList = _mapUpper.get( _menu.getUpperMenuId().toString() );
				} else {
					subList = new ArrayList<ManageMenu>();
				}
				_mapUpper.put( _menu.getUpperMenuId().toString(), subList );
				subList.add( _menu );
			}
		}

		for (ManageMenu _tmenu : list) {
			Long key = _tmenu.getMenuId();

			if(key != null)
    			if( _mapUpper.containsKey( key.toString() ) ) {
    				ArrayList<ManageMenu> menuList = _mapUpper.get( key.toString());
    				Collections.sort( menuList );
    				_tmenu.setListChildMenuL( menuList );
    			}
		}

		for (ManageMenu _menu : list) {
			if(_menu.getUpperMenuId() == null) {
				listTopMenu.add(_menu);
			}
		}
		Collections.sort( listTopMenu );

		retMap.put("data",listTopMenu);
		retMap.put("list",list);

	}

	public void selectList(HashMap<String,Object> paramMap, HashMap<String, Object> retMap) throws Exception {
		this.selectList(paramMap, retMap, null);
	}

	public List<HashMap<String, Object>> getJstreeList() throws Exception {
		ArrayList<HashMap<String, Object>> data = new ArrayList<HashMap<String, Object>>();

		HashMap<String, Object> retMap = new HashMap<String, Object>();

		this.selectList(null, retMap);

		ArrayList<ManageMenu> listTopMenu = (ArrayList<ManageMenu>) retMap.get("data");

		for(ManageMenu menu : listTopMenu) {
			setChildList("#", menu, data);
		}

		return data;

	}
	public void setChildList(
			String parentId,
			ManageMenu menu,
			ArrayList<HashMap<String, Object>> data) {

		HashMap<String, Object> map = new HashMap<String, Object>();
		map.put("parent", parentId);
		if(menu.getListChildMenuL() != null) {
			for(ManageMenu childMenu : menu.getListChildMenuL()) {
				setChildList(String.valueOf(menu.getMenuId()), childMenu, data);
			}
		}
		map.put("id", menu.getMenuId());
		map.put("text", menu.getMenuName());
		Map<String, Object> status = new HashMap<String, Object>();
		status.put("opened", true);
		map.put("state", status);

		data.add(map);
		//return data;
	}

	/*################################################statistics################################################*/
	public ArrayList<Map<String, Object>> selectListStatisticDate(Map<String, Object> paramMap) {
		return mapper.selectListStatisticDate(paramMap);
	}
	public Map<String, Object> selectListStatisticInfo(Map<String, Object> paramMap) {
		return mapper.selectListStatisticInfo(paramMap);
	}
	/*################################################statistics################################################*/
	public int selectCountMenuConnectBoard(HashMap<String, Object> paramMap) {
		return mapper.selectCountMenuConnectBoard(paramMap);
	}
	public MenuConnectBoard selectOneMenuConnectBoard(HashMap<String, Object> paramMap) {
		return mapper.selectOneMenuConnectBoard(paramMap);
	}
	public void insertMenuConnectBoard(MenuConnectBoard data) throws SQLException {
		mapper.insertMenuConnectBoard(data);
	}

	public void deleteMenuConnectBoard(HashMap<String, Object> paramMap) throws SQLException {
		mapper.deleteMenuConnectBoard(paramMap);
	}

	/*################################################statistics################################################*/
	public int selectCountMenuConnectPlace(HashMap<String, Object> paramMap) {
		return mapper.selectCountMenuConnectPlace(paramMap);
	}
	public MenuConnectPlace selectOneMenuConnectPlace(HashMap<String, Object> paramMap) {
		return mapper.selectOneMenuConnectPlace(paramMap);
	}
	public void insertMenuConnectPlace(MenuConnectPlace data) throws SQLException {
		mapper.insertMenuConnectPlace(data);
	}

	public void deleteMenuConnectPlace(HashMap<String, Object> paramMap) throws SQLException {
		mapper.deleteMenuConnectPlace(paramMap);
	}

}
