package kr.co.wayplus.travel.web.manage;


import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.MultipartRequest;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.BoardAttachFile;
import kr.co.wayplus.travel.model.Islandlife;
import kr.co.wayplus.travel.model.IslandlifeAttachFile;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.SortData;
import kr.co.wayplus.travel.service.front.PageService;
import kr.co.wayplus.travel.service.manage.IslandlifeManageService;
import kr.co.wayplus.travel.util.FileInfoUtil;

@Controller
@RequestMapping("/manage/islandlife")
public class IslandlifeManageController extends BaseController  {

    @Value("${cookie-set.domain}")
    private String cookieDomain;
    @Value("${cookie-set.prefix}")
    private String cookieName;

	@Value("${upload.file.path}")
	String externalImageUploadPath;

	final String addPath = "islandlife/";

    @Value("${upload.file.max-size}")
    int maxFileSize;

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final PageService pageService; /*common하게*/
    private final IslandlifeManageService svcIslandlife; /*common하게*/

    @Autowired
    public IslandlifeManageController(
    		PageService pageService,
    		IslandlifeManageService svcIslandlife) {
        this.pageService = pageService;
        this.svcIslandlife = svcIslandlife;
    }

	@GetMapping("/list")
	public ModelAndView islandlifeList(  ){
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> paramMap = new HashMap<>();
		mav.addObject("p", paramMap);
		mav.setViewName("manage/sub/Islandlife/list");
		return mav;
	}

	@PostMapping("/list")
    @ResponseBody
    public HashMap<String,Object> islandlife_list_ajax(
    		HttpServletRequest request,
    		Islandlife content,
    		@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length){
    	HashMap<String,Object> retMap = new HashMap<String, Object>();

    	try {
    		HashMap<String,Object> paramMap = new HashMap<String, Object>();

    		//svcContents.selectList(paramMap, retMap);

    		List<SortData> listSort = getListOrder(request);
    		paramMap.put("listSort", listSort);

    		paramMap.put("sort", "menuName");
    		paramMap.put("sortOrder", "asc");

    		if(length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
    		}

    		//if( content.getUseYn() != null )    paramMap.put("useYn", content.getUseYn());
    		if( content.getDeleteYn() != null ) paramMap.put("deleteYn", content.getDeleteYn());

    		int totalCount = svcIslandlife.selectCountIslandlife(paramMap);

    		retMap.put("recordsTotal", totalCount);
    		retMap.put("recordsFiltered", totalCount);
    		retMap.put("data", svcIslandlife.selectListIslandlife(paramMap));

    		retMap.put("result","success");
    		retMap.put("message","처리 성공");

    		retMap.put("result","success");
    		retMap.put("message","처리 성공");
		} catch (Exception e) {
			retMap.put("result","ERROR");
			retMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
		}

        return retMap;
    }

	@PostMapping("/save")
    @ResponseBody
    public HashMap<String, Object> islandlife_save_ajax(
    		@RequestParam(value="mode", defaultValue="I") String mode,
    		Islandlife IL,
    		MultipartHttpServletRequest request
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser user = (LoginUser)_user;
        		HashMap<String, Object> paramMap = new HashMap<>();

        		String thumbnailUrl = "";

	    		if(mode.equals("I")) {
	    			IL.setCreateId(user.getUserEmail());
	    			svcIslandlife.insertIslandlife(IL);
	    		} else {
	    			IL.setLastUpdateId(user.getUserEmail());
	    			svcIslandlife.updateIslandlife(IL);
	    		}

	    		List<MultipartFile> multipartFiles = null;

	    		Object obj = request.getParameterValues("deletes");

	    		if(request.getParameterValues("deletes") != null) {
	    			String[] deletes = request.getParameterValues("deletes");

	    			for (String strDeletes : deletes) {
	    				logger.debug(strDeletes);
	    				paramMap.clear();
	    				paramMap.put("fileId", strDeletes);

	    				IslandlifeAttachFile _old = svcIslandlife.selectOneIslandlifeAttachFile( paramMap );
	    				if(_old != null) {
	    					FileInfoUtil.deleteImageFile_real(_old); /*실제 이미지 파일 제거*/
	    					svcIslandlife.deleteIslandlifeAttachFile(_old);
	    				}
					}
	    		}

	    		if(request.getFile("attachs") != null) {
	    			multipartFiles = request.getFiles("attachs");

	    			MultipartFile multipartFilesThumbnail = request.getFile("thumbnail");

	                if (multipartFiles.size() > 0) {
	                	File file = new File(externalImageUploadPath + addPath);
	                	if (!file.exists()) file.mkdirs();

	                    for (MultipartFile multipartFile : multipartFiles) {
	                        String uploadName = UUID.randomUUID().toString();
	                        multipartFile.transferTo(new File(externalImageUploadPath+ addPath + uploadName));
	                        logger.debug("User Question File Uploaded : " + multipartFile.getOriginalFilename());

	                        IslandlifeAttachFile baf = new IslandlifeAttachFile();
	                        baf.setIslandlifeId(IL.getId());
	                        baf.setServiceType("thumbnail");
	                        baf.setUploadPath(externalImageUploadPath+ addPath);
	                        baf.setUploadFilename(uploadName);
	                        baf.setOriginFilename(multipartFile.getOriginalFilename());
	                        baf.setFileSize((int) multipartFile.getSize());
	                        baf.setFileMimetype(multipartFile.getContentType());
	                        if (multipartFile.getOriginalFilename().contains(".")) {
	                            baf.setFileExtension(multipartFile.getOriginalFilename().substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
	                        }

	                        paramMap.clear();
	                        paramMap.put("islandlifeId", IL.getId());

	                        if (user != null) {
	                            baf.setUploadId(String.valueOf(user.getUserEmail()));
	                        }

	                        svcIslandlife.insertIslandlifeAttachFile(baf);

	                        if(multipartFilesThumbnail != null) {
	                        	if(multipartFile.getOriginalFilename().equals( multipartFilesThumbnail.getOriginalFilename() )) {
			            			thumbnailUrl = "/upload/"+ addPath +uploadName;
	                        	}
	                        }
	                    }
	                }
	    		}

	    		boolean isThum = false;

	    		if(request.getParameter("thumbnail") != null ) {
	    			isThum = true;
        			thumbnailUrl = request.getParameter("thumbnail");
	    		} else if( thumbnailUrl.length() != 0 ) {
	    			isThum = true;
	    		}

        		if(isThum) {
        			IL.setIslandlifeThumbnail(thumbnailUrl);
        			svcIslandlife.updateIslandlife(IL);
        		}

	    		retrunMap.put("result", "success");
	    		retrunMap.put("message", "처리가 완료 되었습니다.");
        	} else {
        		retrunMap.put("result", "fail");
	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
        	}
		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			retrunMap.put("info", e.getMessage());
			logger.debug(e.getCause().getLocalizedMessage());
		}
        return retrunMap;
    }

    @PostMapping("/delete")
    @ResponseBody
    public HashMap<String, Object> islandlife_delete_ajax(
    		Islandlife IL
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		svcIslandlife.deleteIslandlife(IL);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
		}

        return retrunMap;
    }

    @GetMapping("/attchlist")
    @ResponseBody
    public HashMap<String, Object> islandlife_attachlist(@RequestParam(value="id", defaultValue="0") String id){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		HashMap<String, Object> paramMap = new HashMap<>();
    		paramMap.put("islandlifeId", id);
    		retrunMap.put("listAttach",  svcIslandlife.selectListIslandlifeAttachFile(paramMap));

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
		}

        return retrunMap;
    }
	@PostMapping("/ck-image-upload")
	@ResponseBody
	public HashMap<String, Object> ckImageUpload(MultipartRequest request, @RequestParam(value="service_type", defaultValue="ck-editor") String serviceType){
		HashMap<String, Object> resultMap = new HashMap<>();
		LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		try{
			MultipartFile multipartFile = null;
			if(request.getFile("upload") != null){
				multipartFile = request.getFile("upload");
				if(multipartFile.getSize() > 0){
					if(multipartFile.getSize() > maxFileSize){
						throw new Exception(String.valueOf(maxFileSize/1024/1024) + "MB 이하의 파일만 첨부 가능합니다.");
					}

					String uploadName = UUID.randomUUID().toString();

					File file = new File(externalImageUploadPath+addPath);
					if(!file.exists()) file.mkdirs();

					multipartFile.transferTo(new File(externalImageUploadPath+addPath+uploadName));
					logger.debug("File Uploaded : " + multipartFile.getOriginalFilename());

					IslandlifeAttachFile baf = new IslandlifeAttachFile();
                    //baf.setBoardId(bc.getBoardId());
                    //baf.setContentId(bc.getId());
                    baf.setServiceType(serviceType);
                    baf.setUploadPath(externalImageUploadPath+ addPath);
                    baf.setUploadFilename(uploadName);
                    baf.setOriginFilename(multipartFile.getOriginalFilename());
                    baf.setFileSize((int) multipartFile.getSize());
                    baf.setFileMimetype(multipartFile.getContentType());
                    if (multipartFile.getOriginalFilename().contains(".")) {
                        baf.setFileExtension(multipartFile.getOriginalFilename().substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
                    }

                    if (user != null) {
                        baf.setUploadId(String.valueOf(user.getUserEmail()));
                    }

                    svcIslandlife.insertIslandlifeAttachFile(baf);

					resultMap.put("result", "success");
					resultMap.put("url", "/upload/"+addPath+ uploadName);
					resultMap.put("file_id", baf.getFileId());
				}
			}else{
				throw new Exception("첨부된 파일이 없습니다.");
			}
		}catch (Exception e){
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}
}
