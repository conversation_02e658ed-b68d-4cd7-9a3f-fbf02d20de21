package kr.co.wayplus.travel.service.manage;

import kr.co.wayplus.travel.mapper.manage.BadgeManageMapper;
import kr.co.wayplus.travel.model.BadgeAcquireHistory;
import kr.co.wayplus.travel.model.BadgeAttachImage;
import kr.co.wayplus.travel.model.BadgeContents;
import kr.co.wayplus.travel.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
public class BadgeManageService {

	@Value("${upload.file.path}")
	private String imageUploadPath;
    private final BadgeManageMapper mapper;
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    public BadgeManageService(BadgeManageMapper mapper) {
        this.mapper = mapper;
    }

    public int selectCountBadgeContents(HashMap<String, Object> paramMap) {
        return mapper.selectCountBadgeContents(paramMap);
    }

    public List<BadgeContents> selectListBadgeContents(HashMap<String, Object> paramMap) {
        return mapper.selectListBadgeContents(paramMap);
    }

    public void writeBadgeAttachImage(BadgeAttachImage attachImage) {
        mapper.insertBadgeAttachImage(attachImage);
    }

    public void writeBadgeContents(BadgeContents badgeContents) {
        mapper.insertBadgeContents(badgeContents);
    }

    public BadgeContents selectOneBadgeContents(HashMap<String, Object> param) {
        return mapper.selectOneBadgeContents(param);
    }

    public void modifyBadgeContents(BadgeContents badgeContents) {
        mapper.updateBadgeContents(badgeContents);
    }

    public void updateBadgeContentsDelete(HashMap<String, Object> param) {
        mapper.updateBadgeContentsDelete(param);
    }

    public void writeBadgeAcquireHistory(BadgeAcquireHistory badgeAcquireHistory) {
        mapper.insertBadgeAcquireHistory(badgeAcquireHistory);
    }

    public int selectCountBadgeAcquireHistory(HashMap<String, Object> param) {
        return mapper.selectCountBadgeAcquireHistory(param);
    }

    public List<BadgeAcquireHistory> selectListBadgeAcquireHistory(HashMap<String, Object> param) {
        return mapper.selectListBadgeAcquireHistory(param);
    }

    public void removeBadgeAcquireHistory(BadgeAcquireHistory badgeAcquireHistory) {
        mapper.updateBadgeAcquireHistory(badgeAcquireHistory);
    }

    public ArrayList<BadgeContents> selectBadgeListByAutomationType(HashMap<String, Object> param) {
        return mapper.selectBadgeListByAutomationType(param);
    }

    /**
     * 로그인 부여 뱃지를 확인하여 부여한다.
     * @param user
     */
    public void createLoginBadge(LoginUser user) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("automationType", "login");
        ArrayList<BadgeContents> loginBadgeList = selectBadgeListByAutomationType(param);

        if(loginBadgeList != null){
            //총 로그인 횟수 체크
            int loginCount = mapper.selectTotalLoginCount(user);

            //연속 로그인 횟수 체크
            int maxContinuousLoginCount = mapper.selectContinuousLoginCount(user);

            for (BadgeContents badge : loginBadgeList) {
                //로그인 횟수에 따른 뱃지 부여
                if(badge.getBadgeAutomationType().equals("login") && loginCount >= badge.getBadgeAutomationCount()){
                    badgeAcquire(user.getUserEmail(),user.getUserEmail(), badge);
                }

                //연속 로그인 횟수에 따른 뱃지 부여
                if(badge.getBadgeAutomationType().equals("continuous-login") && maxContinuousLoginCount >= badge.getBadgeAutomationCount()){
                    badgeAcquire(user.getUserEmail(),user.getUserEmail(), badge);
                }
            }
        }
    }

    /**
     * 사용자에게 뱃지를 부여한다.
     * @param userEmail
     * @param badge
     */
    public void badgeAcquire (String userEmail,String createId, BadgeContents badge){
        HashMap<String, Object> param = new HashMap<>();
        param.put("userEmail", userEmail);
        param.put("badgeId", badge.getBadgeId());

        int badgeAcquireCount = selectCountBadgeAcquireHistory(param);
        if(badgeAcquireCount > 0){
            logger.debug("User: %s, BadgeId: %s, BadgeName: %s. 이미 획득한 뱃지입니다.", userEmail, badge.getBadgeId(), badge.getBadgeName());
        }else{
            logger.debug("User: %s, BadgeId: %s, BadgeName: %s. 뱃지를 부여합니다.", userEmail, badge.getBadgeId(), badge.getBadgeName());
            BadgeAcquireHistory badgeAcquireHistory = new BadgeAcquireHistory();
            badgeAcquireHistory.setBadgeId(badge.getBadgeId());
            badgeAcquireHistory.setUserEmail(userEmail);
            badgeAcquireHistory.setCreateId(createId);
            writeBadgeAcquireHistory(badgeAcquireHistory);
        }
    }

    /**
     * 사용자에게 뱃지 부여를 취소한다.
     * @param userEmail
     * @param badge
     */
    public void badgeCancel (String userEmail, BadgeContents badge){
        logger.debug("User: %s, BadgeId: %s, BadgeName: %s. 뱃지를 취소합니다.", userEmail, badge.getBadgeId(), badge.getBadgeName());
        BadgeAcquireHistory badgeAcquireHistory = new BadgeAcquireHistory();
        badgeAcquireHistory.setBadgeId(badge.getBadgeId());
        badgeAcquireHistory.setUserEmail(userEmail);
        badgeAcquireHistory.setDeleteId(userEmail);
        mapper.updateBadgeAcquireHistory(badgeAcquireHistory);
    }
}
