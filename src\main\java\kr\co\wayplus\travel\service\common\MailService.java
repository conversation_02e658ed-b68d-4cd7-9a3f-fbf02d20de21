package kr.co.wayplus.travel.service.common;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import kr.co.wayplus.travel.mapper.common.MailMapper;
import kr.co.wayplus.travel.model.MailLog;
import kr.co.wayplus.travel.model.MailRecivedUser;

@Service
public class MailService {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final MailMapper mapper;

    @Value("${upload.file.path}")
	String externalImageUploadPath;

    final String addPath = "mail/";


    @Autowired
    public MailService( MailMapper mapper ) {
    	this.mapper = mapper;
    }

    private String numberWithUncomma(String number) {
        return number.replaceAll("[^0-9.-]", "");
    }

    //	<!--################################### MailAttachs ###################################-->제휴사
	public int selectCountMailLog(HashMap<String, Object> paramMap) {
		return mapper.selectCountMailLog(paramMap);
	}

	public ArrayList<MailLog> selectListMailLog(HashMap<String, Object> paramMap) {
		return mapper.selectListMailLog(paramMap);
	}
	public MailLog selectOneMailLog(HashMap<String, Object> paramMap) {
		return mapper.selectOneMailLog(paramMap);
	}
	public void updateMailLog(MailLog bc) throws SQLException{
		mapper.updateMailLog(bc);
	}

	//	<!--################################### mailRecivedUser ###################################-->
	public int selectCountMailRecivedUser(HashMap<String, Object> paramMap) {
		return mapper.selectCountMailRecivedUser(paramMap);
	}
	public ArrayList<MailRecivedUser> selectListMailRecivedUser(HashMap<String, Object> paramMap) {
		return mapper.selectListMailRecivedUser(paramMap);
	}
	public void insertMailRecivedUser(MailRecivedUser mru) throws SQLException{
		mapper.insertMailRecivedUser(mru);
	}
	public void deleteMailRecivedUser(MailRecivedUser mru) throws SQLException{
		mapper.deleteMailRecivedUser(mru);
	}


}
