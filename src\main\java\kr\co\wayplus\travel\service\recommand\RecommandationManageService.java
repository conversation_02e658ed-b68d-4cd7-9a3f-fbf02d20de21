package kr.co.wayplus.travel.service.recommand;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import kr.co.wayplus.travel.mapper.manage.ProductManageMapper;
import kr.co.wayplus.travel.mapper.manage.UserManageMapper;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.ProductRecommandInfo;
import kr.co.wayplus.travel.model.UserCustomerOrderList;
import kr.co.wayplus.travel.model.recommandation.RecommendationKey;

@Service
public class RecommandationManageService {
	private final Logger logger = LoggerFactory.getLogger(getClass());

	@Value("${spring.profiles.active}")
    private String ACTIVE_PROFILE;

    private final UserManageMapper userManageMapper;
    private final ProductManageMapper productManageMapper;

    public RecommandationManageService(
    		UserManageMapper mapper1,
    		ProductManageMapper mapper2) {
        this.userManageMapper = mapper1;
        this.productManageMapper = mapper2;
    }


    @Transactional
	public HashMap<String, Object> updateRecommendations() {
    	HashMap<String, Object> retrunMap = new HashMap<>();
    	HashMap<String, Object> params = new HashMap<>();
    	params.put("isUserRole", true);

		ArrayList<LoginUser> userList = userManageMapper.selectUserList( params );
		//HashMap<RecommendationKey, ArrayList<Purchase>> purchasesBySegment = new HashMap<>();
		HashMap<RecommendationKey, ArrayList<UserCustomerOrderList>> purchasesBySegment = new HashMap<>();

		LocalDateTime now = LocalDateTime.now();
		DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		String formatDate1 = now.format(formatter1);
		try {
			ArrayList<RecommendationKey> _plist1 = new ArrayList<RecommendationKey>();
			ArrayList<ProductRecommandInfo> _plist2 = new ArrayList<ProductRecommandInfo>();

			// 1. 사용자 구매 데이터 세그먼트별 집계
			for(LoginUser user : userList) {

				RecommendationKey key = new RecommendationKey(
					user.getUserEmail(),
					user.getAgeGroup(),
					user.getUserGender(),
					user.getJoinReasonType()
	            );

				_plist1.add(key);

				HashMap<String, Object> param = new HashMap<String, Object>();
				param.put("userEmail", user.getUserEmail() );
				ArrayList<UserCustomerOrderList> ucol = userManageMapper.selectListUserCustomerOrderList( param );


				if( ucol.size() > 0 ) {
					purchasesBySegment.computeIfAbsent(key, k -> new ArrayList<>())
		            	//.addAll(user.getPurchases())
						.addAll( ucol );
				}

				//System.out.println( key );
			}

			// 2. 세그먼트별 추천 점수 계산 및 저장
			for (Map.Entry<RecommendationKey, ArrayList<UserCustomerOrderList>> entry : purchasesBySegment.entrySet()) {
	            RecommendationKey key = entry.getKey();
	            ArrayList<UserCustomerOrderList> purchases = entry.getValue();

	            Map<String, Double> scores = calculateProductScores(purchases);

	            for (Map.Entry<String, Double> scoreEntry : scores.entrySet()) {
	                ProductRecommandInfo recommendation = new ProductRecommandInfo();
	                recommendation.setRecommandDate(formatDate1);
	                recommendation.setProductSerial(scoreEntry.getKey());
	                recommendation.setAgeGroup(key.getAgeGroup());
	                recommendation.setGender(key.getGender());
	                recommendation.setPurpose(key.getPurpose());
	                recommendation.setScore(scoreEntry.getValue());

	                //recommendationRepository.save(recommendation);
	                //System.out.println( recommendation );

	                _plist2.add(recommendation);

	                productManageMapper.insertProductRecommandInfo( recommendation );
	            }
	        }

			System.out.println( "# 사용자 목록" );
			for (RecommendationKey key : _plist1) {
				System.out.println( key );
			}
			System.out.println( "# 구매 목록" );
			for (ProductRecommandInfo key : _plist2) {
				System.out.println( key );
			}

			retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}
		return retrunMap;
	}


    private HashMap<String, Double> calculateProductScores(ArrayList<UserCustomerOrderList> purchases) {
    	HashMap<String, Double> scores = new HashMap<>();

        // 구매 빈도, 최근성, 사용자 평가 등을 고려한 점수 계산
        for (UserCustomerOrderList purchase : purchases) {
        	//ProductInfo product = purchase.getProduct();
        	String productSerial = purchase.getProductSerial();
            double score = calculateScore(purchase);

            scores.merge(productSerial, score, Double::sum);
        }

        return scores;
    }

    private double calculateScore(UserCustomerOrderList purchase) {
        double score = 1.0;

        // 최근성 가중치
        long daysSincePurchase = ChronoUnit.DAYS.between( convertDate( purchase.getCreateDate() ), LocalDateTime.now() );
        score *= Math.exp(-0.01 * daysSincePurchase);

        // 구매 금액 가중치
        score *= Math.log10( purchase.getProductAmt() + 1 );

        // 리뷰 평점 가중치
//        if (purchase.getReview() != null) {
//            score *= (purchase.getReview().getRating() / 5.0);
//        }

        return score;
    }

    private Integer calculateAgeGroup(Integer age) {
    	if( age != null) {
    		return age / 10 * 10;
    	} else {
    		return null;
    	}
    }

    private LocalDateTime convertDate( String dateStr ) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return LocalDateTime.parse(dateStr, formatter);
    }
}
