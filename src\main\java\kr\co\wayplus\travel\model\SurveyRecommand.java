package kr.co.wayplus.travel.model;

import java.util.ArrayList;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@Data
public class SurveyRecommand extends CommonDataSet {
	private Integer id;	//고유 번호
	private Integer menuId;
	private String recommandTitle;
	private String recommandSmallName;
	private String recommandBigName;
	private String recommandMidName;
	private String recommandBigNameColor;
	private String recommandContents;
	private String recommandContentsBgColor;

	private SurveyImage file;


	private String menuName;
}
