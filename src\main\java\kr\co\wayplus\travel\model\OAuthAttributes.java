package kr.co.wayplus.travel.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.util.Map;

import org.apache.commons.httpclient.util.HttpURLConnection;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

@Getter
@Setter
public class OAuthAttributes {

    private Map<String, Object> attributes;
    private String nameAttributeKey;
    private String id;
    private String name;
    private String email;
    private String phoneNumber;
    private String birthday;
    private String gender;
    
    @Builder
    public OAuthAttributes(Map<String, Object> attributes, String nameAttributeKey, String id, String name, String email){
        this.attributes = attributes;
        this.nameAttributeKey = nameAttributeKey;
        this.id = id;
        this.name = name;
        this.email = email;
    }
    
    public OAuthAttributes addPhoneNumber(String phoneNumber) {
    	this.phoneNumber = phoneNumber;
    	return this;
    }
    
    public OAuthAttributes addBirthday(String birthday) {
    	this.birthday = birthday;
    	return this;
    }
    
    public OAuthAttributes addGender(String gender) {
    	this.gender = gender;
    	return this;
    }
    

    public static OAuthAttributes build(String registrationId, String userNameAttributeName, Map<String, Object> attributes){
        switch (registrationId){
            case "kakao":
                return kakaoAttributes(userNameAttributeName, attributes);
            case "naver":
                return naverAttributes(userNameAttributeName, attributes);
            case "google": default:
                return googleAttributes(userNameAttributeName, attributes);
        }
    }

    private static OAuthAttributes kakaoAttributes(String userNameAttributeName, Map<String, Object> attributes) {
        Map<String,Object> response = (Map<String, Object>) attributes.get("kakao_account");
        Map<String, Object> profile = (Map<String, Object>) response.get("profile");
        return OAuthAttributes.builder()
                .id(attributes.get("id").toString())
                .name((String)profile.get("nickname"))
                .email((String)response.get("email"))
                .attributes(attributes)
                .nameAttributeKey(userNameAttributeName)
                .build();
    }

    private static OAuthAttributes naverAttributes(String userNameAttributeName, Map<String, Object> attributes) {
        Map<String,Object> response = (Map<String, Object>) attributes.get("response");
        return OAuthAttributes.builder()
                .id((String)response.get("id"))
                .name((String)response.get("name"))
                .email((String)response.get("email"))
                .attributes(attributes)
                .nameAttributeKey(userNameAttributeName)
                .build();
    }

    private static OAuthAttributes googleAttributes(String userNameAttributeName, Map<String, Object> attributes) {
    	String phoneNumber = null;
        String birthday = null;
        String gender = null;
        
        //System.out.println(attributes.get("access_token").toString() );
        
        //getPeopleApiInfo( attributes.get("sub").toString(), attributes);
        
        
//    	if (attributes.containsKey("phoneNumbers") && attributes.get("phoneNumbers") instanceof Map) {
//            Map<String, Object> phoneNumbers = (Map<String, Object>) attributes.get("phoneNumbers");
//            if (phoneNumbers.containsKey("value")) {
//                phoneNumber = (String) phoneNumbers.get("value");
//            }
//        }
//        
//        if (attributes.containsKey("birthdays") && attributes.get("birthdays") instanceof Map) {
//            Map<String, Object> birthdays = (Map<String, Object>) attributes.get("birthdays");
//            if (birthdays.containsKey("date") && birthdays.get("date") instanceof Map) {
//                Map<String, Object> date = (Map<String, Object>) birthdays.get("date");
//                if (date.containsKey("year") && date.containsKey("month") && date.containsKey("day")) {
//                    birthday = date.get("year") + "-" + date.get("month") + "-" + date.get("day");
//                }
//            }
//        }
//        
//        if (attributes.containsKey("genders") && attributes.get("genders") instanceof Map) {
//            Map<String, Object> genders = (Map<String, Object>) attributes.get("genders");
//            if (genders.containsKey("value")) {
//                gender = (String) genders.get("value");
//            }
//        }
    	
        return OAuthAttributes.builder()
                .id((String) attributes.get("sub"))
                .name((String) attributes.get("name"))
                .email((String) attributes.get("email"))
                .attributes(attributes)
                .nameAttributeKey(userNameAttributeName)
                .build()
                .addPhoneNumber(phoneNumber)
                .addBirthday(birthday)
                .addGender(gender);
    }
    
}
