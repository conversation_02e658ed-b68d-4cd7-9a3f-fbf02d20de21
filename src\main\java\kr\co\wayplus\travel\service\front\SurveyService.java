package kr.co.wayplus.travel.service.front;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import kr.co.wayplus.travel.mapper.front.SurveyMapper;
import kr.co.wayplus.travel.model.PlaceSpot;
import kr.co.wayplus.travel.model.Survey;
import kr.co.wayplus.travel.model.SurveyImage;
import kr.co.wayplus.travel.model.SurveyQuestion;
import kr.co.wayplus.travel.model.SurveyQuestionAnswer;
import kr.co.wayplus.travel.model.SurveyRecommand;
import kr.co.wayplus.travel.model.SurveyResult;

@Service
public class SurveyService {

    private final SurveyMapper mapper;

    @Autowired
    public SurveyService(SurveyMapper mapper) {
        this.mapper = mapper;
    }

//	<!--################################### Survey ###################################-->
	public Survey selectOneLastSurvey(HashMap<String, Object> paramMap) {
		return mapper.selectOneLastSurvey(paramMap);
	}
//	<!--################################### SurveyQuestion###################################-->
	public int selectCountLastSurveyQuestion(HashMap<String, Object> paramMap) {
		return mapper.selectCountLastSurveyQuestion(paramMap);
	}
	public ArrayList<SurveyQuestion> selectListLastSurveyQuestion(HashMap<String, Object> paramMap) {
		return mapper.selectListLastSurveyQuestion(paramMap);
	}
//	<!--################################### SurveyQuestionAnswer ###################################-->
    public int selectCountSurveyQuestionAnswer(HashMap<String, Object> paramMap) {
    	return mapper.selectCountSurveyQuestionAnswer(paramMap);
    }
    public ArrayList<SurveyQuestionAnswer> selectListSurveyQuestionAnswer(HashMap<String, Object> paramMap) {
    	return mapper.selectListSurveyQuestionAnswer(paramMap);
    }
	public SurveyQuestionAnswer selectOneSurveyQuestionAnswer(HashMap<String, Object> paramMap) {
		return mapper.selectOneSurveyQuestionAnswer(paramMap);
	}
//	<!--################################### SurveyResult###################################-->
	public int selectCountSurveyResult(HashMap<String, Object> paramMap) {
		return mapper.selectCountSurveyResult(paramMap);
	}
	public ArrayList<SurveyResult> selectListSurveyResult(HashMap<String, Object> paramMap){
		return mapper.selectListSurveyResult(paramMap);
	}
	public SurveyResult selectOneSurveyResult(HashMap<String, Object> paramMap) {
		return mapper.selectOneSurveyResult(paramMap);
	}
	public void insertSurveyResult(SurveyResult sr) throws SQLException {
		mapper.insertSurveyResult(sr);
	}
//	<!--################################### SurveyImage ###################################-->
	public int selectCountSurveyImage(HashMap<String, Object> paramMap) {
    	return mapper.selectCountSurveyImage(paramMap);
    }
    public ArrayList<PlaceSpot> selectListSurveyImage(HashMap<String, Object> paramMap) {
    	return mapper.selectListSurveyImage(paramMap);
    }
	public SurveyImage selectOneSurveyImage(HashMap<String, Object> paramMap) {
		return mapper.selectOneSurveyImage(paramMap);
	}
//	<!--################################### SurveyRecommand ###################################-->
    public int selectCountSurveyRecommand(HashMap<String, Object> paramMap) {
    	return mapper.selectCountSurveyRecommand(paramMap);
    }
    public ArrayList<SurveyRecommand> selectListSurveyRecommand(HashMap<String, Object> paramMap) {
    	return mapper.selectListSurveyRecommand(paramMap);
    }
	public SurveyRecommand selectOneSurveyRecommand(HashMap<String, Object> paramMap) {
		return mapper.selectOneSurveyRecommand(paramMap);
	}

}
