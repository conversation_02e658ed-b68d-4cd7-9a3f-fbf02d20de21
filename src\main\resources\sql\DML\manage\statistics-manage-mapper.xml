<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.manage.StatisticsMapper">
	<!--################################### Statistic ###################################-->
	<select id="selectListStatisticConnectDate" parameterType="HashMap" resultType="HashMap">
		select
		<choose>
			<when test="chartType == 'dateRange'">time, </when>
			<when test="chartType == 'weekRange'">WEEKDAY(STR_TO_DATE( time, '%Y-%m-%d')) week, </when>
		</choose>
		 <choose>
			<when test="chartType == 'weekRange'">case WEEKDAY(STR_TO_DATE( time, '%Y-%m-%d')) when 0 then '월' when 1 then '화' when 2 then '수' when 3 then '목' when 4 then '금' when 5 then '토' else '일' end  weekname, </when>
		</choose>

		count(session_id) cnt
		  from (
			select session_id, SUBSTRING(min(request_time),1,10) time
			  from webservice_log
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
				and request_time between DATE_FORMAT(#{startDate}, '%Y-%m-%d 00:00:00') and DATE_FORMAT(#{endDate}, '%Y-%m-%d 23:59:59')
	  		</if>
		</where>
			 group by session_id) a
		group by
		<choose>
			<when test="chartType == 'dateRange'">time </when>
			<when test="chartType == 'weekRange'">week </when>
		</choose>

		<choose>
			<when test="chartType == 'dateRange'">order by time desc </when>
		</choose>
	</select>

	<select id="selectListStatisticConnectInfo" parameterType="HashMap" resultType="HashMap">
	 select sum(day_cnt) to_sum_cnt
	 		, round(avg(day_cnt),1) to_avg_cnt
	 		, max(case when time = date(now()) then day_cnt else 0 end) to_day_count
	 		, max(case when time = date(adddate(now(), interval -1 day)) then day_cnt else 0 end) to_yesterday_count
	   from(
		 select time
		 		,count(session_id) day_cnt
		  from (
			select session_id, SUBSTRING(min(request_time),1,10) time
			  from webservice_log
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
				and request_time between DATE_FORMAT(#{startDate}, '%Y-%m-%d 00:00:00') and DATE_FORMAT(#{endDate}, '%Y-%m-%d 23:59:59')
	  		</if>
		</where>
			 group by session_id) a
		 group by time
		 order by time desc ) a
	</select>

	<select id="selectListIslandlife" parameterType="HashMap" resultType="HashMap">
	  select a.id, a.title, ifnull(b.count,0) count
	    from island_life a
	    left join (
		  select user_island_life_id, count(user_island_life_id) count
		    from user
		   where user_island_life_id is not null
		   group by user_island_life_id) b on a.id = b.user_island_life_id
	  <where>
	  	and delete_yn = 'N'
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">and time between #{startDate} and #{endDate}</if>
	  </where>
	</select>

	<select id="selectListStatisticProductItemY" parameterType="HashMap" resultType="String">
<![CDATA[
		select DISTINCT time
		  from (
			select
				session_id
				, SUBSTRING(request_time, 1, 10) time
				, SUBSTRING_INDEX(SUBSTRING_INDEX(request_params, 'serial=', -1), '&', 1) product_serial
			from
				webservice_log
			where
				( (request_uri like concat('/jamsiisland/stay', '%') and request_params like concat('%', 'action=view', '%') )
				   or (request_uri like concat('/program/Apply', '%') and request_params like concat('%', 'action=view', '%') )
				   or (request_uri like concat('/cprogram', '%') and request_params like concat('%', 'action=view', '%')) )) a]]>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">time between #{startDate} and #{endDate}</if>
		</where>
	</select>

	<select id="selectListStatisticProductDate" parameterType="HashMap" resultType="HashMap">
	with resv as(
		select sell_time, product_serial, sum(order_count) order_count
	  from(
		SELECT
			product_serial,
			JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.travelSchedule')) as travel_date,
		    t.option_key,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.0'))) as option_id,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.1'))) as price,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.2'))) as value2,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.3'))) as order_count,
		    SUBSTRING(create_date, 1, 10) sell_time
		FROM reservation
		CROSS JOIN JSON_TABLE(
		    JSON_KEYS(JSON_EXTRACT(price_option_json, '$.data')),
		    '$[*]' COLUMNS(option_key VARCHAR(50) PATH '$')
		) AS t
		 where cancel_yn = 'N'
		   and option_key = 'option-0'  ) a )
		select a.product_serial,
			   pt.product_title,
			   <foreach item="item" index="index" collection="dates" separator=",">ifnull(sum(case when time = '${item}' then 1 else 0 end ),0) '${item}_v'</foreach>,
			   <foreach item="item" index="index" collection="dates" separator=",">ifnull(sum(case when time = '${item}' then order_count else 0 end ),0) '${item}_s'</foreach>
		  from (
<![CDATA[
			 select session_id,
			        SUBSTRING(request_time,1,10) time,
			        SUBSTRING_INDEX(SUBSTRING_INDEX(request_params, 'serial=', -1), '&', 1) product_serial
			   from webservice_log
			  where ((request_uri like concat('/jamsiisland/stay','%') and request_params like concat('%','action=view','%') ) or
				     (request_uri like concat('/program/Apply','%') and request_params like concat('%','action=view','%') ) or
				     (request_uri like concat('/cprogram','%') and request_params like concat('%','action=view','%') ) ) ) a]]>
		  left join product_tour pt on  pt.product_serial = a.product_serial and pt.regacy_yn = 'N'
		  left join resv r on r.product_serial = a.product_serial and r.sell_time = a.time
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">time between #{startDate} and #{endDate}</if>
		</where>
		group by product_serial
	</select>

	<select id="selectListStatisticProgramItemY" parameterType="HashMap" resultType="String">
<![CDATA[
		select DISTINCT time
		  from (
			select
				session_id
				, SUBSTRING(request_time, 1, 10) time
				, SUBSTRING_INDEX(SUBSTRING_INDEX(request_params, 'serial=', -1), '&', 1) product_serial
			from
				webservice_log
			where ( request_uri like concat('/program/all', '%') and request_params like concat('%', 'action=view', '%') )) a]]>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">time between #{startDate} and #{endDate}</if>
		</where>
	</select>

	<select id="selectListStatisticProgramDate" parameterType="HashMap" resultType="HashMap">
	with resv as(
		select sell_time, product_serial, sum(order_count) order_count
	  from(
		SELECT
			product_serial,
			JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.travelSchedule')) as travel_date,
		    t.option_key,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.0'))) as option_id,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.1'))) as price,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.2'))) as value2,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.3'))) as order_count,
		    SUBSTRING(create_date, 1, 10) sell_time
		FROM reservation
		CROSS JOIN JSON_TABLE(
		    JSON_KEYS(JSON_EXTRACT(price_option_json, '$.data')),
		    '$[*]' COLUMNS(option_key VARCHAR(50) PATH '$')
		) AS t
		 where cancel_yn = 'N'
		   and option_key = 'option-0'  ) a
		 group by sell_time, product_serial )
		select a.product_serial,
			   pt.product_title,
			   <foreach item="item" index="index" collection="dates" separator=",">ifnull(sum(case when time = '${item}' then view_count else 0 end ),0) '${item}_v'</foreach>,
			   <foreach item="item" index="index" collection="dates" separator=",">ifnull(sum(case when time = '${item}' then order_count else 0 end ),0) '${item}_s'</foreach>
		  from (
<![CDATA[
			 select SUBSTRING(request_time,1,10) time,
			        SUBSTRING_INDEX(SUBSTRING_INDEX(request_params, 'serial=', -1), '&', 1) product_serial,
			        count(*) view_count
			   from webservice_log
			  where ((request_uri like concat('/program/all','%') and request_params like concat('%','action=view','%') ))
			  group by time, product_serial ) a]]>
		  left join product_tour pt on  pt.product_serial = a.product_serial and pt.regacy_yn = 'N'
		  left join resv r on r.product_serial = a.product_serial and r.sell_time = a.time
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">time between #{startDate} and #{endDate}</if>
		</where>
		group by product_serial
	</select>


	<select id="selectListStatisticJamsiislandItemY" parameterType="HashMap" resultType="String">
<![CDATA[
		select DISTINCT time
		  from (
			select
				session_id
				, SUBSTRING(request_time, 1, 10) time
				, SUBSTRING_INDEX(SUBSTRING_INDEX(request_params, 'serial=', -1), '&', 1) product_serial
			from
				webservice_log
			where
				( request_uri like concat('/jamsiisland/stay', '%') and request_params like concat('%', 'action=view', '%') ) ) a]]>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">time between #{startDate} and #{endDate}</if>
		</where>
	</select>


	<select id="selectListStatisticJamsiislandDate" parameterType="HashMap" resultType="HashMap">
	with resv as(
		select sell_time, product_serial, sum(order_count) order_count
	  from(
		SELECT
			product_serial,
			JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.travelSchedule')) as travel_date,
		    t.option_key,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.0'))) as option_id,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.1'))) as price,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.2'))) as value2,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.3'))) as order_count,
		    SUBSTRING(create_date, 1, 10) sell_time
		FROM reservation
		CROSS JOIN JSON_TABLE(
		    JSON_KEYS(JSON_EXTRACT(price_option_json, '$.data')),
		    '$[*]' COLUMNS(option_key VARCHAR(50) PATH '$')
		) AS t
		 where cancel_yn = 'N'
		   and option_key = 'option-0'  ) a
		 group by sell_time, product_serial)
		select a.product_serial,
			   pt.product_title,
			   <foreach item="item" index="index" collection="dates" separator=",">ifnull(sum(case when time = '${item}' then view_count else 0 end ),0) '${item}_v'</foreach>,
			   <foreach item="item" index="index" collection="dates" separator=",">ifnull(sum(case when time = '${item}' then order_count else 0 end ),0) '${item}_s'</foreach>
		  from (
<![CDATA[
			 select SUBSTRING(request_time,1,10) time,
			        SUBSTRING_INDEX(SUBSTRING_INDEX(request_params, 'serial=', -1), '&', 1) product_serial,
			        count(*) view_count
			   from webservice_log
			  where ((request_uri like concat('/jamsiisland/stay','%') and request_params like concat('%','action=view','%')  ))
			  group by time, product_serial ) a]]>
		  join product_tour pt on  pt.product_serial = a.product_serial and pt.regacy_yn = 'N'
		  join menu_user mu on pt.product_menu_id = mu.menu_id and mu.menu_sub_type = 'stay'
		  left join resv r on r.product_serial = a.product_serial and r.sell_time = a.time
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">time between #{startDate} and #{endDate}</if>
		</where>
		group by product_serial
	</select>

	<select id="selectListStatisticJamsiislandGender" parameterType="HashMap" resultType="HashMap">
	with resv as(
		select sell_time, product_serial, sum(order_count) order_count, user_gender
	  from(
		SELECT
			product_serial,
			JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.travelSchedule')) as travel_date,
		    t.option_key,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.0'))) as option_id,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.1'))) as price,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.2'))) as value2,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.3'))) as order_count,
		    SUBSTRING(create_date, 1, 10) sell_time,
		    u.user_gender
		FROM reservation r
		CROSS JOIN JSON_TABLE( JSON_KEYS(JSON_EXTRACT(price_option_json, '$.data')), '$[*]' COLUMNS(option_key VARCHAR(50) PATH '$') ) AS t
		left join( select user_email, ifnull(case when user_gender = '' then 'no' else user_gender end, 'no') user_gender from user) u  on r.user_email = u.user_email
		 where cancel_yn = 'N'
		   and option_key = 'option-0'  ) a
		 group by sell_time, product_serial)
		 select
			r.product_serial
			, pt.product_title
			, sum(case when r.user_gender is not null then 1 else 0 end ) g0
			, sum(case when r.user_gender = 'M' then 1 else 0 end ) g1
			, sum(case when r.user_gender = 'F' then 1 else 0 end ) g2
			, sum(case when r.user_gender = 'no' then 1 else 0 end ) g3
		from resv r
		  join product_tour pt on  pt.product_serial = r.product_serial and pt.regacy_yn = 'N'
		  join menu_user mu on pt.product_menu_id = mu.menu_id and mu.menu_sub_type = 'stay'
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">and sell_time between #{startDate} and #{endDate}</if>
		</where>
		group by product_serial
	</select>

	<select id="selectListStatisticJamsiislandRegion" parameterType="HashMap" resultType="HashMap">
	with resv as(
		select sell_time, product_serial, sum(order_count) order_count, region
	  from(
		SELECT
			product_serial,
			JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.travelSchedule')) as travel_date,
		    t.option_key,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.0'))) as option_id,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.1'))) as price,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.2'))) as value2,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.3'))) as order_count,
		    SUBSTRING(create_date, 1, 10) sell_time,
		     <![CDATA[CASE
			    WHEN address REGEXP '^서울|&서울특별시' THEN '서울'
			    WHEN address REGEXP '^부산|^부산광역시' THEN '부산'
			    WHEN address REGEXP '^대구|^대구광역시' THEN '대구'
			    WHEN address REGEXP '^인천|^인천광역시' THEN '인천'
			    WHEN address REGEXP '^광주|^광주광역시' THEN '광주'
			    WHEN address REGEXP '^대전|^대전광역시' THEN '대전'
			    WHEN address REGEXP '^울산|^울산광역시' THEN '울산'
			    WHEN address REGEXP '^세종|^세종특별자치시' THEN '세종'
			    WHEN address REGEXP '^경기|^경기도' THEN '경기'
			    WHEN address REGEXP '^충청북도|^충북' THEN '충북'
			    WHEN address REGEXP '^충청남도|^충남' THEN '충남'
			    WHEN address REGEXP '^전라북도|^전북|^전북특별자치도' THEN '전북'
			    WHEN address REGEXP '^전라남도|^전남' THEN '전남'
			    WHEN address REGEXP '^경상북도|^경북' THEN '경북'
			    WHEN address REGEXP '^경상남도|^경남' THEN '경남'
			    WHEN address REGEXP '^강원|^강원특별자치도|^강원도' THEN '강원'
			    WHEN address REGEXP '^제주|^제주특별자치도' THEN '제주'
			    ELSE '기타'
			  END AS region]]>
		FROM reservation r
		CROSS JOIN JSON_TABLE( JSON_KEYS(JSON_EXTRACT(price_option_json, '$.data')), '$[*]' COLUMNS(option_key VARCHAR(50) PATH '$') ) AS t
		left join( select user_email, ifnull(case when user_addr_road = '' then null else user_addr_road end, 'no') address from user) u  on r.user_email = u.user_email
		 where cancel_yn = 'N'
		   and option_key = 'option-0'  ) a
		 group by sell_time, product_serial)
		 select
			r.product_serial
			, pt.product_title
			, count(*) sum
			, SUM(CASE WHEN r.region = '서울' THEN order_count ELSE 0 END) as '서울'
			, SUM(CASE WHEN r.region = '부산' THEN order_count ELSE 0 END) as '부산'
			, SUM(CASE WHEN r.region = '대구' THEN order_count ELSE 0 END) as '대구'
			, SUM(CASE WHEN r.region = '인천' THEN order_count ELSE 0 END) as '인천'
			, SUM(CASE WHEN r.region = '광주' THEN order_count ELSE 0 END) as '광주'
			, SUM(CASE WHEN r.region = '대전' THEN order_count ELSE 0 END) as '대전'
			, SUM(CASE WHEN r.region = '울산' THEN order_count ELSE 0 END) as '울산'
			, SUM(CASE WHEN r.region = '세종' THEN order_count ELSE 0 END) as '세종'
			, SUM(CASE WHEN r.region = '경기' THEN order_count ELSE 0 END) as '경기'
			, SUM(CASE WHEN r.region = '충북' THEN order_count ELSE 0 END) as '충북'
			, SUM(CASE WHEN r.region = '충남' THEN order_count ELSE 0 END) as '충남'
			, SUM(CASE WHEN r.region = '전북' THEN order_count ELSE 0 END) as '전북'
			, SUM(CASE WHEN r.region = '전남' THEN order_count ELSE 0 END) as '전남'
			, SUM(CASE WHEN r.region = '경북' THEN order_count ELSE 0 END) as '경북'
			, SUM(CASE WHEN r.region = '경남' THEN order_count ELSE 0 END) as '경남'
			, SUM(CASE WHEN r.region = '강원' THEN order_count ELSE 0 END) as '강원'
			, SUM(CASE WHEN r.region = '제주' THEN order_count ELSE 0 END) as '제주'
			, SUM(CASE WHEN r.region = '기타' THEN order_count ELSE 0 END) as '기타'
		from resv r
		  join product_tour pt on  pt.product_serial = r.product_serial and pt.regacy_yn = 'N'
		  join menu_user mu on pt.product_menu_id = mu.menu_id and mu.menu_sub_type = 'stay'
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">and sell_time between #{startDate} and #{endDate}</if>
		</where>
		group by product_serial
	</select>

	<select id="selectListStatisticJamsiislandAge" parameterType="HashMap" resultType="HashMap">
	with resv as(
		select sell_time, product_serial, age
	  from(
		SELECT
			product_serial,
			JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.travelSchedule')) as travel_date,
		    t.option_key,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.0'))) as option_id,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.1'))) as price,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.2'))) as value2,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.3'))) as order_count,
		    SUBSTRING(create_date, 1, 10) sell_time,
		    u.age
		FROM reservation r
		CROSS JOIN JSON_TABLE( JSON_KEYS(JSON_EXTRACT(price_option_json, '$.data')), '$[*]' COLUMNS(option_key VARCHAR(50) PATH '$') ) AS t
		left join( select user_email, FLOOR((YEAR(CURRENT_DATE) - YEAR(user_birthday)) / 10) * 10 as age from user) u  on r.user_email = u.user_email
		 where cancel_yn = 'N'
		   and option_key = 'option-0'  ) a
		 group by sell_time, product_serial)
		 select
			r.product_serial
			, pt.product_title
			, count(*) agSum
			, sum(case when age is null then 1 else 0 end) ag99
			, sum(case when age between  0 and 19 then 1 else 0 end) ag01
			, sum(case when age between 20 and 29 then 1 else 0 end) ag02
			, sum(case when age between 30 and 39 then 1 else 0 end) ag03
			, sum(case when age between 40 and 49 then 1 else 0 end) ag04
			, sum(case when age between 50 and 59 then 1 else 0 end) ag05
			, sum(case when age between 60 and 69 then 1 else 0 end) ag06
			, sum(case when age between 70 and 79 then 1 else 0 end) ag07
			, sum(case when age between 80 and 89 then 1 else 0 end) ag08
			, sum(case when age >= 90 then 1 else 0 end) ag09
		from resv r
		  join product_tour pt on  pt.product_serial = r.product_serial and pt.regacy_yn = 'N'
		  join menu_user mu on pt.product_menu_id = mu.menu_id and mu.menu_sub_type = 'stay'
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">and sell_time between #{startDate} and #{endDate}</if>
		</where>
		group by product_serial
	</select>

	<select id="selectListStatisticJamsiislandAmount" parameterType="HashMap" resultType="HashMap">
	with resv as(
		select sell_time, product_serial, sum(order_count) order_count, sum(stay_date) stay_date, sum(total_amount) total_amount
	  from(
		SELECT
			product_serial,
			JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.travelSchedule')) as travel_date,
		    t.option_key,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.0'))) as option_id,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.1'))) as price,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.2'))) as value2,
		    JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.3'))) as order_count,
		    COALESCE(NULLIF(JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, CONCAT('$.data.stay-date'))), 'null'), '0') as stay_date,
		    SUBSTRING(create_date, 1, 10) sell_time,
		    total_amount
		FROM reservation r
		CROSS JOIN JSON_TABLE( JSON_KEYS(JSON_EXTRACT(price_option_json, '$.data')), '$[*]' COLUMNS(option_key VARCHAR(50) PATH '$') ) AS t
		 where cancel_yn = 'N'
		   and option_key = 'option-0'  ) a
		 group by sell_time, product_serial)
		 select
			r.product_serial
			, pt.product_title
			, sum(order_count) order_count
			, sum(stay_date) stay_date
			, sum(total_amount) total_amount
		from resv r
		  join product_tour pt on  pt.product_serial = r.product_serial and pt.regacy_yn = 'N'
		  join menu_user mu on pt.product_menu_id = mu.menu_id and mu.menu_sub_type = 'stay'
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">and sell_time between #{startDate} and #{endDate}</if>
		</where>
		group by product_serial
	</select>

	<select id="selectListStatisticDateY" parameterType="HashMap" resultType="String">
	with RECURSIVE
		DateRange AS (
			SELECT DATE_FORMAT(#{startDate}, '%Y-%m-%d') AS date
			UNION ALL
			SELECT DATE_ADD(date, INTERVAL 1 DAY)
			FROM DateRange
			<![CDATA[WHERE DATE_ADD(date, INTERVAL 1 DAY) <=DATE_FORMAT(#{endDate}, '%Y-%m-%d')]]>
		)
	select date
	  from DateRange
	</select>

	<select id="selectListStatisticBoard" parameterType="HashMap" resultType="HashMap">
		with RECURSIVE
		DateRange AS (
			SELECT DATE_FORMAT(#{startDate}, '%Y-%m-%d') AS date
			UNION ALL
			SELECT DATE_ADD(date, INTERVAL 1 DAY)
			FROM DateRange
			<![CDATA[WHERE DATE_ADD(date, INTERVAL 1 DAY) < LAST_DAY(#{endDate})]]>
		), bod as (
			select mu.menu_id,
			       mcb.board_id,
				concat( (case when mu.upper_menu_id is not null
							  then
								(case when menu_type != 'out-link'
								      then (select menu_url from menu_user b where b.menu_id = mu.upper_menu_id)
								      else '' end )
							  else '' end ), menu_url  ) full_menu_url
			  from menu_user mu
			  left join menu_connect_board mcb on mu.menu_id = mcb.menu_id
			 where mu.menu_type = 'board'
			   and mcb.board_id is not null
		), mix as (
		select *
		  from bod, DateRange
		), vc as (
			select time, board_id, count(*) vcnt
			  from (
				select a.time, a.id, ifnull(bc.board_id, a.board_id) board_id
				  from (
				       select
						session_id
						, SUBSTRING(request_time, 1, 10) time
						, bod.board_id<![CDATA[
						, SUBSTRING_INDEX(SUBSTRING_INDEX(request_params, 'action=', -1), '&', 1) action
						, SUBSTRING_INDEX(SUBSTRING_INDEX(request_params, 'id=', -1), '&', 1) id]]>
						, wl.request_uri
						, wl.request_params
					  from webservice_log wl
					  inner join bod on wl.request_uri like concat(bod.full_menu_url, '%')
				 	   /*#and (request_params like concat('%', 'action=view', '%') and request_params like concat('%', 'id=', '%'))*/
					  ) a
				  left join board_contents bc on a.id = bc.id ) data
			 group by board_id, time
		 ), fc as (
			 select time, board_id, count(*) fcnt
			   from (
				  select uf.board_id id
				       , bc.board_id board_id
				  		, SUBSTRING(uf.create_date, 1, 10) time
				    from user_favorites uf
				    left join board_contents bc on bc.id = uf.board_id
				   where uf.board_id != 0
				     and uf.delete_yn = 'N' ) uf
			  group by time
		), cc as (
			 select time, board_id, count(*) ccnt
			   from (
				select board_id, SUBSTRING(bc.create_date, 1, 10) time
				  from board_comment bc
				 where bc.delete_yn ='N' ) a
			  group by board_id
		), bc as (
			select time, board_id, count(*) bcnt
			from(select board_id, SUBSTRING(bc.create_date, 1, 10) time
				   from board_contents bc
				  where bc.delete_yn = 'N') a
			 group by board_id)
		select board_id
			   , title
			   ,<foreach item="item" index="index" collection="dates" separator=",">ifnull(sum(case when date = '${item}' then vcnt else 0 end ),0) '${item}_v'</foreach>
			   ,<foreach item="item" index="index" collection="dates" separator=",">ifnull(sum(case when date = '${item}' then bcnt else 0 end ),0) '${item}_w'</foreach>
			   <!--
			   ,<foreach item="item" index="index" collection="dates" separator=",">ifnull(sum(case when date = '${item}' then fcnt else 0 end ),0) '${item}_f'</foreach>
			   ,<foreach item="item" index="index" collection="dates" separator=",">ifnull(sum(case when date = '${item}' then ccnt else 0 end ),0) '${item}_c'</foreach>
			    -->
		    from (
			select a.board_id,
			       a.date,
			       b.vcnt,
			       c.fcnt,
			       d.ccnt,
			       e.bcnt
			  from mix a
			  left join vc b on a.date = b.time and a.board_id = b.board_id
			  left join fc c on a.date = c.time and a.board_id = c.board_id
			  left join cc d on a.date = d.time and a.board_id = d.board_id
			  left join bc e on a.date = e.time and a.board_id = e.board_id) data
		  join board_setting bs on bs.id = data.board_id and bs.delete_yn = 'N' and bs.use_yn = 'Y'
		  group by data.board_id
	</select>

	<select id="selectListStatisticMission" parameterType="HashMap" resultType="HashMap">
		with RECURSIVE
		DateRange AS (
			SELECT DATE_FORMAT(#{startDate}, '%Y-%m-%d') AS date
			UNION ALL
			SELECT DATE_ADD(date, INTERVAL 1 DAY)
			FROM DateRange
			<![CDATA[WHERE DATE_ADD(date, INTERVAL 1 DAY) < LAST_DAY(#{endDate})]]>
		), bod as (
		select bc.id, bc.title, bc.mission_type
		  from board_contents bc
		  #left join board_contents bc2 on bc2.id = bc.upper_board_id
		 where bc.board_id = 7
		   and bc.upper_board_id is null
		   and bc.delete_yn ='N'
		), cont as (
		select upper_board_id uid,  id, time, count(*) cnt
		  from(
				select bc.upper_board_id, bc.id, bc.title, SUBSTRING(bc.create_date, 1, 10) time
				  from board_contents bc
				 where bc.board_id = 7
				   and bc.upper_board_id is not null
				   and bc.delete_yn ='N') a
	   group by a.upper_board_id, a.time
		), mix as (
		select *
		  from bod, DateRange
		)
		select id
		       , title
			   ,<foreach item="item" index="index" collection="dates" separator=",">ifnull(sum(case when date = '${item}' then cnt else 0 end ),0) '${item}'</foreach>
		  from (
				select a.id,
				       a.title,
				       a.date,
				       a.mission_type,
				       b.cnt
				  from mix a
				  left join cont b on a.date = b.time and a.id = b.uid ) a
		 group by id
	</select>

	<select id="selectListStatisticBadge" parameterType="HashMap" resultType="HashMap">
		with RECURSIVE
		DateRange AS (
			SELECT DATE_FORMAT(#{startDate}, '%Y-%m-%d') AS date
			UNION ALL
			SELECT DATE_ADD(date, INTERVAL 1 DAY)
			FROM DateRange
			<![CDATA[WHERE DATE_ADD(date, INTERVAL 1 DAY) < LAST_DAY(#{endDate})]]>
		), badge as (
			select bc.badge_id, bc.badge_name, bc.badge_type
			  from badge_contents bc
		   where bc.use_yn = 'Y'
		     and bc.delete_yn ='N'
		), mix as (
			select *
			  from badge, DateRange
		), cont as (
			select badge_id, time, count(*) cnt
			  from(
				select badge_id, SUBSTRING(create_date, 1, 10) time
				  from badge_acquire_history bch
				 where delete_yn ='N') a
			  group by a.badge_id
		)
		select badge_id
		       , badge_name
			   ,<foreach item="item" index="index" collection="dates" separator=",">ifnull(sum(case when date = '${item}' then cnt else 0 end ),0) '${item}'</foreach>
		  from (
			select a.badge_id,
			       a.badge_name,
			       a.date,
			       a.badge_type,
			       b.cnt
			  from mix a
			  left join cont b on a.date = b.time and a.badge_id = b.badge_id ) a
		 group by badge_id
	</select>

	<select id="selectListStatisticDemographyGender" parameterType="HashMap" resultType="HashMap">
		with
			RECURSIVE sexs AS (
			  SELECT 'M' as id, '남성' as name, 1 as sort
			  UNION ALL SELECT 'F', '여성', 2
			  UNION ALL SELECT 'no', '미응답', 3
			)
		select *, s.name
		  from (
			select ifnull(case when user_gender = '' then 'no' else user_gender end, 'no') user_geder, count(*) count
			  from user
			 group by ifnull(case when user_gender = '' then 'no' else user_gender end, 'no') ) a
		  left JOIN sexs s on a.user_geder = s.id
	</select>

	<select id="selectListStatisticDemographyRegion" parameterType="HashMap" resultType="HashMap">
		<![CDATA[with
			RECURSIVE regions AS (
			  SELECT 'seoul' as id, '서울' as name, 1 as sort
			  UNION ALL SELECT 'busan', '부산', 2
			  UNION ALL SELECT 'daegu', '대구', 3
			  UNION ALL SELECT 'incheon', '인천', 4
			  UNION ALL SELECT 'gwangju', '광주', 5
			  UNION ALL SELECT 'daejeon', '대전', 6
			  UNION ALL SELECT 'ulsan', '울산', 7
			  UNION ALL SELECT 'sejong', '세종', 8
			  UNION ALL SELECT 'gyeonggi', '경기', 9
			  UNION ALL SELECT 'chungbuk', '충북', 10
			  UNION ALL SELECT 'chungnam', '충남', 11
			  UNION ALL SELECT 'jeonbuk', '전북', 12
			  UNION ALL SELECT 'jeonnam', '전남', 13
			  UNION ALL SELECT 'gyeongbuk', '경북', 14
			  UNION ALL SELECT 'gyeongnam', '경남', 15
			  UNION ALL SELECT 'gangwon', '강원', 16
			  UNION ALL SELECT 'jeju', '제주', 17
			  UNION ALL SELECT 'etc', '기타', 99
			), addr as (
			SELECT
			  CASE
			    WHEN address REGEXP '^서울|&서울특별시' THEN '서울'
			    WHEN address REGEXP '^부산|^부산광역시' THEN '부산'
			    WHEN address REGEXP '^대구|^대구광역시' THEN '대구'
			    WHEN address REGEXP '^인천|^인천광역시' THEN '인천'
			    WHEN address REGEXP '^광주|^광주광역시' THEN '광주'
			    WHEN address REGEXP '^대전|^대전광역시' THEN '대전'
			    WHEN address REGEXP '^울산|^울산광역시' THEN '울산'
			    WHEN address REGEXP '^세종|^세종특별자치시' THEN '세종'
			    WHEN address REGEXP '^경기|^경기도' THEN '경기'
			    WHEN address REGEXP '^충청북도|^충북' THEN '충북'
			    WHEN address REGEXP '^충청남도|^충남' THEN '충남'
			    WHEN address REGEXP '^전라북도|^전북|^전북특별자치도' THEN '전북'
			    WHEN address REGEXP '^전라남도|^전남' THEN '전남'
			    WHEN address REGEXP '^경상북도|^경북' THEN '경북'
			    WHEN address REGEXP '^경상남도|^경남' THEN '경남'
			    WHEN address REGEXP '^강원|^강원특별자치도|^강원도' THEN '강원'
			    WHEN address REGEXP '^제주|^제주특별자치도' THEN '제주'
			    ELSE '기타'
			  END AS region
			FROM (select ifnull(case when user_addr_road = '' then null else user_addr_road end, 'no') address
				    from user) addr )
		select r.name, ifnull(c.count,0) count
		  from regions r
		  left join
		   (select region, count(region) count
			  from addr
			 group by region) c on r.name = c.region]]>
	</select>

	<select id="selectListStatisticDemographyAge" parameterType="HashMap" resultType="HashMap">
		with um as(
		select user_email,
			   count(*) agSum,
			   (case when ag is null then 1 else 0 end) ag99,
			   (case when ag between  0 and 19 then 1 else 0 end) ag01,
			   (case when ag between 20 and 29 then 1 else 0 end) ag02,
			   (case when ag between 30 and 39 then 1 else 0 end) ag03,
			   (case when ag between 40 and 49 then 1 else 0 end) ag04,
			   (case when ag between 50 and 59 then 1 else 0 end) ag05,
			   (case when ag between 60 and 69 then 1 else 0 end) ag06,
			   (case when ag between 70 and 79 then 1 else 0 end) ag07,
			   (case when ag between 80 and 89 then 1 else 0 end) ag08,
			   (case when ag >= 90 then 1 else 0 end) ag09
		  from(
			select user_email,
			       FLOOR((YEAR(CURRENT_DATE) - YEAR(user_birthday)) / 10) * 10 as ag
			  from user ) a
		group by user_email)
	select sum( agSum ) 'agSum'
	       , sum( ag01 ) 'ag01'
	       , sum( ag02 ) 'ag02'
	       , sum( ag03 ) 'ag03'
	       , sum( ag04 ) 'ag04'
	       , sum( ag05 ) 'ag05'
	       , sum( ag06 ) 'ag06'
	       , sum( ag07 ) 'ag07'
	       , sum( ag08 ) 'ag08'
	       , sum( ag09 ) 'ag09'
	       , sum( ag99 ) 'ag99'
	  from um a
	</select>


</mapper>