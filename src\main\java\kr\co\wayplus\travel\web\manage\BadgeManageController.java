package kr.co.wayplus.travel.web.manage;

import jakarta.servlet.http.HttpServletRequest;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.*;
import kr.co.wayplus.travel.service.manage.BadgeManageService;
import kr.co.wayplus.travel.service.manage.MainManageService;
import kr.co.wayplus.travel.service.manage.UserManageService;
import kr.co.wayplus.travel.util.LoggerUtil;

import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping("/manage/badge")
public class BadgeManageController extends BaseController {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final UserManageService userManageService;
    private final BadgeManageService badgeManageService;

    @Value("${upload.file.path}")
    String externalImageUploadPath;
    @Value("${upload.file.max-size}")
    int maxFileSize;
    final String addPath = "images/badge/";

    public BadgeManageController(BadgeManageService badgeManageService, UserManageService userManageService) {
        this.badgeManageService = badgeManageService;
        this.userManageService = userManageService;
    }

    @GetMapping("/list")
    public ModelAndView popupList(@RequestParam(value="type",defaultValue="normal") String type,
                                  @RequestParam(value="useYn",defaultValue="ALL") String useYn,
                                  @RequestParam(value="page",defaultValue="1") int page,
                                  @RequestParam(value="pageSize",defaultValue="10") int pageSize,
                                  @RequestParam(value="searchKey",defaultValue="") String searchKey){
        ModelAndView mav = new ModelAndView();
        HashMap<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("useYn", useYn);
        param.put("searchKey", searchKey);

        int totalCount = badgeManageService.selectCountBadgeContents(param);
        if( pageSize >=0 ) {
	        PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
	        param.put("itemStartPosition", paging.getItemStartPosition());
	        param.put("pagePerSize", paging.getPagePerSize());
	        mav.addObject("paging", paging);
        }
        param.put("sort", "badgeId");
        param.put("sortOrder", "desc");
        mav.addObject("p", param);
        mav.addObject("badgeList", badgeManageService.selectListBadgeContents(param));

        mav.setViewName("manage/sub/badge/list");
        return mav;
    }

    @GetMapping("/form")
    public ModelAndView popupAdd(@RequestParam(value="mode",defaultValue="I") String mode){
        ModelAndView mav = new ModelAndView();
        mav.addObject("mode", mode);
        mav.setViewName("manage/sub/badge/form");
        return mav;
    }

    @GetMapping("/modify")
    public ModelAndView popupModify(@RequestParam(value="mode",defaultValue="U") String mode,
                                    @RequestParam(value="badgeId", defaultValue="0") int badgeId){
        ModelAndView mav = new ModelAndView();
        HashMap<String, Object> param = new HashMap<>();
        param.put("badgeId", badgeId);
        mav.addObject("badge", badgeManageService.selectOneBadgeContents(param));

        mav.setViewName("manage/sub/badge/form");
        return mav;
    }

    @PostMapping("/list")
    @ResponseBody
    public HashMap<String, Object> badge_list_ajax(HttpServletRequest request, BoardSetting bc,
    		@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length,
    		@Param(value="boardId") String boardId,
    		@Param(value="useYn") String useYn,
    		@Param(value="titleLike") String titleLike,
    		@Param(value="contentLike") String contentLike
    		, @Param(value="contentsExis") String contentsExis
    		){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		HashMap<String, Object> paramMap = new HashMap<>();

    		//List<SortData> listSort = getListOrder(request);
    		//paramMap.put("listSort", listSort);

    		//paramMap.put("sort", "menuName");
    		//paramMap.put("sortOrder", "asc");

    		if(length > 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
    		}
		/*
    		paramMap.put("deleteYn", bc.getDeleteYn() );
			paramMap.put("boardId", boardId);
			paramMap.put("useYn", useYn);
			paramMap.put("titleLike", titleLike);
			paramMap.put("contentLike", contentLike);
			paramMap.put("contentsExis", contentsExis);
		*/

    		int totalCount = badgeManageService.selectCountBadgeContents(paramMap);

    		retrunMap.put("recordsTotal", totalCount);
    		retrunMap.put("recordsFiltered", totalCount);
    		retrunMap.put("data", badgeManageService.selectListBadgeContents(paramMap));

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

        return retrunMap;
    }

    @PostMapping("/add")
    @ResponseBody
    public HashMap<String, Object> popupCreate(@ModelAttribute BadgeContents badgeContents, BindingResult bindingResult,
                                               MultipartHttpServletRequest request){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            LoggerUtil.writeBindingResultErrorLog(bindingResult, logger);
            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

            List<MultipartFile> multipartFiles;
            if(request.getFile("image") != null) {
                multipartFiles = request.getFiles("image");
                File file = new File(externalImageUploadPath + addPath);
                if (!file.exists()) file.mkdirs();

                for (MultipartFile multipartFile : multipartFiles) {
                    String uploadName = UUID.randomUUID().toString();
                    multipartFile.transferTo(new File(externalImageUploadPath+ addPath + uploadName));
                    logger.debug("User Question File Uploaded : " + multipartFile.getOriginalFilename());
                    BadgeAttachImage attachImage = new BadgeAttachImage();
                    attachImage.setServiceType("badge");
                    attachImage.setUploadPath(externalImageUploadPath+ addPath);
                    attachImage.setUploadFilename(addPath+uploadName);
                    attachImage.setOriginFilename(multipartFile.getOriginalFilename());
                    attachImage.setFileSize((int) multipartFile.getSize());
                    attachImage.setFileMimetype(multipartFile.getContentType());
                    if (multipartFile.getOriginalFilename().contains(".")) {
                        attachImage.setFileExtension(multipartFile.getOriginalFilename().substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
                    }
                    if (user != null) {
                        attachImage.setUploadId(String.valueOf(user.getUserEmail()));
                    }
                    badgeManageService.writeBadgeAttachImage(attachImage);
                    badgeContents.setBadgeImageFileId(attachImage.getFileId());
                }
            }

            badgeContents.setCreateId(user.getUserEmail());
            badgeManageService.writeBadgeContents(badgeContents);

            resultMap.put("result", "success");
            resultMap.put("message", "저장됐습니다.");
        } catch (Exception e) {
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
            logger.error(e.getMessage());
            e.printStackTrace();
        }

        return resultMap;
    }

    @PutMapping("/update")
    @ResponseBody
    public HashMap<String, Object> badgeUpdate(@ModelAttribute BadgeContents badgeContents, BindingResult bindingResult,
                                               MultipartHttpServletRequest request){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            LoggerUtil.writeBindingResultErrorLog(bindingResult, logger);
            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

            //이미지 파일등록
            List<MultipartFile> multipartFiles;
            if (request.getFile("image") != null) {
                multipartFiles = request.getFiles("image");
                File file = new File(externalImageUploadPath + addPath);
                if (!file.exists()) file.mkdirs();
                for (MultipartFile multipartFile : multipartFiles) {
                    if(multipartFile.getSize() == 0) continue;
                    String uploadName = UUID.randomUUID().toString();
                    multipartFile.transferTo(new File(externalImageUploadPath + addPath + uploadName));
                    logger.debug("User Question File Uploaded : " + multipartFile.getOriginalFilename());
                    BadgeAttachImage attachImage = new BadgeAttachImage();
                    attachImage.setServiceType("badge");
                    attachImage.setUploadPath(externalImageUploadPath + addPath);
                    attachImage.setUploadFilename(addPath + uploadName);
                    attachImage.setOriginFilename(multipartFile.getOriginalFilename());
                    attachImage.setFileSize((int) multipartFile.getSize());
                    attachImage.setFileMimetype(multipartFile.getContentType());
                    if (multipartFile.getOriginalFilename().contains(".")) {
                        attachImage.setFileExtension(multipartFile.getOriginalFilename().substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
                    }
                    if (user != null) {
                        attachImage.setUploadId(String.valueOf(user.getUserEmail()));
                    }
                    badgeManageService.writeBadgeAttachImage(attachImage);
                    badgeContents.setBadgeImageFileId(attachImage.getFileId());
                }
            }

            badgeContents.setLastUpdateId(user.getUserEmail());
            badgeManageService.modifyBadgeContents(badgeContents);

            resultMap.put("result", "success");
            resultMap.put("message", "저장됐습니다.");
        } catch (Exception e) {
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
            logger.error(e.getMessage());
            e.printStackTrace();
        }

        return resultMap;
    }


    @DeleteMapping("/delete")
    @ResponseBody
    public HashMap<String, Object> badgeDelete(@RequestParam(value="badgeId", defaultValue="0") int badgeId){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

            HashMap<String, Object> param = new HashMap<>();
            param.put("deleteId", user.getUserEmail());
            param.put("badgeId", badgeId);
            badgeManageService.updateBadgeContentsDelete(param);

            resultMap.put("result", "success");
            resultMap.put("message", "저장됐습니다.");
        } catch (Exception e) {
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
            logger.error(e.getMessage());
            e.printStackTrace();
        }

        return resultMap;
    }

    @PostMapping("/badge-grant")
    @ResponseBody
    public HashMap<String, Object> badgeGrant(@ModelAttribute BadgeAcquireHistory badgeAcquireHistory, BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            LoggerUtil.writeBindingResultErrorLog(bindingResult, logger);

            HashMap<String, Object> param = new HashMap<>();
            param.put("userEmail", badgeAcquireHistory.getUserEmail());
            param.put("badgeId", badgeAcquireHistory.getBadgeId());
            if(badgeManageService.selectCountBadgeAcquireHistory(param) > 0){
                throw new Exception("이미 획득한 뱃지입니다.");
            }
            badgeAcquireHistory.setCreateId( getBaseUserEmail() );

            badgeManageService.writeBadgeAcquireHistory(badgeAcquireHistory);

            resultMap.put("result", "success");
            resultMap.put("message", "저장됐습니다.");
        } catch (Exception e) {
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
            logger.error(e.getMessage());
            e.printStackTrace();
        }

        return resultMap;
    }

    @GetMapping("/acquire-list")
    public ModelAndView acquireList(@RequestParam(value="type",defaultValue="normal") String type,
                                  @RequestParam(value="useYn",defaultValue="ALL") String useYn,
                                  @RequestParam(value="page",defaultValue="1") int page,
                                  @RequestParam(value="pageSize",defaultValue="10") int pageSize,
                                  @RequestParam(value="searchType",defaultValue="") String searchType,
                                  @RequestParam(value="searchKey",defaultValue="") String searchKey){
        ModelAndView mav = new ModelAndView();
        HashMap<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("useYn", useYn);
        param.put("searchType", searchType);
        param.put("searchKey", searchKey);


        int totalCount = badgeManageService.selectCountBadgeAcquireHistory(param);
        PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
        param.put("itemStartPosition", paging.getItemStartPosition());
        param.put("pagePerSize", paging.getPagePerSize());

        mav.addObject("p", param);
        mav.addObject("badgeAcquireHistory", badgeManageService.selectListBadgeAcquireHistory(param));
        mav.addObject("paging", paging);
        mav.setViewName("manage/sub/badge/acquire-list");
        return mav;
    }

    @GetMapping("/acquire-data-list")
    public ModelAndView acquireDataList(@RequestParam(value="type",defaultValue="normal") String type,
                                  @RequestParam(value="useYn",defaultValue="ALL") String useYn,
                                  @RequestParam(value="page",defaultValue="1") int page,
                                  @RequestParam(value="pageSize",defaultValue="10") int pageSize,
                                  @RequestParam(value="searchKey",defaultValue="") String searchKey,
                                  @RequestParam(value="userEmail",defaultValue="") String userEmail){
        ModelAndView mav = new ModelAndView();
        HashMap<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("useYn", useYn);
        param.put("searchKey", searchKey);
        param.put("userEmail", userEmail);

        int totalCount = badgeManageService.selectCountBadgeAcquireHistory(param);
        PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
        param.put("itemStartPosition", paging.getItemStartPosition());
        param.put("pagePerSize", paging.getPagePerSize());

        mav.addObject("p", param);
        mav.addObject("badgeAcquireHistory", badgeManageService.selectListBadgeAcquireHistory(param));
        mav.addObject("paging", paging);
        mav.setViewName("manage/sub/badge/user-badge-list-popup");
        return mav;
    }

    @GetMapping("/ajax-acquire-list")
    @ResponseBody
    public HashMap<String, Object> ajaxAccquireList(@RequestParam(value="type",defaultValue="normal") String type,
                                  @RequestParam(value="useYn",defaultValue="ALL") String useYn,
                                  @RequestParam(value="start", defaultValue="0") int start,
    		                      @RequestParam(value="length", defaultValue="10") int length,
                                  @RequestParam(value="userEmail",defaultValue="") String userEmail){
        HashMap<String, Object> returnMap = new HashMap<>();

        try {
            HashMap<String, Object> param = new HashMap<>();
            param.put("type", type);
            param.put("useYn", useYn);
            param.put("searchType", "userEmail");
            param.put("searchKey", userEmail);

            if(length >= 0) {
				param.put("itemStartPosition", start);
				param.put("pagePerSize", length);
    		}

            param.put("sort", "lastUpdateDate");
			param.put("sortOrder", "desc");
            int totalCount = badgeManageService.selectCountBadgeAcquireHistory(param);
            returnMap.put("recordsTotal", totalCount);
    		returnMap.put("recordsFiltered", totalCount);

            returnMap.put("data", badgeManageService.selectListBadgeAcquireHistory(param));
            returnMap.put("result", "success");
        } catch (Exception e) {
            returnMap.put("result", "error");
            returnMap.put("message", e.getMessage());
            logger.error(e.getMessage());
            e.printStackTrace();
        }

        return returnMap;
    }

    @PostMapping("/acquire-remove")
    @ResponseBody
    public HashMap<String, Object> acquireRemove(@ModelAttribute BadgeAcquireHistory badgeAcquireHistory, BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            LoggerUtil.writeBindingResultErrorLog(bindingResult, logger);
            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            badgeAcquireHistory.setDeleteId(user.getUserEmail());
            badgeManageService.removeBadgeAcquireHistory(badgeAcquireHistory);

            resultMap.put("result", "success");
            resultMap.put("message", "삭제됐습니다.");
        } catch (Exception e) {
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
            logger.error(e.getMessage());
            e.printStackTrace();
        }

        return resultMap;
    }


    @GetMapping("/grant-list")
    public ModelAndView grantList(@RequestParam(value="type",defaultValue="active") String type,
                                  @RequestParam(value="useYn",defaultValue="ALL") String useYn,
                                  @RequestParam(value="page",defaultValue="1") int page,
                                  @RequestParam(value="pageSize",defaultValue="10") int pageSize,
                                  @RequestParam(value="searchKey",defaultValue="") String searchKey){
        ModelAndView mav = new ModelAndView();

        HashMap<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("searchKey", searchKey);

        param.put("notUserJoinTypes", Arrays.asList("reservation"));

        int totalCount = userManageService.getUserListCount(param);
        PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
        param.put("itemStartPosition", paging.getItemStartPosition());
        param.put("pagePerSize", paging.getPagePerSize());
        mav.addObject("p", param);
        mav.addObject("userList", userManageService.getUserList(param));
        mav.addObject("paging", paging);

        HashMap<String, Object> badgeParam = new HashMap<>();
        badgeParam.put("useYn", "Y");
        mav.addObject("badgeList", badgeManageService.selectListBadgeContents(badgeParam));

        mav.setViewName("manage/sub/badge/grant-list");

        return mav;
    }
}
