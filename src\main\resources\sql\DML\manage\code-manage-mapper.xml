<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.manage.CodeManageMapper">

<!--################################### CodeItem ###################################-->
	 <select id="selectCountCodeItem" parameterType="HashMap" resultType="Integer">
		with recursive tree as (
			select
				1 as lev,concat(code_sort, '|', code) idpath,CAST(concat(0, '-') as varchar(100) ) pid,
				id, upper_id,
				code_depth,code,upper_code,name,code_desc ,code_acronym,code_sort,use_yn,
				create_id,create_date,last_update_id,last_update_date,delete_yn,delete_id,delete_date
			from code_item a
			where upper_code = ''
		union all
			select
				1 + lev as lev, concat(p.idpath, '->', c.code_sort, '|', c.code) as idpath, concat(p.code, '-') as pid,
				c.id, c.upper_id,
				c.code_depth,c.code,c.upper_code,c.name,c.code_desc ,c.code_acronym,c.code_sort,c.use_yn,
				c.create_id,c.create_date,c.last_update_id,c.last_update_date,c.delete_yn,c.delete_id,c.delete_date
			from code_item c
			inner join tree p on p.id = c.upper_id)
		select count(*)
		  from tree a
		  left join menu_connect_place mcp on a.code = mcp.place_code
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperId)" >	and upper_id=#{upperId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(code)" >	and code=#{code}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperCode)" >
				<choose>
					<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(withSubCode) and withSubCode eq true">
					and (upper_code=#{upperCode} or upper_id in (select id from code_item WHERE upper_code =#{upperCode}) )
					</when>
					<otherwise>and upper_code=#{upperCode}</otherwise>
				</choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(name)" >	and name=#{name}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeDesc)" >	and code_desc=#{codeDesc}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeDepth)" >	and code_depth=#{codeDepth}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeAcronym)" >	and code_acronym=#{codeAcronym}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeSort)" >	and code_sort=#{codeSort}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
         </where>
    </select>

	<select id="selectListCodeItem" parameterType="HashMap" resultType="CodeItem">
		with recursive tree as (
			select
				1 as lev,concat(code_sort, '|', code) idpath,CAST(concat(0, '-') as varchar(100) ) pid,
				id, upper_id,
				code_depth,code,upper_code,name,code_desc ,code_acronym,code_sort,use_yn,
				create_id,create_date,last_update_id,last_update_date,delete_yn,delete_id,delete_date
			from code_item a
			where upper_code = ''
		union all
			select
				1 + lev as lev, concat(p.idpath, '->', c.code_sort, '|', c.code) as idpath, concat(p.code, '-') as pid,
				c.id, c.upper_id,
				c.code_depth,c.code,c.upper_code,c.name,c.code_desc ,c.code_acronym,c.code_sort,c.use_yn,
				c.create_id,c.create_date,c.last_update_id,c.last_update_date,c.delete_yn,c.delete_id,c.delete_date
			from code_item c
			inner join tree p on p.id = c.upper_id),
		connect as (
			select b.upper_code, count(a.menu_id) menu_linked
			  from menu_connect_place a
			  join code_item b on a.place_code = b.code
		 	 group by b.upper_code )
		SELECT *
		  FROM(
			select @rownum:=@rownum+1 AS rownum,
				   mcp.menu_id, b.menu_linked,
				   a.lev, a.idpath, a.pid, a.id, a.upper_id,
				   (select count(code) from code_item c where c.upper_code = a.code) ccnt,
			       a.code, COALESCE(a.upper_code,'') upper_code, a.name, a.code_desc , COALESCE(a.code_acronym,'') code_acronym, a.code_depth, a.code_sort, a.use_yn,
			       a.create_id, a.create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.delete_id, a.delete_date
			  from tree a
	          join (SELECT @rownum:= 0) rnum
	          left join menu_connect_place mcp on a.code = mcp.place_code
			  left join connect b on a.code = b.upper_code
	         <where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperId)" >	and upper_id=#{upperId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(code)" >	and code=#{code}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperCode)" >
					<choose>
						<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(withSubCode) and withSubCode eq true">
						and (a.upper_code=#{upperCode} or a.upper_id in (select id from code_item WHERE upper_code =#{upperCode}))
						</when>
						<otherwise>and a.upper_code=#{upperCode}</otherwise>
					</choose>
				</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(name)" >	and name=#{name}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeDesc)" >	and code_desc=#{codeDesc}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeDepth)" >	and code_depth=#{codeDepth}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeAcronym)" >	and code_acronym=#{codeAcronym}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeSort)" >	and code_sort=#{codeSort}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
	         </where>

	         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
					<when test="sort=='id'" >	id	</when>
					<when test="sort=='upperId'" >	upper_id	</when>
					<when test="sort=='code'" >	code	</when>
					<when test="sort=='upperCode'" >	upper_code	</when>
					<when test="sort=='name'" >	name	</when>
					<when test="sort=='codeDesc'" >	code_desc	</when>
					<when test="sort=='codeDepth'" >	code_depth	</when>
					<when test="sort=='codeAcronym'" >	code_acronym	</when>
					<when test="sort=='codeSort'" >	code_sort	</when>
					<when test="sort=='useYn'" >	use_yn	</when>
					<when test="sort=='createId'" >	create_id	</when>
					<when test="sort=='createDate'" >	create_date	</when>
					<when test="sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="sort=='deleteYn'" >	delete_yn	</when>
					<when test="sort=='deleteId'" >	delete_id	</when>
					<when test="sort=='deleteDate'" >	delete_date	</when>

		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="!@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) or !@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
				ORDER BY idpath
			</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listcodeSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		    		<when test="item.sort=='id'" >	id	</when>
					<when test="item.sort=='upperId'" >	upper_id	</when>
					<when test="item.sort=='code'" >	code	</when>
					<when test="item.sort=='upperCode'" >	upper_code	</when>
					<when test="item.sort=='name'" >	name	</when>
					<when test="item.sort=='codeDesc'" >	code_desc	</when>
					<when test="item.sort=='codeDepth'" >	code_depth	</when>
					<when test="item.sort=='codeAcronym'" >	code_acronym	</when>
					<when test="item.sort=='codeSort'" >	code_sort	</when>
					<when test="item.sort=='useYn'" >	use_yn	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='deleteId'" >	delete_id	</when>
					<when test="item.sort=='deleteDate'" >	delete_date	</when>
		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneCodeItem" parameterType="HashMap" resultType="CodeItem">
        SELECT id, upper_id, code, upper_code, name, code_desc, code_acronym, code_depth, code_sort, use_yn, create_id, create_date, last_update_id, last_update_date, delete_yn, delete_id, delete_date
          FROM code_item a
          join (SELECT @rownum:= 0) rnum
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperId)" >	and upper_id=#{upperId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(code)" >	and code=#{code}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperCode)" >	and upper_code=#{upperCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(name)" >	and name=#{name}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeDesc)" >	and code_desc=#{codeDesc}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeDepth)" >	and code_depth=#{codeDepth}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeAcronym)" >	and code_acronym=#{codeAcronym}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeSort)" >	and code_sort=#{codeSort}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
         </where>
	</select>

	<insert id="insertCodeItem" parameterType="CodeItem" useGeneratedKeys="true" keyProperty="code">
        INSERT INTO code_item
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	id=#{id},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperId)" >	upper_id=#{upperId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(code)" >	code=#{code},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperCode)" >	upper_code=#{upperCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(name)" >	name=#{name},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeDesc)" >	code_desc=#{codeDesc},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeDepth)" >	code_depth=#{codeDepth},	</if>
			<choose>
				<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeAcronym)" >	code_acronym=#{codeAcronym},	</when>
				<otherwise>code_acronym=null,</otherwise>
			</choose>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeSort)" >	code_sort=#{codeSort},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},last_update_id=#{createId},	</if>
			create_date = now(), last_update_date = now()
		</set>
    </insert>

    <update id="updateCodeItem" parameterType="CodeItem">
        UPDATE code_item
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperId)" >	upper_id=#{upperId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(code)" >	code=#{code},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperCode)" >	upper_code=#{upperCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(name)" >	name=#{name},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeDesc)" >	code_desc=#{codeDesc},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeDepth)" >	code_depth=#{codeDepth},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeAcronym)" >	code_acronym=#{codeAcronym},	</if>
			<choose>
				<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeAcronym)" >	code_acronym=#{codeAcronym},	</when>
				<otherwise>code_acronym=null,</otherwise>
			</choose>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeSort)" >	code_sort=#{codeSort},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	last_update_date=#{lastUpdateDate},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	id=#{id}	</if>
		</where>
    </update>

    <delete id="deleteCodeItem" parameterType="CodeItem">
        delete from code_item
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(code)" >	and code=#{code}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperCode)" >	and upper_code=#{upperCode}	</if>
		</where>
    </delete>
</mapper>
