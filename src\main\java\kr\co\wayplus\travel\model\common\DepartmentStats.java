package kr.co.wayplus.travel.model.common;

import lombok.Getter;
import lombok.Setter;

//DepartmentStats.java (예시 데이터 클래스)
@Getter
@Setter
public class DepartmentStats {
	private String deptName;
	private int teamCount;
	private int regularEmployeeCount;
	private int contractEmployeeCount;
	private int totalEmployeeCount;
	private int totalCaseCount;
	private int completedCaseCount;
	private double completionRate;

	public double getCompletionRate() {
		if (totalCaseCount == 0)
			return 0.0;
		return (completedCaseCount * 100.0) / totalCaseCount;
	}
}