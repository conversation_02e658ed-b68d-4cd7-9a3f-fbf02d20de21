<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.manage.UserManageMapper">

	<select id="selectUserAccountStatusCount" parameterType="HashMap" resultType="HashMap">
        select account_status status, count(account_status) count
	      from user
	     where user_role not in('MASTER','ADMIN','MANAGER','STAFF')
	     group by account_status
    </select>

    <select id="selectUserListCount" parameterType="HashMap" resultType="Integer">
	with list as (
		 select user_email , GROUP_CONCAT(group_name) group_name
		   from user_group_connect a
		   join user_group b on a.group_id = b.id
		   group by user_email
		), mission as (
	    select create_id mission_writer, count(*) mission_count
		  from board_contents b
		 where board_id = 7
		   and upper_board_id is not null
		   and delete_yn = 'N'
		  group by create_id)
        SELECT count(*)
          FROM user a
          left join list b on a.user_email = b.user_email
          <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isMission) and isMission">join mission m on a.user_email = m.mission_writer</if>
         WHERE
			<choose>
              <when test="isUserRole">user_role is not null</when>
              <when test="userRole != null">user_role = #{userRole}</when>
              <when test="userRoles != null">
              user_role IN<foreach collection="userRoles" item="item" separator="," open="(" close=")">#{item}</foreach>
              </when>
              <otherwise>user_role = 'USER'</otherwise>
           </choose>

               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(notUserJoinTypes)">
               and user_join_type not IN<foreach collection="notUserJoinTypes" item="item" separator="," open="(" close=")">#{item}</foreach>
               </if>

               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(type)">
                   <if test="type != 'ALL'">
                       AND account_status = #{type}
                   </if>
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
                   AND (
                         a.user_email LIKE CONCAT('%', #{searchKey}, '%')
                         OR user_name LIKE CONCAT('%', #{searchKey}, '%')
                         OR REPLACE(user_mobile, '-', '') LIKE CONCAT('%', REPLACE(#{searchKey}, '-', ''), '%')
                       )
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchText)">
                   AND (  CONCAT(ifnull(user_name,''), ifnull(REPLACE(user_mobile, '-', ''),''), ifnull(a.user_email,'')) LIKE CONCAT('%', #{searchText}, '%') )
               </if>
               <if test="isOnlyNewJoin">and user_join_date BETWEEN  now() - interval 3 day and now()</if>
    </select>

    <select id="selectUserList" parameterType="HashMap" resultType="LoginUser">
	with list as (
		 select user_email , GROUP_CONCAT(group_name) group_name
		   from user_group_connect a
		   join user_group b on a.group_id = b.id
		   group by user_email
		) , mission as (
	    select create_id mission_writer, count(*) mission_count
		  from board_contents b
		 where board_id = 7
		   and upper_board_id is not null
		   and delete_yn = 'N'
		  group by create_id)
        SELECT case when a.user_join_type = 'reservation' then '비회원' else a.user_email end as user_email, user_join_type, user_name,
               user_nationality, ifnull(user_mobile,'') user_mobile, user_tel,
               user_addr_zipcode, user_addr_extra,
               user_addr_jibun, user_addr_road, user_addr_detail,
               user_role, user_class_name, user_class_code,
               user_group_code,
               user_token_id, user_grade_id, user_grade,
               user_grade_start, user_grade_end,
               user_membership_id, user_membership_grade,
               user_membership_start, user_membership_end,
               user_verified_email, user_verified_mobile,
               user_ci, user_di, user_di_corp,
               user_join_date, user_modify_date,
               last_login_date, last_password_date,
               last_login_fail_count, account_status,
               naver_token, naver_email, naver_join_date,
               kakao_token, kakao_email, kakao_join_date,
               google_token, google_email, google_join_date,
               facebook_token, facebook_email, facebook_join_date,
               secondary_email, privacy_retention_days, mailing_yn,
               ifnull(b.group_name,'') group_name
               , user_birthday, user_gender, join_root_type, join_reason_type
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isMission) and isMission">,m.mission_count</if>
          FROM user a
          left join list b on a.user_email = b.user_email
          <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isMission) and isMission">join mission m on a.user_email = m.mission_writer</if>
         WHERE
           <choose>
              <when test="isUserRole">user_role is not null</when>
              <when test="userRole != null">user_role = #{userRole}</when>
              <when test="userRoles != null">
              user_role IN<foreach collection="userRoles" item="item" separator="," open="(" close=")">#{item}</foreach>
              </when>
              <otherwise>user_role = 'USER'</otherwise>
           </choose>

           <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(notUserJoinTypes)">
               and user_join_type not IN<foreach collection="notUserJoinTypes" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>

           <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(type)">
               <if test="type != 'ALL'">
                   AND account_status = #{type}
               </if>
           </if>
           <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
               AND (
                       a.user_email LIKE CONCAT('%', #{searchKey}, '%')
                       OR user_name LIKE CONCAT('%', #{searchKey}, '%')
                       OR REPLACE(user_mobile, '-', '') LIKE CONCAT('%', REPLACE(#{searchKey}, '-', ''), '%')
                   )
           </if>
           <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchText)">
               AND (  CONCAT(ifnull(user_name,''), ifnull(REPLACE(user_mobile, '-', ''),''), ifnull(a.user_email,'')) LIKE CONCAT('%', #{searchText}, '%') )
           </if>
           <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
         ORDER BY
		        <choose>
		            <when test="sort=='userName'" >	user_name	</when>
		            <when test="sort=='userInfo'" >	CONCAT(ifnull(user_name,''), ifnull(REPLACE(user_mobile, '-', ''),'')	</when>
		            <when test="sort=='missionCount'" >	m.mission_count	</when>
		            <otherwise>user_join_date</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
           </if>
           <if test="@kr.co.wayplus.travel.util.MybatisUtil@isEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isEmpty(sortOrder)">
         ORDER BY user_join_date DESC
           </if>
         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
    </select>

    <update id="updateUserAccountStatus" parameterType="HashMap">
        UPDATE user
           SET account_status = #{accountStatus}, user_modify_date = now()
         WHERE user_email = #{userEmail} AND user_token_id = #{userTokenId}
    </update>

    <update id="updateUserAccountSimpleInfo" parameterType="HashMap">
        UPDATE user
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)"> user_name = #{userName},</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userMemo)"> user_memo = #{userMemo},</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userGrade)"> user_grade = #{userGrade},</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userMobile)"> user_mobile = #{userMobile},</if>
           user_modify_date = now()
		</set>
         WHERE user_email = #{userEmail} AND user_token_id = #{userTokenId}
    </update>

	<update id="updateUserShowYn" parameterType="LoginUser">
		update user
			set user_show_yn = #{userShowYn}
		where user_email = #{userEmail}
	</update>

    <select id="selectUserDetail" parameterType="HashMap" resultType="LoginUser">
        SELECT user_email, user_join_type, user_name, user_gender,
               user_birthday,
               user_nationality, user_mobile, user_tel,
               user_addr_zipcode, user_addr_extra,
               user_addr_jibun, user_addr_road, user_addr_detail,
               user_role, user_class_name, user_class_code,
               user_group_code,
               user_token_id, user_grade_id, user_grade,
               user_grade_start, user_grade_end,
               user_membership_id, user_membership_grade,
               user_membership_start, user_membership_end,
               user_verified_email, user_verified_mobile,
               user_ci, user_di, user_di_corp,
               user_join_date, user_modify_date,
               last_login_date, last_password_date,
               last_login_fail_count, account_status,
               naver_token, naver_email, naver_join_date,
               kakao_token, kakao_email, kakao_join_date,
               google_token, google_email, google_join_date,
               facebook_token, facebook_email, facebook_join_date,
               secondary_email, privacy_retention_days, mailing_yn,
               user_passport_no, user_name_en, user_passport_img_path
          FROM user
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userRole)">and user_role = #{userRole}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)">and user_name = #{userName}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">and user_email = #{userEmail}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userBirthday)">and user_birthday = #{userBirthday}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey) or @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(email)" >
			and (
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
				concat(user_name,user_mobile) like concat('%',#{searchKey},'%')
				</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(email)" >or</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(email)">
				user_email like concat('%',#{email},'%')
				</if>
				)
			</if>

		</where>
    </select>

    <insert id="insertUser" parameterType="LoginUser" useGeneratedKeys="true" keyProperty="userEmail">
        INSERT INTO user
            SET user_email = #{userEmail}
                , user_password = #{userPassword}
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userJoinType)">, user_join_type = #{userJoinType}</if>
                , user_name = #{userName}
                , user_name_en = #{userNameEn}
                , user_mobile = #{userMobile}
                , user_tel = #{userTel}
                , user_addr_zipcode = #{userAddrZipcode}
                , user_addr_extra = #{userAddrExtra}
                , user_addr_jibun = #{userAddrJibun}
                , user_addr_road = #{userAddrRoad}
                , user_addr_detail = #{userAddrDetail}
                , user_nationality = #{userNationality}
                , user_birthday = #{userBirthday}
                , user_gender = #{userGender}
                , user_passport_no = #{userPassportNo}
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPassportExpirationDay)">, user_passport_expiration_day = #{userPassportExpirationDay}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userNationality)">user_nationality = #{userNationality},</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userRole)">, user_role = #{userRole}</if>
                , user_class_name = #{userClassName}
                , user_class_code = #{userClassCode}
                , user_group_code = #{userGroupCode}
                , user_token_id = #{userTokenId}
                , user_grade = #{userGrade}
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userVerifiedEmail)">, user_verified_email = #{userVerifiedEmail}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userVerifiedMobile)">, user_verified_mobile = #{userVerifiedMobile}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userCi)">, user_ci = #{userCi}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userDi)">, user_di = #{userDi}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userDiCorp)">, user_di_corp = #{userDiCorp}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverToken)">, naver_token = #{naverToken}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverEmail)">, naver_email = #{naverEmail}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverToken)">, naver_join_date = now()</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoToken)">, kakao_token = #{kakaoToken}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoEmail)">, kakao_email = #{kakaoEmail}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoToken)">, kakao_join_date = now()</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleToken)">, google_token = #{googleToken}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleEmail)">, google_email = #{googleEmail}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleToken)">, google_join_date = now()</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookToken)">, facebook_token = #{facebookToken}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookEmail)">, facebook_email = #{facebookEmail}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookToken)">, facebook_join_date = now()</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secondaryEmail)">, secondary_email = #{secondary_email}</if>
                <if test="privacyRetentionDays > 0">, privacy_retention_days = #{privacyRetentionDays}</if>
                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mailingYn)">, mailing_yn = #{mailingYn}</if>
    </insert>


    <update id="updateMyUserInfo" parameterType="LoginUser">
        UPDATE user
           SET user_modify_date = now()
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPassword)">, user_password = #{userPassword}</if>
               , user_name = #{userName}
               , user_mobile = #{userMobile}
               , user_tel = #{userTel}
               , user_addr_zipcode = #{userAddrZipcode}
               , user_addr_extra = #{userAddrExtra}
               , user_addr_jibun = #{userAddrJibun}
               , user_addr_road = #{userAddrRoad}
               , user_addr_detail = #{userAddrDetail}
               , user_nationality = #{userNationality}
               , user_birthday = #{userBirthday}
               , user_gender = #{userGender}
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userRole)">, user_role = #{userRole}</if>
               , user_class_name = #{userClassName}
               , user_class_code = #{userClassCode}
               , user_group_code = #{userGroupCode}
               , user_grade = #{userGrade}
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userVerifiedEmail)">, user_verified_email = #{userVerifiedEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userVerifiedMobile)">, user_verified_mobile = #{userVerifiedMobile}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userCi)">, user_ci = #{userCi}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userDi)">, user_di = #{userDi}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userDiCorp)">, user_di_corp = #{userDiCorp}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverToken)">, naver_token = #{naverToken}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverEmail)">, naver_email = #{naverEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(naverToken)">, naver_join_date = now()</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoToken)">, kakao_token = #{kakaoToken}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoEmail)">, kakao_email = #{kakaoEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(kakaoToken)">, kakao_join_date = now()</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleToken)">, google_token = #{googleToken}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleEmail)">, google_email = #{googleEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(googleToken)">, google_join_date = now()</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookToken)">, facebook_token = #{facebookToken}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookEmail)">, facebook_email = #{facebookEmail}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(facebookToken)">, facebook_join_date = now()</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secondaryEmail)">, secondary_email = #{secondary_email}</if>
               <if test="privacyRetentionDays > 0">, privacy_retention_days = #{privacyRetentionDays}</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mailingYn)">, mailing_yn = #{mailingYn}</if>
         WHERE user_email = #{userEmail} AND user_token_id = #{userTokenId}
    </update>

    <select id="selectUserCustomerInfo" parameterType="HashMap" resultType="UserCustomerInfo">
        SELECT id, user_email,
               customer_grade, customer_note,
               create_type, create_id, create_date
          FROM user_customer_info
         WHERE user_email = #{value}
         ORDER BY id DESC
         LIMIT 1
    </select>

    <update id="updateUserInfoByManager" parameterType="LoginUser">
        UPDATE user
           SET <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)">user_name = #{userName},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userNameEn)">user_name_en = #{userNameEn},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userBirthday)">user_birthday = #{userBirthday},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(accountStatus)">account_status = #{accountStatus},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userGender)">user_gender = #{userGender},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userMobile)">user_mobile = #{userMobile},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPassportNo)">user_passport_no = #{userPassportNo},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPassportExpirationDay)">user_passport_expiration_day = #{userPassportExpirationDay},</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userNationality)">user_nationality = #{userNationality},</if>
               user_modify_date = now()
         WHERE user_email = #{userEmail}
           AND user_token_id = #{userTokenId}
    </update>

    <update id="updateUserInfoByPassportImgPath" parameterType="LoginUser">
        UPDATE user
           SET user_passport_img_path = #{userPassportImgPath}
         WHERE user_email = #{userEmail}
           AND user_token_id = #{userTokenId}
    </update>

    <insert id="insertUserCustomerInfo" parameterType="UserCustomerInfo">
        INSERT INTO user_customer_info
           SET user_email = #{userEmail},
               customer_grade = #{customerGrade},
               customer_note = #{customerNote},
               create_type = #{createType},
               create_id = #{createId},
               create_date = now()
    </insert>

    <select id="selectUserCustomerCounselListCount" parameterType="HashMap" resultType="Integer">
        SELECT count(*) FROM user_customer_counsel
		<where>
           and delete_yn = 'N'
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">AND user_email = #{userEmail}</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateType)">
				<choose>
					<when test="dateType eq 'req'">request_date between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d') </when>
					<when test="dateType eq 'res'">response_date between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d')</when>
				</choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestCategory)">AND request_category = #{requestCategory}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseName)">and response_name like concat('%',#{responseName},'%')</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and concat(request_name,request_tel) like concat('%',#{searchKey},'%')</if>
		</where>
    </select>

    <select id="selectUserCustomerCounselList" parameterType="HashMap" resultType="UserCustomerCounsel">
        SELECT a.id,
               a.user_email,
               b.user_name,
               ifnull(b.user_mobile,'') user_mobile,
               DATE_FORMAT( request_date , '%Y-%m-%d') request_date,
               request_category, request_subcategory,
               request_name, request_relation,
               request_tel, request_text,
               DATE_FORMAT( response_date , '%Y-%m-%d') response_date, response_name,
               response_text,
               response_category,c.name response_category_name, response_subcategory,
               a.create_id,
               a.create_date,
               a.last_update_id,
               a.last_update_date,
               b.user_join_type
          FROM user_customer_counsel a
          LEFT join user b on a.user_email = b.user_email
          left join code_item c on a.response_category = c.code and c.upper_code = 'statusType'
		<where>
           and a.delete_yn = 'N'
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">AND a.user_email = #{userEmail}</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateType)">
				<choose>
					<when test="dateType eq 'req'">request_date between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d') </when>
					<when test="dateType eq 'res'">response_date between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d')</when>
				</choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestCategory)">AND request_category = #{requestCategory}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseName)">and response_name like concat('%',#{responseName},'%')</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and concat(user_name,request_name,request_tel,request_name,response_name) like concat('%',#{searchKey},'%')</if>
		</where>
			         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort=='id'" >	id	</when>
		            <when test="sort=='requestDate'" >	request_date	</when>
		            <when test="sort=='responseDate'" >	response_date	</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort=='id'" >	id	</when>
		            <when test="item.sort=='requestDate'" >	request_date	</when>
		            <when test="item.sort=='responseDate'" >	response_date	</when>
		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>

         LIMIT #{itemStartPosition}, #{pagePerSize}
    </select>

    <select id="selectUserCustomerCounsel" parameterType="HashMap" resultType="UserCustomerCounsel">
        SELECT id, a.user_email,
               b.user_name,
               b.user_mobile,
               DATE_FORMAT( request_date , '%Y-%m-%d') request_date, request_category, request_subcategory,
               request_name, request_relation,
               request_tel, request_text,
               DATE_FORMAT( response_date , '%Y-%m-%d') response_date, response_name,
               response_text,
               response_category, response_subcategory,
               create_id, create_date,
               last_update_id, last_update_date,
               delete_yn
          FROM user_customer_counsel a
          LEFT join user b on a.user_email = b.user_email
		<where>
           and delete_yn = 'N'
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">AND id = #{id}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">AND a.user_email = #{userEmail}</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateType)">
				<choose>
					<when test="dateType eq 'req'">request_date between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d') </when>
					<when test="dateType eq 'res'">response_date between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d')</when>
				</choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestCategory)">AND request_category = #{requestCategory}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseName)">and response_name like concat('%',#{responseName},'%')</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and concat(request_name,request_tel) like concat('%',#{searchKey},'%')</if>
		</where>
    </select>

    <insert id="insertUserCustomerCounsel" parameterType="UserCustomerCounsel">
        INSERT INTO user_customer_counsel
           SET user_email = #{userEmail},
               request_date = date_format(#{requestDate},'%Y-%m-%d'),
               request_category = #{requestCategory},
               request_subcategory = #{requestSubcategory},
               request_name = #{requestName},
               request_relation = #{requestRelation},
               request_tel = #{requestTel},
               request_text = #{requestText},
		<choose>
			<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseDate)">response_date = date_format(#{responseDate},'%Y-%m-%d'),</when>
			<otherwise>response_date = now(),</otherwise>
		</choose>
               response_name = #{responseName},
               response_text = #{responseText},
               response_category = #{responseCategory},
               response_subcategory = #{responseSubcategory},
               create_id = #{createId},
               create_date = now()
    </insert>

    <update id="deleteUserCustomerCounsel" parameterType="UserCustomerCounsel">
        Update user_customer_counsel
           SET delete_yn = 'Y',
               delete_date = now()
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">AND id = #{id}</if>
		</where>
    </update>

    <update id="restoreUserCustomerCounsel" parameterType="UserCustomerCounsel">
        Update user_customer_counsel
           SET delete_yn = 'N',
               delete_date = null
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">AND id = #{id}</if>
		</where>
    </update>

	<select id="selectUserListByRole" parameterType="map" resultType="LoginUser">
        SELECT user_email, user_name, user_nick_name, user_mobile, user_role,
               user_join_date, last_login_date, naver_token, kakao_token, user_profile_image, user_intro
			   , user_intro_page_link, user_live_place_type, user_live_place, user_show_yn
        FROM user as u
        WHERE account_status IN ('active', 'inactive')
          AND user_role = #{role}
        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">AND user_email like concat('%',#{userEmail},'%')</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)">AND user_name like concat('%',#{userName},'%')</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userShowYn)">AND user_show_yn = #{userShowYn}</if>
        ORDER BY user_join_date
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
    </select>

	<select id="selectCountUserListByRole" resultType="Integer">
		SELECT count(user_email)
		FROM user as u
		WHERE account_status IN ('active', 'inactive')
		  AND user_role = #{role}
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)">AND user_email like concat('%',#{userEmail},'%')</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)">AND user_name like concat('%',#{userName},'%')</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userShowYn)">AND user_show_yn = #{userShowYn}</if>
	</select>

	<!--################################### userCustomerOrderHistory ###################################-->
	 <select id="selectCountUserCustomerOrderHistory" parameterType="HashMap" resultType="Integer">
        SELECT count(history_seq)
          FROM (
			SELECT history_seq
			FROM user_customer_order_history a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(historySeq)" >	and hisoty_seq = #{histroySeq}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email = #{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid = #{payMoid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payStatusType)" >	and pay_status_type = #{payStatusType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payTid)" >	and pay_tid = #{payTid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(method)" >	and method = #{method}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(amt)" >	and amt = #{amt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultCode)" >	and status_result_code = #{statusResultCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultMsg)" >	and status_result_msg = #{statusResultMsg}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payStatusDttm)" >	and pay_status_dttm = #{payStatusDttm}	</if>
         </where>) a
    </select>

	<select id="selectListUserCustomerOrderHistory" parameterType="HashMap" resultType="userCustomerOrderHistory">
		SELECT *
		  FROM(
	        SELECT @rownum:=@rownum+1 AS rownum,
					a.*,
					b.product_title,
					b.product_thumbnail,
					(case when mu.upper_menu_id is not null then (select umu.menu_url from menu_user umu where umu.menu_id = mu.upper_menu_id ) else mu.menu_url end) menu_url
			  FROM user_customer_order_history a
			  left join product_tour b on a.product_serial  = b.product_serial and a.product_tour_id  = b.product_tour_id
			  LEFT JOIN menu_user mu on mu.menu_id = b.product_menu_id
			  join (SELECT @rownum:= 0) rnum
	         <where>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(historySeq)" >	and hisoty_seq = #{histroySeq}	</if>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email = #{userEmail}	</if>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid = #{payMoid}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payStatusType)" >	and pay_status_type = #{payStatusType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payTid)" >	and pay_tid = #{payTid}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(method)" >	and method = #{method}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(amt)" >	and amt = #{amt}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultCode)" >	and status_result_code = #{statusResultCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultMsg)" >	and status_result_msg = #{statusResultMsg}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payStatusDttm)" >	and pay_status_dttm = #{payStatusDttm}	</if>
	         </where>

	         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort=='historySeq'" >	hisoty_seq	</when>
		            <when test="sort=='payStatusType'" >	pay_status_type	</when>
		            <when test="sort=='payTid'" >	pay_tid	</when>
					<when test="sort=='method'" >	method	</when>
					<when test="sort=='amt'" >	amt	</when>
					<when test="sort=='statusResultCode'" >	status_result_code	</when>
					<when test="sort=='statusResultMsg'" >	status_result_msg	</when>
					<when test="sort=='payStatusDttm'" >	pay_status_dttm	</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort=='historySeq'" >	hisoty_seq	</when>
		            <when test="item.sort=='payStatusType'" >	pay_status_type	</when>
		            <when test="item.sort=='payTid'" >	pay_tid	</when>
					<when test="item.sort=='method'" >	method	</when>
					<when test="item.sort=='amt'" >	amt	</when>
					<when test="item.sort=='statusResultCode'" >	status_result_code	</when>
					<when test="item.sort=='statusResultMsg'" >	status_result_msg	</when>
					<when test="item.sort=='payStatusDttm'" >	pay_status_dttm	</when>
		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>
	<select id="selectOneUserCustomerOrderHistory" parameterType="HashMap" resultType="userCustomerOrderHistory">
         SELECT @rownum:=@rownum+1 AS rownum,
        		a.*
          FROM user_customer_order_history a
          join (SELECT @rownum:= 0) rnum
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(historySeq)" >	and hisoty_seq = #{historySeq}	</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email = #{userEmail}	</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid = #{payMoid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payStatusType)" >	and pay_status_type = #{payStatusType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payTid)" >	and pay_tid = #{payTid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(method)" >	and method = #{method}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(amt)" >	and amt = #{amt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultCode)" >	and status_result_code = #{statusResultCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultMsg)" >	and status_result_msg = #{statusResultMsg}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payStatusDttm)" >	and pay_status_dttm = #{payStatusDttm}	</if>
         </where>
	</select>

	<insert id="insertUserCustomerOrderHistory" parameterType="userCustomerOrderHistory" useGeneratedKeys="true" keyProperty="historySeq">
        INSERT INTO user_customer_order_history
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email = #{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payTid)" >	pay_tid = #{payTid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	pay_moid = #{payMoid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payStatusType)" >	pay_status_type = #{payStatusType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(method)" >	method = #{method},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(amt)" >	amt = #{amt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultCode)" >	status_result_code = #{statusResultCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultMsg)" >	status_result_msg = #{statusResultMsg},	</if>
			pay_status_dttm = now()
		</set>
    </insert>

<!--################################### Reservation ###################################-->
	 <select id="selectCountUserCustomerPayment" parameterType="HashMap" resultType="Integer">
        SELECT count(id)
          FROM (
			SELECT a.id
			FROM user_customer_payment a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payDate)" >	and pay_date=#{payDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payDivision)" >	and pay_division=#{payDivision}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMethod)" >	and pay_method=#{payMethod}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payComment)" >	and pay_comment=#{payComment}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payAmount)" >	and pay_amount=#{payAmount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mid)" >	and mid=#{mid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tid)" >	and tid=#{tid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusCode)" >	and status_code=#{statusCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusName)" >	and status_name=#{statusName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
         </where>) a
    </select>

	<select id="selectListUserCustomerPayment" parameterType="HashMap" resultType="UserCustomerPayment">
		SELECT *
		  FROM(
	        SELECT @rownum:=@rownum+1 AS rownum,
	        		a.id	,a.reservation_id	,a.user_email	,a.pay_date	,a.pay_division	,
					a.pay_method	,a.pay_comment	,a.pay_amount	,a.mid	,a.tid	,a.status_code	,
					a.status_name	,a.create_id,	u.user_name create_name, a.create_date	,
					a.last_update_id	,a.last_update_date	,a.delete_yn	,a.delete_id	,a.delete_date
	          FROM user_customer_payment a
	          left join code_item b on a.pay_method = b.code and b.upper_code = 'UserCustomerPaymentType'
	          left join `user` u on a.user_email = u.user_email
	          join (SELECT @rownum:= 0) rnum
	         <where>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payDate)" >	and pay_date=#{payDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payDivision)" >	and pay_division=#{payDivision}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMethod)" >	and pay_method=#{payMethod}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payComment)" >	and pay_comment=#{payComment}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payAmount)" >	and pay_amount=#{payAmount}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mid)" >	and mid=#{mid}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tid)" >	and tid=#{tid}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusCode)" >	and status_code=#{statusCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusName)" >	and status_name=#{statusName}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
	         </where>

	         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort=='id'" >	id	</when>
					<when test="sort=='reservationId'" >	reservation_id	</when>
					<when test="sort=='userEmail'" >	user_email	</when>
					<when test="sort=='payDate'" >	pay_date	</when>
					<when test="sort=='payDivision'" >	pay_division	</when>
					<when test="sort=='payMethod'" >	pay_method	</when>
					<when test="sort=='payComment'" >	pay_comment	</when>
					<when test="sort=='payAmount'" >	pay_amount	</when>
					<when test="sort=='mid'" >	mid	</when>
					<when test="sort=='tid'" >	tid	</when>
					<when test="sort=='statusCode'" >	status_code	</when>
					<when test="sort=='statusName'" >	status_name	</when>
					<when test="sort=='createId'" >	create_id	</when>
					<when test="sort=='createDate'" >	create_date	</when>
					<when test="sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="sort=='deleteYn'" >	delete_yn	</when>
					<when test="sort=='deleteId'" >	delete_id	</when>
					<when test="sort=='deleteDate'" >	delete_date	</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort=='id'" >	id	</when>
					<when test="item.sort=='reservationId'" >	reservation_id	</when>
					<when test="item.sort=='userEmail'" >	user_email	</when>
					<when test="item.sort=='payDate'" >	pay_date	</when>
					<when test="item.sort=='payDivision'" >	pay_division	</when>
					<when test="item.sort=='payMethod'" >	pay_method	</when>
					<when test="item.sort=='payComment'" >	pay_comment	</when>
					<when test="item.sort=='payAmount'" >	pay_amount	</when>
					<when test="item.sort=='mid'" >	mid	</when>
					<when test="item.sort=='tid'" >	tid	</when>
					<when test="item.sort=='statusCode'" >	status_code	</when>
					<when test="item.sort=='statusName'" >	status_name	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='deleteId'" >	delete_id	</when>
					<when test="item.sort=='deleteDate'" >	delete_date	</when>
		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectListUserCustomerPaymentVirtualTotal" parameterType="HashMap" resultType="HashMap">
		SELECT *
		  FROM(
			SELECT @amtg := sum(case when pay_division in ('G','A') then pay_amount else 0 end) amt_g,
			       @amtd := sum(case when pay_division = 'D' then pay_amount else 0 end) amt_d,
			       @amte := sum(case when pay_division = 'E' then pay_amount else 0 end) amt_e,
			       @amtg - @amtd - @amte amt_b
			  FROM user_customer_payment
	         <where>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
	         </where>

	         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <otherwise>pay_date</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectCountUserCustomerPaymentVirtual" parameterType="HashMap" resultType="Integer">
		SELECT count(id)
		  FROM(
			SELECT id
			  FROM user_customer_payment a
	          join (SELECT @rownum:= 0) rnum
	          left join `user` u on a.user_email = u.user_email
			  left join `user` u1 on a.create_id = u1.user_email
	         <where>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and a.id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and a.reservation_id=#{reservationId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and a.user_email=#{userEmail}	</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateType)">
					<choose>
						<when test="dateType eq 'payDate'">   a.pay_date between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d') </when>
						<when test="dateType eq 'createDate'">a.create_date between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d')</when>
					</choose>
				</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payDivision)">AND a.pay_division = #{payDivision}</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseName)">and u1.user_name like concat('%',#{responseName},'%')</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and concat(u.user_name,u.user_mobile, pay_comment) like concat('%',#{searchKey},'%')</if>
	         </where>
			) a
	</select>

	<select id="selectListUserCustomerPaymentVirtual" parameterType="HashMap" resultType="HashMap">
		SELECT *
		  FROM(
			SELECT @rownum:=@rownum+1 AS rownum,
			       a.id,
			       IFNULL( a.reservation_id, '') reservation_id,
			       IFNULL( a.user_email, '') user_email,
			       IFNULL( u.user_name,'') user_name,
			       IFNULL( u.user_mobile,'~연락처없음~') user_mobile,
			       date_format(pay_date,'%Y-%m-%d %H:%i:%S') pay_date,
			       case when pay_division in ('G','A') then pay_amount else 0 end amt_g,
			       case when pay_division = 'D' then pay_amount else 0 end amt_d,
			       case when pay_division = 'E' then pay_amount else 0 end amt_e,
			       pay_comment,
			       a.create_id,
			       u1.user_name create_name,
			       date_format(a.create_date,'%Y-%m-%d %H:%i:%S') create_date
			  FROM user_customer_payment a
			  left join `user` u on a.user_email = u.user_email
			  left join `user` u1 on a.create_id = u1.user_email
	          join (SELECT @rownum:= 0) rnum
	         <where>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and a.id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and a.reservation_id=#{reservationId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and a.user_email=#{userEmail}	</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateType)">
					<choose>
						<when test="dateType eq 'payDate'">   a.pay_date between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d') </when>
						<when test="dateType eq 'createDate'">a.create_date between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d')</when>
					</choose>
				</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payDivision)">AND a.pay_division = #{payDivision}</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(responseName)">and u1.user_name like concat('%',#{responseName},'%')</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and concat(u.user_name,u.user_mobile, pay_comment) like concat('%',#{searchKey},'%')</if>
	         </where>

	         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isEmpty(listSort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		        	<when test="sort=='payDate'" >	pay_date	</when>
		        	<when test="sort=='payComment'" >	pay_comment	</when>
		            <otherwise>pay_date</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort=='payDate'" >	pay_date	</when>
		            <when test="item.sort=='payComment'" >	pay_comment	</when>
					<when test="item.sort=='amtG'" >	amt_g	</when>
					<when test="item.sort=='amtD'" >	amt_d	</when>
					<when test="item.sort=='amtE'" >	amt_e	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createName'" >	create_name	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='deleteId'" >	delete_id	</when>
					<when test="item.sort=='deleteDate'" >	delete_date	</when>
					<when test="item.sort=='deleteName'" >	create_name	</when>
		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneUserCustomerPayment" parameterType="HashMap" resultType="UserCustomerPayment">
         SELECT @rownum:=@rownum+1 AS rownum,
        		a.id	,a.reservation_id	,a.user_email	,a.pay_date	,a.pay_division	,a.pay_method	,a.pay_comment	,a.pay_amount	,a.mid	,a.tid	,a.status_code	,a.status_name	,a.create_id	,a.create_date	,a.last_update_id	,a.last_update_date	,a.delete_yn	,a.delete_id	,a.delete_date
          FROM user_customer_payment a
          left join code_item b on a.pay_method = b.code and b.upper_code = 'UserCustomerPaymentType'
          left join `user` u on a.user_email = u.user_email
          join (SELECT @rownum:= 0) rnum
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and a.id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and a.reservation_id=#{reservationId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and a.user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payDate)" >	and a.pay_date=#{payDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payDivision)" >	and a.pay_division=#{payDivision}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMethod)" >	and a.pay_method=#{payMethod}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payComment)" >	and a.pay_comment=#{payComment}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payAmount)" >	and a.pay_amount=#{payAmount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mid)" >	and a.mid=#{mid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tid)" >	and a.tid=#{tid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusCode)" >	and a.status_code=#{statusCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusName)" >	and a.status_name=#{statusName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and a.create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and a.create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and a.last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and a.last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and a.delete_date=#{deleteDate}	</if>
         </where>
	</select>

	<insert id="insertUserCustomerPayment" parameterType="UserCustomerPayment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_customer_payment
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	reservation_id=#{reservationId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<choose>
				<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payDate)">pay_date=#{payDate},</when>
				<otherwise>pay_date=now(),</otherwise>
			</choose>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payDivision)" >	pay_division=#{payDivision},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMethod)" >	pay_method=#{payMethod},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payComment)" >	pay_comment=#{payComment},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payAmount)" >	pay_amount=#{payAmount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mid)" >	mid=#{mid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tid)" >	tid=#{tid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusCode)" >	status_code=#{statusCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusName)" >	status_name=#{statusName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			create_date = now()
		</set>
    </insert>

    <update id="updateUserCustomerPayment" parameterType="UserCustomerPayment">
        UPDATE user_customer_payment
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	reservation_id=#{reservationId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payDate)" >	pay_date=#{payDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payDivision)" >	pay_division=#{payDivision},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMethod)" >	pay_method=#{payMethod},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payComment)" >	pay_comment=#{payComment},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payAmount)" >	pay_amount=#{payAmount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mid)" >	mid=#{mid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tid)" >	tid=#{tid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusCode)" >	status_code=#{statusCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusName)" >	status_name=#{statusName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
		</where>
    </update>

    <update id="restoreUserCustomerPayment" parameterType="UserCustomerPayment">
        UPDATE user_customer_payment
           SET delete_yn='N'
        WHERE id = #{id}
    </update>

    <update id="deleteUserCustomerPayment" parameterType="UserCustomerPayment">
        UPDATE user_customer_payment
           SET delete_yn='Y'
        WHERE id = #{id}
    </update>

    <!--################################### userCustomerOrder ###################################-->
	 <select id="selectCountUserCustomerOrder" parameterType="HashMap" resultType="Integer">
        SELECT count(pay_moid)
          FROM (
			SELECT pay_moid
			FROM user_customer_order a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid=#{payMoid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	and product_amt=#{productAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	and product_delivery_amt=#{productDeliveryAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	and product_count=#{productCount}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	and product_status=#{productStatus}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderStatus)" >	and order_status=#{orderStatus}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	and order_date=#{orderDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderTime)" >	and order_Time=#{orderTime}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCode)" >	and vbank_code=#{bankCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankName)" >	and vbank_name=#{vbankName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankNum)" >	and vbank_num=#{vbankNum}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpDate)" >	and vbank_exp_date=#{vbankExpDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpTime)" >	and vbank_exp_time=#{vbankExpTime}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankCode)" >	and vbank_cancel_bank_code=#{vbankCancelBankCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankNum)" >	and vbank_cancel_bank_num=#{vbankCancelBankNum}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankName)" >	and vbank_cancel_bank_name=#{vbankCancelBankName}	</if>

         </where>) a
    </select>

	<select id="selectListUserCustomerOrder" parameterType="HashMap" resultType="UserCustomerOrder">
		SELECT *
		  FROM(
	        SELECT @rownum:=@rownum+1 AS rownum,
					a.*,
					b.product_title,
					b.product_thumbnail,
					(case when mu.upper_menu_id is not null then (select umu.menu_url from menu_user umu where umu.menu_id = mu.upper_menu_id ) else mu.menu_url end) menu_url
			  FROM user_customer_order a
			  left join product_tour b on a.product_serial  = b.product_serial and a.product_tour_id  = b.product_tour_id
			  LEFT JOIN menu_user mu on mu.menu_id = b.product_menu_id
			  join (SELECT @rownum:= 0) rnum
	         <where>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid=#{payMoid}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	and product_amt=#{productAmt}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	and product_delivery_amt=#{productDeliveryAmt}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	and product_count=#{productCount}	</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	and product_status=#{productStatus}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderStatus)" >	and order_status=#{orderStatus}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	and order_date=#{orderDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderTime)" >	and order_Time=#{orderTime}	</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCode)" >	and vbank_code=#{bankCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankName)" >	and vbank_name=#{vbankName}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankNum)" >	and vbank_num=#{vbankNum}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpDate)" >	and vbank_exp_date=#{vbankExpDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpTime)" >	and vbank_exp_time=#{vbankExpTime}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankCode)" >	and vbank_cancel_bank_code=#{vbankCancelBankCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankNum)" >	and vbank_cancel_bank_num=#{vbankCancelBankNum}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankName)" >	and vbank_cancel_bank_name=#{vbankCancelBankName}	</if>
	         </where>

	         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort=='travelScheduleDt'" >	travel_schedule_dt	</when>
		            <when test="sort=='productTitle'" >	p.product_title	</when>
		            <when test="sort=='id'" >	id	</when>
					<when test="sort=='userEmail'" >	user_email	</when>
					<when test="sort=='userName'" >	user_name	</when>
					<when test="sort=='userMobile'" >	user_mobile	</when>
					<when test="sort=='berthType'" >	berth_type	</when>
					<when test="sort=='productSerial'" >	product_serial	</when>
					<when test="sort=='applyCode'" >	apply_code	</when>
					<when test="sort=='reservationCode'" >	reservation_code	</when>
					<when test="sort=='cancelYn'" >	cancel_yn	</when>
					<when test="sort=='cancelCode'" >	cancel_code	</when>
					<when test="sort=='berthJson'" >	berth_json	</when>
					<when test="sort=='travelScheduleJson'" >	travel_schedule_json	</when>
					<when test="sort=='airTypeRequest'" >	air_type_request	</when>
					<when test="sort=='airScheduleJson'" >	air_schedule_json	</when>
					<when test="sort=='vehicleType'" >	vehicle_type	</when>
					<when test="sort=='vehicleCount'" >	vehicle_count	</when>
					<when test="sort=='vehicleJson'" >	vehicle_json	</when>
					<when test="sort=='totalAmount'" >	total_amount	</when>
					<when test="sort=='createId'" >	create_id	</when>
					<when test="sort=='createDate'" >	create_date	</when>
					<when test="sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="sort=='deleteYn'" >	delete_yn	</when>
					<when test="sort=='deleteId'" >	delete_id	</when>
					<when test="sort=='deleteDate'" >	delete_date	</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort=='travelScheduleDt'" >	travel_schedule_dt	</when>
		            <when test="item.sort=='productTitle'" >	p.product_title	</when>
		            <when test="item.sort=='id'" >	id	</when>
					<when test="item.sort=='userEmail'" >	user_email	</when>
					<when test="item.sort=='userName'" >	user_name	</when>
					<when test="item.sort=='userMobile'" >	user_mobile	</when>
					<when test="item.sort=='berthType'" >	berth_type	</when>
					<when test="item.sort=='productSerial'" >	product_serial	</when>
					<when test="item.sort=='applyCode'" >	apply_code	</when>
					<when test="item.sort=='reservationCode'" >	reservation_code	</when>
					<when test="item.sort=='cancelYn'" >	cancel_yn	</when>
					<when test="item.sort=='cancelCode'" >	cancel_code	</when>
					<when test="item.sort=='berthJson'" >	berth_json	</when>
					<when test="item.sort=='travelScheduleJson'" >	travel_schedule_json	</when>
					<when test="item.sort=='airTypeRequest'" >	air_type_request	</when>
					<when test="item.sort=='airScheduleJson'" >	air_schedule_json	</when>
					<when test="item.sort=='vehicleType'" >	vehicle_type	</when>
					<when test="item.sort=='vehicleCount'" >	vehicle_count	</when>
					<when test="item.sort=='vehicleJson'" >	vehicle_json	</when>
					<when test="item.sort=='totalAmount'" >	total_amount	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='deleteId'" >	delete_id	</when>
					<when test="item.sort=='deleteDate'" >	delete_date	</when>

		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>
	<select id="selectOneUserCustomerOrder" parameterType="HashMap" resultType="UserCustomerOrder">
         SELECT @rownum:=@rownum+1 AS rownum,
        		a.*
          FROM user_customer_order a
          join (SELECT @rownum:= 0) rnum
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid=#{payMoid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	and product_amt=#{productAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	and product_delivery_amt=#{productDeliveryAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	and product_count=#{productCount}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	and product_status=#{productStatus}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderStatus)" >	and order_status=#{orderStatus}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderStatusName)" >	and order_status_name=#{orderStatusName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	and order_date=#{orderDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderTime)" >	and order_Time=#{orderTime}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCode)" >	and vbank_code=#{bankCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankName)" >	and vbank_name=#{vbankName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankNum)" >	and vbank_num=#{vbankNum}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpDate)" >	and vbank_exp_date=#{vbankExpDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpTime)" >	and vbank_exp_time=#{vbankExpTime}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankCode)" >	and vbank_cancel_bank_code=#{vbankCancelBankCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankNum)" >	and vbank_cancel_bank_num=#{vbankCancelBankNum}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankName)" >	and vbank_cancel_bank_name=#{vbankCancelBankName}	</if>
         </where>
	</select>

	<insert id="insertUserCustomerOrder" parameterType="UserCustomerOrder">
        INSERT INTO user_customer_order
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	pay_moid=#{payMoid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tid)" >	tid=#{tid},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	reservation_id=#{reservationId},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	product_amt=#{productAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	product_delivery_amt=#{productDeliveryAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	product_count=#{productCount},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderStatus)" >	order_status=#{orderStatus},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderStatusName)" >	order_status_name=#{orderStatusName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	order_date=#{orderDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(optionTime)" >	option_time=#{optionTime},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultCode)" >	status_result_code = #{statusResultCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultMsg)" >	status_result_msg = #{statusResultMsg},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(method)" >	method = #{method},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(macketId)" >	macket_id = #{macketId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mid)" >	mid = #{mid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tid)" >	tid = #{tid},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCode)" >	vbank_code=#{bankCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankName)" >	vbank_name=#{vbankName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankNum)" >	vbank_num=#{vbankNum},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpDate)" >	vbank_exp_date=#{vbankExpDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpTime)" >	vbank_exp_time=#{vbankExpTime},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankCode)" >	vbank_cancel_bank_code=#{vbankCancelBankCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankNum)" >	vbank_cancel_bank_num=#{vbankCancelBankNum},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankName)" >	vbank_cancel_bank_name=#{vbankCancelBankName},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payType)" >	pay_type=#{payType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMethod)" >	pay_method=#{payMethod},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tossPaymentJson)" >	toss_payment_json=#{tossPaymentJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tossPaymentCancelJson)" >	toss_payment_cancel_json=#{tossPaymentCancelJson},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			create_date = now()
		</set>
    </insert>

    <update id="updateUserCustomerOrder" parameterType="UserCustomerOrder">
        UPDATE user_customer_order
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	product_amt=#{productAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	product_delivery_amt=#{productDeliveryAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	product_count=#{productCount},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderStatus)" >	order_status=#{orderStatus},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderStatusName)" >	order_status_name=#{orderStatusName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	order_date=#{orderDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(optionTime)" >	option_time=#{optionTime},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultCode)" >	status_result_code = #{statusResultCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultMsg)" >	status_result_msg = #{statusResultMsg},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(method)" >	method = #{method},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(macketId)" >	macket_id = #{macketId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mid)" >	mid = #{mid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tid)" >	tid = #{tid},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCode)" >	vbank_code=#{bankCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankName)" >	vbank_name=#{vbankName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankNum)" >	vbank_num=#{vbankNum},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpDate)" >	vbank_exp_date=#{vbankExpDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpTime)" >	vbank_exp_time=#{vbankExpTime},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankCode)" >	vbank_cancel_bank_code=#{vbankCancelBankCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankNum)" >	vbank_cancel_bank_num=#{vbankCancelBankNum},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankName)" >	vbank_cancel_bank_name=#{vbankCancelBankName},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payType)" >	pay_type=#{payType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMethod)" >	pay_method=#{payMethod},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tossPaymentJson)" >	toss_payment_json=#{tossPaymentJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tossPaymentCancelJson)" >	toss_payment_cancel_json=#{tossPaymentCancelJson},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)">and pay_moid=#{payMoid}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >and reservation_id=#{reservationId}	</if>
		</where>
    </update>

    <update id="restoreUserCustomerOrder" parameterType="UserCustomerOrder">
        UPDATE user_customer_order_list
           SET delete_yn='N'
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)">and pay_moid=#{payMoid}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >and user_email=#{userEmail}</if>
		</where>
    </update>

    <update id="deleteUserCustomerOrder" parameterType="UserCustomerOrder">
        UPDATE user_customer_order_list
           SET delete_yn='Y'
        <where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)">and pay_moid=#{payMoid}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >and user_email=#{userEmail}</if>
		</where>
    </update>

	<!--################################### userCustomerOrderList ###################################-->
	 <select id="selectCountUserCustomerOrderList" parameterType="HashMap" resultType="Integer">
        SELECT count(pay_product_seq)
          FROM (
			SELECT
				pay_product_seq
			FROM User_customer_order_list a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payProductSeq)" >	and pay_product_seq=#{payProductSeq}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmailr}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid=#{payMoid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_serial=#{productSerial}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	and product_tour_id=#{productTourId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productName)" >	and product_name=#{productName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	and product_count=#{productCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	and product_amt=#{productAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	and product_delivery_amt=#{productDeliveryAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	and product_status=#{productStatus}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelProductCount)" >	and cancel_product_count=#{cancelProductCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
         </where>) a
    </select>

	<select id="selectListUserCustomerOrderList" parameterType="HashMap" resultType="UserCustomerOrderList">
		SELECT *
		  FROM(
	        SELECT @rownum:=@rownum+1 AS rownum,
					a.*,
					b.product_title,
					b.product_thumbnail,
					(case when mu.upper_menu_id is not null then (select umu.menu_url from menu_user umu where umu.menu_id = mu.upper_menu_id ) else mu.menu_url end) menu_url
			  FROM user_customer_order_list a
			  left join product_tour b on a.product_serial  = b.product_serial and a.product_tour_id  = b.product_tour_id
			  LEFT JOIN menu_user mu on mu.menu_id = b.product_menu_id
			  join (SELECT @rownum:= 0) rnum
	         <where>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payProductSeq)" >	and pay_product_seq=#{payProductSeq}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid=#{payMoid}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_serial=#{productSerial}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	and product_tour_id=#{productTourId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productName)" >	and product_name=#{productName}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	and product_count=#{productCount}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	and product_amt=#{productAmt}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	and product_delivery_amt=#{productDeliveryAmt}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	and product_status=#{productStatus}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelProductCount)" >	and cancel_product_count=#{cancelProductCount}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
	         </where>

	         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort=='travelScheduleDt'" >	travel_schedule_dt	</when>
		            <when test="sort=='productTitle'" >	p.product_title	</when>
		            <when test="sort=='id'" >	id	</when>
					<when test="sort=='userEmail'" >	user_email	</when>
					<when test="sort=='userName'" >	user_name	</when>
					<when test="sort=='userMobile'" >	user_mobile	</when>
					<when test="sort=='berthType'" >	berth_type	</when>
					<when test="sort=='productSerial'" >	product_serial	</when>
					<when test="sort=='applyCode'" >	apply_code	</when>
					<when test="sort=='reservationCode'" >	reservation_code	</when>
					<when test="sort=='cancelYn'" >	cancel_yn	</when>
					<when test="sort=='cancelCode'" >	cancel_code	</when>
					<when test="sort=='berthJson'" >	berth_json	</when>
					<when test="sort=='travelScheduleJson'" >	travel_schedule_json	</when>
					<when test="sort=='airTypeRequest'" >	air_type_request	</when>
					<when test="sort=='airScheduleJson'" >	air_schedule_json	</when>
					<when test="sort=='vehicleType'" >	vehicle_type	</when>
					<when test="sort=='vehicleCount'" >	vehicle_count	</when>
					<when test="sort=='vehicleJson'" >	vehicle_json	</when>
					<when test="sort=='totalAmount'" >	total_amount	</when>
					<when test="sort=='createId'" >	create_id	</when>
					<when test="sort=='createDate'" >	create_date	</when>
					<when test="sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="sort=='deleteYn'" >	delete_yn	</when>
					<when test="sort=='deleteId'" >	delete_id	</when>
					<when test="sort=='deleteDate'" >	delete_date	</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort=='travelScheduleDt'" >	travel_schedule_dt	</when>
		            <when test="item.sort=='productTitle'" >	p.product_title	</when>
		            <when test="item.sort=='id'" >	id	</when>
					<when test="item.sort=='userEmail'" >	user_email	</when>
					<when test="item.sort=='userName'" >	user_name	</when>
					<when test="item.sort=='userMobile'" >	user_mobile	</when>
					<when test="item.sort=='berthType'" >	berth_type	</when>
					<when test="item.sort=='productSerial'" >	product_serial	</when>
					<when test="item.sort=='applyCode'" >	apply_code	</when>
					<when test="item.sort=='reservationCode'" >	reservation_code	</when>
					<when test="item.sort=='cancelYn'" >	cancel_yn	</when>
					<when test="item.sort=='cancelCode'" >	cancel_code	</when>
					<when test="item.sort=='berthJson'" >	berth_json	</when>
					<when test="item.sort=='travelScheduleJson'" >	travel_schedule_json	</when>
					<when test="item.sort=='airTypeRequest'" >	air_type_request	</when>
					<when test="item.sort=='airScheduleJson'" >	air_schedule_json	</when>
					<when test="item.sort=='vehicleType'" >	vehicle_type	</when>
					<when test="item.sort=='vehicleCount'" >	vehicle_count	</when>
					<when test="item.sort=='vehicleJson'" >	vehicle_json	</when>
					<when test="item.sort=='totalAmount'" >	total_amount	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='deleteId'" >	delete_id	</when>
					<when test="item.sort=='deleteDate'" >	delete_date	</when>

		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>
	<select id="selectOneUserCustomerOrderList" parameterType="HashMap" resultType="UserCustomerOrderList">
         SELECT @rownum:=@rownum+1 AS rownum,
        		a.*
          FROM User_customer_order_list a
          join (SELECT @rownum:= 0) rnum
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payProductSeq)" >	and pay_product_seq=#{payProductSeq}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid=#{payMoid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_serial=#{productSerial}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	and product_tour_id=#{productTourId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productName)" >	and product_name=#{productName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	and product_count=#{productCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	and product_amt=#{productAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	and product_delivery_amt=#{productDeliveryAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	and product_status=#{productStatus}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelProductCount)" >	and cancel_product_count=#{cancelProductCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
         </where>
	</select>

	<insert id="insertUserCustomerOrderList" parameterType="UserCustomerOrderList" useGeneratedKeys="true" keyProperty="payProductSeq">
        INSERT INTO user_customer_order_list
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payProductSeq)" >	pay_product_seq=#{payProductSeq},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	reservation_id =#{reservationId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	pay_moid=#{payMoid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	product_serial=#{productSerial},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	product_tour_id=#{productTourId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productName)" >	product_name=#{productName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productPrice)" >	product_price=#{productPrice},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	product_count=#{productCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	product_amt=#{productAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	product_delivery_amt=#{productDeliveryAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	product_status=#{productStatus},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	order_date=#{orderDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(optionPack)" >	option_pack=#{optionPack},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(optionTime)" >	option_time=#{optionTime},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelProductCount)" >	cancel_product_count=#{cancelProductCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			create_date = now()
		</set>
    </insert>

    <update id="updateUserCustomerOrderList" parameterType="UserCustomerOrderList">
        UPDATE user_customer_order_list
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	reservation_id =#{reservationId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	pay_moid=#{payMoid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	product_serial=#{productSerial},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	product_tour_id=#{productTourId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productName)" >	product_name=#{productName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productPrice)" >	product_price=#{productPrice},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	product_count=#{productCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	product_amt=#{productAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	product_delivery_amt=#{productDeliveryAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	product_status=#{productStatus},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	order_date=#{orderDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(optionPack)" >	option_pack=#{optionPack},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(optionTime)" >	option_time=#{optionTime},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelProductCount)" >	cancel_product_count=#{cancelProductCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payProductSeq)" >and pay_product_seq=#{payProductSeq}	</if>
		</where>
    </update>

    <update id="restoreUserCustomerOrderList" parameterType="UserCustomerOrderList">
        UPDATE User_customer_order_list
           SET delete_yn='N'
        WHERE id = #{id}
    </update>

    <update id="deleteUserCustomerOrderList" parameterType="UserCustomerOrderList">
        UPDATE User_customer_order_list
           SET delete_yn='Y'
        WHERE id = #{id}
    </update>

<!--################################### UserAttachFile ###################################-->
	<select id="selectCountUserAttachFile" parameterType="HashMap" resultType="Integer">
		SELECT count(file_id)
		  FROM user_attach_file
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType) and
					  @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey) ">
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType=='userEmail'" >and title LIKE CONCAT('%', #{searchKey}, '%')</if>
			</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
		</where>
	</select>

	<select id="selectListUserAttachFile" parameterType="HashMap" resultType="UserAttachFile">
		select *
		  from (
			SELECT @rownum:=@rownum+1 AS rownum,
			       file_id, user_email, service_type, upload_path, upload_filename, file_extension, file_size, file_mimetype, origin_filename, upload_id, upload_date
			  FROM user_attach_file
			  join (SELECT @rownum:= 0) rnum
			<where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType) and
						  @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey) ">
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType=='userEmail'" >and title LIKE CONCAT('%', #{searchKey}, '%')</if>
				</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>

			</where> ) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneUserAttachFile" parameterType="HashMap" resultType="UserAttachFile">
		SELECT file_id, user_email, service_type, upload_path, upload_filename, file_extension, file_size, file_mimetype, origin_filename, upload_id, upload_date
		  FROM user_attach_file
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>
		</where>
	</select>

    <insert id="insertUserAttachFile" parameterType="UserAttachFile" useGeneratedKeys="true" keyProperty="fileId">
        INSERT INTO user_attach_file SET
			upload_path = #{uploadPath}
			, upload_filename = #{uploadFilename}
			, file_extension = #{fileExtension}
			, file_size = #{fileSize}
			, file_mimetype = #{fileMimetype}
			, origin_filename = #{originFilename}
        	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >,user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >,upload_id = #{uploadId}	</if>
			, upload_date = now()
    </insert>

    <update id="deleteUserAttachFile" parameterType="UserAttachFile">
		update user_attach_file set
			delete_yn = 'Y'
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >,delete_id=#{deleteId}	</if>
			,delete_date=now()
		 WHERE file_id = #{fileId}
	</update>

<!--################################### userGroup ###################################-->
	<select id="selectCountUserGroup" parameterType="HashMap" resultType="Integer">
		SELECT count(id)
		  FROM user_group a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >
		 inner join user_group_connect d on a.id = d.group_id and d.user_email = #{userEmail}
		</if>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and a.id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupName)" >	and a.group_name=#{groupName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupPurpose)" >	and a.group_purpose=#{groupPurpose}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and a.create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and a.create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and a.last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and a.last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and a.delete_date=#{deleteDate}	</if>
		</where>
	</select>

	<select id="selectListUserGroup" parameterType="HashMap" resultType="UserGroup">
		select *
		  from (
			SELECT @rownum:=@rownum+1 AS rownum,
			       a.id, a.group_name, ifnull(a.group_purpose, '') group_purpose, ifnull(a.group_text, '') group_text,
			       ifnull(b.user_count,0) user_count, ifnull(c.reservation_count,0) reservation_count,
			       a.create_id, a.create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.delete_id, a.delete_date
			  FROM user_group a
			  left join (select group_id, count(user_email) user_count  from user_group_connect group by group_id) b on a.id = b.group_id
			  left join (select group_id, count(group_id) reservation_count from (select json_value(group_json,'$.data.groupId') group_id from reservation) a group by group_id) c on a.id = c.group_id
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >
			 inner join user_group_connect d on a.id = d.group_id and d.user_email = #{userEmail}
			</if>
			  join (SELECT @rownum:= 0) rnum
			<where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and a.id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupName)" >	and a.group_name=#{groupName}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupPurpose)" >	and a.group_purpose=#{groupPurpose}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and a.create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and a.create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and a.last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and a.last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and a.delete_date=#{deleteDate}	</if>
			</where> ) a
		ORDER BY rownum	DESC
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneUserGroup" parameterType="HashMap" resultType="UserGroup">
		SELECT id, group_name, group_purpose, group_text, create_id, create_date, last_update_id, last_update_date, delete_yn, delete_id, delete_date
		  FROM user_group
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupName)" >	and group_name=#{groupName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupPurpose)" >	and group_purpose=#{groupPurpose}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>
	</select>

    <insert id="insertUserGroup" parameterType="UserGroup" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_group SET
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	id=#{id},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupName)" >	group_name=#{groupName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupPurpose)" >	group_purpose=#{groupPurpose},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupText)" >	group_text=#{groupText},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			create_date=now()
    </insert>

    <update id="updateUserGroup" parameterType="UserGroup">
		update user_group set
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	id=#{id},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupName)" >	group_name=#{groupName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupPurpose)" >	group_purpose=#{groupPurpose},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupText)" >	group_text=#{groupText},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date=now()
		 WHERE id=#{id}
	</update>

    <update id="deleteUserGroup" parameterType="UserGroup">
		update user_group set
			delete_yn = 'Y'
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >,delete_id=#{deleteId}	</if>
			,delete_date=now()
		 WHERE id=#{id}
	</update>

<!--################################### UserGroupConnect ###################################-->
	<select id="selectCountUserGroupCustomer" parameterType="HashMap" resultType="Integer">
        SELECT count(a.user_email)
          FROM user a
          left join user_group_connect b on a.user_email = b.user_email
         WHERE user_role = 'USER'
			  <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupId)">AND b.group_id = #{groupId}</if>
			  <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupIdNot)">AND(b.group_id != #{groupIdNot} or b.group_id is null)</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(type)">
                   <if test="type != 'ALL'">AND account_status = #{type}</if>
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
                   AND (
                           user_email LIKE CONCAT('%', #{searchKey}, '%')
                           OR user_name LIKE CONCAT('%', #{searchKey}, '%')
                           OR REPLACE(a.user_mobile, '-', '') LIKE CONCAT('%', REPLACE(#{searchKey}, '-', ''), '%')
                       )
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchText)">
                   AND (  CONCAT(ifnull(a.user_name,''), ifnull(REPLACE(a.user_mobile, '-', ''),''), ifnull(a.user_email,'')) LIKE CONCAT('%', #{searchText}, '%') )
               </if>
    </select>

	<select id="selectListUserGroupCustomer" parameterType="HashMap" resultType="LoginUser">
        SELECT @rownum:=@rownum+1 AS rownum,
               a.user_email, user_join_type, user_name,
               user_nationality, ifnull(user_mobile,'') user_mobile, user_tel,
               user_addr_zipcode, user_addr_extra,
               user_addr_jibun, user_addr_road, user_addr_detail,
               user_role, user_class_name, user_class_code,
               user_group_code,
               user_token_id, user_grade_id, user_grade,
               user_grade_start, user_grade_end,
               user_membership_id, user_membership_grade,
               user_membership_start, user_membership_end,
               user_verified_email, user_verified_mobile,
               user_ci, user_di, user_di_corp,
               user_join_date, user_modify_date,
               last_login_date, last_password_date,
               last_login_fail_count, account_status,
               naver_token, naver_email, naver_join_date,
               kakao_token, kakao_email, kakao_join_date,
               google_token, google_email, google_join_date,
               facebook_token, facebook_email, facebook_join_date,
               secondary_email, privacy_retention_days, mailing_yn,
               b.group_id, b.create_id, b.create_date
          FROM user a
          left join user_group_connect b on a.user_email = b.user_email
          join (SELECT @rownum:= 0) rnum
         WHERE user_role = 'USER'
			  <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupId)">AND b.group_id = #{groupId}</if>
			  <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupIdNot)">AND(b.group_id != #{groupIdNot} or b.group_id is null)</if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(type)">
                   <if test="type != 'ALL'">AND account_status = #{type}</if>
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
                   AND (
                           user_email LIKE CONCAT('%', #{searchKey}, '%')
                           OR user_name LIKE CONCAT('%', #{searchKey}, '%')
                           OR REPLACE(user_mobile, '-', '') LIKE CONCAT('%', REPLACE(#{searchKey}, '-', ''), '%')
                       )
               </if>
               <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchText)">
                   AND (  CONCAT(ifnull(a.user_name,''), ifnull(REPLACE(a.user_mobile, '-', ''),''), ifnull(a.user_email,'')) LIKE CONCAT('%', #{searchText}, '%') )
               </if>
         ORDER BY user_join_date DESC
         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
    </select>

	<select id="selectCountUserGroupConnect" parameterType="HashMap" resultType="Integer">
		SELECT count(group_id)
		  FROM user_group_connect
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupId)" >	and group_id=#{groupId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
		</where>
	</select>

	<select id="selectListUserGroupConnect" parameterType="HashMap" resultType="UserGroupConnect">
		select *
		  from (
			SELECT @rownum:=@rownum+1 AS rownum,
			       user_email, group_id, create_id, create_date
			  FROM user_group_connect
			  join (SELECT @rownum:= 0) rnum
			<where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupId)" >	and group_id=#{groupId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			</where> ) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneUserGroupConnect" parameterType="HashMap" resultType="UserGroupConnect">
		SELECT user_email, group_id, create_id, create_date
		  FROM user_group_connect
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupId)" >	and group_id=#{groupId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
		</where>
	</select>

    <insert id="insertUserGroupConnect" parameterType="UserGroupConnect">
        INSERT INTO user_group_connect SET
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupId)" >	group_id=#{groupId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	create_date=#{createDate},	</if>
			create_date=now()
    </insert>
	<!--
    <update id="deleteUserGroupConnect" parameterType="UserGroupConnect">
		update user_group_connect set
			delete_yn = 'Y'
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >,delete_id=#{deleteId}	</if>
			,delete_date=now()
		 WHERE group_id=#{groupId} and user_email = #{userEmail}
	</update>
	 -->
    <delete id="deleteUserGroupConnect" parameterType="UserGroupConnect">
		delete from user_group_connect
		 WHERE group_id=#{groupId} and user_email = #{userEmail}
	</delete>

	<update id="restoreUserGroupConnect" parameterType="UserGroupConnect">
		update user_group_connect set
			delete_yn = 'N'
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >,delete_id=null	</if>
			,delete_date=null
		 WHERE group_id=#{groupId} and user_email = #{userEmail}
	</update>
</mapper>