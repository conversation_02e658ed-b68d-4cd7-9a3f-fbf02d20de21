<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.front.ExternalApiMapper">

    <insert id="insertTouristSpot" parameterType="PlaceSpot">
        INSERT INTO tourist_spot
               (
                    areaCode, sigunguCode,
                    contentId, contentTypeId, locationCode,
                    cat1, cat2, cat3,
                    title, zipcode,
                    addr1, addr2, tel, telName,
                    homepage, overview, booktour,
                    firstImage, firstImage2,
                    mapX, mapY, mLevel,
                    createdTime, modifiedTime,
                    create_date, last_update_date
               )
        VALUES (
                   #{areacode}, #{sigungucode},
                   #{contentid}, #{contenttypeid}, #{locationcode},
                   #{cat1}, #{cat2}, #{cat3},
                   #{title}, #{zipcode},
                   #{addr1}, #{addr2}, #{tel}, #{telname},
                   #{homepage}, #{overview}, #{booktour},
                   #{firstimage}, #{firstimage2},
                   #{mapx}, #{mapy}, #{mlevel},
                   #{createdtime}, #{modifiedtime},
                   now(), now()
               )
    </insert>

</mapper>