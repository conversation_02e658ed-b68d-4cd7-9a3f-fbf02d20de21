package kr.co.wayplus.travel.service.front;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.StringWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.json.GsonJsonParser;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

import jakarta.servlet.http.HttpServletRequest;
import kr.co.wayplus.travel.mapper.front.ExternalApiMapper;
import kr.co.wayplus.travel.model.PlaceSpot;

@Service
public class ExternalApiService {

    private final Logger logger = LoggerFactory.getLogger(ExternalApiService.class);

    //계정 wayplus-jeju
    private static final String KAKAO_JAVASCRIPT_KEY = "0fe9c61d04bd3f45255a7e1611dead2a";
    private static final String KAKAO_RESTAPI_KEY = "2d7e4b9d55df3e7e3ab75e37017e070d";
    //계정 darkclouds, ~24-08-18
    private static final String PUBLIC_DATA_PORTAL_KEY = "hwDJWAuKiMGWX6tSKCJSrN0rb7wPkHir6o1Plf2LCqOnsTxrPsAdCAW9V22%2F%2FtpLEU7AvlhwyZICxBD5%2BvjzwQ%3D%3D";
    private final ExternalApiMapper apiMapper;

    @Autowired
    public ExternalApiService(ExternalApiMapper apiMapper) {
        this.apiMapper = apiMapper;
    }

    /**
     * OK 렌터카 검색 페이지 접속하여 세션 쿠키값을 받아온다.
     * @param request
     * @return result, cookies
     */
    public HashMap<String, Object> connectMainPage(HttpServletRequest request) {
        HashMap<String, Object> result = new HashMap<>();
        List<String> cookies;

        try {
            URL searchURL = new URL("https://www.jejuokrent.co.kr/home/<USER>/list.do");
            HttpURLConnection conn = (HttpURLConnection) searchURL.openConnection();
            logger.debug("Sending GET request to URL : https://www.jejuokrent.co.kr/home/<USER>/list.do");
            conn.setConnectTimeout(3000);
            conn.setRequestProperty("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9");
            conn.setRequestProperty("Accept-Encoding", "gzip, deflate, br");
            conn.setRequestProperty("Accept-Language", "ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7");
            conn.setRequestProperty("Connection", "keep-alive");
            conn.setRequestProperty("Host", "www.jejuokrent.co.kr");
            conn.setRequestProperty("Upgrade-Insecure-Requests", "1");
            conn.setRequestProperty("User-Agent", request.getHeader("User-Agent"));
            conn.connect();

            String errorMessage = "";
            if(conn.getResponseCode() == 200){
                cookies = conn.getHeaderFields().get("Set-Cookie");
                logger.debug("Response Set-Cookie : " + cookies);
                result.put("cookies", cookies);
                result.put("result", "success");
                conn.disconnect();
            }else {
                errorMessage = "OK렌터카 API 접속에 실패했습니다.";
            }

            if(!errorMessage.equals("")){
                throw new Exception(errorMessage);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
            result.put("result", "error");
            result.put("message", e.getMessage());
        }
        return result;
    }

    /**
     * OK 렌터카 실시간 예약조회 호출 후 결과를 받아온다.
     * @param paramMap 검색 파라미터
     * @param cookies OK렌터카 세션 쿠키
     * @return result, list
     */
    public HashMap<String, Object> getOkrentacarList(HttpServletRequest request, HashMap<String, String> paramMap, List<String> cookies) {
        HashMap<String, Object> result = new HashMap<>();

        try {
            BufferedReader br;
            InputStreamReader isr;
            OutputStreamWriter osw;
            String line;
            String param = "";
            param += "resveBeginDe="+paramMap.get("resveBeginDe");
            param += "&resveBeginTime="+paramMap.get("resveBeginTime");
            param += "&resveEndDe="+paramMap.get("resveEndDe");
            param += "&resveEndTime="+paramMap.get("resveEndTime");
            param += "&searchVhctyType="+paramMap.get("searchVhctyType");
            param += "&insrncApplcCode="+paramMap.get("insrncApplcCode");

            URL searchURL = new URL("https://www.jejuokrent.co.kr/api/search/resvSearch.json");
            HttpURLConnection conn = (HttpURLConnection) searchURL.openConnection();
            logger.debug("Sending POST request to URL : https://www.jejuokrent.co.kr/api/search/resvSearch.json");
            conn.setConnectTimeout(3000);
            conn.setRequestProperty("Accept", "application/json, text/javascript, */*; q=0.01");
            conn.setRequestProperty("Accept-Encoding", "gzip, deflate, br");
            conn.setRequestProperty("Accept-Language", "ko-KR,ko;q=0.9,en-US;q=0.8,en;q=0.7");
            conn.setRequestProperty("Connection", "keep-alive");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            conn.setRequestProperty("Host", "www.jejuokrent.co.kr");
            conn.setRequestProperty("Origin", "https://www.jejuokrent.co.kr");
            conn.setRequestProperty("Referer", "https://www.jejuokrent.co.kr/home/<USER>/list.do");
            for(String cookie : cookies) {
                conn.addRequestProperty("Cookie", cookie);
            }
            conn.setRequestProperty("User-Agent", request.getHeader("User-Agent"));
            conn.setRequestProperty("X-Requested-With", "XMLHttpRequest");
            conn.setDoOutput(true);
            osw = new OutputStreamWriter(conn.getOutputStream());
            osw.write(param);
            osw.flush();
            conn.connect();

            String errorMessage = "";
            if(conn.getResponseCode() == 200){
                isr = new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8);
            }else {
                isr = new InputStreamReader(conn.getErrorStream());
                errorMessage = "OK렌터카 API 호출에 실패했습니다.";
            }
            br = new BufferedReader(isr);
            StringBuilder buffer = new StringBuilder();
            while((line = br.readLine()) != null) {
                logger.debug(line);
                buffer.append(line);
            }

            Map<String, Object> okResultMap = new HashMap<>();
            GsonJsonParser jsonParser = new GsonJsonParser();
            okResultMap = jsonParser.parseMap(buffer.toString());
            result.put("data", okResultMap);
            result.put("result", "success");

            if(!errorMessage.equals("")){
                throw new Exception(errorMessage);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
            result.put("result", "error");
            result.put("message", e.getMessage());
        }

        return result;
    }

    public void updateVisitKoreaTourSpotData() throws Exception {
        String tourSpotApiUrl = "http://apis.data.go.kr/B551011/KorService/areaBasedList";
        String commonParameter = "MobileOS=ETC&MobileApp=AppTest&_type=json";
        String tourSpotParameter = "&numOfRows=30&listYN=Y&arrange=C&areaCode=39&pageNo=";
        String tourInfoApiUrl = "http://apis.data.go.kr/B551011/KorService/detailCommon";
        String tourInfoParameter = "&defaultYN=Y&firstImageYN=Y&areacodeYN=Y&catcodeYN=Y&addrinfoYN=Y&mapinfoYN=Y&overviewYN=Y";
        int totalPage = 1;
        int page = 1;
        while (page <= totalPage){
            HashMap<String, String> tourResult = callOpenApi(tourSpotApiUrl, commonParameter+tourSpotParameter+String.valueOf(page), "GET");
            if(tourResult.containsKey("resultData")){
                Gson gson = new Gson();
                JsonObject jsonObject = gson.fromJson(tourResult.get("resultData"), JsonObject.class);
                jsonObject = gson.fromJson(jsonObject.get("response"), JsonObject.class);
                JsonObject headerObject = gson.fromJson(jsonObject.get("header"), JsonObject.class);
                logger.debug(headerObject.toString());

                if(headerObject.get("resultCode").getAsString().equals("0000")){
                    JsonObject bodyObject = gson.fromJson(jsonObject.get("body"), JsonObject.class);
                    totalPage = (int) Math.ceil(bodyObject.get("totalCount").getAsDouble() / 30);
                    logger.debug("Api Result Count : " + bodyObject.get("totalCount") + ", Total Page : " + String.valueOf(totalPage));

                    JsonObject items = gson.fromJson(bodyObject.get("items"), JsonObject.class);
                    JsonArray itemArray = items.get("item").getAsJsonArray();
                    for(int i=0; i<itemArray.size(); i++){
                        logger.debug(itemArray.get(i).toString());
                        PlaceSpot touristSpot = gson.fromJson(itemArray.get(i), PlaceSpot.class);

                        logger.debug("Read Array Item No." + i + ", ContentId: " + touristSpot.getContentId() + ", Title: " + touristSpot.getTsTitle());
                        HashMap<String, String> tourInfoResult = callOpenApi(tourInfoApiUrl, commonParameter+tourInfoParameter+"&contentId="+touristSpot.getContentId(), "GET");
                        if(tourInfoResult.containsKey("resultData")){
                            jsonObject = gson.fromJson(tourInfoResult.get("resultData"), JsonObject.class);
                            jsonObject = gson.fromJson(jsonObject.get("response"), JsonObject.class);
                            headerObject = gson.fromJson(jsonObject.get("header"), JsonObject.class);
                            logger.debug(headerObject.toString());

                            if(headerObject.get("resultCode").getAsString().equals("0000")){
                                bodyObject = gson.fromJson(jsonObject.get("body"), JsonObject.class);
                                items = gson.fromJson(bodyObject.get("items"), JsonObject.class);
                                JsonArray detailArray = items.get("item").getAsJsonArray();
                                PlaceSpot touristSpotDetail = gson.fromJson(detailArray.get(0), PlaceSpot.class);
                                touristSpot.setTsSummary(touristSpotDetail.getTsSummary());
                                touristSpot.setTsHomepage(touristSpotDetail.getTsHomepage());
                            }
                        }

                        apiMapper.insertTouristSpot(touristSpot);
                    }
                }
            }

            page++;
        }






    }


    /**
     *
     * @param url 호출 API
     * @param parameter 호출 시 파라미터
     * @param method HTTP 커넥션 호출 방식
     * @return
     */
    private HashMap<String, String> callOpenApi(String url, String parameter, String method){
        logger.debug("Call Public Open API");
        logger.debug("API URL : " + url);
        logger.debug("parameter : " + parameter);
        logger.debug("SERVICE KEY : " + PUBLIC_DATA_PORTAL_KEY);

        HashMap<String, String> resultMap = new HashMap<>();
        try {
            URL httpUrl = null;
            HttpURLConnection urlConnection = null;
            String urlParameter = "?serviceKey="+PUBLIC_DATA_PORTAL_KEY+"&"+parameter;

            switch (method){
                case "POST":
                    httpUrl = new URL(url);
                    urlConnection = (HttpURLConnection) httpUrl.openConnection();
                    urlConnection.setDoOutput(true);
                    urlConnection.setDoOutput(true);
                    urlConnection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");

                    DataOutputStream dataOutputStream = new DataOutputStream(urlConnection.getOutputStream());
                    dataOutputStream.writeBytes(urlParameter);
                    dataOutputStream.flush();
                    dataOutputStream.close();
                break;
                case "GET":
                default:
                    httpUrl = new URL(url + urlParameter);
                    urlConnection = (HttpURLConnection) httpUrl.openConnection();
                    urlConnection.setConnectTimeout(3000);
                    urlConnection.setReadTimeout(10000);
                    urlConnection.setUseCaches(false);
                break;
            }

            int connectResult = urlConnection.getResponseCode();
            if(connectResult == HttpURLConnection.HTTP_OK){
                logger.debug("Public Open API Response OK");
                InputStream inputStream = urlConnection.getInputStream();
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
                StringWriter stringWriter = new StringWriter();
                char[] buffer = new char[1024];
                int read = 0;
                while ((read = bufferedReader.read(buffer)) != -1){
                    stringWriter.write(buffer, 0, read);
                }
                bufferedReader.close();
                urlConnection.disconnect();

                logger.debug(stringWriter.toString());
                resultMap.put("resultCode", "S");
                resultMap.put("resultMsg", String.valueOf(connectResult));
                resultMap.put("resultData", stringWriter.toString());
            }else{
                logger.debug(urlConnection.getResponseMessage());
                resultMap.put("resultCode", "F");
                resultMap.put("resultMsg", urlConnection.getResponseMessage());
                resultMap.put("resultData", null);
            }

        } catch (IOException e) {
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("resultCode", "E");
            resultMap.put("resultMsg", e.getMessage());
            resultMap.put("resultData", null);
        }

        return resultMap;
    }
}
