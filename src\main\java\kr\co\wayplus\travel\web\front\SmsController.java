package kr.co.wayplus.travel.web.front;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Random;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import kr.co.wayplus.travel.service.common.MessageSenderService;
import kr.co.wayplus.travel.service.front.SmsService;

@RestController
@RequestMapping("/api/sms")
public class SmsController {

    private final SmsService smsService;
    private final MessageSenderService messageSenderService;

    public SmsController(SmsService smsService, MessageSenderService messageSenderService) {
        this.smsService = smsService;
        this.messageSenderService = messageSenderService;
    }
    
    @PostMapping("/send")
    public ResponseEntity<?> sendSms(@RequestParam String phoneNumber, HttpSession session) {
        try {
            String formattedPhoneNumber = phoneNumber.replaceAll("-", "");
            
            if (session.getAttribute("SMS_SUCCESS") != null) {
                session.removeAttribute("SMS_SUCCESS");
            }

            String verificationCode = generateRandomCode();
            
            smsService.sendSms(formattedPhoneNumber, "강화유니버스입니다. 회원가입 인증번호는 [" + verificationCode + "] 입니다.");
            
            // 세션에 인증번호와 만료시간 저장
            session.setAttribute("SMS_CODE:" + formattedPhoneNumber, verificationCode);
            session.setAttribute("SMS_EXPIRE:" + formattedPhoneNumber, 
                System.currentTimeMillis() + (3 * 60 * 1000)); // 3분 후 만료
            
            return ResponseEntity.ok().body(Map.of("success", true));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.ok().body(Map.of("success", false));
        }
    }
    
    @PostMapping("/verify")
    public ResponseEntity<?> verifySms(@RequestParam String phoneNumber, 
                                     @RequestParam String code,
                                     HttpSession session) {
        String formattedPhoneNumber = phoneNumber.replaceAll("-", "");
        String savedCode = (String) session.getAttribute("SMS_CODE:" + formattedPhoneNumber);
        Long expireTime = (Long) session.getAttribute("SMS_EXPIRE:" + formattedPhoneNumber);
        
        boolean isValid = false;
        if (savedCode != null && expireTime != null) {
            isValid = savedCode.equals(code) && System.currentTimeMillis() < expireTime;
        }
        
        if (isValid) {
            // 검증 성공 시 세션에서 관련 데이터 삭제
            session.removeAttribute("SMS_CODE:" + formattedPhoneNumber);
            session.removeAttribute("SMS_EXPIRE:" + formattedPhoneNumber);
            
            session.setAttribute("SMS_SUCCESS", 
                System.currentTimeMillis() + (3 * 60 * 1000));
        }
        
        return ResponseEntity.ok().body(Map.of("success", isValid));
    }

    @GetMapping("/message-schedule-test")
    @ResponseBody
    public ResponseEntity<?> messageScheduleTest() {
        try {
            messageSenderService.productMessageSendCheckService();
            return ResponseEntity.ok().body("suceess");
        } catch (Exception e) {
            return ResponseEntity.ok().body("fail");
        }
    }
    
    private String generateRandomCode() {
        return String.format("%06d", new Random().nextInt(1000000));
    }
} 