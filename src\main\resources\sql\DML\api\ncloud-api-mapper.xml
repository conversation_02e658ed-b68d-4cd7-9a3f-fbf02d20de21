<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.api.NCloudApiMapper">

	<!--################################### logMessageSend ###################################-->
	<select id="selectCountLogMessageSend" parameterType="HashMap" resultType="Integer">
		SELECT count(*)
		  FROM log_message_send a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(from)" >	and from=#{from}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(to)" >	and to=#{to}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(messageType)" >	and message_type=#{messageType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subject)" >	and subject=#{subject}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	and content=#{content}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(files)" >	and files=#{files}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reserveTime)" >	and reserve_time=#{reserveTime}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reserveTimeZone)" >	and reserve_time_zone=#{reserveTimeZone}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestTime)" >	and request_time=#{requestTime}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestUserid)" >	and request_userid=#{requestUserid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(resultRequestId)" >	and result_request_id=#{resultRequestId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(resultStatusCode)" >	and result_status_code=#{resultStatusCode}	</if>
		</where>
	</select>

	<select id="selectListLogMessageSend" parameterType="HashMap" resultType="LogMessageSend">
	SELECT * FROM (
		SELECT @rownum:=@rownum+1 AS rownum,
		       id, `from`, `to`, message_type, subject, content, files, reserve_time, reserve_time_zone, request_time, request_userid, result_request_id, result_status_code
		  FROM log_message_send a
		  join (SELECT @rownum:= 0) rnum
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(from)" >	and from=#{from}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(to)" >	and to=#{to}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(messageType)" >	and message_type=#{messageType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subject)" >	and subject=#{subject}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	and content=#{content}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(files)" >	and files=#{files}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reserveTime)" >	and reserve_time=#{reserveTime}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reserveTimeZone)" >	and reserve_time_zone=#{reserveTimeZone}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestTime)" >	and request_time=#{requestTime}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestUserid)" >	and request_userid=#{requestUserid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(resultRequestId)" >	and result_request_id=#{resultRequestId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(resultStatusCode)" >	and result_status_code=#{resultStatusCode}	</if>
		</where>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
	    	ORDER BY
	        <choose>
		        <when test="sort=='id'" >	id	</when>
				<when test="sort=='from'" >	from	</when>
				<when test="sort=='to'" >	to	</when>
				<when test="sort=='messageType'" >	message_type	</when>
				<when test="sort=='subject'" >	subject	</when>
				<when test="sort=='content'" >	content	</when>
				<when test="sort=='files'" >	files	</when>
				<when test="sort=='reserveTime'" >	reserve_time	</when>
				<when test="sort=='reserveTimeZone'" >	reserve_time_zone	</when>
				<when test="sort=='requestTime'" >	request_time	</when>
				<when test="sort=='requestUserid'" >	request_userid	</when>
				<when test="sort=='resultRequestId'" >	result_request_id	</when>
				<when test="sort=='resultStatusCode'" >	result_status_code	</when>
	            <otherwise>rownum</otherwise>
	        </choose>
	        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
	    	ORDER BY <foreach item="item" index="index" collection="listSort" separator=",">
	    	<choose>
		        <when test="item.sort=='id'" >	id	</when>
				<when test="item.sort=='from'" >	from	</when>
				<when test="item.sort=='to'" >	to	</when>
				<when test="item.sort=='messageType'" >	message_type	</when>
				<when test="item.sort=='subject'" >	subject	</when>
				<when test="item.sort=='content'" >	content	</when>
				<when test="item.sort=='files'" >	files	</when>
				<when test="item.sort=='reserveTime'" >	reserve_time	</when>
				<when test="item.sort=='reserveTimeZone'" >	reserve_time_zone	</when>
				<when test="item.sort=='requestTime'" >	request_time	</when>
				<when test="item.sort=='requestUserid'" >	request_userid	</when>
				<when test="item.sort=='resultRequestId'" >	result_request_id	</when>
				<when test="item.sort=='resultStatusCode'" >	result_status_code	</when>
	        </choose>
	    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
		</if>) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneLogMessageSend" parameterType="HashMap" resultType="LogMessageSend">
		SELECT id, `from`, `to`, message_type, subject, content, files, reserve_time, reserve_time_zone, request_time, request_userid, result_request_id, result_status_code
		  FROM log_message_send
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(from)" >	and from=#{from}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(to)" >	and to=#{to}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(messageType)" >	and message_type=#{messageType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subject)" >	and subject=#{subject}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	and content=#{content}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(files)" >	and files=#{files}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reserveTime)" >	and reserve_time=#{reserveTime}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reserveTimeZone)" >	and reserve_time_zone=#{reserveTimeZone}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestTime)" >	and request_time=#{requestTime}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(requestUserid)" >	and request_userid=#{requestUserid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(resultRequestId)" >	and result_request_id=#{resultRequestId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(resultStatusCode)" >	and result_status_code=#{resultStatusCode}	</if>
		</where>
	</select>

    <insert id="insertSmsSendRequestLog" parameterType="LogMessageSend" useGeneratedKeys="true" keyProperty="id">
        INSERT log_message_send
           SET `from` = #{from},
               `to` = #{to},
               message_type = #{messageType},
               subject = #{subject},
               content = #{content},
               files = #{files},
               reserve_time = #{reserveTime},
               reserve_time_zone = #{reserveTimeZone},
               request_time = now(),
               request_userid = #{requestUserid},
			   send_reservation_id = #{sendReservationId},
			   send_message_type = #{sendMessageType}
    </insert>

    <update id="updateSmsSendRequestLogResult" parameterType="LogMessageSend">
        UPDATE log_message_send
           SET result_request_id = #{resultRequestId},
               result_status_code = #{resultStatusCode}
         WHERE id = #{id}
    </update>
<!--
    <insert id="insertAuthenticationMessage" parameterType="MessageAuthentication" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO message_authentication
           SET service_name = #{serviceName},
               message_type = #{messageType},
               subject = #{subject},
               content = #{content},
               content_type = #{contentType},
               to_name = #{toName},
               to_address = #{toAddress},
               send_request_time = now(),
               send_request_userid = #{sendRequestUserId},
               auth_number = #{authNumber},
               auth_status = #{authStatus},
               auth_valid_time = DATE_ADD(now(), INTERVAL #{authValidTime} SECOND),
               create_id = #{sendRequestUserId},
               create_date = now()
    </insert>

    <update id="updateAuthenticationMessageExpired" parameterType="MessageAuthentication">
        UPDATE message_authentication
           SET auth_status = 'expired', auth_response_time = now(), auth_response_type = 'resend',
               last_update_id = #{sendRequestUserId}, last_update_date = now()
         WHERE to_address = #{toAddress} AND service_name = #{serviceName}
           AND auth_status = 'active' AND send_result_time &gt; DATE_SUB(now(), INTERVAL 30 MINUTE)
    </update>

    <update id="updateAuthenticationMessageSendResult" parameterType="MessageAuthentication">
        UPDATE message_authentication
        SET send_result = #{sendResult},
            send_result_time = #{sendResultTime},
            send_result_message = #{sendResultMessage},
            last_update_id = #{lastUpdateId}, last_update_date = now()
        WHERE id = #{id} AND to_address = #{toAddress}
    </update>

    <select id="selectAuthenticationMessageNumber" parameterType="HashMap" resultType="MessageAuthentication">
        SELECT id, service_name, message_type,
               subject, content, content_type,
               to_name, to_address,
               send_request_time, send_request_userid,
               send_result, send_result_time, send_result_message,
               auth_number, auth_status,
               auth_response_time, auth_response_type, auth_response_id,
               create_id, create_date, last_update_id, last_update_date
        FROM message_authentication
        WHERE to_address = #{toAddress} AND service_name = #{serviceName}
          AND auth_number = #{authNumber} AND auth_valid_time &gt; now()
          AND auth_status = 'active' AND send_result_time &gt; DATE_SUB(now(), INTERVAL 30 MINUTE)
    </select>

    <update id="updateAuthenticationMessageAuthResult" parameterType="HashMap">
        UPDATE message_authentication
           SET auth_status = 'success',
               auth_response_time = now(),
               auth_response_id = #{authResponseId},
               auth_response_type = #{authResponseType},
               last_update_id = #{authResponseId}, last_update_date = now()
         WHERE id = #{id} AND to_address = #{toAddress}
    </update>
 -->
</mapper>
