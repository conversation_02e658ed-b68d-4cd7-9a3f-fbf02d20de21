/*공통*/
.wrap_mg_t {
    margin-top: 140px;
}

.wrap_mg_b {
    margin-bottom: 200px;
}

@media screen and (max-width:600px) {
    .wrap_mg_t {
        margin-top: 80px;
    }

    .wrap_mg_b {
        margin-bottom: 80px;
    }
}

/*서브배너*/
.sub_banner {
    height: 630px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sub_banner_title {
    color: #FFF;
    text-shadow: 2px 2px 0px #000;
    text-align: center;
    font-family: Tenada;
    font-size: 50px;
    font-weight: 800;
    margin-bottom: 50px;
}

.sub_banner_ex_box {
    text-align: center;
    font-size: 18px;
    font-weight: 400;
    line-height: 30px;
}

.sub_banner_ex_mg {
    margin: 15px 0;
}

@media screen and (max-width:600px) {
    .sub_banner {
        height: 500px;
    }

    .sub_banner_title {
        font-size: 36px;
    }

    .sub_banner_ex_box {
        font-size: 16px;
    }
}

/*더보기 버튼*/
.more_btn{
    margin-top:75px;
    display:flex;
    justify-content: center;
}
.more_btn button{
    display:flex;
    align-items: center;
    justify-content: center;
    border:none;
    background:none;
    color:#222;
    font-size:16px;
    cursor: pointer;
}
.more_btn button:hover{
    text-decoration: underline;
}
.more_btn button img{
    margin-right:10px;
}
@media screen and (max-width:600px) {
    .more_btn{
        margin-top:40px;
    }
}





/* 검색페이지 */
.ganghwa_universe_search_banner{
    width:100%;
    height:325px;
    background: url(/images/ganghwa_universe_search_banner.jpg);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    display:flex;
    justify-content: center;
    margin-bottom:130px;
}
.search_banner{
    display:flex;
    flex-direction: column;
    align-items: center;
}
.search_banner h3{
    margin-top:100px;
    margin-bottom:60px;
    font-family: 'tenada';
    text-align: center;
    color:#fff;
    font-size:32px;

}
.search_btn{
    width: 30px;
    height: 30px;
    border: none;
    background:url(/images/icon/search_banner_icon.svg) center center no-repeat;
    background-size:30px 30px;
    cursor: pointer;
}
.search_form {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom:1px solid #fff;
}
.search_banner input{
    width:657px;
    border:none;
    background:none;
    padding:14px 60px 14px 10px;
    box-sizing: border-box;
    font-size:16px;
    color:#fff;
}
.search_banner input::placeholder{
    color:#fff;
    font-size:16px;
    font-weight:300;
}

.search_page_wrap{
    border-top:1px solid #222;
    padding-top:55px;
}

.search_page_wrap .title{
    display:flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom:35px;
}
.search_page_wrap .title div{
    display:flex;
    align-items: center;
}
.search_page_wrap .title h4{
    font-size:24px;
    font-weight:600;
    color:#333;
    margin-right:30px;
}
.search_page_wrap .title p{
    font-size:16px;
    color:#666;
}
.search_page_wrap .title strong{
    font-weight:700;
    color:#009944;
}
.search_page_wrap .title button{
    width:33px;
    height:33px;
    background: url(/images/icon/search_more.svg) center center no-repeat;
    border:none;
    cursor: pointer;
}
.search_circle_tab{
    margin-bottom:25px;
}
.search_project_wrap{
    margin-bottom:55px;
}
.search_review_wrap .title{
    padding-bottom:14px;
    border-bottom:1px solid #aaa;
}



@media screen and (max-width:768px) {
    .ganghwa_universe_search_banner{
        margin-bottom:60px;
        height:250px;
    }
    .search_banner{
        padding:0 20px;
    }
    .search_banner h3{
        font-size:24px;
        word-break: keep-all;
        margin-top:60px;
        margin-bottom:30px;
    }
    .search_btn{
        width:24px;
        height:24px;
        background: url(/images/icon/search_banner_icon.svg) center center no-repeat;
        background-size:24px 24px;
    }
    .search_banner input{
        width:calc(100% - 20px);
        /* background: url(/images/icon/search_banner_icon.svg) center right 10px no-repeat; */
        background-size: 24px 24px;
    }
    .search_page_wrap .title div{
        align-items: flex-start;
        flex-direction: column;
    }
    .search_page_wrap .title div h4{
        margin-bottom:10px;
    }

}


/*강화유니버스_소개+세계관*/
.ganghwa_universe_banner {
    height: 715px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url(/images/ganghwa_universe_banner.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    color: #fff;
    position: relative;
}

.ganghwa_universe_big_title {
    text-align: center;
    font-family: Tenada;
    font-size: 60px;
    font-weight: 800;
}

.ganghwa_universe_line_wh {
    position: absolute;
    bottom: 0; left: 50%;
    margin-left: -0.5px;
    width: 1px;
    height: 258px;
    background: #FFF;
}

.ganghwa_universe_wh {
    position: relative;
    padding: 230px 0;
    color: #222;
    overflow: hidden;
}

.ganghwa_universe_ex_middle {
    text-align: center;
    font-size: 30px;
    font-weight: 600;
    line-height: 46px;
}

.ganghwa_universe_line_bl_top {
    position: absolute;
    top: 0; left: 50%;
    margin-left: -0.5px;
    width: 1px;
    height: 190px;
    background: #222;
}

.ganghwa_universe_line_bl_bottom {
    position: absolute;
    bottom: 0; left: 50%;
    margin-left: -0.5px;
    width: 1px;
    height: 190px;
    background: #222;
}

.ganghwa_universe_bl {
    padding: 150px 0;
    color: #fff;
    background: url(/images/ganghwa_universe_bl_bg.png);
    background-repeat: no-repeat;
    background-size: cover;
}

.ganghwa_universe_ex_middle_mg_t {
    margin-top: 30px;
}

.new_world {
    padding: 180px 0 140px 0;
    display: flex;
    align-items: center;
    background: url(/images/new_world_bg.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    color: #fff;
}

.new_world_contents {
    display: flex;
    justify-content: right;
}

.new_world .ganghwa_universe_big_title {
    margin-bottom: 56px;
}

.ganghwa_universe_ex_small {
    text-align: center;
    font-size: 18px;
    font-weight: 400;
    line-height: 30px;
}

.ganghwa_universe_ex_small_mg_b {
    margin-bottom: 20px;
}

.korea_city {
    padding: 190px 0;
    color: #222;
    overflow: hidden;
}

.ganghwa_universe_ex_semi_middle {
    text-align: center;
    font-size: 40px;
    font-weight: 600;
    line-height: 56px;
}

.korea_city .ganghwa_universe_ex_semi_middle {
    margin-bottom: 36px;
}

.korea_city_img_box {
    display: flex;
    margin-top: 77px;
}

.korea_city_img_01 {
    margin-top: 80px;
}

.korea_city_img_02 {
    margin: 0 23px;
}

.korea_city_img_03 {
    margin-top: 62px;
}

.love_world {
    padding: 190px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url(/images/love_world_bg.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    color: #fff;
}

.love_world .ganghwa_universe_middle_title {
    text-align: center;
    text-shadow: 2px 2px 0px #000;
    font-family: Tenada;
    font-size: 50px;
    font-weight: 800;
    margin-top: 30px;
}

.keyworld {
    padding: 175px 0;
}

.keyworld_list_box {
    margin: 90px 0 40px 0;
}

.keyworld_list {
    display: flex;
    justify-content: center;
}

.keyworld_list li {
    width: 118px;
    height: 48px;
    border-radius: 10px;
    background: #007BD0;
    color: #FFF;
    text-align: center;
    font-size: 20px;
    font-weight: 700;
    line-height: 48px;
    margin-right: 14px;
}

.keyworld_list li:last-child {
    margin-right: 0;
}

.keyworld_dot {
    width: fit-content;
    margin: 0 auto;
    margin-top: 40px;
}

.keyworld_go {
    width: 326px;
    padding: 16px 25px;
    box-sizing: border-box;
    margin: 0 auto;
    border-radius: 50px;
    text-align: center;
    border: 1px solid #444;
    background: #FFF;
    color: #222;
    text-align: center;
    font-weight: 500;
    margin-top: 65px;
}

.colorful_ganghwa_universe {
    padding: 121px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url(/images/colorful_ganghwa_universe_bg.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    color: #fff;
}

.colorful_ganghwa_universe_title {
    text-align: center;
    font-family: Tenada;
    font-size: 36px;
    font-weight: 800;
    line-height: 62px;
    margin-bottom: 35px;
}

.colorful_ganghwa_universe_title_big {
    font-size: 48px;
}

@media screen and (max-width:600px) {
    .ganghwa_universe_banner {
        height: 400px;
    }

    .ganghwa_universe_line_wh {
        height: 150px;
    }

    .ganghwa_universe_big_title {
        font-size: 32px;
    }

    .ganghwa_universe_wh {
        padding: 100px 0;
    }

    .ganghwa_universe_line_bl_top {
        height: 100px;
    }

    .ganghwa_universe_line_bl_bottom {
        height: 100px;
    }

    .ganghwa_universe_ex_middle {
        font-size: 24px;
        line-height: 36px;
    }
    .ganghwa_universe_ex_middle br{
        display:none;
    }
    .ganghwa_universe_bl {
        padding: 100px 0;
    }

    .new_world {
        padding: 100px 0;
    }

    .new_world .ganghwa_universe_big_title {
        margin-bottom: 30px;
    }

    .korea_city {
        padding: 100px 0;
    }

    .korea_city_img_box {
        display: block;
    }

    .korea_city_img img {
        width: 100%; height: auto;
    }

    .korea_city_img_01 {
        margin-top: 0;
    }

    .korea_city_img_02 {
        margin: 20px 0;
    }

    .korea_city_img_03 {
        margin-top: 0;
    }

    .ganghwa_universe_ex_semi_middle {
        font-size: 28px;
        line-height: 1.5;
    }

    .korea_city_img_box {
        margin-top: 30px;
    }

    .love_world {
        padding: 100px 0;
    }

    .love_world .ganghwa_universe_middle_title {
        font-size: 36px;
    }

    .keyworld {
        padding: 100px 0;
    }

    .keyworld_go {
        width: 100%;
    }
    .keyworld_list li{
        margin-left:8px;
    }
    .colorful_ganghwa_universe {
        padding: 100px 0;
    }
    .ganghwa_universe_ex_small br{
        display:none;
    }
    .ganghwa_universe_middle_title br{
        display:none;
    }
    .ganghwa_universe_ex_semi_middle br{
        display:none;
    }
    .colorful_ganghwa_universe_title{

    }
}

/*강화유니버스_뉴-로컬 키워드11 & 약속문*/
.eleven_keyworld {
    margin-bottom: 150px;
}

.common_title {
    color: #222;
    font-family: Tenada;
    font-size: 50px;
    font-weight: 800;
}

.common_txt_ex {
    color: #056FB8;
    font-size: 22px;
    font-weight: 400;
    line-height: 48px;
    margin-top: 35px;
}

.eleven_keyworld_contents {
    margin-top: 70px;
}

.eleven_keyworld_list_box {
    display: grid;
    grid-template-columns: repeat(3, calc(33.3% - 26.6px));
    grid-column-gap: 40px;
    grid-row-gap: 30px;
}

.eleven_keyworld_list {
    height: 185px;
    border-radius: 20px;
    background: #FFF;
    box-shadow: 2px 2px 11.3px 5px rgba(0, 0, 0, 0.09);
    display: flex;
    align-items: center;
    justify-content: center;
}

.eleven_keyworld_list_name {
    color: #222;
    text-align: center;
    font-size: 22px;
    font-weight: 700;
    line-height: 30px;
    margin-top: 10px;
}

.promise_statement {
    padding: 150px 0;
    background: url(/images/promise_statement_bg.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    color: #fff;
    margin-bottom: -1px;
}

.promise_statement_contents {
    display: flex;
    justify-content: space-between;
}

.promise_statement .common_title {
    color: #fff;
}

.promise_statement .common_txt_ex {
    color: #fff;
}

.promise_statement_ing_title {
    width: 272px;
    height: 45px;
    border-radius: 22.5px;
    border: 1px solid #000;
    color: #FFF;
    text-align: center;
    font-family: Pretendard;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 45px;
    margin-bottom: 20px;
}

.tour_ing_box .promise_statement_ing_title {
    background: #007BD0;
}

.promise_statement_ing_list {
    display: flex;
    align-items: center;
    font-size: 18px;
    line-height: 32px;
}

.promise_statement_ing_dot {
    width: 4px; height: 4px;
    border-radius: 50%;
    background-color: #fff;
    margin: 0 10px;
}

.meet_ing_box {
    margin-top: 78px;
}

.meet_ing_box .promise_statement_ing_title {
    background: #094;
}

@media screen and (max-width:1024px) {
    .promise_statement_contents {
        flex-wrap: wrap;
    }

    .promise_statement .common_title_box {
        width: 100%;
        margin-bottom: 50px;
    }

    .promise_statement .common_txt_ex p {
        display: inline;
    }
}

@media screen and (max-width:600px) {
    .eleven_keyworld{
        margin-bottom:80px;
    }
    .eleven_keyworld_list_box {
        display: grid;
        grid-template-columns: repeat(2, calc(50% - 10px));
        grid-column-gap: 20px;
        grid-row-gap: 20px;
    }

    .promise_statement {
        padding: 100px 0;
    }
    .common_txt_ex{
        font-size:16px;
        line-height:1.5;
        margin-top:15px;
    }
    .eleven_keyworld_contents{
        margin-top:50px;
    }
    .promise_statement_ing_list{
        align-items: flex-start;
        margin-bottom:10px;
    }
    .promise_statement_ing_dot{
        margin:0;
        margin-right:6px;
        margin-top:12px;
    }
    .promise_statement_ing_list span{
        width:calc(100% - 10px);
        line-height:1.6;
    }

}
/*강화유니버스_강화쿠키레터*/
.neslatters-wrap{
    margin-top:80px;
}
@media screen and (max-width:600px) {
    .neslatters-wrap{
        margin-top:60px;
    }
}









/*강화유니버스_언론 속 강화유니버스*/
.press_ganghwa_universe .circle_tab_box {
    margin: 76px 0 36px 0;
}

.press_ganghwa_universe_flex_box {
    display: flex;
}

.press_ganghwa_universe_flex_box .review_search_box {
    margin-top: 80px;
}

.all_list_number {
    color: #666;
    margin-bottom: 10px;
}

.number_point {
    color: #094;
    font-weight: 700;
}

.border_list_box {
    border-top: 2px solid #000;
}

.border_list {
    border-bottom: 1px solid #aaa;
}

.press_list_contents .border_list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 33px 0;
}

.border_list_day_title {
    width: calc(100% - 42px - 20px);
}

.newspaper_name {
    color: #007BD0;
    font-size: 16px;
    margin-right: 15px;
}

.newspaper_day {
    color: #888;
}

.newspaper_title {
    color: #333;
    font-size: 18px;
    margin-top: 15px;
}

.list_go {
    cursor: pointer;
    width: 45px; height: 27px;
    background: url(/images/icon/list_go.svg);
}

/*프로젝트*/
.project_contents_list_box {
    display: grid;
    grid-template-columns: repeat(4, calc(25% - 24px));
    grid-column-gap: 32px;
    grid-row-gap: 70px;
}

.project_contents_list {
    cursor: pointer;
}

.project_contents_list:hover .project_img img {
    transform: scale(1.1);
}

.project_img_box {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.project_img {
    width: 100%; height: auto;
    max-height: 326px;
    overflow: hidden;
}

.project_img img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s;
}

.like_heart {
    position: absolute;
    top: 10px; right: 10px;
    width: 38px; height: 38px;
    background: url(/images/icon/like_heart.svg);
    cursor: pointer;
}
.like_heart:hover{
    background: url(/images/icon/like_heart_on.svg) center center no-repeat;
}

.project_contents_list .contents_list_title {
    margin: 15px 0 10px 0;
}

.contents_list_year {
    color: #007BD0;
}

@media screen and (max-width:768px) {
    .project_contents_list_box {
        display: grid;
        grid-template-columns: repeat(2, calc(50% - 10px));
        grid-column-gap: 20px;
        grid-row-gap: 50px;
    }
    .press_ganghwa_universe .circle_tab_box{
        margin-top:60px;
        margin-bottom:30px;
    }
}

@media screen and (max-width:425px) {
    .project_contents_list_box {
        display: block;
    }

    .project_contents_list {
        margin-bottom: 50px;
    }
}

/*프로젝트_뷰페이지*/
.view_title_box {
    margin-bottom: 50px;
}

.view_title {
    color: #222;
    font-size: 30px;
    font-weight: 600;
    margin-bottom: 37px;
}

.view_day {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    color: #666;
    margin-top:20px;
}

.view_day_line {
    width: 1px;
    height: 12px;
    background: #888;
    margin: 0 10px;
}

.view_contents_box {
    border-top: 1px solid #000;
    border-bottom: 1px solid #000;
    margin: 0 0 50px 0;
}

.view_contents {
    padding: 50px 0 118px 0;
}

.view_contents_ex_bold {
    color: #333;
    font-size: 24px;
    font-weight: 600;
    line-height: 30px;
    margin-bottom: 15px;
}

.view_contents_ex {
    color: #333;
    font-size: 18px;
    font-weight: 400;
    line-height: 32px;
}

.view_contents_img {
    width: fit-content;
    max-width: 557px;
    margin: 118px auto 0 auto;
}

.view_contents_img img {
    width: 100%; height: auto;
}

.file_box {
    font-size: 0;
    line-height: 16px;
    padding: 20px 0;
    border-top: 1px solid #D9D9D9;
}

.file_box .file_item {
    display: inline-block;
    font-size: 16px;
    padding: 0 12px 0 0;
    border-right: 1px solid #111;
    color: #111;
    font-weight: 700;
}

.file_box .file_item:last-child {
    border: none;
    padding: 0 0 0 12px;
}

.file_name_box {
    display: flex;
    align-items: center;
}

.file_name {
    color: #666;
    font-weight: 400;
}

.file_icon {
    width: 21px;
    height: 21px;
    background: url(/images/icon/file_icon.svg);
    margin-left: 15px;
    cursor: pointer;
}

.line_btn {
    width: 171px;
    height: 50px;
    padding: 16px 20px;
    box-sizing: border-box;
    border-radius: 50px;
    border: 1px solid #444;
    background: #FFF;
    color: #222;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    margin: 0 auto;
}

.line_btn:hover {
    cursor: pointer;
    background: #EDEDED;
    text-decoration: underline;
}

.up_next_view {
    border-top: 1px solid #aaa;
    border-bottom: 1px solid #aaa;
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.up_icon {
    width: 15px; height: 10px;
    background: url(/images/icon/up_icon.svg);
    margin-right: 10px;
}



.down_prev_view {
    border-bottom: 1px solid #aaa;
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.down_icon {
    width: 15px; height: 10px;
    background: url(/images/icon/down_icon.svg);
    margin-right: 10px;
}

@media screen and (max-width:1024px) {
    .view_title_box {
        margin-bottom: 20px;
    }
    .view_title{
        margin-bottom:20px;
    }
}
@media screen and (max-width:768px) {

}


/*잠시섬_프로그램소개*/
.jamsisum_program_banner {
    padding: 133px 0 125px 0;
    background: url(/images/jamsisum_program_banner.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    color: #fff;
    overflow: hidden;
}

.jamsisum_program_banner_contents {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.jamsisum_program_banner_title_box {
    position: relative;
    width: fit-content;
}

.jamsisum_program_banner_title {
    text-shadow: 2px 2px 0px #000;
    font-family: Tenada;
    font-size: 60px;
    font-weight: 800;
}


.jamsisum_program_banner_title_icon {
    position: absolute;
    top: -20px; right: -200px;
    animation-name: jamsisumprogrambannericon;
    animation-duration: 1s;
    animation-iteration-count: infinite;
}

@keyframes jamsisumprogrambannericon {
    0% {top: -20px;}
    50% {top: -30px;}
    100% {top: -20px;}
}

.jamsisum_program_banner_ex_top {
    font-size: 30px;
    font-weight: 600;
    margin: 33px 0 15px 0;
}

.jamsisum_program_banner_ex_small {
    font-size: 18px;
    font-weight: 400;
    line-height: 30px;
}

.jamsisum_program_banner_ex_small_mg_b {
    margin-bottom: 20px;
}

.jamsisum_program_img img {
    width: 100%; height: auto;
}

.jamsisum_program_intro {
    padding: 145px 0 200px 0;
}

.jamsisum_program_intro_title {
    color: #222;
    font-size: 40px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 100px;
}

.jamsisum_program_intro_list_box {
    display: grid;
    grid-template-columns: repeat(2, calc(50% - 37.5px));
    grid-column-gap: 75px;
    grid-row-gap: 75px;
}

.jamsisum_program_intro_list_img img {
    width: 100%; height: auto;
}

.jamsisum_program_intro_list_title {
    color: #222;
    text-align: center;
    font-size: 24px;
    font-weight: 700;
    line-height: 32px;
    margin: 34px 0 15px 0;
}

.jamsisum_program_intro_list_ex {
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: 400;
    line-height: 32px;
}

.jamsisum_application {
    padding: 138px 0 120px 0;
    background: url(/images/jamsisum_application_bg.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    color: #fff;
    margin-bottom: -1px;
}

.jamsisum_application_title {
    font-size: 36px;
    font-style: normal;
    font-weight: 700;
    text-align: center;
    margin-bottom: 60px;
}

.jamsisum_application_btn {
    width: 290px;
    height: 53px;
    padding: 15px 20px; box-sizing: border-box;
    border: 1px solid #222;
    background: #FFF;
    color: #333;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    margin: 0 auto;
}

.jamsisum_application_btn:hover {
    cursor: pointer;
    text-decoration: underline;
}

@media screen and (max-width:1024px) {
    .jamsisum_program_banner_contents {
        flex-wrap: wrap;
        justify-content: center;
    }

    .jamsisum_program_img {
        margin-top: 30px;
    }
}

@media screen and (max-width:768px) {
    .jamsisum_program_intro {
        padding: 60px 0 60px 0;
    }
    .jamsisum_program_intro_list_box {
        display: grid;
        grid-template-columns: repeat(2, calc(50% - 10px));
        grid-column-gap: 20px;
        grid-row-gap: 50px;
    }
    .jamsisum_program_banner {
        padding: 60px 0 60px 0;
    }
    .jamsisum_program_banner_title_box {
        font-size:40px;
    }
    .jamsisum_program_banner_ex_top{
        font-size:24px;
    }
    .jamsisum_program_intro_title {
        font-size:24px;
        margin-bottom:30px;
    }
    .jamsisum_application_title {
        font-size:30px;
        line-height:1.6;
        margin-bottom:30px;
    }
    .jamsisum_program_banner_title {
        font-size:50px;
    }
    .jamsisum_program_banner_ex_small{
        font-size:16px;
    }
    .jamsisum_program_intro_list_title{
        font-size:20px;
    }
    .jamsisum_program_intro_list_ex{
        font-size:16px;
        line-height:24px;
    }
    .jamsisum_program_intro_list_ex br,
    .jamsisum_program_banner_ex_small br
    {
        display:none;
    }
}

@media screen and (max-width:600px) {


    .jamsisum_application {
        padding: 100px 0;
    }
}

@media screen and (max-width:425px) {
    .jamsisum_program_banner_title_icon {
        position: absolute;
        top: -20px;
        right: -150px;
    }

    .jamsisum_program_intro_list_box {
        display: block;
    }

    .jamsisum_program_intro_list {
        margin-bottom: 50px;
    }

    .jamsisum_program_intro_list:last-child {
        margin-bottom: 0;
    }
}

/*잠시섬_숙소소개_예약*/
.big_calender_wrap.on {
    box-shadow: 2px 2px 12px 0px rgba(0, 0, 0, 0.20);
    margin: 80px 0;
}
.big_calender_wrap{
    box-shadow:unset;
    margin: 80px 0;
}
.big_calender_drop_box {
    padding: 25px 45px;
    display: flex;
    justify-content: space-between;
    background: #F5F5F5;
    color: #222;
}

.big_calender_drop_title {
    font-size: 18px;
}

.big_calender_drop {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.big_calender_drop .big_calender_drop_icon {
    min-width: 15px; height: 10px;
    background: url(/images/icon/big_calender_drop_icon.svg);
    transform: rotate(-180deg);
    margin-left: 8px;
}
.big_calender_drop .noshow{
    display:block;
}
.big_calender_drop .show{
    display:none;
}
.big_calender_drop.on .noshow{
    display:none;
}
.big_calender_drop.on .show{
    display:block;
}
.big_calender_drop.on .big_calender_drop_icon{
    transform: unset;
}

.big_calender.on {
    padding: 62px 50px;
    box-sizing: border-box;
    background: #fff;
    display:block;
}
.big_calender{
    display:none;
}

.big_calender_title_box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 66px;
}

.big_calender_title {
    display: flex;
    align-items: center;
    color: #222;
    font-size: 36px;
    font-weight: 800;
}

.big_calender_arrow_box {
    display: flex;
    margin-left: 36px;
}

.big_calender_prev {
    cursor: pointer;
    width: 34px; height: 33px;
    background: url(/images/icon/big_calender_prev.svg);
}

.big_calender_next {
    cursor: pointer;
    width: 34px; height: 33px;
    background: url(/images/icon/big_calender_prev.svg);
    transform: rotate(180deg);
    margin-left: 10px;
}

.calender_circle_wrap {
    display: flex;
}

.calender_circle_box {
    display: flex;
    align-items: center;
}

.calender_circle_blue_box {
    color: #333;
    margin-right: 14px;
}

.calender_circle_blue {
    min-width: 6px;
    height: 6px;
    background: #056FB8;
    border-radius: 50%;
    margin-right: 5px;
}

.calender_circle_gray_box {
    color: #aaa;
}

.calender_circle_gray {
    min-width: 6px;
    height: 6px;
    background: #888;
    border-radius: 50%;
    margin-right: 5px;
}

.big_calender_week {
    display: grid;
    grid-template-columns: repeat(7, calc(14.2% - 42px));
    grid-column-gap: 49px;
    margin-bottom: 22px;
}

.big_calender_week .sun{
    color: #E74133;
}

.big_calender_day_box {
    display: grid;
    grid-template-columns: repeat(7, calc(14.2% - 42px));
    grid-column-gap: 49px;
    grid-row-gap: 30px;
}

.big_calender_day {
    border-top: 2px solid #222;
    padding-top: 15px;
}

.big_calender_sun {
    border-top: 2px solid #E74133;
}

.big_calender_day_number {
    color: #222;
    font-size: 28px;
    margin-bottom: 15px;
}
.calender_circle_box_contents .calender_circle_box {
    margin-right: 0;
    margin-bottom: 8px;
}

.stay_list_box {
    border-top: 1px solid #000;
}

.stay_list {
    display: flex;
    justify-content: space-between;
    padding: 44px 0;
    border-bottom: 1px solid #aaa;
}

.stay_list_img_box {
    position: relative;
    width: calc(100% - 570px - 100px);
    height: 456px;
    overflow: hidden;
}

.stay_list_img {
    height: 100%;
}

.stay_list_img img {
    width: 100%; height: 100%;
    object-fit: cover;
}

.stay_list_circle {
    position: absolute;
    bottom: 20px; right: 20px;
    display: flex;
}

.stay_list_circle .on {
    background: #fff;
    border: 1px solid #000;
}

.stay_list_circle li {
    width: 9px; height: 9px;
    border-radius: 50%;
    border: 1px solid #fff;
    margin-right: 10px;
}

.stay_list_circle li:last-child {
    margin-right: 0;
}

.stay_list_txt_box {
    width: 570px;
}

.stay_list_txt_box .stay_info_room {
    margin: 30px 0;
}

.stay_list_ex {
    color: #333;
    font-size: 18px;
    line-height: 32px;
}
.stay-loading_box {
    display: flex; justify-content: center; width: 100%; margin-top: 100px;
}
.stay-loading_box img {
    width: 100px; height: 100px;
}


/*  섬살이 유형 */
.my_stay_island_life_box{
    display: grid;
    grid-template-columns: repeat( 3 , calc(33.4% - 20px));
    grid-column-gap: 30px; /* 좌우 간격 */
    grid-row-gap: 80px;  /* 상하 간격 */
}
.stay_island_life_box{
    padding-top:100px;
    display: grid;
    grid-template-columns: repeat( 4 , calc(25% - 22.5px));
    grid-column-gap: 30px; /* 좌우 간격 */
    grid-row-gap: 100px;  /* 상하 간격 */
}
.island_life_box_item{
    cursor: pointer;
}
.island_life_box_item .gray-bg{
    background-color:#eee;
    padding-top:50px;
    margin-bottom:10px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}
.island_life_box_item .text-area{
    margin-bottom:50px;
}
.island_life_box_item .text-area h4{
    font-family: 'tenada';
    font-size:55px;
    font-weight:800;
    text-align: center;
    -webkit-text-stroke: 1px black;
    color:#fff;
    margin-bottom:15px;
}
.island_life_box_item .text-area p{
    text-align: center;
    line-height:1.5;
    font-size:16px;
    color:#333;
    width:80%;
    margin:0 auto;
    word-break: keep-all;
    height:48px;
}
.island_life_box_item .img-area{
    position:relative;
}
.island_life_box_item .img-area .img-box{
    overflow: hidden;
    width:100%;
    aspect-ratio: 1/0.88;
    border-top-left-radius:160px;
}
.island_life_box_item .img-area .island_life_thumbnail_img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition: all 0.3s;
}
.island_life_box_item .img-area .arrow{
    position:absolute;
    top:-25px;
    right:20px;
    width:50px;
    height:50px;
    border-radius: 50%;
    background-color:#222;
    display:flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
}
.island_life_box_item .btn{
    width:100%;
    height:55px;
    border:1px solid #aaa;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    font-family: 'pretendard';
    font-size:16px;
    color:#333;
    cursor: pointer;
}
@media (hover: hover) {
    .island_life_box_item:hover .text-area p{
        color:#fff;
    }
    .island_life_box_item:hover .img-area .island_life_thumbnail_img{
        transform: scale(1.1);
    }
    .island_life_box_item:hover .btn{
        background-color:#333;
        color:#fff;
        border:1px solid #333;
    }
    .island_life_box_item:hover .img-area .arrow{
        opacity: 1;
    }
}
.island_card_noline_box{
    display:flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.island_card_noline_box p{
    font-weight:700;
    color:#333;
    margin-bottom:10px;
}
.island_card_noline_box button{
    color:#056FB8;
    background-color:unset;
    border:unset;
}
.island_card_noline_box button img{
    margin-right:5px;
}

@media screen and (max-width:1280px) {
    .my_stay_island_life_box{
        grid-template-columns: repeat( 3 , calc(33.3% - 20px));
    }
    .stay_island_life_box{
        grid-template-columns: repeat( 3 , calc(33.3% - 20px));
    }

}
@media screen and (max-width:1024px) {
    .my_stay_island_life_box{
        grid-template-columns: repeat( 2 , calc(50% - 15px));
    }
    .stay_island_life_box{
        grid-template-columns: repeat( 2 , calc(50% - 15px));
    }
}
@media screen and (max-width:768px) {
    .my_stay_island_life_box{
        display: grid;
        grid-template-columns: repeat( 1 , calc(100%));
        grid-column-gap: 00px; /* 좌우 간격 */
        grid-row-gap: 40px;  /* 상하 간격 */
    }
    .stay_island_life_box{
        padding-top:60px;
        grid-template-columns: repeat( 1 , calc(100%));
        grid-column-gap: 00px; /* 좌우 간격 */
        grid-row-gap: 40px;  /* 상하 간격 */
    }
    .island_life_box_item .gray-bg{
        padding-top:30px;
    }
    .island_life_box_item .text-area{
        margin-bottom:30px;
    }
    .island_life_box_item .text-area h4{
        font-size:40px;
        margin-bottom:5px;
    }
    .island_life_box_item .img-area .img-box{
        border-top-left-radius: 140px;
    }
    .island_life_box_item:hover .img-area .arrow{
        opacity: 1;
        width:36px;
        height:36px;
        top:-18px;
        right:20px;
    }
    .island_life_box_item .btn{
        height:50px;
        background-color:#333;
        border:1px solid #333;
        color:#fff;
    }
}

/*  섬살이 유형 클릭 시*/
.modal_island_life_select_btn {
    width: 100%;
    height: 55px;
    line-height:55px;
    margin-top: 45px;
    margin-bottom:30px;
    background-color:#FFFFFF;
    cursor: pointer;
    border: 1px solid #AAAAAA;
    color: #333333;
    font-size: 16px;
    font-family: 'Pretendard';

}
.modal_island_life_select_btn:hover {
    background-color: #272727; color: #FFFFFF;
}
.island_info_modal_box {
    padding: 0 !important;
}
.pw_popup.island_info_modal_box .close{
    opacity: 0.9;
    right:0px;
}
.pw_popup_contents.island_info_modal_contents_box {
    background: #fff;
    flex-direction: column;
    align-items: center;
    justify-content: normal;
    height: 700px;
    /* overflow-y: auto; */
    padding: 30px;
    border-radius: 10px;
    box-sizing: border-box;
}
.island_info_modal_inner_top_box {
    display: flex;
    width: 657px;
    height: 270px;
    margin-bottom: 43px;
    justify-content: center;
}
.island_info_modal_thumbnail_img {
    width: 300px;
    height: 270px;
    object-fit: cover;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}
.island_info_modal_sub_title_box {
    width: 350px; height: 100%; border-top-right-radius: 10px; border-bottom-right-radius: 10px;
}
.island_info_modal_text {
    margin-bottom: 30px; text-align: left; padding: 18px 30px 0 30px; font-weight: 600; font-size: 16px; color: #FFFFFF;
}
.island_info_modal_title {
    font-family: Tenada; font-weight: 800; font-size: 55px; color: #FFFFFF; -webkit-text-stroke: 1px black;
}
.island_info_modal_sub_title {
    padding: 0 43px; color: #FFFFFF; line-height: 30px;word-break: keep-all;
}
.island_info_modal_contents {
     word-break: keep-all;
}
.island_info_modal_contents img{
    max-width:100%;
    height:unset;
}

@media screen and (max-width:768px) {
    .pw_popup_contents.island_info_modal_contents_box{
        padding:20px 20px;
    }
    .island_info_modal_inner_top_box {
        width:100%;
        height:unset;
        flex-direction: column;
        border-bottom-left-radius: 0;
        border-top-right-radius: 10px;
        margin-bottom:20px;
    }
    .island_info_modal_thumbnail_img{
        width:100%;
        height:unset;
        aspect-ratio: 1/0.47;
    }
    .island_info_modal_sub_title_box{
        border-top-right-radius: 0;
        border-bottom-left-radius: 10px;
        padding:20px 20px;
        width:100%;
        height:unset;
        box-sizing: border-box;
    }
    .island_info_modal_text{
        margin-bottom:20px;
        margin-top:15px;
        padding:0;
        width:100%;
        text-align: center;
    }
    .island_info_modal_title{
        font-size:40px;
    }
    .island_info_modal_sub_title{
        width:100%;
        padding:0;
        text-align: center;
        font-size:14px;
        line-height:1.5;
        word-break: keep-all;
        box-sizing: border-box;
    }
    .modal_island_life_select_btn{
        height:40px;
        line-height:40px;
        background-color:#333;
        border:1px solid #333;
        color:#fff;
    }
}
@media screen and (max-height:1000px) {
    .pw_popup_contents.island_info_modal_contents_box{
       height: calc(100vh - 300px);
       overflow-y: auto;
    }
}








.price_schedule_price_wrap {
    display: flex;
    align-items: center;
    margin: 52px 0;
    color: #222;

    font-size: 20px;
    font-weight: 500;
}

.price_schedule_price_wrap .price_schedule_price_box {
    margin: 0;
    margin-left: 20px;
}

.arrow_right_now_box {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.arrow_right_now_txt {
    color: #333;
    font-size: 18px;
    font-weight: 600;
    margin-left: 14px;
}

.arrow_right_now_box:hover .arrow_right_now_txt {
    text-decoration: underline;
}

@media screen and (max-width:1280px) {
    .stay_list_img_box {
        width: calc(100% - 400px - 50px);
        height: auto;
        max-height: 456px;
    }

    .stay_list_txt_box {
        width: 400px;
    }
}

@media screen and (max-width:1024px) {
    .big_calender_drop_box {
        padding: 25px 20px;
    }

    .big_calender {
        padding: 20px;
    }

    .big_calender_week {
        display: grid;
        grid-template-columns: repeat(7, calc(14.2% - 8.5px));
        grid-column-gap: 10px;
    }

    .big_calender_day_box {
        display: grid;
        grid-template-columns: repeat(7, calc(14.2% - 8.5px));
        grid-column-gap: 10px;
        grid-row-gap: 20px;
    }

    .stay_list {
        flex-wrap: wrap;
    }

    .stay_list_img_box {
        width: 100%;
        margin-bottom: 20px;
    }

    .stay_list_txt_box {
        width: 100%;
    }
}

@media screen and (max-width:768px) {

    .big_calender_title_box {
        flex-wrap: wrap;
    }
    .big_calender_arrow_box {
        margin-left: 20px;
    }
    .calender_circle_wrap {
        margin-top: 20px;
    }

    .big_calender_title {
        font-size: 24px;
        width:100%;
        justify-content: space-between;
    }
    .big_calender_wrap.on{
        margin-top:40px;
    }
    .big_calender_drop_box{
        padding:30px 20px;
        flex-wrap: wrap;
    }
    .big_calender_drop {
        margin-top:20px;
        border:1px solid #555;
        height:40px;
        display:flex;
        align-items: center;
        justify-content: center;
        width: 100%;
    }
    .big_calender.on{
        padding: 30px 20px;
    }
    .big_calender_day_box{
        display:flex;
        width:100%;
        overflow-x: scroll;
    }
    .big_calender_day_box .big_calender_day {
        margin-right:20px;
    }
    .calender_circle_box_contents{
        float: unset;
    }
    .big_calender_week{
        display:none;
    }
    .big_calender_day_number{
        font-size:18px;
    }
    .big_calender_day_box .big_calender_day .calender_circle_box_contents span{
        white-space :nowrap;
        font-size:14px;
    }
    .calender_day_box{
        display:none;
    }
    .calender_circle_gray{
        min-width: unset;
        width:4px;
        height:4px;
    }
    .calender_circle_box_contents .calender_circle_box{
        justify-content: unset;
    }
    .stay_info_room_txt{
        font-size:20px;
    }
}



/*잠시섬_숙소소개_예약상세*/
.deleted_product_text {
    text-align: center;
    font-size: 30px;
    color: red;
    margin-bottom: 30px;
}
.reserve_view_top{
    display:flex;
    margin-bottom:80px;
}
.reserve_view_top .text_area{
    margin-left:85px;
    width:42%;
}
.reserve_view_top .text_area .cat01{
    font-size:18px;
    color:#E83929;
    margin-bottom:30px;
    display:block;
}
.reserve_view_top .text_area h4{
    font-size:30px;
    font-weight:700;
    color:#222;
    line-height:40px;
    margin-bottom:45px;
}
.reserve_view_top .text_area p{
    font-size:18px;
    color:#333;
    line-height:32px;
    margin-bottom:35px;
}
.reserve_view_top .text_area .price_area{
    display:flex;
    align-items: center;
    margin-bottom:40px;
}
.reserve_view_top .text_area .price_area .b_color{
    color:#222;
    font-size:20px;
}
.reserve_view_top .text_area .price_area .b_color strong{
    font-weight:600;
}
.reserve_view_top .text_area .price_area .r_color{
    color:#E83929;
    margin-left:30px;
    font-weight:600;
    font-size:20px;
}
.reserve_view_top .text_area .price_area .r_color span{
    display: inline-block;
    width:100px;
    height:25px;
    line-height:25px;
    color:#E83929;
    border:1px solid #E83929;
    border-radius: 4px;
    margin-right:10px;
    font-size:14px;
    text-align: center;
}
.reserve_view_top .text_area .box_area{
    border:1px dashed #7d7d7d;
    padding:15px 20px;
    color:#222;
    display:block;
    align-items: center;
    justify-content: center;
    margin-bottom:40px;
}
.reserve_view_top .text_area .box_area strong{
    font-weight:600;
}
.reserve_view_top .text_area .btn_area{
    display:flex;
    align-items: center;
    justify-content: space-between;
    gap:15px;
}
.reserve_view_top .text_area .btn_area button{
    color:#fff;
    font-size:18px;
    line-height:50px;
    background-color:#222;
    font-weight:600;
    border:none;
    width:290px;
    height:50px;
    cursor: pointer;
}
.reserve_view_top .text_area .btn_area button:hover{
    background-color:#fff;
    border:1px solid #222;
    color:#222;
}
.reserve_view_swiper {
    width:715px;
    height:480px;
    position:relative;
    background-color:#eaeaea;
    overflow: hidden;
}
.reserve_view_swiper .swiper-button-next,
.reserve_view_swiper .swiper-button-prev
{
    width:36px;
    height:36px;
    border-radius: 50%;
    overflow: hidden;
}
.reserve_view_swiper .swiper-button-next:after,
.reserve_view_swiper .swiper-button-prev:after
{
    content:'';
    width:100%;
    height:100%;
    border:1px solid #b1b1b1;
    border-radius: 50%;
    overflow: hidden;
}
.reserve_view_swiper .swiper-button-next:after{
    background:url(/images/icon/reserve_view_next.svg) center center no-repeat #e7e7e7;
}
.reserve_view_swiper .swiper-button-prev:after{
    background:url(/images/icon/reserve_view_prev.svg) center center no-repeat #e7e7e7;
}
.reserve_view_swiper .swiper-button-next:hover:after{
    background:url(/images/icon/reserve_view_next_on.svg) center center no-repeat #222;
    border:none;
}
.reserve_view_swiper .swiper-button-prev:hover:after{
    background:url(/images/icon/reserve_view_prev_on.svg) center center no-repeat #222;
    border:none;
}
.reserve_view_swiper img{
    width:100%;
    height:100%;
    object-fit:cover;
}

.reserve_view_wrap .stay_info_txt_list_box{
    padding:30px 35px;
    border:1px solid #aaa;
    margin-bottom:55px;
}
.lodging_info{
    padding:30px 0px;
    border-bottom:1px solid #444;
}
.lodging_info:first-child   {
    border-top:1px solid #444;
}
.lodging_info .title{
    cursor: pointer;
    display:flex;
}
.lodging_info .title h3{
    font-size:22px;
    color:#222;
    font-weight:600;
}
.lodging_info .title img{
    margin-left:10px;
}
.lodging_info .accordion_item{
    padding:25px 25px;
    background-color:#F5F5F5;
    margin-top:20px;
}
.lodging_info .list_wrap {
    margin-bottom:30px;
}
.lodging_info .list_wrap:last-child{
    margin-bottom:0;
}
.lodging_info .list_wrap h5{
    margin-bottom:15px;
    color:#333;
    font-size:20px;
    font-weight:600;
}
.lodging_info .list_wrap div{
    display:flex;
}
.lodging_info .list_wrap div ul{
    width:250px;
    margin-right:30px;
}
.list_style01 li{
    position:relative;
    padding-left:8px;
    color:#333;
    margin-bottom:15px;
}
.list_style01 li:last-child{
    margin-bottom:0;
}
.list_style01 li:before{
    content:'';
    position:absolute;
    top:8px;
    left:0;
    width:3px;
    height:3px;
    border-radius: 50%;
    background-color:#333;
}

.lodging_info .accordion_item{
    display:none;
    transition: all 0.3s;
    overflow: hidden;
}
.lodging_info .accordion_item.active{
    display:block;
}


@media screen and (max-width:1024px) {
    .reserve_view_top {
        flex-direction: column;
    }
    .reserve_view_top .reserve_view_swiper{
        width:100%;
        margin-bottom:40px;
    }
    .reserve_view_top .text_area{
        width:100%;
        margin-left:0px;
    }
    .reserve_view_top .text_area .btn_area.m-fix{
        position:fixed;
        left:0;
        bottom:0;
        width:100%;
        padding:0 20px;
        height:70px;
        border-top:1px solid #ccc;
        background-color:#fff;
        box-sizing: border-box;
        justify-content: center;
        z-index: 9;
    }
    .reserve_view_top .text_area .btn_area.m-fix button{
        width:calc(50% - 10px);
    }
    .lodging{
        padding:30px 15px;
    }
    .reserve_view_top .text_area .price_area {
        align-items: flex-start;
        margin-bottom: 30px;
        flex-direction: column;
    }
    .reserve_view_top .text_area .price_area .b_color{
        margin-bottom:10px;
    }
    .reserve_view_top .text_area .price_area .r_color{
        margin:0;
    }
}


@media screen and (max-width:768px) {

    .reserve_view_top{
        margin-bottom:40px;
    }
    .reserve_view_top .reserve_view_swiper{
        width:100%;
        height:unset;
        aspect-ratio: 1/1;
        margin-bottom:20px;
    }
    .reserve_view_top .text_area .cat01{
        margin-bottom:10px;
    }
    .reserve_view_top .text_area h4{
        font-size:24px;
        margin-bottom:20px;
    }
    .reserve_view_top .text_area .box_area{
        padding:20px 15px;
        margin-bottom:20px;
    }
    .reserve_view_wrap .stay_info_txt_list_box{
        padding:20px 15px;
    }
    .lodging_info .title{
        justify-content: space-between;
    }
    .lodging_info .title h3{
        font-size:18px;
    }
    .reserve_view_top .text_area p{
        line-height:1.5;
    }

}

@media screen and (max-width:500px) {
    .lodging_info .accordion_item{
        padding:20px 15px;
    }
    .lodging_info .list_wrap div{
        display:flex;
    }
    .lodging_info .list_wrap div ul:first-child{
        margin-left:00px;
    }
    .reserve_view_swiper .swiper-button-prev,
    .reserve_view_swiper .swiper-button-next
    {
        display:none;
    }

}



/*잠시섬_숙소소개_일정선택*/
.common_title_small_box {
    display: flex;
    justify-content: space-between;
}

.common_title_small {
    color: #222;
    font-family: Tenada;
    font-size: 45px;
    font-weight: 800;
}

.step_box {
    display: flex;
    height: 54px;
}

.step_by_step {
    position: relative;
    display: flex;
    align-items: center;
}

.step_by_step_circle {
    width: 17px;
    height: 17px;
    border-radius: 50%;
    border: 1px solid #222;
}

.step_by_step_line {
    width: 22px;
    height: 1px;
    background: #222;
}

.step_by_step_icon {
    display: none;
    position: absolute;
    top: -20px;
    left: -21px;
    width: 60px;
    height: 28px;
    border: 1px solid #222;
    box-sizing: border-box;
    text-align: center;
    color: #056FB8;
    font-family: Tenada;
    font-size: 13px;
    font-weight: 800;
    line-height: 28px;
    border-radius: 2px;
}

.step_by_step_last .step_by_step_icon {
    position: absolute;
    top: -20px;
    left: unset;
    right: 0;
}

.step_by_step .on {
    background: #056FB8;
}

.step_by_step .go {
    display: block;
}

.line_top_contents {
    border-top: 1px solid #000;
    margin-top: 60px;
}

.stay_info_box {
    display: flex;
    padding: 40px 0;
    border-bottom: 1px solid #aaa;
}

.stay_info_img  {
    min-width: 281px;
    height: 219px;
    margin-right: 54px;
}

.stay_info_img img {
    width: 100%; height: 100%;
    object-fit: cover;
}

.stay_info_txt_box {
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.stay_info_name {
    color: #E83929;
    font-size: 18px;
}

.stay_info_name_green{
    color: #05A54B;
}

.stay_info_name_blue{
    color: #056FB8;
}

.stay_info_room {
    display: flex;
    margin: 0px 20px 44px 0;
}

.stay_info_room_txt {
    color: #222;
    font-size: 26px;
    font-weight: 700;
    margin-right: 22px;
}

.stay_info_room_people {
    padding:5px 12px;
    border-radius: 4px;
    background: #056FB8;
    color: #FFF;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
}

.stay_info_txt_list {
    display: flex;
    color: #333;
    margin-bottom: 15px;
}
.stay_info_txt_list div{
    display: flex;
    align-items: center;
}
.stay_info_txt_list:last-child {
    margin-bottom: 0;
}

.stay_info_txt_list_bold {
    min-width: 58px;
    color: #222;
    font-weight: 700;
    margin: 0 25px 0 7px;
}

.stay_info_btn {
    padding:8px 20px;
    border-radius: 4px;
    border: 1px solid #222;
    background: #FFF;
    color: #222;
    text-align: center;
    height:fit-content;
}

.stay_info_btn:hover {
    cursor: pointer;
    background: #222;
    color: #fff;
}

.select_schedule {
    margin-top: 30px;
    border-bottom: 1px solid #AAAAAA;
    padding-bottom: 47px;
}

.select_schedule_title {
    color: #222;
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 42px;
}

.calender_schedule {
    display: flex;
}
@media screen and (max-width:768px) {
    .common_title_small_box{
        flex-direction: column;
    }
    .common_title_small{
        font-size:36px;
        margin-bottom:30px;
    }
    .line_top_contents{
        margin-top:40px;
    }
    .step_by_step_icon{
        left:0;
    }
}


/*캘린더*/
.calender {
    width: 436px;
    padding: 22px 44px;
    box-sizing: border-box;
    border: 1px solid #D9D9D9;
    color: #111;
}

.calender_top {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
}

.calender_arrow {
    width: 10px;
    height: 16px;
}

.calender_arrow_prev {
    cursor: pointer;
    background: url(/images/icon/calender_arrow.svg);
}

.calender_arrow_next {
    cursor: pointer;
    background: url(/images/icon/calender_arrow.svg);
    transform: rotate(180deg);
}

.calender_month {
    color: #333;
    font-size: 20px;
    font-weight: 600;
    margin: 0 12px;
}


.calender_day_box {
    display: grid;
    grid-template-columns: repeat(7, 14.2%);
    grid-column-gap: 0px;
    margin-bottom: 12px;
    font-size: 15px;
    text-align: center;
}

.calender_date_box {
    display: grid;
    grid-template-columns: repeat(7, 14.2%);
    grid-column-gap: 0px;
    text-align: center;
}

/* .calender_date_box div {
    padding: 5px 10px;
    margin: 5px;
    border-radius: 5px;
} */

.calender_date_box .day_div {
    cursor: pointer;
    /* opacity: 1; */
}

.lastday {
    color: #D9D9D9;
}

.saturday {
    color: #335EE0;
}

.sunday {
    color: #E74133;
}

.calender_date_box .today {
    background: rgba(231, 65, 51, 1);
    color: #fff;
}

.price_schedule {
    width: 100%;
    margin-left: 62px;
}

.price_schedule_notice {
    color: #222;
    font-size: 18px;
    font-weight: 600;
    margin: 10px 0;
}

.price_schedule_day {
    color: #666;
}

.price_schedule_price_box {
    display: flex;
    margin: 35px 0 50px 0;
    color: #E83929;
    font-size: 20px;
    font-weight: 600;
}

.price_schedule_price_point {
    width: 99px;
    height: 25px;
    border: 1px solid #E83929;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    line-height: 25px;
    margin-right: 11px;
}

.price_schedule_gray_box {
    padding: 25px 34px 36px 34px;
    background: #F5F5F5;
    color: #222;
    line-height: 32px;
}

.price_schedule_gray_big {
    margin-top: 16px;
    font-size: 20px;
}

@media screen and (max-width:1024px) {
    .stay_info_txt_box {
        flex-wrap: wrap;
    }

    .stay_info_btn {
        margin-top: 30px;
    }

    .calender_schedule {
        flex-wrap: wrap;

    }

    .price_schedule {
        margin-left: 0;
        margin-top: 30px;
    }
}

@media screen and (max-width:768px) {
    .stay_info_box {
        flex-wrap: wrap;
    }

    .stay_info_img {
        margin: 0 auto;
        margin-bottom: 30px;
    }
    .stay_info_txt_list {
        flex-direction: column;
        margin-bottom:20px;
    }
    .stay_info_txt_list div{
        margin-bottom:8px;
    }
    .stay_info_txt_list > span{
        font-size:15px;
        line-height:1.5;
    }
    .price_schedule_gray_box{
        padding:20px 20px;
    }
}

@media screen and (max-width:440px) {
    .calender {
        width: 100%;
        padding: 20px;
    }

    .calender_date_box div {
        /* margin: 0;
        padding: 5px; */
    }
}

/*잠시섬 숙소 소개_예약 전 숙지*/
.before_reservation_final_payment {
    margin-top: 60px;
    display: flex;
    justify-content: space-between;
}

.before_reservation_checklist {
    width: 100%;
}

.scroll_chk_box {
    margin-top: 30px;
    margin-bottom: 60px;
}

.scroll_box {
    padding: 7px;
    box-sizing: border-box;
    border: 1px solid #AAA;
}

.scroll_contents {
    padding: 23px;
    box-sizing: border-box;
    overflow-y: scroll;
    height: 360px;
    font-size: 18px;
    line-height: 32px;
    color: #333;
}

/* 스크롤바 설정*/
.scroll_contents::-webkit-scrollbar{
    width: 11px;
}

/* 스크롤바 막대 설정*/
.scroll_contents::-webkit-scrollbar-thumb{
    background-color: #CBCBCB;
}

/* 스크롤바 뒷 배경 설정*/
.scroll_contents::-webkit-scrollbar-track{
    background-color: #F1F1F1;
}

.scroll_contents_list {
    display: flex;
    align-items: center;
}

.scroll_contents_dot {
    min-width: 4px; height: 4px;
    border-radius: 50%;
    background: #333;
    margin: 0 10px;
}

.scroll_contents_bold {
    font-size: 20px;
    font-weight: 600;
    margin-top: 20px;
}

.argee_secession {
    display: flex;
    align-items: center;
    margin-top: 17px;
}

#agree_chk_11, #agree_chk_12, #agree_chk_13 {display: none;}

.agree {
    display: flex;
    align-items: center;
}

.agree_chk {
    width: 24px;
    height: 24px;
    border: 1px solid #aaa;
    box-sizing: border-box;
    margin-right: 13px;
    position: relative;
    cursor: pointer;
}

.agree_chk_txt {
    color: #222;
    font-size: 18px;
}

#agree_chk_11:checked + label::after, #agree_chk_12:checked + label::after, #agree_chk_13:checked + label::after {
    content:'✔';
    font-size: 18px;
    width: 24px;
    height: 24px;
    text-align: center;
    position: absolute;
    left: 0;
    top:0;
}

.final_payment_details {
    position: relative;
    top: 0; right: 0;
    margin-left: 60px;
}

.final_payment_details_fix {
    position: sticky;
    top: 60px;
    width: 600px;
    padding: 46px 46px 56px 46px;
    box-sizing: border-box;
    background: #F5F5F5;
}

.final_payment_details_title {
    color: #222;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 46px;
}

.final_payment_details .stay_info_room_txt {
    margin: 10px 0 15px 0;
}

.final_payment_details_img_info {
    display: flex;
}

.final_payment_details_img {
    width: 140px;
    height: 108px;
    margin-right: 35px;
}

.final_payment_details_img img {
    width: 100%; height: 100%;
    object-fit: cover;
}

.schedule_final_payment_price {
    margin-top: 40px;
    padding-top: 32px;
    border-top: 1px solid #aaa;
}

.schedule_final_payment_price_title {
    color: #222;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 15px;
}

.schedule_final_payment_price_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 65px;
    padding: 0 30px;
    box-sizing: border-box;
    background: #FFF;
    color: #222;
    font-size: 18px;
    font-weight: 400;
    margin-bottom: 25px;
}

.schedule_final_payment_price_box .bold {
    font-size: 26px;
    font-weight: 600;
}
.price-area h5{
    font-size:22px;
    color:#222;
    font-weight:600;
}
.price-area em{
    font-size:16px;
    font-weight:400;
    margin-left:5px;
}
.point-area input{
    width:calc(100% - 30px);
    border:none;
    background-color:unset;
}
.total-price span{
    font-size:18px;
    color:#007BD0;
    font-weight:700;
}
.total-price h5{
    font-size:26px;
    color:#222;
    font-weight:600;
}
.total-price em{
    font-size:16px;
    font-weight:400;
    margin-left:5px;
}
.point-info-text{
    padding-bottom:25px;
    border-bottom:1px solid #aaa;
    margin-bottom:25px;
    color:#333;
}
.final_payment_btn {
    height: 53px;
    background: #222;
    color: #FFF;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    line-height: 53px;
    margin-top: 42px;
}

.final_payment_btn:hover {
    cursor: pointer;
    text-decoration: underline;
}

@media screen and (max-width:1024px) {
    .before_reservation_final_payment {
        flex-wrap: wrap;
    }

    .before_reservation_checklist {
        order: 2;
    }

    .final_payment_details {
        width: 100%;
        order: 1;
        margin-left: 0;
        margin-bottom: 50px;
    }

    .final_payment_details_fix {
        width: 100%;
    }
}

@media screen and (max-width:600px) {
    .scroll_contents {
        padding: 10px;
    }

    .final_payment_details_img_info {
        flex-wrap: wrap;
    }

    .final_payment_details_fix {
        padding: 20px;
    }

    .final_payment_details_img {
        margin-bottom: 20px;
    }

    .final_payment_details_txt {
        width: 100%;
    }
    .point-info-text{
        font-size:15px;
    }
}

/*잠시섬_예약완료*/
.common_txt_center {
    text-align: center;
}

.common_txt_center_ex {
    color: #333;
    margin-top: 30px;
}

.reservation_completed {
    width: 744px;
    padding: 40px 20px;
    box-sizing: border-box;
    border: 1px solid #AAA;
    margin: 50px auto;
    text-align: center;
    color: #222;
}

.reservation_completed .stay_info_room {
    justify-content: center;
}

.reservation_completed_list_box {
    display: flex;
    justify-content: center;
}

.reservation_completed_list {
    display: flex;
    align-items: center;
}

.reservation_completed_list_day {
    margin-bottom: 15px;
}

.reservation_completed_list .bold {
    font-size: 26px;
    font-weight: 600;
}

.reservation_completed_sub_title {
    width: 92px;
    margin-right: 55px;
    font-weight: 600;
    text-align: left;
}

.reservation_completed_sub_title_bold {
    font-weight: 700;
}

.mypage_go_btn {
    width: 157px;
    height: 35px;
    border-radius: 4px;
    border: 1px solid #222;
    background: #FFF;
    color: #222;
    text-align: center;
    line-height: 35px;
    margin: 0 auto;
}

.mypage_go_btn:hover {
    cursor: pointer;
    text-decoration: underline;
}

@media screen and (max-width:768px) {
    .reservation_completed {
        width: 100%;
    }
    .stay_info_room{
        flex-direction: column;
        margin-bottom:30px;
    }
    .stay_info_room_txt{
        width:100%;
        margin-right:0;
        margin-bottom:10px;
    }
}

@media screen and (max-width:425px) {
    .reservation_completed_list {
        flex-wrap: wrap;
        justify-content: center;
    }

    .reservation_completed_sub_title {
        width: 100%;
        text-align: center;
        margin-right: 0;
    }

    .reservation_completed .stay_info_room {
        display: block;
    }

    .reservation_completed .stay_info_room_txt {
        margin-right: 0;
    }

    .reservation_completed .stay_info_room_people {
        margin: 0 auto;
        margin-top: 20px;
    }

}

.jamsisum_more_program {
    margin-top: 130px;
    padding: 128px 0;
    background: url(/images/jamsisum_more_program_bg.png);
    background-repeat: no-repeat;
    background-size: cover;
    overflow: hidden;
}

.more_title_box {
    display: flex;
    justify-content: space-between;
    color: #000;
    font-size: 30px;
    font-weight: 700;
    margin-bottom: 50px;
}

.more_title_icon {
    min-width: 33px; height: 33px;
    background: url(/images/icon/more.svg);
    cursor: pointer;
}

.slide_contents_wrap {
    position: relative;
}

.slide_arrow {
    position: absolute;
    top: 50%;
    margin-top: -55px;
    width: 56px;
    height: 55px;
    border-radius: 50%;
    cursor: pointer;
    z-index: 99999;
}

.slide_arrow_prev {
    left: -76px;
    background: url(/images/icon/slide_arrow_prev.svg);
    background-size: cover;
}


.slide_arrow_next {
    right: -76px;
    background: url(/images/icon/slide_arrow_prev.svg);
    rotate: 180deg;
    background-size: cover;
}

.slide_contents {
    width: 1430px !important;
    margin-left: -15px !important;
    display: flex;
    flex-wrap: nowrap;
}

.slide_contents_list {
    width: 33.3%;
    margin: 0 15px;
    cursor: pointer;
}

.slide_contents_list:hover .product_img img {
    transform: scale(1.1);
}

.slide_contents_list .product_img {
    width: 100%; height: auto;
    overflow: hidden;
    border: 1px solid #000;
}

.slide_contents_list_ver {
    /* width: 25%; */
    margin: 0 15px;
    cursor: pointer;
}

.slide_contents_list_ver:hover .product_img img {
    transform: scale(1.1);
}

.slide_contents_mg_b {
    margin-bottom: 130px;
}

@media screen and (max-width:1660px) {
    .slide_arrow_prev {
        left: 20px;
    }

    .slide_arrow_next {
        right: 20px;
    }
}

@media screen and (max-width:600px) {
    .jamsisum_more_program {
        margin-top: 50px;
        padding: 50px 0;
    }

    .slide_arrow {
        display: none;
    }

    .slide_contents {
        margin-left: -10px;
    }

    .slide_contents_list {
        width: 298px;
        margin: 0 10px;
    }


}

/*잠시섬_이용후기*/
.common_title_flex_box {
    display: flex;
    justify-content: space-between;
}

.review_write_btn_box {
    width: 280px;
    height: 66px;
    border-radius: 10px;
    border: 1px solid #000;
    background: #FFF;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000;
    text-align: center;
    font-size: 18px;
    font-weight: 700;
}

.review_write_btn_box:hover {
    cursor: pointer;
    text-decoration: underline;
}

.review_write_btn {
    display: flex;
    align-items: center;
}

.review_write_btn_icon {
    width: 28px;
    height: 28px;
    background: url(/images/icon/review_write_btn_icon.svg);
    margin-left: 18px;
}

.review_search_box {
    min-width: 265px;
    padding: 30px;
    box-sizing: border-box;
    border-radius: 10px;
    border: 1px solid #222;
    margin-right: 115px;
}

.review_search_title {
    color: #222;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 20px;
}

.review_search {
    width: 100%;
    height: 30px;
    border-bottom: 1px solid #aaa;
}

.review_search_txt {
    width: calc(100% - 20px);
    float: left;
    background: none;
    border: none;
    outline: none;
    font-family: Pretendard;
    color: #666;
    font-size: 16px;
}

.review_search_btn {
    float: right;
    width: 16px;
    height: 16px;
    border: none;
    background: url(/images/icon/search.svg);
    background-size: cover;
    cursor: pointer;
}

.review_search_tab_contents {
    display: flex;
}

.review_tab_contents .circle_tab_box {
    margin: 0;
}

.review_view_contents .view_contents {
    padding: 50px 0 60px 0;
}

.review_view_contents .view_contents_box {
    border-bottom: none;
    margin: 0;
}

.review_view_contents .view_contents_img {
    max-width: 656px;
    margin: 60px auto 0 auto;
}

.line_btn_mg_t {
    margin-top: 80px;
}

.review_con_wrap{
    display:flex;
    justify-content: space-between;
    margin-top:60px;
    position: relative;
}
.naver-review {
    width: 100%;
    height: 100%;
    border: none;
}

.review_con_right{
    width:465px;
    height:auto;
    position:relative;
    margin-left:65px;
}
.api_wrap{
    background-color:#eaeaea;
    position: sticky;
    top:30px;
    width:100%;
    height:1000px;
    /* overflow-y: scroll; */
}
.naver_api{
    padding:40px;
    height:2000px;
}

.review_con_left{
    width:870px;
}
.review_list_wrap .title{
    margin-top:70px;
    padding-bottom:15px;
    border-bottom:2px solid #000;
    color:#666;
    font-weight:400;
    font-size:16px;
}
.review_list_wrap .title strong{
    font-weight:700;
    color:#009944;
}
.review_list .review_list_item{
    padding:30px 10px;
    border-bottom:1px solid #aaa;
}
.review_list .review_list_item .img_area{
    display:flex;
    flex-wrap: wrap;
    margin-bottom:10px;
}
.review_list .review_list_item .img_area li{
    width:120px;
    height:120px;
    overflow: hidden;
    margin-right:15px;
    margin-bottom:15px;
    cursor: pointer;
}
.review_list .review_list_item .img_area li img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition: all 0.3s;
}
.review_list .review_list_item .img_area li img:hover{
    transform: scale(1.1);
}
.review_list .review_list_item .text_area h4{
    font-size:22px;
    color:#333;
    font-weight:600;
    line-height:26px;
    margin-bottom:10px;
}
.review_list .review_list_item .text_area p{
    line-height:28px;
    color:#444;
}

@media screen and (max-width: 1400px) {
    .review_con_left{
        width:61%;
    }
    .review_con_right{
        flex:1;
        margin-left:30px;
    }
}


@media screen and (max-width: 1300px) {
    .review_tab_contents .circle_tab {
        overflow-x: scroll;
        /*
        -ms-overflow-style: none;
        scrollbar-width: none;
        */
    }
    /*
    .review_tab_contents .circle_tab::-webkit-scrollbar {
        display: none !important;
    }
        */
}

@media screen and (max-width: 1024px) {
    .review_con_left{
        width:61%;
    }
    .review_con_right{
        width:31%;
    }
}


@media screen and (max-width: 768px) {
    .review_search_box {
        width: 100%;
        padding:20px 15px;
    }
    .common_title_flex_box {
        flex-direction: column;
    }
    .common_title{
        font-size:30px;
        font-weight:600;
    }
    .review_write_btn_box{
        position:fixed;
        bottom:20px;
        box-sizing: border-box;
        width:calc(100% - 40px);
        height:50px;
        z-index: 999;
    }
    .review_con_wrap {
        flex-direction: column;
    }
    .review_con_left{
        width:100%
    }
    .review_list .review_list_item{
        padding:30px 0;
    }
    .review_list .review_list_item .text_area h4{
        font-size:16px;
    }
    .review_list .review_list_item .text_area p{
        font-size:14px;
    }
    .review_con_right {
        width:100%;
        margin:60px 0 0 0;
    }


}

@media screen and (max-width: 600px) {
    .review_list .review_list_item .img_area li{
        width:calc(50% - 5px);
        height:unset;
        aspect-ratio: 1/1;
        margin-right:10px;
        margin-bottom:10px;
    }
    .review_list .review_list_item .img_area li:nth-child(even){
        margin-right:0;
    }

}


/*잠시섬_이용후기_모달*/
.img_modal_wrap{
    position:fixed;
    top:0;
    left:0;
    width:100%;
    height:100%;
    background-color:#111;
    display:none;
    padding-top:140px;
    z-index: 99999999999;
    justify-content: center;
}
.img_modal_wrap.on{
    display:flex;
}
.img_modal{
    position: relative;
}
.img_modal .swiper_wrap .mySwiper2{
    width:100%;
    aspect-ratio: 1/1;
}
.swiper_wrap{
    background-color:#fff;
    width:600px;
    padding:30px 30px;
    border-radius: 10px;
}
.swiper_wrap .swiper-slide{
    position:relative;
    width:100%;
    overflow: hidden;
}
.swiper_wrap .mySwiper2{
    margin-bottom:20px;
}
.swiper_wrap .mySwiper2 .swiper-slide{
    background-color:#fff;
}
.swiper_wrap .mySwiper2 .swiper-slide img{
    position:absolute;
    top:50%;
    left:50%;
    transform: translate(-50%,-50%);
    width:100%;
    height:100%;
    object-fit: cover;
}
.swiper_wrap .mySwiper .swiper-slide {
    width: 25%;
    aspect-ratio: 1/1;
    opacity: 0.4;
    overflow: hidden;
    cursor: pointer;
}
.swiper_wrap .mySwiper .swiper-slide img{
    width:100%;
    height:100%;
    object-fit: cover;
}
.swiper_wrap .mySwiper .swiper-slide-thumb-active {
    opacity: 1;
}
.swiper_wrap .mySwiper  .swiper-wrapper{
    height:unset;
}
.modal_close_btn{
    position:absolute;
    top:-60px;
    right:0;
    border:none;
    background:none;
    width:44px;
    height:44px;
    background:url(/images/icon/review_modal_cloase_btn.svg) center center no-repeat;
    cursor: pointer;
}
.img_modal .swiper-button-prev,
.img_modal .swiper-button-next
{
    width:54px;
    height:54px;
}
.img_modal .swiper-button-prev:after,
.img_modal .swiper-button-next:after{
    content:'';
    width:54px;
    height:54px;
    box-sizing: border-box;
    border:1.5px solid #333;
    border-radius: 50%;
}
.img_modal .swiper-button-prev:after{
    background:url(/images/icon/review_modal_prev.svg) center center no-repeat #fff;
    background-size:16px 20px;
}
.img_modal .swiper-button-next:after{
    background:url(/images/icon/review_modal_next.svg) center center no-repeat #fff;
    background-size:16px 20px;
}
.img_modal .swiper-button-prev:hover:after{
    background:url(/images/icon/review_modal_prev_on.svg) center center no-repeat #222;
    background-size:16px 20px;
    border:none;
}
.img_modal .swiper-button-next:hover:after{
    background:url(/images/icon/review_modal_next_on.svg) center center no-repeat #222;
    background-size:16px 20px;
    border:none;
}

@media screen and (max-width: 1400px) {
    .img_modal_wrap {
        padding-top:100px;
    }
    .swiper_wrap{
        width:500px;
    }
}
@media screen and (max-width: 1280px) {
    .img_modal_wrap {
        padding-top:100px;
    }
    .swiper_wrap{
        width:460px;
    }
}

@media screen and (max-width: 600px) {
    .img_modal_wrap.on{
        align-items: center;
        padding:20px 0;
    }
    .img_modal{
        width:calc(100% - 40px);
    }
    .swiper_wrap{
        width:100%;
        padding:20px 20px
    }
    .img_modal .swiper-button-prev,
    .img_modal .swiper-button-next
    {
        display:none;
    }
    .modal_close_btn{
        background-size: 50px 50px;
        right:0px;
        top:-60px;
    }
}

/*잠시섬_이용후기_글쓰기*/
.review_write_contents {
    margin-top: 88px;
}

.review_write_title_input {
    width: 100%;
    height: 45px;
    border: 1px solid #CCC;
    background: #FFF;
    padding: 0 17px;
    box-sizing: border-box;
    color: #888;
    font-family: Pretendard;
    font-size: 16px;
    font-weight: 400;
}

.review_write_editor {
    width: 100%; height: 530px;
    border: 1px solid #CCC;
    background: #FFF;
    text-align: center; line-height: 530px;
    color: #F00;
}

/*잠시섬 커뮤니티_가이드라인*/
.community_contents {
    margin-top: 74px;
}

.arrow_side_menu_list_box {
    margin-bottom: 35px;
}

.arrow_side_menu_list_box .on {
    background: #222;
    color: #FFF;
    font-weight: 700;
}

.arrow_side_menu_list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 10px 20px 10px 25px;
    box-sizing: border-box;
    height: 53px;
    margin-bottom: 15px;
    background: #fff;
    color: #444;
    font-size: 22px;
    cursor: pointer;
}

.arrow_side_menu_list_icon {
    width: 9px; height: 14px;
    background: url(/images/icon/arrow_side_menu_list_icon.svg);
}

.icon_day_time_box {
    display: flex;
    color: #444;
    margin-bottom: 15px;
}

.time_icon {
    width: 18px; height: 18px;
    background: url(/images/icon/time_icon.svg);
    margin-right: 8px;
}

.bold_line_top_contents {
    border-top: 2px solid #000;
}

.gideline_contents {
    padding: 43px 0 0 10px;
    color: #333;
}

.gideline_contents_title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 15px;
}

.gideline_contents_ex {
    line-height: 28px;
}

/*잠시섬 커뮤니티_QNA*/
.qna_list {
    border-bottom: 1px solid #aaa;
}

.qna_list_question {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 17px 10px;
    color: #333;
    font-size: 18px;
    font-weight: 400;
}

.qna_list_question_icon {
    width: 16px; height: 10px;
    background: url(/images/icon/qna_list_question_icon.svg);
}

.qna_list_question .on {
    background: url(/images/icon/qna_list_question_icon_on.svg);
}

.qna_list_answer {
    min-height: 74px;
    padding: 12px 20px;
    background: #F4F4F4;
    border-top: 1px solid #aaa;
    color: #333;
    font-size: 16px;
    font-weight: 400;
}

.inquiry_board {
    display: flex;
    align-items: center;
    border-radius: 80px;
    border: 1px solid #000;
    background: #FFF3DC;
    margin-top: 74px;
    padding: 26px 28px 26px 70px;
    box-sizing: border-box;
}

.inquiry_board_title {
    color: #222;
    font-size: 20px;
    font-weight: 700;
    margin-right: 70px;
}

.inquiry_board_ex_icon {
    width: calc(100% - 237px - 70px);
    display: flex;
    justify-content: space-between;
}

.inquiry_board_ex {
    color: #333;
    font-size: 15px;
    font-weight: 400;
    line-height: 24px;
}

.inquiry_board_icon {
    cursor: pointer;
    min-width: 45px; height: 45px;
    background: url(/images/icon/inquiry_board_icon.svg);
}

@media screen and (max-width: 1200px) {
    .inquiry_board {
        flex-wrap: wrap;
        padding: 20px 20px 20px 40px;
    }

    .inquiry_board_title {
        width: 100%;
        margin-right: 0;
    }

    .inquiry_board_ex_icon {
        width: 100%;
        margin-top: 20px;
    }
}

@media screen and (max-width: 768px) {
    .inquiry_board {
        border-radius: 20px;
        padding: 20px;
        margin-top: 50px;
    }

    .inquiry_board_ex_icon {
        flex-wrap: wrap;
    }

    .inquiry_board_ex {
        width: 100%;
        margin-bottom: 20px;
    }
    .inquiry_board_icon{
        width:100%;
        border-radius: 25px;
        background:url(/images/icon/inquiry_board_icon.svg) center center no-repeat #000;
    }								.arrow_side_menu_list_box{
        display:flex;
        border-top:1px solid #222;
        border-bottom:1px solid #ccc;
        align-items: center;
        height:68px;
    }
    .arrow_side_menu_list {
        word-break: keep-all;
        font-size:16px;
        text-align: center;
        line-height:1.5;
        padding:10px 10px;
        margin-bottom:0;
        height:68px;
        box-sizing: border-box;
        border-right:1px solid #ccc;
        justify-content: center
    }
    .arrow_side_menu_list:last-child{
        border-right:none;
    }
    .arrow_side_menu_list span{

    }
    .arrow_side_menu_list_icon {
        display:none;
    }
    .review_write_title_input{
        margin-bottom:20px;
    }
}

/*잠시섬 커뮤니티_잠시섬소식*/
.community_title_img_list {
    display: flex;
    justify-content: space-between;
    padding: 29px 10px;
    border-bottom: 1px solid #aaa;
}

.community_title_img_post_title_box {
    width: calc(100% - 170px - 30px);
}

.community_title_img_post_title {
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.community_title_img_post_ex {
    color: #444;
    line-height: 28px;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin: 25px 0;
}

.community_title_img_post_img {
    width: 170px; height: 170px;
    overflow: hidden;
}

.community_title_img_post_img img {
    width: 100%; height: 100%;
    object-fit: cover;
}

@media screen and (max-width: 600px) {
    .community_title_img_list {
        flex-wrap: wrap;
    }

    .community_title_img_post_title_box {
        width: 100%;
        order: 2;
    }

    .community_title_img_post_img {
        order: 1;
        margin: 0 auto;
        margin-bottom: 20px;
    }
}

/*잠시섬 커뮤니티_잠시섬소식_뷰페이지*/
.article_view_contents .view_contents {
    padding: 50px 0 80px 0;
}

.article_view_contents .view_contents_box {
    border-bottom: none;
    margin: 0;
}

.article_view_contents .view_contents_img {
    width: 100%;
    max-width: unset;
    margin: 20px 0;
}
.article_view_contents .view_contents_img img{
    max-width:600px;
}
@media screen and (max-width: 768px) {
    .article_view_contents .view_contents_img img{
        max-width:100%;
    }
}
/*잠시섬 커뮤니티_인사*/
.greet_search {
    position: relative;
    width: 100%;
    height: 61px;
    border-radius: 100px;
    background: #F3F3F3;
    margin-bottom: 76px;
    padding: 22px 130px 22px 28px; box-sizing: border-box;
}

.greet_search_txt {
    width: 100%;
    background: none;
    border: none;
    outline: none;
    font-family: Pretendard;
    color: #666;
    font-size: 16px;
}

.greet_search_btn {
    position: absolute;
    top: 0; right: 0;
    width: 123px;
    height: 61px;
    border-radius: 100px;
    border: 1px solid #000;
    background: #FFC960;
    cursor: pointer;
    color: #222;
    font-family: Pretendard;
    font-size: 16px;
    font-weight: 700;
}

.greet_list {
    display: flex;
    padding: 30px 0;
    border-bottom: 1px solid #aaa;
}
@media screen and (max-width: 768px) {
    .greet_search{
        border-radius: 10px;
        padding: 22px 100px 22px 10px;
        margin-bottom:50px;
    }
    .greet_search_btn {
        width: 70px;
        font-size:14px;
        border-radius: 10px;
        top:50%;
        right:10px;
        transform: translateY(-50%);
        height:41px;
    }



}

/*잠시섬 커뮤니티_잠시섬 미션지 인증*/

.mission_wrap{
    margin-top:75px;
}
.mission_top{
    display:flex;
    margin-bottom:50px;
}
.mission_top .mission_top_con{
    width:calc(50% - 15px);
    padding:30px 40px;
    border:1px solid #222;
    border-radius: 10px;
}
.mission_title{
    font-size:20px;
    color:#009944;
    font-weight:700;
    margin-bottom:30px;
}
.mission_top_con01{
    margin-right:30px;
}
.mission_con01_list{
    display:flex;
    flex-direction: column;
}
.mission_con01_list div{
    display:flex;
    align-items: center;
    margin-bottom:15px;
}
.mission_con01_list div:last-child{
    margin-bottom:0;
}
.mission_con01_list div:first-child span{
    background-color:#FFC960;
}
.mission_con01_list div span{
    display:flex;
    align-items: center;
    justify-content: center;
    width:36px;
    height:36px;
    border-radius: 50%;
    font-size:22px;
    font-weight:900;
    color:#222;
    background-color:#FFE7B8;
    border:1px solid #222;
    margin-right:10px;
}
.mission_con01_list div p{
    flex:1;
}
.mission_con02_list{
    display:flex;
    align-items: center;
    justify-content: space-around;
    padding:0 35px;
}
.mission_con02_list .list_item{
    display:flex;
    flex-direction: column;
    align-items: center;
    position:relative;
    padding-top:13px;
}
.mission_con02_list .list_item div{
    width:110px;
    height:110px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom:10px;
}
.mission_con02_list .list_item div img{
    width:100%;
    height:100%;
    object-fit: cover;
}
.mission_con02_list .list_item:first-child span{
    background-color:#FFC960;
}
.mission_con02_list .list_item span{
    position:absolute;
    top:0;
    left:0;
    width:36px;
    height:36px;
    display:flex;
    align-items: center;
    justify-content: center;
    color:#222;
    font-size:22px;
    font-weight:900;
    border: 1px solid #222;
    background-color:#FFE7B8;
    border-radius: 50%;
}
.mission_con02_list .list_item p{
    font-size:18px;
    color:#222;
}
.totle_price{
    font-size:16px;
    font-weight:400;
    color:#666;
    padding-bottom:10px;
    border-bottom:2px solid #222;
    margin-bottom:40px;
}
.totle_price strong{
    font-weight:700;
    color:#009944;
}
.mission_list{
    display: grid;
    grid-template-columns: repeat(3, calc(33.333% - 36.666px));
    grid-column-gap: 55px;
    grid-row-gap: 40px;
}
.mission_list_item .img_area{
    width:100%;
    aspect-ratio: 1/1;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom:15px;
}
.mission_list_item .img_area img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition: all 0.3s;
}
.mission_list_item:hover .img_area img{
    transform: scale(1.1);
}
.mission_list .text_area .flag{
    display:inline-block;
    padding:5px 15px;
    border:1px solid #222;
    border-radius: 50px;
    color:#222;
    font-size:15px;
    font-weight:600;
    margin-bottom:15px;
}
.mission_list .text_area h4{
    font-size:18px;
    color:#444;
    line-height:30px;
    font-weight:400;
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 최대 줄 수를 2줄로 설정 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom:15px;
}
.mission_list_item:hover .text_area h4{
    font-weight:500;
}


@media screen and (max-width: 1024px) {
    .mission_top {
        flex-direction: column;
    }
    .mission_top .mission_top_con{
        width:100%;
        box-sizing: border-box;
    }
    .mission_top_con01{
        margin-right:0;
        margin-bottom:20px;
    }
    .mission_list {
        grid-template-columns: repeat(2, calc(50% - 20px));
        grid-column-gap: 40px;
        grid-row-gap: 50px;
    }
}


@media screen and (max-width: 768px) {
    .mission_con02_list{
        justify-content: space-between;
        padding:0;
    }

}

@media screen and (max-width: 500px) {
    .mission_wrap{
        margin-top:50px;
    }
    .mission_top .mission_top_con{
        padding:20px 15px;
    }
    .mission_con02_list {
        flex-direction: column;
    }
    .mission_con02_list .list_item{
        margin-bottom:20px;
    }
    .mission_list {
        grid-template-columns: repeat(1, 100%);
        grid-column-gap: 40px;
        grid-row-gap: 50px;
    }
}

/*잠시섬 커뮤니티_잠시섬 미션지 인증_글쓰기*/
.mission_write_wrap{
    margin-top:75px;
}
.mission_write.write_wrap.mt-70{
    margin-top:70px;
}
.write_wrap .write01 {
    display: flex;
    margin-bottom:20px;
}
.write_wrap .write01 .write01_01{
    width:22.5%;
    margin-right:20px;
}
.write_wrap .write01 .write01_02{
    width:76%;
    margin-left:10px;
    font-family: 'pretendard';
    color:#888;
    font-size:16px;
}
.write_wrap .write01 .write01_02 option{
    font-family: 'pretendard';
    color:#888;
    font-size:16px;
}
.write_wrap .write01 .write01_01,
.write_wrap .write01 .write01_02{
    -webkit-appearance:none; /* for chrome */
    -moz-appearance:none; /*for firefox*/
    appearance:none;
    height:45px;
    border:1px solid #bbb;
    background:url(/images/icon/select_drop.svg) center right 15px no-repeat;
    padding-left:20px;
}

.write_wrap .write01_01 select::-ms-expand,
.write_wrap .write02_01 select::-ms-expand
{
    display:none;/*for IE10,11*/
}
.mission_photo_text_wrap {
    min-width: 120px; margin-right: 10px;
}

.mission_photo_wrap {
    position: relative; max-width: 1230px; overflow: hidden;
}
.mission_swiper_container {
    height: 115px;
}

.photo_write{
    padding:15px 20px;
    border:1px solid #bbb;
    margin-bottom:20px;
}
.photo_write p{
    font-size:16px;
    color:#666;
    margin-bottom:10px;
}
.photo_write input{
    display:none;
}
.photo_write label{
    border:1px dashed #bbb;
    background-color:#f5f5f5;
    cursor: pointer;
    display:flex;
    align-items: center;
    justify-content: center;
    width:85px;
    height:85px;
}
.photo_write label span{
    display:flex;
    align-items: center;
    justify-content: center;
    background-color:#d9d9d9;
    width:25px;
    height:25px;
    border-radius: 50%;
}
.photo_write label span img{
    width:10px;
    height:10px;
}
.write_wrap .write03 textarea{
    width:100%;
    height:550px;
    box-sizing: border-box;
    border:1px solid #bbb;
    padding:20px 20px;
}
@media screen and (max-width: 1024px) {
    .write_wrap .write01 .write01_01{
        width:calc(40% - 10px);
        margin-right:20px;
    }
    .write_wrap .write01 .write01_02{
        width:calc(60% - 10px);
    }
}
@media screen and (max-width: 768px) {
    .mission_write_wrap{
        margin-top:50px;
    }
    .write_wrap .write01 {
        flex-direction: column;
    }
    .write_wrap .write01 .write01_01{
        width:100%;
        margin-right:0px;
        margin-bottom:20px;
        padding:0 15px;
    }
    .write_wrap .write01 .write01_02{
        width:100%;
        padding:0 15px;
        margin-left:0;
    }
    .photo_write{
        padding:15px 15px;
        flex-direction: column;
    }
    .mission_photo_text_wrap{
        margin-bottom:15px;
    }
    .write_wrap .write03 textarea{
        padding:15px 15px;
    }
    .mission_write.write_wrap.mt-70{
        margin-top:40px;
    }
    .photo_write p{
        font-size:15px;
    }
    .mission_swiper_container{
        margin-bottom:30px;
    }
    .program_detail_arrow_prev{
        top:140px;
        left:0;
    }
    .program_detail_arrow_next{
        top:140px;
        right:0;
    }

}

/*잠시섬 커뮤니티_아이디어 제안*/
.idea_contents_list_box {
    display: grid;
    grid-template-columns: repeat(3, calc(33.3% - 20px));
    grid-column-gap: 30px;
    grid-row-gap: 30px;
}

.idea_contents_list {
    padding: 30px;
    border-radius: 10px;
    background: #F8F8F8;
}

.idea_contents_list_title {
    color: #333;
    font-size: 18px;
    font-weight: 600;
    line-height: 28px;
}

.idea_contents_list_ex {
    margin: 12px 0 50px 0;
    overflow: hidden;
    color: #444;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    font-family: Pretendard;
    font-size: 16px;
    font-weight: 400;
    line-height: 28px;
}

.id_like_heart_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.id_like_heart_name {
    color: #888;
    margin-bottom: 13px;
}

.like_heart_icon {
    width: 39px; height: 38px;
    background: url(/images/icon/like_heart.svg);
}

.like_heart_icon_on {
    width: 38px; height: 38px;
    background: url(/images/icon/like_heart_on.svg);
}

.idea_title_box {
    height: 45px; margin-bottom: 20px; padding: 0 3px;
    border: 1px solid #CCC;
    background: #FFF;

}

.idea_title {
    width: 99%; height: 42px; padding: 0 5px; border: none;
    box-sizing: border-box;
}

@media screen and (max-width: 1200px) {
    .idea_contents_list_box {
        display: grid;
        grid-template-columns: repeat(2, calc(50% - 15px));
        grid-column-gap: 30px;
        grid-row-gap: 30px;
    }
}

@media screen and (max-width: 1024px) {
    .idea_contents_list_box {
        display: block;
    }

    .idea_contents_list {
        margin-bottom: 30px;
    }
}

@media screen and (max-width: 768px) {
    .idea_contents_list_box {
        display: grid;
        grid-template-columns: repeat(2, calc(50% - 15px));
        grid-column-gap: 30px;
        grid-row-gap: 30px;
    }
}

@media screen and (max-width: 600px) {
    .idea_contents_list_box {
        display: block;
    }

    .idea_contents_list {
        margin-bottom: 30px;
    }
}

/*잠시섬 커뮤니티_강화유니버스 피플*/

.ganghwa_people_wrap{
    margin-top:75px;
}

.people_list{
    display: grid;
    grid-template-columns: repeat(4, calc(25% - 22.5px));
    grid-column-gap: 30px;
    grid-row-gap: 60px;
}

.people_item {
    cursor: pointer;
}

.people_item .img_area{
    width:100%;
    height:auto;
    aspect-ratio: 1/1;
    margin-bottom:15px;
    border-radius: 10px;
    overflow: hidden;
    position:relative;

}
.people_item .img_area:hover img{
    /* transform: scale(1.1); */
}
.people_item .img_area img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition:all 0.3s;
}
.people_item .img_area .like_span{
    position:absolute;
    top:10px;
    right:10px;
    width:36px;
    height:36px;
    border:1px solid #222;
    border-radius: 50%;
    background-color:#fff;
    display:flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background:url(/images/icon/heart_icon.svg) center center no-repeat #fff;
}
.people_item .img_area .like_span_on{
    position:absolute;
    top:10px;
    right:10px;
    width:36px;
    height:36px;
    border:1px solid #222;
    border-radius: 50%;
    background-color:#fff;
    display:flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background:url(/images/icon/heart_icon_on.svg) center center no-repeat #fff;
}
.people_item .img_area .like_span:hover{
    background:url(/images/icon/heart_icon_on.svg) center center no-repeat #fff;
}

.people_item .text_area h4{
    white-space: nowrap;      /* 텍스트를 한 줄로 표시 */
    overflow: hidden;         /* 넘친 텍스트를 숨김 */
    text-overflow: ellipsis;  /* 넘친 부분을 '...'으로 표시 */
    font-size:20px;
    color:#333;
    font-weight:600;
    margin-bottom:15px;
}
.people_item .text_area h5{
    color:#444;
    font-size:16px;
    font-weight:400;
    line-height:25px;
    margin-bottom:15px;
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 최대 줄 수를 2줄로 설정 */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}
.people_item .link_box{
    display:block;
    width:35px;
    height:35px;
    border-radius: 50%;
    background:url(/images/icon/link_icon.svg) center center no-repeat #eee;
}
.my_intro_box .link_box{
    display:block;
    width:35px;
    height:35px;
    border-radius: 50%;
    background:url(/images/icon/link_icon.svg) center center no-repeat #eee;
}
@media screen and (max-width: 1280px) {
    .people_list{
        grid-template-columns: repeat(3, calc(33% - 20px));
        grid-column-gap: 30px;
    }
}

@media screen and (max-width: 768px) {
    .people_list{
        grid-template-columns: repeat(2, calc(50% - 10px));
        grid-column-gap: 20px;
        grid-row-gap: 40px;
    }
    .ganghwa_people_wrap{
        margin-top:50px;
    }
}

@media screen and (max-width: 500px) {
    .people_list{
        grid-template-columns: repeat(1, 100%);
        grid-column-gap: 00px;
        grid-row-gap: 40px;
    }
}

/*강화유니버스 커뮤니티 _ 강화유니버스 피플 상세*/
.wrap_gray_bg{
    background-color:#F0F0F3;
}
.people_view_wrap{
    width:1200px;
    margin:0 auto;
    padding:95px 0 140px 0;
}
.back_btn{
    display:flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom:20px;
}
.back_btn button{
    background:none;
    border:none;
    color:#444;
    font-size:16px;
    cursor: pointer;
    display:flex;
    align-items: center;
}
.back_btn button span{
    display:block;
    margin-right:10px;
    width:31px;
    height:31px;
    border-radius: 50%;
    background:url(/images/icon/goback_icon.svg) center center no-repeat #222;
}
.my_profile_wrap{
    padding:48px;
    background-color:#fff;
    border-radius: 10px;
    display:flex;
    margin-bottom:30px;
}
.my_profile_wrap .img_area{
    width:165px;
    height:165px;
    border-radius: 50%;
    overflow: hidden;
    margin-right:40px;
}
.my_profile_wrap .img_area img{
    width:100%;
    height:100%;
    object-fit: cover;
}
.my_profile_wrap .text_area{
    width:calc(100% - 205px);
    padding-top:15px;
}
.my_profile_wrap .text_area h4{
    font-size:22px;
    font-weight:600;
    color:#222;
    margin-bottom:10px;
}
.my_profile_wrap .text_area span{
    display:block;
    font-size:15px;
    color:#666;
    margin-bottom:15px;
}
.my_profile_wrap .text_area p{
    font-size:15px;
    color:#444;
    line-height:30px;
}

.people_view_wrap .number{
    font-size:40px;
    color:#333;
    font-weight:700;
    margin-top:26px;
}
.people_view_wrap .my_intro_txt{
    height:40px;
    margin-right:15px;
}
.people_view_wrap .my_intro_txt::placeholder{
    color:#333;
    font-size:16px;
}
.people_view_wrap .my_intro_box{
    justify-content: flex-start;
}
.people_view_wrap .my_intro_box span{
    display:block;
    width:35px;
    height:35px;
    border-radius: 50%;
    cursor: pointer;
    background:url(/images/icon/link_icon.svg) center center no-repeat #eee;
}

@media screen and (max-width: 1200px) {
    .people_view_wrap{
        width:100%;
        padding:95px 15px 140px 15px;
        box-sizing: border-box;
    }
}

@media screen and (max-width: 600px) {
    .people_view_wrap {
        padding:60px 0 80px 0;
    }
    .my_profile_wrap{
        padding:20px;
        flex-direction: column;
        align-items: center;

    }
    .my_profile_wrap .img_area{
        margin-right:0;
    }
    .my_profile_wrap .text_area{
        width:100%;
        text-align: center;
    }
    .people_view_wrap .my_intro_box{

    }
    .people_view_wrap .my_intro_txt{
        padding:0 5px;
        width:calc(100% - 50px);
    }


}

/*강화유니버스 커뮤니티 프로그램*/
.ganghwa_universe_community_banner {
    background: url(/images/ganghwa_universe_community_banner.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    color: #fff;
}

.circle_tab_box {
    margin: 100px 0 50px 0;
}

.circle_tab {
    display: flex;
}
.circle_tab li, .circle_tab li label {
    display: flex;
    align-items: center;
    color: #222;
    font-size: 20px;
    font-weight: 600;
    margin-right: 30px;
    text-wrap: nowrap;
}

.circle_tab li:last-child {
    margin-right: 0;
}

.circle_tab .on .circle_tab_dot {
    background: #05A54B;
}

.circle_tab_dot {
    min-width: 16px;
    min-height: 16px;
    border-radius: 50%;
    background: #DCDCDC;
    border: 1px solid #000;
    margin-right: 8px;
}

.line_title_box {
    color: #222;
    font-size: 28px;
    font-weight: 600;
}

.line_title_point {
    width: 60px;
    height: 2px;
    background: #000;
    margin-bottom: 20px;
}

.program_contents_list_wrap {
    margin: 50px 0 100px 0;
}

.program_contents_list {
    cursor: pointer;
}

.program_contents_list_box {
    display: grid;
    grid-template-columns: repeat(4, calc(25% - 21.75px));
    grid-column-gap: 29px;
    grid-row-gap: 50px;
}

.program_contents_list:hover .product_img img {
    transform: scale(1.1);
}

.program_contents_list.show {
    opacity: 1;
}
.program_contents_list.hide {
    opacity: 0;
}

.main_1_slide_contents_box {
    width: 1400px;
    overflow: hidden;
}
.main_1_slide_contents .product_img {
    max-width: 1400px; overflow: hidden;
    width: 100%; height: auto;
    max-height: 447px;
    overflow: hidden;
}

.main_1_slide_contents .product_img img {
    display: block;
    width: 100%; height: 100%;
    object-fit: cover;
    transition: all 0.3s;
}

.main_1_slide_contents_box {
    width: 1400px;
    overflow: hidden;
}
.main_1_slide_contents_list {
    cursor: pointer;
}
.main_1_slide_contents_list:hover .product_img img {
    transform: scale(1.1);
}

.main_community_program_slide_contents_box {
    width: 1400px;
    overflow: hidden;
}
.main_community_program_slide_contents .product_img {
    max-width: 1400px; overflow: hidden;
    width: 100%; height: auto;
    max-height: 332px;
    overflow: hidden;
}
.main_community_program_slide_contents .product_img img {
    display: block;
    width: 100%; height: 100%;
    object-fit: cover;
    transition: all 0.3s;
}
.main_community_program_slide_contents_list_ver {
    cursor: pointer;
}

.main_community_program_slide_contents_list_ver:hover .product_img img {
    transform: scale(1.1);
}


.product_img {
    width: 100%; height: auto;
    max-height: 328px;
    min-height: 323px;
    overflow: hidden;
}

.product_img img {
    display: block;
    width: 100%; height: 100%;
    object-fit: cover;
    transition: all 0.3s;
    aspect-ratio: 1;
}

.category_box {
    display: flex;
    margin: 20px 0 15px 0;
}

.category {
    width: fit-content;
    min-width: 55px;
    padding:0 10px;
    height: 26px;
    border-radius: 50px;
    border: 1px solid #222;
    color: #FFF;
    font-size: 15px;
    font-weight: 600;
    line-height: 26px;
    text-align: center;
    padding:0 0;
}

.category_blak {
    border: 1px solid #222;
    background: #FFF;
    color: #222;
}

.contents_list_title {
    font-size: 20px;
    font-weight: 600;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.like_box {
    display: flex;
    align-items: center;
    color: #888;
}

.like_box_mg_t {
    margin-top: 20px;
}

.like_icon {
    width: 14px;
    height: 13px;
    background: url(/images/icon/like.svg);
    margin-right: 6px;
}
.like_icon_on {
    width: 14px;
    height: 13px;
    background: url(/images/icon/like.svg);
    margin-right: 6px;
}

.bubble_icon {
    width: 14px;
    height: 13px;
    background: url(/images/icon/bubble.svg);
    margin-right: 6px;
}

@media screen and (max-width:1024px) {
    .circle_tab {
        overflow-x: scroll;
    }
    .circle_tab{
        -ms-overflow-style: none;
        scrollbar-width: none;
    }
    .circle_tab::-webkit-scrollbar {
        display: none !important;
    }
}

@media screen and (max-width:768px) {
    .program_contents_list_box {
        display: grid;
        grid-template-columns: repeat(2, calc(50% - 10px));
        grid-column-gap: 20px;
        grid-row-gap: 50px;
    }
    .circle_tab_box{
        margin:60px 0 40px 0;
    }
    .circle_tab li{
        font-size:22px;
    }
    .line_title_box{
        font-size:20px;
    }
}


/*강화유니버스 커뮤니티 프로그램_상세*/
.program_detail_contents {
    position: relative;
    display: flex;
}

.program_detail_info {
    width: calc(100% - 442px - 60px);
    padding: 90px 0 200px 0;
}

.program_detail_img_box {
    position: relative;
    margin-right: 60px;
}

.program_detail_img img {
    display: block;
    width: 100%; height: 100%;
    object-fit: cover;
}

.program_detail_arrow_prev {
    position: absolute;
    top: 50%; left: 20px;
    width: 36px; height: 36px;
    background: url(/images/icon/program_detail_arrow.svg);
    z-index: 1;
}

.program_detail_arrow_next {
    position: absolute;
    top: 50%; right: 20px;
    width: 36px; height: 36px;
    background: url(/images/icon/program_detail_arrow.svg);
    transform: rotate(180deg);
    z-index: 1;
}

.circle_tab_detail_box {
    margin: 80px 0 20px 0;
}

#tab_01_chk, #tab_02_chk {display: none;}

#tab_01_chk:checked ~ .circle_tab_detail_box .tab_01_btn .circle_tab_dot {background: #05A54B;}
#tab_02_chk:checked ~ .circle_tab_detail_box .tab_02_btn .circle_tab_dot {background: #05A54B;}

#tab_01_chk:checked ~ .program_info_contents_box .program_info_sheet {display: block;}
#tab_02_chk:checked ~ .program_info_contents_box .program_note_sheet {display: block;}

.program_info_contents_box {
    border-top: 1px solid #aaa;
}

.program_info_contents {
    margin-right: 60px;
}

.program_info_note_sheet {
    min-height: 1024px;
    background: #F9F9F9;
    padding: 30px 40px;
    margin: 40px 0 60px 0;
}

.program_info_sheet {
    display: none;
}

.program_note_sheet {
    display: none;
}

.program_detail_review {
    border-top: 1px solid #888;
}

.program_detail_review_write {
    margin: 40px 0 55px 0;
    display: flex;
}

.program_detail_review_like {
    cursor: pointer;
    width: 60px; height: 60px;
    background: url(/images/program_detail_review_like.png);
    background-size:60px 60px;
}
.program_detail_review_like_on {
    cursor: pointer;
    width: 60px; height: 60px;
    background: url(/images/program_detail_review_like_on.png);
    background-size:60px 60px;
}
@media(hover : hover){
    .program_detail_review_like:hover{
        width: 60px; height: 60px;
        background: url(/images/program_detail_review_like_on.png);
        background-size:60px 60px;
    }
    .program_detail_review_like_on:hover{
        width: 60px; height: 60px;
        background: url(/images/program_detail_review_like.png);
        background-size:60px 60px;
    }
}
.program_detail_review_input {
    width: calc(100% - 61px - 61px - 14px - 14px);
    padding: 0 30px; box-sizing: border-box;
    margin: 0 14px;
    border-radius: 40px;
    background: #F3F3F3;
    border: none;
    color: #888;
    font-family: Pretendard;
    font-size: 16px;
    font-weight: 400;
}

.program_detail_review_enter {
    cursor: pointer;
    width: 61px; height: 61px;
    background: url(/images/program_detail_review_enter.png);
}

.program_detail_review_list_box {
    margin-bottom: 80px;
}
.program_detail_review_list {
    display: flex;
    margin-bottom: 30px;
}

.program_detail_review_profile {
    min-width: 55px; height: 56px;
    border-radius: 50%;
    margin-right: 30px;
}

.program_detail_review_profile img {
    width: 100%; height: 100%;
    object-fit: cover; border-radius: 50%;
}

.program_detail_review_id_day {
    display: flex;
    justify-content: space-between;
}

.program_detail_review_id {
    color: #333;
    font-weight: 600;
}

.program_detail_review_day {
    color: #888;
}

.program_detail_review_ex {
    margin: 11px 0 15px 0;
    color: #444;
    line-height: 24px;
}

.program_detail_review_like_rewrite {
    display: flex;
    align-items: center;
    color: #888;
    font-size: 15px;
}
.program_detail_review_like_rewrite span{
    /* cursor: pointer; */
}
@media (hover : hover) {
    .program_detail_review_like_rewrite span:hover{
        color:#555;
    }
}
.program_detail_review_like_rewrite span:hover{

}
.program_detail_review_like_rewrite_line {
    width: 1px;
    height: 12px;
    background: #888;
    margin: 0 10px;
}
.review_review{
    display:flex;
    margin-top:15px;
}
.review_review input{
    width: calc(100% - 50px);
    padding: 0 15px;
    box-sizing: border-box;
    margin-right:10px;
    border-radius: 40px;
    background: #F3F3F3;
    border: none;
    color: #888;
    font-family: Pretendard;
    font-size: 16px;
    font-weight: 400;
}
.review_review .program_detail_review_enter{
    cursor: pointer;
    width: 40px;
    height: 40px;
    background: url(/images/program_detail_review_enter.png) center center no-repeat;
    background-size:40px 40px;
}

@media screen and (max-width:1024px) {
    .program_detail_review_profile{
        margin-right:10px;
        min-width:30px;
        min-height:30px;
    }
    .program_detail_review_like {
        width: 40px;
        height: 40px;
        background-size:40px 40px;
    }
    @media (hover:hover) {
        .program_detail_review_like:hover{
            width: 40px;
            height: 40px;
            background-size:40px 40px;
        }
    }
    .program_detail_review_input{
        height:40px;
        width:calc(100% - 100px);
        margin:0 10px;
        padding:0 10px;
    }
    .program_detail_review_enter{
        width:40px;
        height:40px;
        background-size:40px 40px;
    }
}
@media screen and (max-width:768px) {
    .program_detail_review_profile{
        min-width:30px;
        min-height:30px;
        width:30px;
        height:30px;
    }
    .program_detail_review_day{
        display:none;
    }
    .program_detail_review_id{
        font-size:14px;
    }
    .program_detail_review_like_rewrite{
        font-size:13px;
    }
}

/*페이지네이션*/
.paging_box {
    margin-top: 60px;
}

.paging {
    width: fit-content;
    margin: 0 auto;
    display: flex;
    align-items: center;
}

.paging_arrow {
    width: 11px;
    height: 19px;
    cursor: pointer;
}

.paging_arrow_prev {
    background: url(/images/icon/paging_arrow.svg) center center no-repeat;
    margin-right: 24px;
}

.paging_arrow_next {
    background: url(/images/icon/paging_arrow.svg) no-repeat;
    transform: rotate(180deg);
    margin-left: 24px;
}

.paging_number {
    display: flex;
    color: #666;
    font-size: 16px;
}

.paging_number li {
    margin-right: 24px;
    cursor: pointer;
}

.paging_number li:last-child {
    margin-right: 0;
}

.paging_number .on {
    color: #222;
    font-weight: 500;
    text-decoration: underline;
}

.various_program {
    margin-top: 75px;
    padding-top: 50px;
    border-top: 1px solid #000;
}

.various_program_title {
    color: #222;
    font-size: 30px;
    font-weight: 700;
}

.various_program_list_arrow {
    display: flex;
    margin-top: 50px;
    position: relative;
}

.view_product_list {
    width: calc(100% - 36px - 18px - 36px - 18px); overflow: hidden; margin: 0 auto;
}

.various_program_arrow_prev {
    position: absolute;
    top: 50%; left: 0;
    margin-top: -18px;
    width: 36px;
    height: 36px;
    background: url(/images/icon/program_detail_arrow.svg);
}

.various_program_arrow_next {
    position: absolute;
    top: 50%; right: 0;
    margin-top: -18px;
    width: 36px;
    height: 36px;
    background: url(/images/icon/program_detail_arrow.svg);
    transform: rotate(180deg);
}

.various_program_list_box {
    width: calc(100% - 36px - 18px - 36px - 18px);
    /* margin: 0 auto; */
    display: flex;
    /* grid-template-columns: repeat(3, calc(33.3% - 14px)); */
    /* grid-column-gap: 21px; */
}

.various_program_list {
    height: auto;
    max-height: 230px;
    border: 1px solid #000;
    box-sizing: border-box;
}

.various_program_list img {
    display: block;
    width: 100%; height: 100%;
    object-fit: cover;
}

.program_detail_fix_box {
    position: relative;
    top: 0; right: 0;
    padding: 0 0 0 60px;
    border-left: 1px solid #aaa;
    background: #fff;
}

.program_detail_fix {
    width: 442px;
    position: sticky;
    top: 0px;
    padding: 90px 0 50px 0;
}

.program_detail_category {
    color: #05A54B;
    font-size: 18px;
}

.program_detail_title {
    color: #222;
    font-size: 30px;
    font-weight: 700;
    margin: 30px 0;
}

.program_detail_ex {
    color: #333;
    line-height: 28px;
}

.program_detail_price {
    color: #222;
    font-size: 18px;
    font-weight: 500;
    margin: 42px 0 26px 0;
}

.program_detail_day_time {
    color: #444;
    line-height: 30px;
}

.program_detail_situation_like {
    display: flex;
    margin: 20px 0 0 0;
}

.program_detail_situation {
    width: 110px;
    height: 27px;
    border-radius: 4px;
    background: #CE3322;
    margin-right: 20px;
    color: #FFF;
    font-size: 14px;
    text-align: center;
    line-height: 27px;
}

.program_detail_reservation_btn {
    height: 53px;
    background: #222;
    color: #FFF;
    font-size: 18px;
    font-weight: 600;
    padding: 16px 0;
    box-sizing: border-box;
    text-align: center;
    margin: 63px 0 28px 0;
}

.program_detail_reservation_btn:hover {
    cursor: pointer;
    text-decoration: underline;
}

.program_detail_caution_txt {
    height: 56px;
    border-radius: 5px;
    border: 1px dashed #7D7D7D;
    color: #222;
    text-align: center;
    font-size: 15px;
    padding: 20px 0;
    box-sizing: border-box;
}

@media screen and (max-width:1024px) {
    .program_detail_info {
        width: calc(100% - 300px - 30px);
        padding: 90px 0 200px 0;
    }

    .program_detail_fix_box {
        padding: 0 0 0 30px;
    }

    .program_detail_fix {
        width: 300px;
    }

    .program_detail_img_box {
        margin-right: 30px;
    }

    .program_info_contents {
        margin-right: 30px;
    }
}

@media screen and (max-width:768px) {
    .program_detail_contents {
        flex-wrap: wrap;
    }

    .program_detail_info {
        width: 100%;
        order: 2;
        padding: 0 0 100px 0;
    }

    .program_detail_fix_box {
        width: 100%;
        order: 1;
        padding: 0;
        border: none;
    }

    .program_detail_fix {
        position: unset;
        width: 100%;
    }

    .program_detail_img_box {
        margin-right: 0px;
    }

    .program_info_contents {
        margin-right: 0px;
    }
    .various_program_title{
        font-size:24px;
        line-height:1.5;
    }
}

/*강화유니버스 커뮤니티 프로그램_예약페이지*/
.ganghwa_universe_community_program_reservation .stay_info_img {
    min-width: 280px;
    height: 280px;
}

.select_schedule_people {
    display: flex;
    padding:50px 0;
}

.calender_gray_outer_box {
    width: calc(100% - 440px);
}

.calender_gray_box {
    /* width: calc(100% - 440px); */
    padding: 30px 40px;
    box-sizing: border-box;
    background: #F5F5F5;
}

.select_schedule_people .calender {
    width: 654px;
    margin: 0 auto;
    border: none;
    padding: 0;
}

.select_schedule_people .calender_date_box div {
    /* margin: 5px 25px; */
}

.select_schedule_people .today {
    background: #007BD0;
}

.how_many_people {
    padding: 30px 60px;
    box-sizing: border-box;
}

.people_pick_caution {
    display: flex;
    color: #555555;
    font-size: 14px;
    margin-top: 10px;
}

.people_select_box {
    width: 135px;
    height: 50px;
    border: 1px solid #CCC;
    padding: 0 15px;
    color: #333;
    font-family: Pretendard;
    font-size: 18px;
    margin-right: 20px;
    appearance:none;
    background:url(/images/icon/select_drop.svg) no-repeat right 15px center;
}

.next_btn {
    width: 262px;
    height: 53px;
    background: #222;
    color: #FFF;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    line-height: 53px;
    margin: 0 auto;
    margin-top: 100px;
}

.next_btn:hover {
    cursor: pointer;
    text-decoration: underline;
}

@media screen and (max-width:1280px) {
    .select_schedule_people .calender {
        width: 100%;
    }

    .select_schedule_people .calender_date_box div {
        /* margin: 5px;
        padding: 5px; */
    }
}

@media screen and (max-width:900px) {
    .select_schedule_people {
        flex-wrap: wrap;
    }

    .calender_gray_outer_box {
        width: 100%;
    }
    @media screen and (max-width:768px) {
        .next_btn{
            margin-top:60px;
        }
    }
}

@media screen and (max-width:600px) {
    .calender_gray_box {
        padding: 20px;
    }

    .how_many_people {
        padding: 20px;
    }
}

/*강화유니버스 커뮤니티 프로그램_예약페이지*/
.ganghwa_universe_community_program_last .final_payment_details_img {
    min-width: 140px;
    height: 140px;
}

.ganghwa_universe_community_program_last .program_detail_price {
    margin: 21px 0 0 0;
}

/*프로그램*/
.program_banner {
    background: url(/images/program_banner.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    color: #fff;
}

/*B2B문의*/
.b2b_collabolation {
    height: 792px;
    padding: 137px 0 0 0; box-sizing: border-box;
    background: url(/images/b2b_collabolation_bg.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    color: #fff;
}

.b2b_collabolation_contents {
    display: flex;
    justify-content: space-between;
}

.b2b_collabolation_contents_title_box {
    margin-top: 100px;
}

.b2b_collabolation_contents_title {
    text-shadow: 2px 2px 0px #000;
    font-family: Tenada;
    font-size: 50px;
    font-weight: 800;
}

.b2b_collabolation_contents_txt {
    font-size: 26px;
    font-weight: 700;
    line-height: 30px;
    margin: 53px 0 30px 0;
}

.collabolation_img_box {
    display: flex;
}

.collabolation_img {
    margin-top: 66px;
    margin-right: 34px;
}

.collabolation_img_box img {
    width: 100%; height: auto;
}

.collabolation_img_mg_b {
    margin-bottom: 34px;
}

.b2b_what {
    padding: 180px 0;
}

.b2b_what_contents {
    display: flex;
    justify-content: space-between;
}

.three_cicle {
    width: 113px; height: 39px;
    background: url(/images/icon/three_cicle.svg);
}

.b2b_what_title {
    color: #222;
    font-size: 46px;
    font-weight: 700;
    margin: 40px 0 30px 0;
}

.b2b_what_title_ex {
    color: #222;
    font-size: 18px;
    font-weight: 400;
    line-height: 30px;
    margin-bottom: 53px;
}

.b2b_contact_btn {
    display: block;
    width: 306px;
    padding: 15px 30px;
    box-sizing: border-box;
    border: 1px solid #094;
    background: #FFF;
    color: #094;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
}

.b2b_contact_btn:hover {
    text-decoration: underline;
}

.b2b_what_contents_list_wrap {
    width: calc(100% - 390px - 97px);
    margin-top: 200px;
}

.b2b_what_contents_list_box {
    border-top: 1px solid #000;
}

.b2b_what_contents_list {
    padding: 22px 15px;
    border-bottom: 1px solid #000;
    color: #222;
    font-size: 18px;
    font-weight: 400;
    line-height: 30px;
}

.b2b_black_bg {
    padding: 180px 0;
    background: #333;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    color: #fff;
}

.b2b_black_bg_title {
    color: #FFF;
    text-align: center;
    font-size: 32px;
    font-weight: 700;
    line-height: 46px;
    margin-bottom: 80px;
}

.b2b_tab_area{
    margin-bottom:80px;
    display:flex;
    justify-content: center;
}
.b2b_tab_area .tab_list{
    display:flex;
    align-items: center;
}
.b2b_tab_area .tab_list .tab_item{
    margin-right:20px;
    cursor: pointer;
    padding:14px 32px;
    border-radius: 50px;
    border:1px solid #fff;
    font-size:22px;
    font-weight:700;
}
.b2b_tab_area .tab_list .tab_item.active{
    color:#14D769;
    border:1px solid #14D769;
}
.b2b_black_bg .tab_content{
    display:none;
}
.b2b_tab_area .tab_list .tab_item:last-child{
    margin-right:0;
}

.b2b_poster_list_box {
    display: grid;
    grid-template-columns: repeat(4, calc(25%));
}

.b2b_poster_list {
    height: auto;
    overflow: hidden;
}

.b2b_poster_list img {
    display: block;
    width: 100%; height: 100%;
    object-fit: cover;
}

.b2b_black_bg .paging_number {
    color: #696969;
}

.b2b_black_bg .paging_number .on {
    color: #fff;
}

.b2b_contact_us {
    padding: 182px 0 0 0;
}

.b2b_contact_us_title {
    color: #222;
    text-align: center;
    font-size: 46px;
    font-weight: 700;
    margin-bottom: 130px;
}

.b2b_contact_us_form {
    display: flex;
    justify-content: space-between;
}

.b2b_contact_us_form_list {
    width: calc(50% - 100px);
    margin-bottom: 60px;
}

.b2b_contact_us_form_list_title {
    color: #222;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
}

.b2b_contact_us_form_list_input {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    border: none;
    border-bottom: 1px solid #aaa;
    color: #888;
    font-family: Pretendard;
    font-size: 15px;
    font-weight: 400;
}

.b2b_contact_us_form_list_select {
    width: 100%;
    padding: 10px;
    border: none;
    border-bottom: 1px solid #aaa;
    color: #888;
    font-family: Pretendard;
    font-size: 15px;
    font-weight: 400;
    appearance:none;
    background:url(/images/icon/select_drop.svg) no-repeat right 8px center;
}

.b2b_contact_us_inquiries {
    margin-bottom: 50px;
}

.b2b_contact_us_form_list_txt {
    width: 100%;
    height: 200px;
    border: 1px solid #AAA;
    background: #FFF;
    resize: none;
    padding: 15px; box-sizing: border-box;
    font-family: Pretendard;
    font-size: 15px;
    font-weight: 400;
}

.scroll_contents_gray {
    background: #aaa;
}

.b2b_contact_us_info {
    color: #333;
    line-height: 26px;
    margin-bottom: 10px;
}

.b2b_contact_us_info_en {
    color: #AAA;
    font-size: 15px;
    line-height: 25px;
}

.b2b_contact_us .argee_secession {
    margin-bottom: 113px;
}

.b2b_contact_us .agree_chk {
    width: 22px;
    height: 22px;

}

.b2b_contact_us .agree_chk_txt {
    color: #444;
    font-size: 15px;
}

.inquiry_reception_btn {
    display: block;
    width: 290px;
    height: 53px;
    border: 1px solid #222;
    background: #FFF;
    text-align: center;
    color: #333;
    font-size: 18px;
    font-weight: 600;
    line-height: 53px;
    margin: 0 auto;
}

.inquiry_reception_btn:hover {
    cursor: pointer;
    text-decoration: underline;
}

@media screen and (max-width:1200px) {
    .b2b_collabolation {
        height: unset;
        padding: 100px 0;
    }
    .b2b_collabolation_contents {
        flex-wrap: wrap;
        justify-content: center;
    }

    .collabolation_img_box {
        margin-top: 50px;
    }
}

@media screen and (max-width:1024px) {
    .b2b_what_contents_list_wrap {
        width: calc(100% - 390px - 50px);
        margin-top: 200px;
    }

    .b2b_contact_us_form_list {
        width: calc(50% - 20px);
    }
}

@media screen and (max-width:800px) {
    .b2b_tab_area {
        overflow-x: scroll;
        justify-content: unset;
    }

    .b2b_tab_area .tab_list {
        width: max-content;
    }

    .b2b_tab_area .tab_list .tab_item {
        width: max-content;
    }

}

@media screen and (max-width:768px) {
    .b2b_collabolation{
        padding:60px 0;
    }
    .b2b_collabolation_contents_title_box{
        margin-top:20px;
    }
    .b2b_collabolation_contents_title {
        font-size: 32px;
    }
    .b2b_collabolation_contents_txt {
        margin:40px 0 20px 0;
    }
    .b2b_what_contents {
        flex-wrap: wrap;
    }
    .b2b_what_contents_list_wrap {
        width: 100%;
        margin-top: 50px;
    }
    .b2b_what_title_ex{
        margin-bottom:30px;
    }
   .collabolation_img_box{
        flex-direction: column;
    }
    .collabolation_img {
        margin:0;
        margin-bottom:10px;
        width:100%;
    }
    .collabolation_img_mg_b{
        margin-bottom:10px;
    }
    .b2b_what{
        padding:80px 0;
    }
    .b2b_what_title {
        font-size:32px;
    }
    .b2b_what_contents_list{
        padding:20px 0;
    }
    .b2b_black_bg{
        padding:80px 0;
    }
    .b2b_tab_area{
        margin-bottom:40px;
    }
    .b2b_tab_area .tab_list .tab_item{
        padding:10px 15px;
        border-radius: 25px;
        font-size:16px;
        margin-right:10px;
    }
    .b2b_black_bg_title{
        font-size:24px;
        line-height:1.5;
        margin-bottom:20px;
    }
    .b2b_contact_us{
        padding:80px 0;
    }
    .b2b_contact_us_title{
        font-size:32px;
        margin-bottom:40px;
    }
    .b2b_contact_us_form_list{
        margin-bottom:30px;
    }
    .b2b_contact_us_form_list_title{
        margin-bottom:10px;
    }

    .b2b_contact_us_info p{
        margin-bottom:10px;
        line-height:20px;
    }
    .b2b_contact_us .argee_secession{
        margin-bottom:60px;
    }

}

@media screen and (max-width:600px) {
    .b2b_contact_us_form {
        flex-wrap: wrap;
    }

    .b2b_contact_us_form_list {
        width: 100%;
    }
    .contents_list_title {
        font-size: 18px;
    }


}

@media screen and (max-width:425px) {

}

@media screen and (max-width:375px) {
    .b2b_contact_btn {
        width: 100%;
    }

    .inquiry_reception_btn {
        width: 100%;
    }
}

/*팝업_쉐도우*/
.shadow{
    position: fixed;left: 0;top: 0;background: rgba(0, 0, 0, 0.50);width: 100%;height: 100vh;display: none;
    z-index: 999999999999;
}

.user_view_close {
    width: 37px;
    height: 37px;
    background: url(/images/icon/popup_close_black.svg);
    border: 0;
    position: absolute;
    right: 0px;top: -57px;
    cursor: pointer;
}

/*접수 완료_팝업*/
.receive_popup {
    position: fixed;
    width: 716px; height: 340px;
    left: 50%;top: 50%;
    transform: translate(-50%,-50%);
    padding: 20px; box-sizing: border-box;
    display: none;
    background: #fff;
    z-index: 9999999999999999;
    border-radius: 10px;
}
.receive_popup .close{
    width: 37px;
    height: 37px;
    background: url(/images/icon/popup_close.svg);
    border: 0;
    position: absolute;
    right: 0px;top: -57px;
    cursor: pointer;
}

.receive_popup_contents {
    width: 100%; height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.receive_popup_contents .three_cicle {
    margin: 0 auto;
    margin-bottom: 24px;
}

.popup_title {
    font-size: 30px;
    font-weight: 600;
}

.popup_ex {
    line-height: 24px;
    margin: 30px 0;
}

@media screen and (max-width:800px) {
    .receive_popup {
        width: calc(100% - 30px);
    }

    .popup_ex_txt {
        padding: 10px;
    }
}

@media screen and (max-width:600px) {
    .popup_title {
        font-size: 24px;
    }
}

/*B2B문의_뷰페이지*/
.portfolio_contents_side {
    width: calc(100% - 300px - 80px);
}

.b2b_view_contents .portfolio_contents_side {
    padding-bottom: 200px;
}

.b2b_view_contents .view_contents_img {
    max-width: 800px;
    margin: 37px auto 40px auto;
}

.portfolio_side_menu {
    width: 300px;
}

.ganghwa_universe_portfolio_title {
    color: #222;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 30px;
}

.ganghwa_universe_portfolio {
    height: 1880px;
    background: #F5F5F5;
    padding: 40px 30px; box-sizing: border-box;
    overflow-y: scroll;
}

.ganghwa_universe_portfolio_list img {
    width: 100%; height: auto;

}

/* 스크롤바 설정*/
.ganghwa_universe_portfolio::-webkit-scrollbar{
    width: 7px;
}

/* 스크롤바 막대 설정*/
.ganghwa_universe_portfolio::-webkit-scrollbar-thumb{
    background-color: #F5F5F5;
}

/* 스크롤바 뒷 배경 설정*/
.ganghwa_universe_portfolio::-webkit-scrollbar-track{
    background-color: #F5F5F5;
}

.ganghwa_universe_portfolio_list_box {
    display: grid;
    grid-template-columns: repeat(2, calc(50% - 9px));
    grid-column-gap: 18px;
    grid-row-gap: 18px;
}

@media screen and (max-width:768px) {
    .portfolio_contents_side {
        width: 100%;
    }

    .portfolio_side_menu {
        width: 100%;
        margin-top: 50px;
    }

    .b2b_view_contents .portfolio_contents_side {
        padding-bottom: 50px;
    }

    .ganghwa_universe_portfolio {
        height: 1000px;
    }
}

/*마이페이지*/
.mypage_banner {
    position: relative;
    height: 442px;
    background: url(/images/mypage_bg.jpg);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    color: #fff;
    overflow: hidden;
}

.mypage_banner_title {
    position: relative;
    z-index: 99999;
    text-shadow: 2px 2px 0px #000;
    font-family: Tenada;
    font-size: 60px;
    font-weight: 800;
    padding-top: 150px;
}

.mypage_bg_point {
    position: absolute;
    top: 105px; right: 50%;
    margin-right: -811px;
    width: 450px; height: 256px;
    background: url(/images/mypage_bg_point.png);
}

.mypage_contents_box {
    position: relative;
    background: #F3F3F3;
}

.mypage_contents {
    position: absolute;
    top: -140px; left: 50%;
    margin-left: -700px;
    display: flex;
    justify-content: space-between;
}

.my_profile_box {
    position: relative;
    width: 327px;
    height: 398px;
    padding: 25px; box-sizing: border-box;
    border-radius: 10px;
    background: #FFF;
}

.setting_btn {
    cursor: pointer;
    position: absolute;
    top: 25px; right: 25px;
    width: 33px; height: 33px;
    background: url(/images/icon/setting_btn.svg);
}

.my_profile_img {
    width: 165px;
    height: 165px;
    border-radius: 50%;
    border: 1px solid #D9D9D9;
    overflow: hidden;
    margin: 0 auto;
    margin-top: 55px;
}

.my_profile_img img {
    width: 100%; height: 100%;
    object-fit: cover;
}

.my_profile_nickname {
    color: #222;
    text-align: center;
    font-size: 22px;
    font-weight: 700;
    margin: 36px 0 10px 0;
}

.my_profile_email {
    color: #666;
    text-align: center;
    font-size: 15px;
    font-weight: 400;
}

.my_history_box {
    width: calc(100% - 327px - 50px);
}

.badge_situation {
    height: 398px;
    padding: 48px; box-sizing: border-box;
    border-radius: 10px;
    background: #FFF;
    margin-bottom: 30px;
}

.mypage_contents_title_box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 63px;
}

.mypage_contents_title {
    color: #222;
    font-size: 26px;
    font-weight: 700;
}

.all_view_btn_box {
    display: flex;
    align-items: center;
    color: #444;
    font-size: 15px;
}

.all_view_btn_box:hover {
    cursor: pointer;
    text-decoration: underline;
}

.all_view_btn_icon {
    width: 14px; height: 14px;
    background: url(/images/icon/all_view_btn_icon.svg);
    margin-right: 5px;
}

.my_badge_list_box {
    display: grid;
    grid-template-columns: repeat(6, calc(16.6% - 19.1px));
    grid-column-gap: 23px;
    grid-row-gap: 26px;
}

.my_badge_list {
    color: #444;
    text-align: center;
    font-size: 15px;
}

.my_badge_img {
    margin-bottom: 15px;
}

.my_badge_img img {
    width: 100%; height: auto;
}

.island_category {
    height: 334px;
    padding: 48px; box-sizing: border-box;
    border-radius: 10px;
    background: #FFF;
    margin-bottom: 30px;
}

.island_category_result_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.island_category_box {
    width: 144px;
    height: 144px;
    border-radius: 10px;
    background: #E83929;
    color: #FFF;
    text-align: center;
    text-shadow: 2px 2px 0px #000;
    font-family: Tenada;
    font-size: 36px;
    font-weight: 800;
    line-height: 144px;
}

.island_category_result {
    width: calc(100% - 144px - 20px);
    color: #333;
    text-align: center;
    font-size: 24px;
}

.island_category_result .bold {
    font-weight: 700;
    text-decoration-line: underline;
}


.reset_btn {
    width: 125px; height: 35px;
    border-radius: 4px;
    border: 1px solid #222;
    background: #FFF;
    color: #222;
    font-size: 16px;
    font-weight: 400;
    line-height: 35px;
    margin: 0 auto;
    margin-top: 30px;
}

.reset_btn:hover {
    cursor: pointer;
    text-decoration: underline;
}

.community_contribution {
    height: 270px;
    padding: 48px; box-sizing: border-box;
    border-radius: 10px;
    background: #FFF;
    margin-bottom: 30px;
}

.community_contribution_list_box {
    display: grid;
    grid-template-columns: repeat(5, calc(20%));
}

.community_contribution_list {
    border-left: 1px solid #aaa;
    box-sizing: border-box;
    color: #333;
    text-align: center;
}

.community_contribution_list:last-child {
    border-right: 1px solid #aaa;
}

.mypage_contents .number {
    font-size: 40px;
    font-weight: 700;
    margin-top: 26px;
    transition: all 0.3s;
    cursor: pointer;
}
@media (hover: hover) {
    .mypage_contents .number:hover {
        text-decoration: underline;
    }
}
.mypage_simple_box {
    padding: 35px 48px;
    box-sizing: border-box;
    border-radius: 10px;
    background: #FFF;
    margin-bottom: 30px;
}

.mypage_simple_box .mypage_contents_title_box {
    align-items: center;
    margin-bottom: 0;
}

.mypage_simple_box .txt_mg_l {
    margin-left: 14px;
}

.mypage_arrow {
    cursor: pointer;
    width: 45px;
    height: 43px;
    background: url(/images/icon/mypage_arrow.svg);
}

.my_intro_box {
    width: calc(100% - 190px - 20px - 70px);
    display: flex;
    justify-content: space-between;
}

.my_intro_txt {
    width: calc(100% - 20px - 70px);
    border: none;
    border-bottom: 1PX solid #222;
    padding: 0 20px; box-sizing: border-box;
}

.my_intro_btn {
    width: 70px; height: 35px;
    border-radius: 4px;
    border: 1px solid #222;
    background: #FFF;
    color: #222;
    font-size: 16px;
    font-weight: 400;
    line-height: 35px;
    text-align: center;
}

.my_intro_btn:hover {
    cursor: pointer;
    background: #222;
    color: #fff;
}

@media screen and (max-width:1440px) {
    .mypage_bg_point {
        margin-right: -400px;
    }

    .mypage_contents {
        position: absolute;
        top: -140px;
        left: 20px;
        margin-left: 0;
    }
}

@media screen and (max-width:1024px) {
    .mypage_contents  {
        flex-wrap: wrap;
    }

    .my_profile_box {
        width: 100%;
        margin-bottom: 50px;
    }

    .my_history_box {
        width: 100%;
    }

    .my_intro_box {
        width: calc(100% - 50px - 20px - 70px);
        display: flex;
        justify-content: space-between;
    }

    .badge_situation {
        height: unset;
    }
}

@media screen and (max-width:768px) {
    .my_badge_list_box {
        display: grid;
        grid-template-columns: repeat(3, calc(33.3% - 15.3px));
        grid-column-gap: 23px;
        grid-row-gap: 20px;
    }
    .mypage_banner_title{
        font-size:30px;
        text-align: center;
        padding-top:100px;
    }
}

@media screen and (max-width:600px) {
    .badge_situation {
        padding: 20px;
    }

    .island_category {
        height: unset;
        padding: 20px;
    }

    .island_category_result_box {
        flex-wrap: wrap;
    }

    .island_category_box {
        margin: 0 auto;
        margin-bottom: 20px;
    }

    .island_category_result {
        width: 100%;
    }

    .community_contribution {
        height: unset;
        padding: 20px;
    }

    .community_contribution_list_box {
        display: grid;
        grid-template-columns: repeat(3, calc(33.3%));
        grid-row-gap: 20px;
    }

    .community_contribution_list:nth-child(3n) {
        border-right: 1px solid #aaa;
    }

    .mypage_simple_box {
        padding: 20px;
    }

    .mypage_contents_title_box {
        flex-wrap: wrap;
        margin-bottom: 30px;
    }
    .mypage_contents_title{
        font-size:20px;
    }
    .my_intro_box {
        width: 100%;
    }
    .mypage_arrow{
        width:32px;
        height:32px;
        background-size:32px 32px;
    }
}

@media screen and (max-width:425px) {
    .my_badge_list_box {
        display: grid;
        grid-template-columns: repeat(2, calc(50% - 11.5px));
        grid-column-gap: 23px;
        grid-row-gap: 20px;
    }
}

.user_view_badge_list {
    min-height: 700px;
    padding-top: 100px;
}

/*마이페이지_프로필 설정*/
.mypage_profile_contents_box {
    position: relative;
    background: #F3F3F3;
}

.mypage_title_close_contents {
    position: absolute;
    top: -340px;
    left: 50%;
    margin-left: -700px;
}

.mypage_close_contents_title {
    color: #FFF;
    font-size: 30px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 50px;
}

.mypage_close_contents_box {
    position: relative;
}

.mypage_close_contents {
    width: 100%;
    background: #FFF;
    border-radius: 10px;
    padding: 60px 70px; box-sizing: border-box;
    min-height:400px;
}
.no-badge-wrap .no-badge{
    text-align: center;
}
.no-badge-wrap .mypage_sub_title {
    margin-bottom:40px;
}
.mypage_close_contents_box .close{
    width: 37px;
    height: 37px;
    background: url(/images/icon/popup_close.svg);
    border: 0;
    position: absolute;
    right: 0px;top: -57px;
    cursor: pointer;
}

.my_profile_img_change {
    display: flex;
    align-items: center;
    margin-bottom: 48px;
}

.my_profile_setting_img {
    width: 165px;
    height: 165px;
    border-radius: 50%;
    border: 1px solid #D9D9D9;
    overflow: hidden;
}

.my_profile_setting_img img {
    width: 100%; height: 100%; aspect-ratio: 1/1;
    object-fit: cover;
}

.img_change_btn {
    width: 147px; height: 35px;
    border-radius: 4px;
    border: 1px solid #222;
    background: #FFF;
    color: #222;
    font-size: 16px;
    font-weight: 400;
    line-height: 35px;
    margin-left: 40px;
    text-align: center;
}

.img_change_btn:hover {
    cursor: pointer;
    text-decoration: underline;
}

.mypage_setting_list {
    margin-bottom: 40px;
}

.pw_change_btn_box {
    position: relative;
}

.pw_change_btn_box input {
    padding-right: 129px;
    box-sizing: border-box;
}

.pw_change_btn {
    position: absolute;
    top: 0; right: 0;
    width: 109px;
    height: 28px;
    border-radius: 2px;
    background: #373737;
    border: none;
    color: #FFF;
    font-size: 15px;
    line-height: 28px;
    text-align: center;
}

.pw_change_btn:hover {
    cursor: pointer;
    opacity: 0.9;
}
.pw_change_btn:disabled {
    cursor: not-allowed;
    background: #ccc;
    color: #1010104D;
}

.mypage_setting_grid_box {
    display: grid;
    grid-template-columns: repeat(3, calc(33.3% - 22px));
    grid-column-gap: 33px;
}

#men, #women, #no {display: none;}
#men:checked ~ .mypage_setting_men{color: #fff; background: #222;}
#women:checked ~ .mypage_setting_women{color: #fff; background: #222;}
#no:checked ~ .mypage_setting_no{color: #fff; background: #222;}

.mypage_setting_gender {
    height: 40px;
    border: 1px solid #AAA;
    background: #F9F9F9;
    color: #888;
    font-size: 15px;
    line-height: 40px;
    text-align: center;
}

.mypage_setting_gender:hover {
    cursor: pointer;
    color: #222;
    border: 1px solid #222;
}

.mypage_setting_flex_box {
    display: flex;
}

.mypage_setting_flex_box .b2b_contact_us_form_list_select {
    width: 397px;
    margin-right: 32px;
}

.mypage_setting_flex_box .b2b_contact_us_form_list_input {
    width: calc(100% - 397px - 32px);
}

.argee_secession_mg_b {
    margin-bottom: 60px;
}

.line_small_btn {
    width: 111px;
    height: 50px;
    padding: 16px 20px;
    box-sizing: border-box;
    border-radius: 50px;
    border: 1px solid #444;
    background: #FFF;
    color: #222;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    margin: 0 auto;
}

.line_small_btn:hover {
    cursor: pointer;
    text-decoration: underline;
}

.pw_popup {
    position: fixed;
    width: 716px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    padding: 40px 50px;
    box-sizing: border-box;
    display: none;
    background: #fff;
    z-index: 9999999999999999;
    border-radius: 10px;
}

.pw_popup_contents {
    width: 100%; height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.pw_popup_contents>div {
    width: 100%;
}

.pw_popup_line {
    border-top: 1px solid #000;
    margin-top: 26px;
    padding-top: 63px;
    margin-bottom: 60px;
}

.pw_popup_line_contents {
    display: flex;
    align-items: center;
}

.pw_popup_line_contents_mg_b {
    margin-bottom: 40px;
}

.pw_popup_line_contents .b2b_contact_us_form_list_title {
    min-width: 127px;
    margin-right: 60px;
    margin-bottom: 0;
}

@media screen and (max-width:1440px) {
    .mypage_title_close_contents {
        position: absolute;
        top: -340px;
        left: 20px;
        margin-left: 0;
    }
}

@media screen and (max-width:768px) {
    .mypage_close_contents {
        padding: 20px;
    }

    .pw_popup {
        width: calc(100% - 40px);
    }
}

@media screen and (max-width:600px) {
    .mypage_setting_grid_box {
        display: grid;
        grid-template-columns: repeat(3, calc(33.3% - 6.6px));
        grid-column-gap: 10px;
    }

    .mypage_setting_flex_box {
        flex-wrap: wrap;
    }

    .mypage_setting_flex_box .b2b_contact_us_form_list_select {
        width: 100%;
        margin-right: 0;
    }

    .mypage_setting_flex_box .b2b_contact_us_form_list_input {
        width: 100%;
    }

    .pw_popup {
        padding: 20px;
    }

    .pw_popup_line_contents {
        flex-wrap: wrap;
    }
}

@media screen and (max-width:540px) {
    .my_profile_img_change {
        flex-wrap: wrap;
        justify-content: center;
    }

    .img_change_btn {
        margin-left: 0;
        margin-top: 20px;
    }
}

/*마이페이지_나의 배지 현황*/
.mypage_sub_title {
    color: #333;
    font-size: 20px;
    font-weight: 600;
    line-height: 32px;
    text-align: center;
    margin-bottom: 70px;
}

.special_badge {
    margin-top: 130px;
}

.special_badge_title {
    color: #222;
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 40px;
}

/*마이페이지_완료한 미션내역*/
.misson_contents {
    padding: 0px 70px;
    display: grid;
    grid-template-columns: repeat(2, calc(50% - 35px));
    grid-column-gap: 70px;
}

.misson_contents_list {
    margin-bottom: 50px;
}

.misson_contents_list:last-child {
    margin-bottom: 0;
}

.misson_contents_list_img {
    margin-bottom: 17px;
}

.misson_contents_list_img:last-child {
    margin-bottom: 0;
}

.misson_contents_list_img img {
    display: block;
    width: 100%; height: auto;
}

.misson_contents_ex {
    margin: 17px 0;
    color: #444;
    font-size: 18px;
    font-weight: 400;
    line-height: 30px;
}

@media screen and (max-width:1024px) {
    .misson_contents {
        padding: 0px;
        display: grid;
        grid-template-columns: repeat(2, calc(50% - 15px));
        grid-column-gap: 30px;
    }
}

@media screen and (max-width:600px) {
    .misson_contents {
        display: block;
    }

    .misson_contents_line_mg_t {
        margin-top: 50px;
    }

}

/*마이페이지_포인트*/
.my_point_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40px;
    border: 1px solid #000;
    border-radius: 10px;
}

.my_point {
    display: flex;
    align-items: center;
}

.my_point_title {
    color: #222;
    font-size: 26px;
    font-weight: 700;
}

.my_point_coin {
    display: flex;
    align-items: center;
}

.my_point_coin_flex {
    display: flex;
    align-items: center;
}

.my_point_coin_icon {
    min-width: 32px; height: 32px;
    background: url(/images/icon/my_point_coin_icon.svg);
    margin: 0 20px 0 40px;
}

.my_point_coin_txt {
    font-size: 40px;
    font-weight: 700;
    color: #222;
}

.my_point_line {
    width: 1px; height: 20px;
    border-right: 1px solid #444;
    margin: 0 20px;
}

.my_point_txt {
    font-size: 18px;
    font-weight: 500;
    color: #222;
}

.my_point_coin_green {
    font-size: 18px;
    font-weight: 600;
    color: #009944;
    margin-left: 20px;
}

.point_change {
    width: 147px; height: 35px;
    background: #222;
    color: #fff;
    border-radius: 4px;
    text-align: center;
    line-height: 35px;
    border: none;
    cursor: pointer;
}

.point_change:hover {
    opacity: 0.9;
}

.my_point_ex_box {
    margin-top: 20px;
}

.my_point_ex {
    display: flex;
    align-items: center;
    color: #444;
    margin-bottom: 10px;
}

.my_point_ex_dot {
    min-width: 3px; height: 3px;
    background: #444;
    border-radius: 50%;
    margin: 0 10px;
}


.point_list_title_wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 17px;
}

.point_list_title {
    color: #222;
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 17px;
}

.point_list_box_question {
    display: flex;
    color: #444;
}

.point_list_box_question_mark {
    width: 17px; height: 17px;
    background: url(/images/icon/point_list_box_question_mark.svg);
    margin-left: 5px;
}

.mypage_border_list {
    display: flex;
    align-items: center;
    padding: 22px 0;
    border-bottom: 1px solid #aaa;
}

.mypage_border_list_day {
    min-width: 90px;
    margin-right: 70px;
    color: #888;
}

.mypage_border_list_contents {
    width: 100%;
    display: flex;
    justify-content: space-between;
    color: #333;
    font-size: 18px;
}

.point_color_blue {
    color: #056FB8;
    font-weight: 600;
}

.point_color_blak {
    color: #333;
    font-weight: 600;
}

.mypage_border_pay {
    display: flex;
    align-items: center;
}

.mypage_border_pay span {
    display: inline-block;
    width: 70px;
    text-align: center;
}

.mypage_border_pay_bold {
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.mypage_border_pay_mg_r {
    margin-right: 50px;
}

.mypage_border_pay_blue {
    color: #056FB8;
}

.mypage_border_pay_red {
    color: #E83929;
}

.point_popup_line {
    border-top: 1px solid #000;
    margin-top: 26px;
    padding-top: 63px;
    margin-bottom: 60px;
    color: #222;
    line-height: 28px;
    font-size: 18px;
}

.provision {
    padding: 50px 0;
}

.point_provision {
    border-bottom: 2px dashed #ccc;
}

.provision_title {
    font-size: 20px;
    font-weight: 700;
    color: #056FB8;
}

.provision_ex_box {
    margin-top: 20px;
}

.provision_ex {
    display: flex;
    align-items: center;
    color: #222;
    margin-bottom: 10px;
}

.provision_ex_dot {
    min-width: 3px; height: 3px;
    background: #222;
    border-radius: 50%;
    margin: 0 10px 0 0;
}

@media screen and (max-width:1024px) {
    .my_point_box {
        flex-wrap: wrap;
    }

    .point_change {
        margin: 0 auto;
        margin-top: 20px;
    }
}

@media screen and (max-width:768px) {
    .my_point {
        flex-wrap: wrap;
    }

    .my_point_title {
        width: 100%;
    }

    .my_point_coin_icon {
        margin: 0 20px 0 0;
    }
}

@media screen and (max-width:600px) {
    .my_point_title {
        text-align: center;
    }

    .my_point_box {
        padding: 20px;
    }

    .mypage_border_list_day {
        margin-right: 30px;
    }

    .mypage_border_list_contents {
        flex-wrap: wrap;
    }

    .mypage_border_pay {
        display: none;
    }

    .my_point_coin {
        width: 100%;
        flex-wrap: wrap;
        margin: 15px 0;
    }

    .my_point_coin_flex {
        width: 100%;
        justify-content: center;
    }

    .my_point_line {
        display: none;
    }
}

@media screen and (max-width:375px) {
    .my_point_coin_green {
        margin-left: 10px;
    }
}

/*마이페이지_예약내역 및 결제내역_리스트*/
.mypage_reservation_contents_box .argee_secession {
    margin-bottom: 42px;
}

.mypage_reservation_contents_box .agree {
    margin-right: 20px;
}

.point_list_box_mg_b {
    margin-bottom: 100px;
}

.point_list_title_box {
    display: flex;
    justify-content: space-between;
}

.point_list_title_box span {
    font-size: 15px;
    color: #888;
}
.mypage_reservation_pd_box {
    padding: 25px 0;
    border-bottom: 1px solid #aaa;
}

/*마이페이지_예약내역 및 결제내역*/
.mypage_pay_contents_box .stay_info_room {
    margin: 14px 0 0 0;
}

.mypage_pay_contents_box .stay_info_name {
    color: #333;
}
.right-btn-area{
    display:flex;
    align-items: center;
}
.receipt_btn {
    height: 30px;
    padding: 5px 17px; box-sizing: border-box;
    border-radius: 17px;
    border: 1px solid #444;
    background: #FFF;
    color: #333;
    text-align: center;
    margin-bottom:17px;
}

.receipt_btn:hover {
    cursor: pointer;
    text-decoration: underline;
}

@media screen and (max-width: 600px) {
    .point_list_box_mg_b {
        margin-bottom: 50px;
    }

}

@media screen and (max-width: 425px) {
    .point_list_title_box {
        flex-wrap: wrap;
    }

    .point_list_title_box span {
        margin-bottom: 20px;
    }
    .mypage_pay_contents_box .stay_info_room {
        flex-wrap: wrap;
    }

    .mypage_pay_contents_box .stay_info_room_txt {
        margin-right: 0;
        margin-bottom: 20px;
    }
    .mypage_pay_contents_box .reservation_completed_sub_title {
        text-align: left;
    }

    .reservation_completed_list {
        justify-content: unset;
    }
}

/*마이페이지_게시글*/
.mypage_boder_contents_box .community_title_img_post_title_box {
    width: calc(100% - 96px - 50px);
}

.mypage_boder_contents_box .community_title_img_post_img {
    width: 96px; height: 96px;
    overflow: hidden;
}

.mypage_boder_contents_box .community_title_img_post_ex {
    color: #444;
    line-height: 28px;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin: 25px 0;
}

.delete_box {
    display: flex;
    align-items: center;
    color: #333;
    font-size: 15px;
    font-weight: 600;
}

.delete_icon {
    cursor: pointer;
    width: 20px;
    height: 20px;
    background: url(/images/icon/delete_icon.svg);
    margin-right: 6px;
}

@media screen and (max-width: 600px) {
    .mypage_boder_contents_box .community_title_img_post_title_box {
        width: 100%;
        order: 2;
    }
}

/*마이페이지_문의내역*/
.ing_ok_circle {
    padding:4px 14px;
    border-radius: 50px;
    border: 1px solid #222;
    background: #222;
    color: #FFF;
    font-size: 15px;
    font-weight: 600;
}

.ing_wait_circle {
    padding:4px 14px;
    border-radius: 50px;
    border: 1px solid #222;
    background: #FFF;
    color: #222;
    font-size: 15px;
    font-weight: 600;
}

/*로그인_페이지*/
.user_bg {
    padding: 150px 0 200px 0;
    background: #F3F3F3;
}

.user_contents {
    width: 750px; min-height: 995px;
    border: 1px solid #000;
    background: #FFF;
    margin: 0 auto;
    box-sizing: border-box;
}

.login_contents {
    padding: 140px 132px;
    box-sizing: border-box;
}

.user_contents .three_cicle {
    width: 134px; height: 45px;
    background: url(/images/icon/three_cicle.svg);
    background-repeat: no-repeat;
    background-size: cover;
    margin: 0 auto;
    margin-bottom: 32px;
}

.user_title_box {
    color: #222;
    text-align: center;
    font-family: Tenada;
    font-size: 45px;
    font-weight: 800;
}

.user_ex {
    color: #444;
    text-align: center;
    font-size: 18px;
    line-height: 32px;
    margin-top: 30px;
}

.user_input_box {
    margin-top: 44px;
}

.user_input_box .b2b_contact_us_form_list_input {
    margin-top: 24px;
    border-radius: 0;
}

.user_blue_btn {
    width: 319px;
    height: 45px;
    padding: 10px 28px; box-sizing: border-box;
    border-radius: 22.5px;
    border: 1px solid #000;
    background: #056FB8;
    color: #FFF;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    margin: 0 auto;
    margin-top: 40px;
}

.user_blue_btn:hover {
    cursor: pointer;
    background: #fff;
    color: #056FB8;
}

.find_join {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 93px 0 27px 0;
}

.find_box {
    color: #444;
    font-size: 18px;
}

.join_box {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.join_icon {
    width: 9px; height: 15px;
    background: url(/images/icon/join_icon.svg);
    margin-left: 11px;
}

.simple_login_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 30px;
    border-top: 1px solid #222;
}

.sns_box {
    display: flex;
}

.kakao {
    width: 47px;
    height: 47px;
    background: url(/images/icon/kakao.svg);
    margin-right: 10px;
    cursor: pointer;
}

.google {
    width: 47px;
    height: 47px;
    background: url(/images/icon/google.svg);
    margin-right: 10px;
    cursor: pointer;
}


.naver {
    width: 47px;
    height: 47px;
    background: url(/images/icon/naver.svg);
    cursor: pointer;
}

@media screen and (max-width: 768px) {
    .user_contents {
        width: calc(100% - 40px);
        min-height: unset;
    }
    .user_ex{
        margin-top:20px;
        line-height:26px;
        font-size:18px;
    }
    .user_bg {
        padding: 50px 0;
    }
    .login_contents {
        padding: 50px 20px;
    }
    .user_title_box {
        font-size: 28px;
    }
    .find_box {
        font-size: 15px;
    }
    .join_box {
        font-size: 15px;
    }
    .find_join {
        margin-top:60px;
    }
    .user_blue_btn {
        width: 100%;
        margin-top:30px;
    }
    .user_input_box{
        margin-top:20px;
    }
    .user_input_box .b2b_contact_us_form_list_input{
        margin-top:18px;
    }
    .simple_login_box{
        flex-direction: column;
    }
    .simple_login_box span{
        margin-bottom:20px;
    }
    .google,
    .kakao{
        margin-right:20px;
    }
}



/*아이디, 비밀번호 찾기*/
.idpw_contents {
    padding: 150px 118px;
    box-sizing: border-box;
}

.user_btn_box {
    display: flex;
    margin: 66px 0 45px 0;
}

#id_chk, #pw_chk {display: none;}
#id_chk:checked ~ .user_btn_box .id_btn {background: #222; color: #fff;}
#pw_chk:checked ~ .user_btn_box .pw_btn {background: #222; color: #fff;}

#id_chk:checked ~ .sheet_box .id_sheet {display: block;}
#pw_chk:checked ~ .sheet_box .pw_sheet {display: block;}

.user_btn_box label {
    cursor: pointer;
    display: block;
    width: 50%;
    height: 48px;
    background: #EBEBEB;
    color: #666;
    text-align: center;
    font-size: 18px;
    line-height: 48px;
}

.id_sheet, .pw_sheet {display: none;}


@media screen and (max-width: 768px) {
    .idpw_contents {
        padding: 50px;
    }
}

@media screen and (max-width: 425px) {
    .idpw_contents {
        padding: 50px 20px;
    }
}

/*프로필설정*/
.profile_contents {
    min-height: unset;
    padding: 140px 80px;
    box-sizing: border-box;
}

.next_go {
    width: fit-content;
    margin: 0 auto;
    margin-top: 58px;
}

@media screen and (max-width: 768px) {
    .profile_contents {
        padding: 50px;
    }
}

@media screen and (max-width: 425px) {
    .profile_contents {
        padding: 50px 20px;
    }
}

/*아이디, 비밀번호찾기_결과페이지*/
.idpw_find_contents {
    padding: 200px 0 400px;
}

.idpw_find_title {
    color: #333;
    text-align: center;
    font-size: 26px;
    font-weight: 600;
}

.idpw_find_contents .line_btn {
    margin-top: 50px;
}

@media screen and (max-width: 425px) {
    .idpw_find_contents {
        padding: 100px 0 100px;
    }
}

/*회원가입*/
.join_contents {
    padding: 130px 100px;

}
.email_join {
    margin: 70px 0 40px 0;
    padding: 38px 20px;
    border: 1px solid #056FB8;
    color: #056FB8;
    font-family: Pretendard;
    font-size: 20px;
    font-weight: 600;
    text-align: center;
}

.email_join:hover {
    cursor: pointer;
    text-decoration: underline;
}


.join_line {
    width: 100%;
    border-bottom: 1px solid #444;
    margin-top: 60px;
    margin-bottom: 20px;
}

.join_contents_list {
    margin-bottom: 76px;
}

.join_id_box {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
}

.input_write_box {
    display: block;
    width: 100%; height: 40px;
    border: 1px solid #CCC;
    background: #FFF;
    padding: 0 12px;
    box-sizing: border-box;
    color: #444;
    font-family: Pretendard;
    font-size: 15px;
}

.user_mg_b {
    margin-bottom: 10px;
}

.join_id {
    width: calc(100% - 130px - 10px);
}

.check_btn {
    width: 130px; height: 40px;
    border: 1px solid #666; box-sizing: border-box;
    background: #F2F2F2;
    color: #333;
    text-align: center;
    line-height: 40px;
    cursor: pointer;
}

.check_btn:hover {
    cursor: pointer;
    background: #222;
    color: #fff;
}

.join_tel_box {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
}

.join_tel {
    width: calc(100% - 130px - 10px);
    overflow: hidden;
}

.join_tel select, .join_tel input {
    width: calc(33.3% - 13.3px);
    float: left;
}

.join_tel_select {
    appearance:none;
    background:url('/images/icon/select_drop.svg') no-repeat right 14px center;
}

.hyphen {
    float: left;
    color: #aaa;
    font-size: 22px;
    line-height: 40px;
    margin: 0 5px;
}

.join_email_box {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
}

.at {
    color: #444;
    font-size: 15px;
    line-height: 40px;
    margin: 0 5px;
}

.join_email_box select {
    appearance:none;
    background:url('/images/icon/select_drop.svg') no-repeat right 14px center;
    margin-left: 10px;
}

.required_info {
    color: #8C8C8C;
    font-size: 15px;
    line-height: 15px;
    margin-top: 18px;
}

.join_contents_raw {
    margin-bottom: 70px;
}

.all_agree {
    overflow: hidden;
}






/*체크박스_커스텀*/
input[type="checkbox"]{
    display: none;
}

input[type="checkbox"] + label{
    float: left;
    display: block;
    width: 22px;
    height: 22px;
    border:1px solid #aaa; box-sizing: border-box;
    position: relative;
    cursor: pointer;
}

input[id="all_agree"]:checked + label::after{
    content:'✔';
    font-size: 16px;
    width: 22px;
    height: 22px;
    text-align: center;
    position: absolute;
    left: 0; top:0;
}

.all_agree span{
    width: calc(100% - 22px - 5px);
    float: left;
    display: block;
    color: #444;
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;
    margin-left: 5px;
}

.join_raw_line {
    width: 100%;
    border-bottom: 1px solid #444;
    margin-top: 20px;
    margin-bottom: 20px;
}

.join_use {
    margin-bottom: 30px ;
}

.join_raw_txt_box {
    overflow: hidden;
}

.join_raw_title {
    float: left;
    color: #333;
    font-weight: 600;
    line-height: 15px;
}

.use_chk {
    float: right;;
}

/*체크박스_커스텀*/
input[type="checkbox"]{
    display: none;
}

input[type="checkbox"] + label{
    float: left;
    display: block;
    width: 22px;
    height: 22px;
    border:1px solid #aaa; box-sizing: border-box;
    position: relative;
    cursor: pointer;
}

input[id="use_chk"]:checked + label::after{
    content:'✔';
    font-size: 16px;
    width: 22px;
    height: 22px;
    text-align: center;
    position: absolute;
    left: 0; top:0;
}

.use_chk span{
    float: left;
    display: block;
    color: #444;
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;
    margin-left: 5px;
}

/*스크롤바 커스텀*/
.join_raw{
    display: inline-block;
    width: 100%;
    height: 166px;
    padding: 15px;
    overflow-y: scroll;
    border: 1px solid #ccc;
    box-sizing: border-box;
    margin-top: 8px;
}

/* 스크롤바 설정*/
.join_raw::-webkit-scrollbar{
    width: 17px;
}

/* 스크롤바 막대 설정*/
.join_raw::-webkit-scrollbar-thumb{
    background: #A1A1A1;
    border: 5px solid #fff;
}

.privacy_chk {
    float: right;
}

/*체크박스_커스텀*/
input[type="checkbox"]{
    display: none;
}

input[type="checkbox"] + label{
    float: left;
    display: block;
    width: 22px;
    height: 22px;
    border:1px solid #aaa; box-sizing: border-box;
    position: relative;
    cursor: pointer;
}

input[id="privacy_chk"]:checked + label::after{
    content:'✔';
    font-size: 16px;
    width: 22px;
    height: 22px;
    text-align: center;
    position: absolute;
    left: 0; top:0;
}

.privacy_chk span{
    float: left;
    display: block;
    color: #444;
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;
    margin-left: 5px;
}

.join_btn_box {
    display: flex;
    justify-content: center;
}

.join_btn {
    padding:13px 46px;
    border-radius: 23px;
    border: 1px solid #000;
    color: #222;
    text-align: center;
}

.join_btn:hover {
    background: #EDEDED;
    text-decoration: underline;
}

.join_btn_mg_r {
    margin-right: 15px;
}
.certify-area{
    display:flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom:10px;
}
.certify-area div{
    width:calc(100% - 138px);
    display:flex;
    align-items: center;
    justify-content: space-between;
    margin-right:10px;
}
.certify-area div input{
    width:calc(100% - 70px);
    height:40px;
    border: 1px solid #CCC;
    background: #FFF;
    padding: 0 12px;
    box-sizing: border-box;
    color: #444;
    font-family: Pretendard;
    font-size: 15px;
}
.certify-area div p{
    width:50px;
    color: rgb(250, 72, 72);
}
.certify-area button{
    width:130px;
    height:40px;
    border:1px solid #666;
    box-sizing: border-box;
    background-color:#f2f2f2;
    cursor: pointer;
}
.certify-area button:hover{
    color:#fff;
    background-color:#222;
}


@media screen and (max-width: 768px) {
    .join_contents_raw{
        margin-bottom:40px;
    }
    .join_contents_list{
        margin-bottom:60px;
    }
    .join_contents {
        padding: 50px 20px;
    }
    .join_id {
        width: 100%;
    }
    .join_tel_box{
        flex-direction: column;
    }
    .join_tel {
        width: 100%;
    }
    .check_btn {
        width: 60px;
        font-size: 15px;
    }
    .email_join{
        padding:30px 10px;
        margin:40px 0 40px 0;
        font-size:18px;
    }
    .join_tel_box .check_btn{
        width:100%;
    }
    .join_raw_title{
        font-size:14px;
    }
    .privacy_chk span,
    .use_chk span{
        font-size:14px;
    }
    .join_contents_raw input[type="checkbox"] + label{
        width:32px;
        height:32px;
    }
    .m-mg-b{
        margin-bottom:20px;
    }
    .join_raw_txt_box {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        flex-direction: column;
    }
    .join_raw_txt_box .join_raw_title{
        margin-bottom:10px;
    }
    .certify-area{
        flex-direction: column;
        align-items: unset;
        margin-bottom:20px;
    }
    .certify-area div{
        width:100%;
        margin-bottom:10px;

    }
    .certify-area input{
        width:calc(100% - 50px);
    }
    .certify-area button{
        width:100%;
    }
    .join_btn_box .join_btn{
        padding:13px 5px;
        width:calc(50% - 5px);
        display:flex;
        align-items: center;
        justify-content: center;
    }
    .join_id_box{
        flex-direction: column;
        margin-bottom:10px !important;
    }
    .join_id_box input{
        margin-bottom:10px;
    }
    .join_id_box .check_btn{
        width:100%;
        margin-bottom:10px;
    }
}





.verification-code-box {
    display: none;
    position: absolute; top: 41px; right: -2px; width: 200px; height: 40px; background-color: #fff; border: 1px solid #ccc;
    border-radius: 5px; text-align: center; line-height: 30px;
}
.check_btn:hover .verification_btn{
    color:#fff;
}
.verification_btn {
    background:none;
    border:none;
    width:100%;
    height:100%;
    cursor: pointer;
    color:#222;
}
.verification_btn:disabled {
    position:absolute;
    top:0;
    left:0;
    width:100%;
    height:100%;
    background-color:#f2f2f2;
    color:#aaa;
    cursor: not-allowed;
}
.check_btn:hover .verification_btn:disabled{
    color:#aaa;
}
.verification-code-box .verification-code-inner-box {
    display: flex; align-items: center; justify-content: center; padding: 0 5px;
}
.verification-code-box .verification-code {
    width: 90px; border: none; border: 1px solid #A9A9A9; height: 32px; margin: 4px;
}
.verification-code-box .timer{
    margin-right: 5px; color: rgb(250, 72, 72);
}
.verification-code-box .confirm-btn {
    width: 60px; height: 30px; border: none; color: #fff; background-color: #222222; border-radius: 5px;
}






/*회원가입_완료*/
.join_ok_img {
    width: 100%;
    overflow: hidden;
    border-bottom: 1px solid #000;
    box-sizing: border-box;
}

.join_ok_img img {
    display: block;
    width: 100%; height: 100%;
    object-fit: cover;
}

.join_ok_contents {
    padding: 120px 150px;
}

@media screen and (max-width: 768px) {
    .join_ok_contents {
        padding: 50px;
    }
}

@media screen and (max-width: 425px) {
    .join_ok_contents {
        padding: 50px 20px;
    }
}

/*프로필설정_안내창띄움*/
.profile_popup_page {
    height: 1000px;
}

.profile_popup {
    z-index: 2;
    position: absolute;
    top: 71px; right: 114px;
    width: 270px; height: 48px;
    border-radius: 10px;
    border: 2px solid #056FB8;
    background: #FFF;
    color: #056FB8;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile_popup_icon {
    width: 32px; height: 32px;
    background: url(/images/icon/profile_popup_icon.svg);
    margin-right: 7px;
}

@media screen and (max-width: 1600px) {
    .profile_popup {
        right: 114px;
    }
}
@media screen and (max-width: 1400px) {
    .profile_popup {
        right: 25px;
    }
}

@media screen and (max-width: 1024px) {
    .profile_popup {
        width: 50px; right: 21px;
    }

    .profile_popup span {
        display: none;
    }

    .profile_popup_icon {
        margin-right: 0;
    }
    .user_view_wrap {
        margin-top: 0 !important;
    }

    .press_ganghwa_universe_flex_box {
        display: block;
    }
    .press_ganghwa_universe_flex_box .review_search_box {
        margin-right: 0;
    }
    .press_ganghwa_universe_flex_box .review_search_box {
        margin-top: 40px;
    }
}
@media screen and (max-width: 768px) {
    .profile_popup {
        top: 52px;
    }
}

/*개인정보처리방침, 이용약관*/
.terms_wrap {
    margin-top: 120px;
    margin-bottom: 120px;
}

.terms_title {
    color: #333;
    text-align: center;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 100px;
}

.terms_sub_title {
    color: #222;
    font-size: 18px;
    font-weight: 500;
    line-height: 24px;
}

.terms_txt {
    color: #222;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
}

.terms_list {
    margin-bottom: 30px;
}

@media screen and (max-width:600px) {
    .terms_wrap {
        margin-top: 50px;
        margin-bottom: 50px;
    }

    .terms_title {
        text-align: left;
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 40px;
    }

    .terms_sub_title {
        font-size: 16px;
    }

    .terms_txt {
        font-size: 14px;
        line-height: 20px;
    }
    .main_1_slide_contents_box .swiper-slide {
        max-width: 300px;
        width: 100%;

    }
    .main_1_slide_contents .product_img {
        width: 298px; height: 302px;
    }
    .main_1_slide_contents {
        max-width: unset;
    }

    .main_community_program_slide_contents_box .swiper-slide {
        max-width: 300px;
        width: 100%;

    }
    .main_community_program_slide_contents .product_img {
        width: 298px; height: 302px;
    }
    .main_community_program_slide_contents {
        max-width: unset;
    }

    .user_view_badge_list {
        min-height: 550px;
    }
}

.cont-ck-content {
    min-height: 700px;
}


@media screen and (max-width:600px) {

    .cont-ck-content {
        min-height: 500px;
    }
}


/* 개편후 */
/* 개발 완료 후 삭제 할 것*/



/*  sub-banner */
.sub-banner{
    width:100%;
    height:330px;
    background:url(/images/sub-banner01.jpg) center center no-repeat;
    background-repeat:no-repeat;
    background-position: center center;
    background-size: cover;
    margin-bottom:80px;
    position:relative;
}
.sub-banner .text-area{
    position:absolute;
    top:50%;
    left:calc(50% - 700px);
    transform: translateY(-50%);
}
.sub-banner .text-area h4{
    font-family: 'tenada';
    font-size:70px;
    color:#fff;
    margin-bottom:10px;
}
.sub-banner .text-area p{
    font-size:22px;
    color:#fff;
}
@media screen and (max-width:1400px) {
    .sub-banner .text-area{
        left:20px;
    }
}
@media screen and (max-width:768px) {
    .sub-banner{
        background:url(/images/mo-sub-banner01.jpg) center center no-repeat;
        background-size: cover;
        height:200px;
        position:relative;
        margin-bottom:40px;
    }
    .sub-banner .text-area{
        position:absolute;
        top:50%;
        left:50%;
        transform:translate(-50%,-50%);
        padding-top:0;
        text-align: center;
    }
    .sub-banner .text-area h4{
        font-size:36px;
        margin-bottom:0;
    }
    .sub-banner .text-area p{
        font-size:18px;
        line-height:28px;
    }
}





/* tab */
.tab{
    margin-bottom:60px;
}
.tab ul{
    width:100%;
    display:flex;
    align-items: center;
    justify-content: center;
}
.tab ul li{
    padding:16px 30px;
    font-size:20px;
    color:#444;
    border:1px solid #ccc;
    border-radius: 50px;
    margin-right:15px;
    cursor: pointer;
    white-space: nowrap;

}
.tab ul li:last-child{
    margin-right:0;
}
.tab ul li.on{
    background-color:#007BD0;
    color:#fff;
    border:1px solid #000;
    font-weight:600;
}
@media screen and (max-width:1024px) {
    .tab ul{
        justify-content: flex-start;
        width:100%;
        overflow-x: scroll;
        padding-bottom:10px;
    }
    .tab ul li{
        white-space: nowrap;
    }
}

@media screen and (max-width:768px) {
    .tab{
        margin-bottom:40px;
    }
    .tab ul li{
        font-size:18px;
        padding: 10px 15px;
        margin-right:10px;
    }
}

/* sub-tab */
.sub-tab{
    width:100%;
    display:flex;
    gap: 10px;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
}
.sub-tab ul li{
    padding:16px 30px;
    font-size:20px;
    color:#444;
    border:1px solid #ccc;
    border-radius: 50px;
    margin-right:15px;
    cursor: pointer;
    white-space: nowrap;

}
.sub-tab ul li:last-child{
    margin-right:0;
}
.sub-tab ul li.on{
    background-color:#007BD0;
    color:#fff;
    border:1px solid #000;
    font-weight:600;
}


/* 리스트 공통 사항 */
.common-list{
    width:100%;
    margin-bottom:200px;
}
.common-list .list-wrap{
    width:100%;
    display:grid;
    grid-template-columns: repeat(4, calc(25% - 24px));
    grid-column-gap: 32px;
    grid-row-gap: 40px;
}
.common-list .list-wrap li{
    cursor: pointer;
}
.common-list .list-wrap li .img-area{
    width:100%;
    aspect-ratio: 1/1;
    overflow: hidden;
}
.common-list .list-wrap li .img-area img,
.common-list02 .list-wrap li .img-area img
{
    width:100%;
    height:100%;
    transition: 0.3s all;
    object-fit: covercommon-list mission-list;
}

.common-list02 .list-wrap li{
    display:flex;
    cursor: pointer;
}
.common-list02 .list-wrap li .img-area{
    overflow: hidden;
}

.common-list02 .list-wrap .text-area .write-info{
    display:flex;
    align-items: center;
    font-size:16px;
    color:#666;
    font-weight:300;
}
.common-list02 .list-wrap .text-area .write-info em{
    width:1px;
    height:12px;
    background-color:#888;
    margin:0 15px;
}



.common-img-area{
    overflow: hidden;
}

.common-img-area img{
    width:100%;
    height:100%;
    transition: 0.3s all;
}



@media (hover : hover) {
    .common-list .list-wrap li:hover .img-area img,
    .common-list02 .list-wrap li:hover .img-area img
    {
        transform: scale(1.1);
    }
    .common-img-area img:hover{
        transform: scale(1.1);
    }
}
@media screen and (max-width:1024px) {
    .common-list .list-wrap{
        grid-template-columns: repeat(3, calc(33.33% - 13.33px));
        grid-column-gap: 20px;
        grid-row-gap: 40px;
    }
}
@media screen and (max-width:768px) {
    .common-list{
        margin-bottom:60px;
    }
    .common-list .list-wrap{
        grid-template-columns: repeat(1, calc(100%));
        grid-column-gap: 0px;
        grid-row-gap: 30px;
    }
}








/*페이지네이션*/
.paging_box {
    width:100%;
    margin-top: 65px;
}

.paging {
    width: fit-content;
    margin: 0 auto;
    display: flex;
    align-items: center;
}

.paging_arrow {
    width: 11px;
    height: 19px;
    cursor: pointer;
}

.paging_arrow_prev {
    background: url(/images/icon/paging_arrow.svg) center center no-repeat;
    margin-right: 24px;
}

.paging_arrow_next {
    background: url(/images/icon/paging_arrow.svg) no-repeat;
    transform: rotate(180deg);
    margin-left: 24px;
}

.paging_number {
    display: flex;
    color: #666;
    font-size: 16px;
}

.paging_number li {
    margin-right: 24px;
    cursor: pointer;
}

.paging_number li:last-child {
    margin-right: 0;
}

.paging_number .on {
    color: #222;
    font-weight: 500;
    text-decoration: underline;
}

@media screen and (max-width:768px) {
    .paging_box {
        margin-top:60px;
    }
}




/* 강화유니버스 스와이퍼 */
.ganghwa-universe-swiper{
    height:700px;
}
.ganghwa-universe-swiper .swiper-slide{
    position:relative;
}
.ganghwa-universe-swiper .swiper-slide img{
    width:100%;
    height:100%;
    object-fit: cover;
}
.ganghwa-universe-swiper .text-area{
    position:absolute;
    top:50%;
    left:50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color:#fff;
}
.ganghwa-universe-swiper .text-area h4{
    font-family: 'tenada';
    font-size:60px;
    line-height:75px;
    margin-bottom:23px;
}
.ganghwa-universe-swiper .text-area h6{
    font-size:30px;
    line-height:42px;
    margin-bottom:23px;
    font-weight:500;
}
.ganghwa-universe-swiper .text-area p{
    font-size:24px;
    line-height:38px;
}
.ganghwa-universe-swiper .swiper-horizontal>.swiper-pagination-bullets,
.ganghwa-universe-swiper .swiper-pagination-bullets.swiper-pagination-horizontal,
.ganghwa-universe-swiper .swiper-pagination-custom,
.ganghwa-universe-swiper .swiper-pagination-fraction{
    bottom:65px;
}
.ganghwa-universe-swiper .swiper-pagination-bullet{
    width:9px;
    height:9px;
    background-color:#7a7a7a;
    border:1px solid #000;
    opacity: 1;
}
.ganghwa-universe-swiper  .swiper-pagination-bullet.swiper-pagination-bullet-active{
    background-color:#fff;
}

.ganghwa-universe-swiper .swiper-button-prev,
.ganghwa-universe-swiper .swiper-button-next{
    display:none;
}


@media screen and (max-width:768px) {
    .ganghwa-universe-swiper{
        height:600px;
    }
    .ganghwa-universe-swiper .text-area h4{
        font-size:30px;
        line-height:48px;
        margin-bottom:5px;
    }
    .ganghwa-universe-swiper .text-area h4 br{
        display:block;
    }
    .ganghwa-universe-swiper .text-area h6{
        font-size:20px;
        line-height:28px;
        margin-bottom:5px;
    }
    .ganghwa-universe-swiper .text-area p{
        font-size:16px;
        line-height:24px;
    }
    .ganghwa-universe-swiper .swiper-pagination-bullet{
        display:none;
    }
}



/* 강화유니버스 */
.vision-wrap{
    padding:150px 0;
    background:url(/images/Ganghwa-universe-02.jpg) center center no-repeat;
    background-size:cover;
}
.vision-wrap .text-area{
    width:984px;
    margin:0 auto;
    background:rgba(0, 0, 0, 0.2);
    padding:80px 20px;
    border-radius: 20px;
}
.vision-wrap .text-area h4{
    font-size:50px;
    font-family:'tenada';
    text-align: center;
    color:#fff;
    margin-bottom:40px;
    text-shadow: 3px 3px 0 #000;
}
.vision-wrap .text-area p{
    text-align: center;
    font-size:22px;
    line-height:34px;
    margin-bottom:15px;
    color:#fff;
    font-weight:500;
}

@media screen and (max-width:1024px) {
    .vision-wrap .text-area{
        width:calc(100% - 40px);
    }
    .vision-wrap .text-area p br{
        display:none;
    }
}
@media screen and (max-width:768px) {
    .vision-wrap{
        padding:60px 0;
    }
    .vision-wrap .text-area{
        padding:45px 20px;
    }
    .vision-wrap .text-area h4{
        font-size:35px;
        margin-bottom:20px;
    }
    .vision-wrap .text-area p{
        font-size:16px;
        line-height:24px;
    }
}


.work-wrap{
    padding-top:150px;
}
.work-list{
    width:926px;
    margin:0 auto 65px auto;
}
.work-wrap .contents{
    padding-bottom:150px;
    border-bottom:1px solid #333;
}
.work-title{
    font-family: 'tenada';
    font-size:50px;
    color:#222;
    text-align: center;
    margin-bottom:50px;
}
.work-list ul {
    width:100%;
    display:grid;
    grid-template-columns: repeat(2, calc(50% - 20px));
    grid-column-gap: 40px;
    grid-row-gap: 40px;
}
.work-list ul li{
    position:relative;
    border:1px solid #000;
    overflow: hidden;
    width:100%;
    aspect-ratio: 1/1.22;
}
.work-list ul li img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition: 0.3s all;
}
.work-list ul li .text-area{
    position:absolute;
    top:0;
    left:0;
    z-index: 9;
    width:100%;
    height:100%;
    padding:35px 40px;
}
.work-list ul li .text-area h6{
    font-size:23px;
    line-height:32px;
    font-weight:600;
    margin-bottom:20px;
    height:64px;
    color:#222;
}
.work-list ul li .text-area .explain-area{
    height:calc(100% - 100px);
    overflow-y: scroll;
    padding-right:20px;
}
.work-list ul li .text-area .explain-area::-webkit-scrollbar{
    width:7px;
}
.work-list ul li .text-area .explain-area::-webkit-scrollbar-thumb{
    width:7px;
    border-radius: 10px;
    background-color:#555;
}
.work-list ul li .text-area .explain-area::-webkit-scrollbar-track{
    display:none;
}
.work-list ul li .text-area .explain-area p{
    font-size:18px;
    line-height:30px;
    color:#222;
}
@media (hover : hover) {
    .work-list ul li:hover img{
        transform: scale(1.1);
    }
    .work-list ul li .text-area{
        padding:30px 20px;
    }
}

@media screen and (max-width:1024px) {
    .work-list{
        width:100%;
    }
    .work-list ul {
        grid-template-columns: repeat(2, calc(50% - 10px));
        grid-column-gap: 20px;
        grid-row-gap: 20px;
    }

}
@media screen and (max-width:768px) {
    .work-wrap{
        padding-top:60px;
    }
    .work-list ul {
        grid-template-columns: repeat(1, calc(100%));
        grid-column-gap: 0px;
        grid-row-gap: 10px;
    }
    .work-title{
        margin-bottom:30px;
        font-size:35px;
    }
    .work-list{
        margin-bottom:40px;
    }
    .work-wrap .contents{
        padding-bottom:60px;
    }
    .work-list ul li .text-area h6{
        height:unset;
        font-size:20px;
        line-height:30px;
        color:#222;
        margin-bottom:15px;
    }
}

.keyword-wrap{
    padding:150px 0 200px 0;
}
.keyword-title {
    margin-bottom:65px;
}
.keyword-title h4{
    font-family: 'tenada';
    font-size:50px;
    color:#222;
    text-align: center;
    margin-bottom:40px;
}
.keyword-title p{
    font-size:28px;
    color:#222;
    text-align: center;
}
.keyword-list ul{
    width:988px;
    margin:0 auto;
    border:1px solid #000;
}
.keyword-list ul li{
    width:100%;
    padding:20px 20px;
    text-align: center;
    border-bottom:1px solid #000;
}
.keyword-list ul li:last-child{
    border-bottom:unset;
}
.keyword-list ul li h6{
    font-size:20px;
    color:#007BD0;
    font-weight:700;
    margin-bottom:12px;
    display:flex;
    align-items: center;
    justify-content: center;
}
.keyword-list ul li h6 img{
    margin-right:8px;
}
.keyword-list ul li p{
    font-size:18px;
    color:#333;
}
.Keyword-img04{
    width:25px;
    height:25px;
}
.Keyword-img06{
    width:21px;
    height:21px;
}
.Keyword-img08{
    width:27px;
    height:27px;
}
@media screen and (max-width:1024px) {
    .keyword-list ul{
        width:100%;
    }
}

@media screen and (max-width:768px) {
    .keyword-wrap{
        padding:60px 0;
    }
    .keyword-title h4{
        font-size:35px;
        margin-bottom:25px;
    }
    .keyword-title p{
        font-size:22px;
        line-height:30px;
        padding:0 20px;
    }
    .keyword-title{
        margin-bottom:35px;
    }
    .keyword-list ul li p{
        font-size:16px;
        line-height:22px;
    }
}




/* 프로젝트 리스트 */
.top-text-box {
    width: 100%; margin-bottom: 40px; text-align: center; border-top: 1px solid #222222; border-bottom: 1px solid #CCCCCC;
}
.top-text {
    padding: 30px 0; font-size: 28px; font-weight: 500; color: #007BD0;
}
.project-list .img-area{
    margin-bottom:15px;
}
.project-list .text-area h4{
    font-size:20px;
    color:#333;
    font-weight:600;
    margin-bottom:8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.project-list .text-area p{
    font-size:16px;
    color:#007BD0;
}




/* 프로젝트 상세 */
.project-view {
    margin-bottom:200px;
}
.project-view .view-top{
    border-bottom:1px solid #000;
}
.project-view .view-top h4{
    font-size:32px;
    line-height:40px;
    color:#222;
    font-weight:600;
    margin-bottom:6px;
}
.project-view .view-top p{
    color:#666;
    margin-bottom:20px;
}
.project-view .view-con{
    padding:45px 0 60px 0;
    border-bottom:1px solid #888;
    width:100%;
    line-height:1.5;
    color:#333;
    white-space: inherit;
}
.project-view .view-con pre{
    white-space: inherit;
}
.project-view .view-con img{
    max-width:50%;
    height:unset;
}
.project-view .view-con pre{
    line-height:1.5;
    color:#333;
}
.project-view .view-con p{
    line-height:1.5;
    color:#333;
}
.project-view .view-file{
    border-bottom:1px solid #888;
    padding: 30px 0;
    display:flex;
    align-items: center;
    margin-bottom:50px;
}
.project-view .view-file h5{
    font-weight:700;
    color:#222;
    width:80px;
    font-size:16px;
}
.project-view .view-file ul {
    padding-left:30px;
    border-left:1px solid #666;
}
.project-view .view-file ul li{
    display:flex;
    align-items: center;
    cursor: pointer;
    margin-bottom:5px;
}
.project-view .view-file ul li p{
    margin-right:15px;
}
@media (hover : hover) {
    .project-view .view-file ul li:hover{
        text-decoration: underline;
    }
}
@media screen and (max-width:768px) {
    .project-view{
        margin-bottom:80px;
    }
    .project-view .view-top h4{
        font-size:21px;
        line-height:30px;
    }
    .project-view .view-con{
        padding:35px 0 40px 0;
    }
    .project-view .view-con img{
        max-width: unset;
        width:100%;
    }
    .project-view .view-file{
        align-items: flex-start;
        flex-direction: column;
    }
    .project-view .view-file h5{
        margin-bottom:15px;
    }
    .project-view .view-file ul{
        border-left:none;
        padding-left:0;
    }
    .project-view .view-file ul li{
        margin-bottom:10px;
        display:flex;
        justify-content: space-between;
    }
}




/* 잠시섬 - 배너 */
.main-banner-swiper02{
    height:700px;
}
.main-banner-swiper02 .swiper-slide{
    height:700px;
}
.main-banner-swiper02 .text-area p{
    margin-bottom:0px;
}
.main-banner-swiper02 .text-area button{
    display:none;
}
@media screen and (max-width:768px) {
    .main-banner-swiper02{
        height:600px;
    }
    .main-banner-swiper02 .swiper-slide{
        height:600px;
    }
    .main-banner-swiper02 .text-area{
        padding-top:10px;
    }
}



/* 잠시섬 - 특별한 프로그램*/
.special-program{
    padding:150px 0;
    background:url(/images/special-program-bg.jpg) center center no-repeat;
    background-size:cover;
}
.special-program-contents{
    width:914px;
    margin:0 auto;
}
.special-program-title{
    font-family: 'tenada';
    color:#222;
    font-weight:700;
    font-size:50px;
    text-align: center;
    margin-bottom:50px;
}
.special-program-title br{
    display:none;
}
.special-program-list ul{
    width:100%;
    display:grid;
    grid-template-columns: repeat(2, calc(50% - 15px));
    grid-column-gap: 30px;
    grid-row-gap: 30px;
}
.special-program-list ul li{
    position:relative;
    width:100%;
    height:unset;
    aspect-ratio: 1/1.22;
    overflow: hidden;
    border:1.5px solid #000;
}
.special-program-list ul li img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition: 0.3s all;
}
.special-program-list ul li .text-area{
    position:absolute;
    top:40px;
    left:40px;
    padding-right:40px;
    height:100%;
}
.special-program-list ul li .text-area h6{
    font-size:23px;
    line-height:30px;
    color:#fff;
    font-weight:700;
    margin-bottom:10px;
}
.special-program-list ul li .text-area .explain-area{
    max-height: 418px;
    overflow-y: scroll;
    padding-right:10px;
}
.special-program-list ul li .text-area .explain-area::-webkit-scrollbar{
    width:7px;
}
.special-program-list ul li .text-area .explain-area::-webkit-scrollbar-thumb{
    width:7px;
    background-color:#aaa;
    border-radius: 10px;
}
.special-program-list ul li .text-area .explain-area::-webkit-scrollbar-track{
    display:none;
}
.special-program-list ul li .text-area .explain-area p{
    font-size:18px;
    line-height:30px;
    color:#fff;
    font-weight:300;
}

@media screen and (max-width:1024px) {
    .special-program-contents{
        width:calc(100% - 40px);
    }
    .special-program-list ul li .text-area .explain-area{
        max-height:calc(100% - 100px);
    }
}
@media screen and (max-width:768px) {
    .special-program{
        padding:50px 0 60px 0;
    }
    .special-program-title{
        font-size:35px;
        line-height:40px;
        margin-bottom:30px;
    }
    .special-program-title br{
        display:block;
    }
    .special-program-list ul{
        grid-template-columns: repeat(1, calc(100%));
        grid-column-gap: 0px;
        grid-row-gap: 10px;
    }
    .special-program-list ul li{
        aspect-ratio: 1/1.4;
    }
    .special-program-list ul li .text-area{
        top:20px;
        left:20px;
        padding-right:10px;
    }
    .special-program-list ul li .text-area .explain-area p{
        font-size:18px;
        line-height:28px;
    }
}



/* 잠시섬 - 프로그램 캘린더*/
.program-calender-wrap{
    padding:150px 0 180px 0;
    background-color:#ededed;
}
.program-calender-title{
    font-family: 'tenada';
    color:#222;
    font-weight:700;
    font-size:50px;
    text-align: center;
    margin-bottom:50px;
}

@media screen and (max-width:768px) {
    .program-calender-wrap{
        padding:50px 0 60px 0;
    }
    .program-calender-title{
        font-size:35px;
        margin-bottom:20px;
    }
}


/*달력*/
.big_calender_wrap{
    /* box-shadow: 2px 2px 12px 0px rgba(0, 0, 0, 0.20); */
    border:1px solid #000;
    box-shadow: unset;
    margin:0;
}
.big_calender_wrap.on{
    box-shadow: unset;
    margin:0;
}
.big_calender_drop_box {
    padding: 25px 45px;
    display: flex;
    justify-content: space-between;
    background: #FFC960;
    color: #222;
    border-bottom:1px solid #000;
}
.big_calender_drop_title {
    font-size: 18px;
}
.big_calender_drop {
    display: flex !important;
    align-items: center;
    cursor: pointer;
}
.big_calender_drop .big_calender_drop_icon {
    width:14px; height: 10px;
    background: url(/images/icon/big_calender_drop_icon03.svg) center center no-repeat;
    transform: rotate(-180deg);
    margin-left: 8px;
}
.big_calender_drop .noshow{
    display:block;
}
.big_calender_drop .show{
    display:none;
}
.big_calender_drop.on .noshow{
    display:none;
}
.big_calender_drop.on .show{
    display:block;
}
.big_calender_drop.on .big_calender_drop_icon{
    transform: unset;
}
.big_calender.on {
    padding: 60px 40px;
    box-sizing: border-box;
    background: #fff;
    display:block;
}
.big_calender{
    display:none;
}
.big_calender_title_box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 66px;
}
.big_calender_title {
    display: flex;
    align-items: center;
    color: #222;
    font-size: 36px;
    font-weight: 800;
}
.big_calender_arrow_box {
    display: flex;
    margin-left: 36px;
}

.big_calender_prev {
    cursor: pointer;
    width: 34px; height: 33px;
    background: url(/images/icon/big_calender_prev.svg);
}
.big_calender_next {
    cursor: pointer;
    width: 34px; height: 33px;
    background: url(/images/icon/big_calender_prev.svg);
    transform: rotate(180deg);
    margin-left: 10px;
}
.calender_circle_wrap {
    display: flex;
}
.calender_circle_box {
    display: flex;
    align-items: center;
}
.calender_circle_blue_box {
    color: #222;
    margin-right: 14px;
}
.calender_circle_blue {
    min-width: 6px;
    height: 6px;
    background: #056FB8;
    border-radius: 50%;
    margin-right: 5px;
}
.calender_circle_1,
.calender_circle_2,
.calender_circle_3,
.calender_circle_4,
.calender_circle_5,
.calender_circle_6{
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 5px;
}
.calender_circle_1 {
    background-color:#E83929;
}
.calender_circle_2 {
    background-color:#009944;
}
.calender_circle_3 {
    background-color:#056FB8;
}
.calender_circle_4 {
    background-color:#E66907;
}
.calender_circle_5 {
    background-color:#FFC960;
}
.calender_circle_6 {
    background-color:#766EDF;
}
.calender_circle_gray_box {
    color: #ccc;
}
.calender_circle_gray {
    min-width: 6px;
    height: 6px;
    background: #aaa;
    border-radius: 50%;
    margin-right: 5px;
}
.big_calender_week {
    display: grid;
    grid-template-columns: repeat(7, calc(14.2% - 42px));
    grid-column-gap: 49px;
    margin-bottom: 22px;
}
.big_calender_week .sun{
    color: #E74133;
}
.big_calender_day_box {
    display: grid;
    grid-template-columns: repeat(7, calc(14.285% - 34.285px));
    grid-column-gap: 40px;
    grid-row-gap: 30px;
}
.big_calender_day {
    border-top: 2px solid #222;
    padding-top: 15px;
}
.big_calender_sun {
    border-top: 2px solid #E74133;
}
.big_calender_day_number {
    color: #222;
    font-size: 28px;
    margin-bottom: 15px;
}

.calender_circle_box_contents .calender_circle_box {

    margin-right: 0;
    margin-bottom: 8px;
}
.big_calender_day_box .big_calender_day{
    min-height:120px;
}
.calender_circle_box_contents .calender_circle_box span{
    font-size:15px;
    width:calc(100% - 11px);
}
@media screen and (max-width:1024px) {
    .big_calender_drop_box {
        padding: 25px 20px;
    }
    .big_calender {
        padding: 20px;
    }
    .big_calender_week {
        display: grid;
        grid-template-columns: repeat(7, calc(14.2% - 8.5px));
        grid-column-gap: 10px;
    }
    .big_calender_day_box {
        display: grid;
        grid-template-columns: repeat(7, calc(14.2% - 8.5px));
        grid-column-gap: 10px;
        grid-row-gap: 20px;
    }
    .big_calender.on{
        padding:40px 20px;
    }
    .big_calender_title_box{
        margin-bottom:40px;
    }
}

@media screen and (max-width:768px) {
    .big_calender_title_box {
        flex-wrap: wrap;
    }
    .big_calender_arrow_box {
        margin-left: 20px;
    }
    .calender_circle_wrap {
        margin-top: 20px;
    }
    .big_calender_title {
        font-size: 24px;
        width:100%;
        justify-content: space-between;
    }
    .big_calender_drop_box{
        padding:30px 20px;
        flex-wrap: wrap;
    }
    .big_calender_drop {
        display:none !important;
        margin-top:20px;
        border:1px solid #555;
        height:40px;
        display:flex;
        align-items: center;
        justify-content: center;
        width: 100%;
    }
    .big_calender.on{
        padding: 30px 20px;
    }
    .big_calender_day_box{
        display:flex;
        width:100%;
        overflow-x: scroll;
        padding-bottom:20px;
    }
    .big_calender_day_box .big_calender_day {
        margin-right:20px;
        min-height:100px;
    }
    .calender_circle_box_contents{
        float: unset;
    }
    .big_calender_week{
        display:none;
    }
    .big_calender_day_number{
        font-size:18px;
    }
    .big_calender_day_box .big_calender_day .calender_circle_box_contents span{
        white-space :nowrap;
        font-size:14px;
    }
    .calender_circle_gray{
        min-width: unset;
        width:4px;
        height:4px;
    }
    .calender_circle_1,
    .calender_circle_2,
    .calender_circle_3,
    .calender_circle_4,
    .calender_circle_5,
    .calender_circle_6{
        min-width: unset;
        width:4px;
        height:4px;
    }
    .calender_circle_box_contents .calender_circle_box{
        justify-content: unset;
    }
    .big_calender_drop_title{
        font-size:16px;
        line-height:24px;
        text-align: center;
    }
}
/*달력 끝 끝*/



/* 잠시섬 - 매일매일 영감모임 */
.every-meeting-wrap{
    padding:150px 0 120px 0;
}
.every-meeting-title{
    margin-bottom:45px;
    display:flex;
    align-items: center;
    justify-content: space-between;
}
.every-meeting-title h4{
    font-family: 'tenada';
    color:#222;
    font-weight:700;
    font-size:50px;
}
.every-meeting-list {
    width:100%;
    overflow: hidden;
}
.every-meeting-list ul{
    display:flex;
    justify-content: flex-start;
    align-items: center;
}
.every-meeting-list ul li{
    width:calc(25% - 22.5px);
    margin-right:30px;
    border:1px solid #000;
    cursor: pointer;
}
.every-meeting-list ul li:last-child{
    margin-right:0;
}
.every-meeting-list ul li .img-area{
    width:100%;
    aspect-ratio: 1/1;
    overflow: hidden;
}
.every-meeting-list ul li .img-area img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition: 0.3s all;
}
@media (hover : hover) {
    .every-meeting-list ul li .img-area img:hover{
        transform: scale(1.1);
    }
}
.every-meeting-list ul li .text-area{
    padding:20px 30px;
    border-top:1px solid #000;
}
.every-meeting-list ul li .text-area p{
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-word;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    font-size:21px;
    line-height:32px;
    color:#222;
    font-weight:500;
    height:64px;
}
@media screen and (max-width:1024px) {
    .every-meeting-list {
        padding-bottom:20px;
        overflow-x: scroll;
    }
    .every-meeting-list ul{
        width:fit-content;
    }
    .every-meeting-list ul li{
        width:328px;
    }
}
@media screen and (max-width:768px) {
    .every-meeting-wrap{
        padding: 60px 0;
    }
    .every-meeting-list ul li{
        width:250px;
        margin-right:20px;
    }
    .every-meeting-title {
        margin-bottom:35px;
        justify-content: center;
    }
    .every-meeting-title h4{
        font-size:35px;
    }
    .every-meeting-title span{
        display:none;
    }
    .every-meeting-list ul li .text-area{
        padding:15px 20px;
    }
    .every-meeting-list ul li .text-area p{
        font-size:18px;
        line-height:24px;
        height:48px;
    }
}




/* 잠시섬 이용후기 */
.jamsisum-review-wrap{
    margin-bottom:200px;
}
.jamsisum-review-title{
    font-family: 'tenada';
    color:#222;
    font-weight:700;
    font-size:50px;
    margin-bottom:50px;
    margin-bottom:40px;
}
.jamsisum-review-wrap .tab{
    margin-bottom:30px;
}
.jamsisum-review-wrap .tab ul{
    justify-content: flex-start;
    width: 100%;
    overflow-x: scroll;
    padding-bottom: 10px;
}
.jamsisum-review-wrap .tab ul::-webkit-scrollbar{
    display:block;
    width:7px;
    height:10px;
}
.jamsisum-review-wrap .tab ul::-webkit-scrollbar-thumb{
    border-radius: 10px;
    background-color:#888;
    height:5px;
}
.jamsisum-review-wrap .tab ul::-webkit-scrollbar-track{
    display:none;
}
.review-list{
    border-top:2px solid #000;
}
.review-list ul li{
    padding:20px 15px;
    display:flex;
    border-bottom:1px solid #ccc;
    cursor: pointer;
}
.review-list ul li .img-area{
    width:192px;
    aspect-ratio: 1/1;
    overflow: hidden;
    margin-right:50px;
}
.review-list ul li .img-area img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition: 0.3s all;
}
.review-list ul li .text-area{
    padding-top:10px;
    width:calc(100% - 192px - 50px);
}
.review-list ul li .text-area h5{
    font-size:21px;
    color:#333;
    margin-bottom:22px;
    font-weight:600;
}
.review-list ul li .text-area h6{
    font-size:18px;
    line-height:28px;
    height:56px;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-word;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-bottom:60px;
    color:#444;
    font-weight:400;
}
.review-list ul li .text-area .name-date{
    display:flex;
    align-items: center;
}
.review-list ul li .text-area .name-date span{
    font-size:16px;
    color:#666;
}
.review-list ul li .text-area .name-date em{
    width:1px;
    height:12px;
    background-color:#888;
    margin:0 12px;
}
.review-list ul li .text-area .name-date p{
    font-size:16px;
    color:#666;
}
@media (hover : hover) {
    .review-list ul li:hover .img-area img{
        transform: scale(1.1);
    }
}
@media screen and (max-width:768px) {
    .jamsisum-review-wrap{
        margin-bottom:80px;
    }
    .jamsisum-review-title{
        font-size:35px;
        margin-bottom:25px;
        text-align: center;
    }
    .review-list ul {
        padding-top:10px;
    }
    .review-list ul li {
        flex-direction: column;
        padding:20px 0;
    }
    .review-list ul li .img-area{
        width:100%;
        margin-bottom:15px;
    }
    .review-list ul li .text-area{
        width:100%;
        padding-top:0;
    }
    .review-list ul li .text-area h5{
        font-size:20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom:10px;
    }
    .review-list ul li .text-area h6{
        display:none;
    }
}


/* 잠시섬의 숙소를 예약 배너 + 리스트*/
.reservation-banner{
    height:478px;
    background:url(/images/reservation-bg.jpg) center center no-repeat;
    background-size:cover;
}
.reservation-banner .text-area {
    padding-top:110px;
}
.reservation-banner .text-area h4{
    font-size:50px;
    font-weight:700;
    color:#fff;
    margin-bottom:30px;
}
.reservation-banner .text-area p{
    font-size:22px;
    color:#fff;
    font-weight:300;
}
.reservation-calender-wrap{
    transform: translateY(-150px);
}
.stay-list-wrap{
    transform: translateY(-20px);
    width:100%;
    display:grid;
    grid-template-columns: repeat(2, calc(50% - 25px));
    grid-column-gap: 50px;
    grid-row-gap: 80px;
    margin-bottom:110px;
}
.stay-list .swiper {
    margin-bottom:30px;
    border:1px solid #ccc;
}
.stay-list .swiper .swiper-slide{
    width:100%;
    aspect-ratio: 1/0.666;
    overflow: hidden;
}
.stay-list .swiper .swiper-slide img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition: 0.3s all;
}
.stay-list .swiper-pagination {
    text-align: right;
    padding-right:20px;
    padding-bottom:8px;
}
.stay-list .swiper-pagination-bullet{
    width:9px;
    height:9px;
    border:1px solid #ccc;
    background-color:#f9f9f9;
}
.stay-list .swiper-pagination-bullet-active{
    background-color:#fff;
}
.stay-list .text-area{
    cursor: pointer;
}
.stay-list .text-area h4{
    font-size:28px;
    font-weight:700;
    color:#222;
    margin-bottom:20px;
}
.stay-list .text-area p{
    font-size:18px;
    line-height:28px;
    color:#444;
    font-weight:300;
    margin-bottom:30px;
}
.price-wrap{
    display:flex;
    align-items: center;
    justify-content: space-between;
}
.price-wrap .black-price{
    display:flex;
    align-items: center;
}
.price-wrap .red-price{
    display:flex;
    align-items: center;
}
.price-wrap .black-price span{
    padding:4px 11px;
    color:#333;
    border:1px solid #333;
    font-size:14px;
    text-align: center;
    margin-right:12px;
    border-radius: 5px;
}
.price-wrap .black-price h5{
    font-size:22px;
    font-weight:600;
    color:#222;
}
.price-wrap .red-price span {
    padding:4px 11px;
    color:#E83929;
    border:1px solid #E83929;
    font-size:14px;
    text-align: center;
    margin-right:12px;
    border-radius: 5px;
}

.price-wrap .red-price h5{
    font-size:22px;
    font-weight:600;
    color:#E83929;
}
@media (hover : hover) {
    .stay-list .swiper .swiper-slide img:hover{
        transform: scale(1.1);
    }
}
@media screen and (max-width:1400px) {
    .price-wrap{
        flex-direction: column;
        align-items: flex-start;
    }
    .price-wrap .black-price{
        margin-right:0;
        margin-bottom:20px;
    }
    .reserve_view_wrap .price-wrap .black-price span,
    .reserve_view_wrap .price-wrap .red-price span{
        width:110px;
    }
    .reserve_view_wrap .price-wrap .black-price{
        margin-bottom:10px;
    }
}
@media screen and (max-width:1024px) {

    .price-wrap .black-price{
        margin-bottom:10px;
    }

}
@media screen and (max-width:768px) {
    .reservation-banner{
        height:280px;
    }
    .reservation-banner .text-area{
        padding-top:60px;
    }
    .reservation-banner .text-area h4{
        font-size:36px;
        text-align: center;
        word-break: keep-all;
    }
    .reservation-banner .text-area p{
        font-size:16px;
        text-align: center;
    }
    .reservation-calender-wrap{
        transform: translateY(-70px);
    }
    .stay-list-wrap{
        grid-template-columns: repeat(1, calc(100%));
        grid-column-gap: 0px;
        grid-row-gap: 50px;
        margin-bottom: 60px;
    }
    .stay-list .swiper{
        margin-bottom:15px;
    }
    .stay-list .text-area h4{
        font-size:24px;
        line-height:1.4;
        margin-bottom:20px;
    }
    .stay-list .text-area p{
        display:none;
        font-size:16px;
        line-height:1.5;
    }
    .price-wrap .black-price h5{
        font-size:18px;
    }
    .price-wrap .red-price h5{
        font-size:18px;
    }
    .price-wrap .black-price span,
    .price-wrap .red-price span{
        width:120px;
        padding:0;
        line-height:28px;
    }
    .price-wrap .black-price{
        margin-bottom:15px;
    }
    .price-wrap .black-price,
    .price-wrap .red-price{
        margin-right:0;
    }

}




/* 숙소 예약 상세  */
.reserve_view_wrap{
    padding-top:110px;
    padding-bottom:200px;
}
.h4-title{
    display:flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom:60px;
}
.h4-title h4 {
    font-family: 'tenada';
    color:#222;
    font-weight:700;
    font-size:50px;
}
.step_box {
    display: flex;
    height: 54px;
}
.step_by_step {
    display: flex;
    align-items: center;
}
.step_by_step_circle {
    width: 17px;
    height: 17px;
    border-radius: 50%;
    border: 1px solid #222;
}
.step_by_step_line {
    width: 22px;
    height: 1px;
    background: #222;
}
.step_by_step .on {
    background: #056FB8;
}
.step_by_step .go {
    display: block;
}
.reserve_view_top{
    display:flex;
    margin-bottom:80px;
}
.reserve_view_top .text_area{
    margin-left:85px;
    width:42%;
}
.reserve_view_top .text_area .cat01{
    font-size:18px;
    color:#E83929;
    margin-bottom:30px;
    display:block;
}
.reserve_view_top .text_area h4{
    font-size:30px;
    font-weight:700;
    color:#222;
    line-height:40px;
    margin-bottom:45px;
}
.reserve_view_top .text_area p{
    font-size:18px;
    color:#333;
    line-height:32px;
    margin-bottom:35px;
}
.reserve_view_top .text_area .price_area{
    display:flex;
    align-items: center;
    margin-bottom:40px;
}
.reserve_view_top .text_area .price_area .b_color{
    color:#222;
    font-size:20px;
}
.reserve_view_top .text_area .price_area .b_color strong{
    font-weight:600;
}
.reserve_view_top .text_area .price_area .r_color{
    color:#E83929;
    margin-left:30px;
    font-weight:600;
    font-size:20px;
}
.reserve_view_top .text_area .price_area .r_color span{
    display: inline-block;
    width:100px;
    height:25px;
    line-height:25px;
    color:#E83929;
    border:1px solid #E83929;
    border-radius: 4px;
    margin-right:10px;
    font-size:14px;
    text-align: center;
}
.reserve_view_top .text_area .box_area{
    border:1px dashed #7d7d7d;
    padding:15px 20px;
    color:#222;
    display:block;
    align-items: center;
    justify-content: center;
    margin-top:40px;
    margin-bottom:40px;
}
.reserve_view_top .text_area .box_area strong{
    font-weight:600;
}
.reserve_view_top .text_area .btn_area{
    display:flex;
    align-items: center;
    justify-content: space-between;
}
.reserve_view_top .text_area .btn_area button{
    color:#fff;
    font-size:18px;
    background-color:#222;
    font-weight:600;
    border:none;
    cursor: pointer;
    width:100%;
    height:unset;
    padding:10px 10px;
    line-height:1.3;
    box-sizing: border-box;
    border:1px solid #222;
}
.reserve_view_swiper {
    width:715px;
    height:480px;
    position:relative;
    background-color:#eaeaea;
    overflow: hidden;
}
.reserve_view_swiper .swiper-button-next,
.reserve_view_swiper .swiper-button-prev
{
    width:36px;
    height:36px;
    border-radius: 50%;
    overflow: hidden;
}
.reserve_view_swiper .swiper-button-next:after,
.reserve_view_swiper .swiper-button-prev:after
{
    content:'';
    width:100%;
    height:100%;
    border:1px solid #b1b1b1;
    border-radius: 50%;
    overflow: hidden;
}
.reserve_view_swiper .swiper-button-next:after{
    background:url(/images/icon/reserve_view_next.svg) center center no-repeat #e7e7e7;
}
.reserve_view_swiper .swiper-button-prev:after{
    background:url(/images/icon/reserve_view_prev.svg) center center no-repeat #e7e7e7;
}
.reserve_view_swiper img{
    width:100%;
    height:100%;
    object-fit:cover;
}
.reserve_view_wrap .stay_info_txt_list_box {
    padding: 30px 35px;
    border: 1px solid #aaa;
    margin-bottom: 55px;
}
.lodging_info{
    padding:30px 0px;
    border-bottom:1px solid #444;
}
.lodging_info:first-child   {
    border-top:1px solid #444;
}
.lodging_info .title{
    cursor: pointer;
    display:flex;
}
.lodging_info .title h3{
    font-size:22px;
    color:#222;
    font-weight:600;
}
.lodging_info .title img{
    margin-left:10px;
}
.lodging_info .accordion_item{
    padding:25px 25px;
    background-color:#F5F5F5;
    margin-top:20px;
}
.lodging_info .list_wrap {
    margin-bottom:30px;
}
.lodging_info .list_wrap:last-child{
    margin-bottom:0;
}
.lodging_info .list_wrap h5{
    margin-bottom:15px;
    color:#333;
    font-size:20px;
    font-weight:600;
}
.lodging_info .list_wrap div{
    display:flex;
    flex-direction: column;
}
.lodging_info .list_wrap div ul{
    margin-bottom:15px;
    margin-right:0;
    width:100%;
}
.list_style01 li{
    position:relative;
    padding-left:8px;
    color:#333;
    margin-bottom:15px;
}
.list_style01 li:last-child{
    margin-bottom:0;
}
.list_style01 li:before{
    content:'';
    position:absolute;
    top:8px;
    left:0;
    width:3px;
    height:3px;
    border-radius: 50%;
    background-color:#333;
}
.list_style01.list-style02 li{
    line-height:2.0;
}
.list_style01.list-style02 li::before{
    top:14px;
}
.lodging_info .accordion_item{
    display:none;
    transition: all 0.3s;
    overflow: hidden;
    word-break: break-all;
}
.lodging_info .accordion_item.active{
    display:block;
}
.lodging_info .accordion_item pre{ 
    white-space: pre-wrap;
}
.stay_info_txt_list {
    display: flex;
    color: #333;
    margin-bottom: 15px;
}

.stay_info_txt_list > div{
    display:flex;
    align-items: center;
}
.stay_info_txt_list:last-child {
    margin-bottom: 0;
}

.stay_info_txt_list_bold {
    min-width: 58px;
    color: #222;
    font-weight: 700;
    margin: 0 25px 0 7px;
}
.reserve_view_wrap .price-wrap .black-price,
.reserve_view_wrap .price-wrap .red-price{
    width:100%;
}
@media (hover : hover) {
    .reserve_view_top .text_area .btn_area button:hover{
        background-color:#fff;
        color:#222;

    }
    .reserve_view_swiper .swiper-button-next:hover:after{
        background:url(/images/icon/reserve_view_next_on.svg) center center no-repeat #222;
        border:none;
    }
    .reserve_view_swiper .swiper-button-prev:hover:after{
        background:url(/images/icon/reserve_view_prev_on.svg) center center no-repeat #222;
        border:none;
    }
}




@media screen and (max-width:1024px) {
    .reserve_view_top {
        flex-direction: column;
    }
    .reserve_view_top .reserve_view_swiper{
        width:100%;
        margin-bottom:40px;
    }
    .reserve_view_top .text_area{
        width:100%;
        margin-left:0px;
    }
    .lodging{
        padding:30px 15px;
    }
    .reserve_view_top .text_area .price_area {
        align-items: flex-start;
        margin-bottom: 30px;
        flex-direction: column;
    }
    .reserve_view_top .text_area .price_area .b_color{
        margin-bottom:10px;
    }
    .reserve_view_top .text_area .price_area .r_color{
        margin:0;
    }

}


@media screen and (max-width:768px) {
    .reserve_view_wrap{
        padding-top:60px;
        padding-bottom:100px;
    }
    .reserve_view_top{
        margin-bottom:60px;
    }
    .h4-title{
        flex-direction: column;
        align-items: flex-start;
        margin-bottom:30px;
    }
    .h4-title h4{
        font-size:32px;
    }
    .reserve_view_top .reserve_view_swiper{
        width:100%;
        height:unset;
        aspect-ratio: 1/1;
        margin-bottom:20px;
    }
    .reserve_view_top .text_area .cat01{
        margin-bottom:10px;
    }
    .reserve_view_top .text_area h4{
        font-size:22px;
        margin-bottom:20px;
    }
    .reserve_view_top .text_area p{
        font-size:16px;
        line-height:1.5;
        margin-bottom:20px;
    }
    .reserve_view_top .text_area .box_area{
        padding:20px 15px;
        margin-bottom:20px;
    }
    .reserve_view_wrap .stay_info_txt_list_box{
        padding:20px 15px;
    }
    .reserve_view_wrap .price-wrap .black-price,
    .reserve_view_wrap .price-wrap .red-price{
        margin-right:0;
    }
    .reserve_view_wrap .price-wrap .black-price{
        margin-bottom:15px;
    }
    .reserve_view_wrap .price-wrap .black-price span,
    .reserve_view_wrap .price-wrap .red-price span{
        width:120px;
        margin-right:12px;
    }
    .stay_info_txt_list {
        flex-direction: column;
        margin-bottom:20px;
    }
    .stay_info_txt_list > div{
        margin-bottom:10px;
    }
    .lodging_info .title h3{
        font-size:20px;
    }
    .lodging_info{
        padding:20px 0;
    }

    .lodging_info .accordion_item{
        padding:20px 15px;
    }
    .lodging_info .list_wrap div{
        display:flex;
    }
    .lodging_info .list_wrap div ul:first-child{
        margin-left:00px;
    }
    .reserve_view_swiper .swiper-button-prev,
    .reserve_view_swiper .swiper-button-next
    {
        display:none;
    }
}


/* 숙소 예약 상세 1단계 */
.stay_info_box {
    display: flex;
    padding: 50px 0;
    border-bottom: 1px solid #000;
    border-top: 1px solid #000;
}
.stay_info_img  {
    width:280px;
    height:220px;
    margin-right: 50px;
}
.stay_info_img img {
    width: 100%; height: 100%;
    object-fit: cover;
}
.stay_info_txt_box {
    width:calc(100% - 280px - 50px);
    display: flex;
    justify-content: space-between;
}
.stay_info_name {
    color: #E83929;
    font-size: 18px;
    margin-bottom:15px;
}
.stay_info_name02{
    color: #05A54B;
    font-size: 18px;
    margin-bottom:15px;
}
.stay_info_name_green{
    color: #05A54B;
}
.stay_info_name_blue{
    color: #056FB8;
}
.stay_info_room {
    display: flex;
    margin-bottom:30px;
}
.stay_info_room_txt {
    color: #222;
    font-size: 26px;
    font-weight: 700;

}
.stay_info_room_people {
    border-radius: 4px;
    background: #056FB8;
    color: #FFF;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    height:fit-content;
    width:fit-content;
}
.stay_info_btn {
    padding:8px 20px;
    border-radius: 4px;
    border: 1px solid #222;
    background: #FFF;
    color: #222;
    text-align: center;
    min-width: 130px;
}
.stay_info_btn:hover {
    cursor: pointer;
    background: #222;
    color: #fff;
}
.select_schedule {
    margin-top: 30px;
    border-bottom: 1px solid #AAAAAA;
    padding-bottom: 47px;
}
.select_schedule_title {
    color: #222;
    font-size: 21px;
    font-weight: 600;
    margin-bottom: 42px;
}
.select_schedule_people {
    display: flex;
    border-bottom:1px solid #aaa;
    margin-bottom:100px;
}
.calender_gray_box {
    width: 700px;
    padding: 30px 40px;
    box-sizing: border-box;
    background: #F5F5F5;
}
.select_schedule_people .calender {
    width: 654px;
    margin: 0 auto;
    border: none;
    padding: 0;
}
.select_schedule_people .calender_date_box div {
    /* margin: 5px 25px; */
}
.select_schedule_people .today {
    background: #007BD0;
}
.how_many_people {
    padding:30px 0 30px 50px;
    width:700px;
}
.how_many_people .con01{
    padding-bottom:40px;
    border-bottom:1px solid #aaa;
}
.how_many_people .con01 > h5{
    font-size:18px;
    color:#222;
    font-weight:600;
    margin-bottom:10px;
}
.how_many_people .con01 > p{
    font-size:16px;
    color:#666;
    font-weight:300;
    margin-bottom:20px;
}

.how_many_people .con02{
    padding:35px 0 25px 0;
    border-bottom:1px solid #aaa;
}
.how_many_people .con02 div{
    display:flex;
    align-items: center;
    margin-bottom:10px;
}
.how_many_people .con02 div span{
    font-size:18px;
    color:#222;
    font-weight:600;
    margin-right:30px;
}
.how_many_people .con02 div p{
    font-size:18px;
    color:#333;
    font-weight:400;
}
.how_many_people .con03{
    padding-top:30px;
    display:flex;
    align-items: center;
    justify-content: space-between;
}
.how_many_people .con03 span{
    font-size:18px;
    color:#007BD0;
    font-weight:700;
}
.how_many_people .con03 div{
    font-size:24px;
    color:#222;
    font-weight:600;
}
.how_many_people .con04{
    padding:25px 0 20px 0;
    border-bottom:1px solid #aaa;
}
.how_many_people .con04 .con04-top{
    display:flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom:15px;

}
.how_many_people .con04 .con04-top span{
    font-size:18px;
    color:#222;
    font-weight:600;
}
.how_many_people .con04 .con04-top p{
    font-size:18px;
    color:#333;
}
.how_many_people .con04 .con04-item{
    display:flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom:10px;
}
.how_many_people .con04 .con04-item span{
    font-size:16px;
    color:#555;
}
.how_many_people .con04 .con04-item p{
    font-size:16px;
    color:#555;
}
.next_btn {
    width: 262px;
    height: 53px;
    background: #222;
    color: #FFF;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    line-height: 53px;
    margin: 0 auto;
    margin-top: 100px;
}
@media screen and (max-width:1280px) {
    .stay_info_txt_box{
        flex-direction: column;
        width:100%;
    }
    .stay_info_box .stay_info_txt{
        margin-bottom:20px;
    }
}
@media screen and (max-width:1024px) {
    .how_many_people{
        width:100%;
        padding-top:40px;
    }
    .how_many_people .con03{
        padding-bottom:30px;
    }

    .stay_info_room{
        margin-bottom:20px;
    }
    .stay_info_btn{
        width:200px;
    }
}
@media screen and (max-width:900px) {
    .how_many_people{
        padding-left:0;
    }
}
@media screen and (max-width:768px) {
    .stay_info_box{
        flex-direction: column;
        padding-top:40px;
    }
    .stay_info_img{
        width:100%;
        height:unset;
        aspect-ratio: 1/0.785;
        margin-bottom:20px;
    }
    .stay_info_room{
        flex-direction: column-reverse;
        margin-right:0;
    }
    .stay_info_room_people{
        margin-bottom:10px;
    }
    .stay_info_btn{
        width:100%;
        margin-top:40px;
    }
    .how_many_people .con02{
        padding-bottom:0;
    }
    .how_many_people .con02 div{
        flex-direction: column;
        align-items: flex-start;
        margin-bottom:25px;
    }
    .how_many_people .con02 div span{
        font-size:20px;
        margin-bottom:10px;
        margin-right:0;
        width:100%;
    }
    .how_many_people .con02 div p{
        font-size:16px;
    }
}




/*캘린더*/
.calender {
    width: 436px;
    padding: 22px 44px;
    box-sizing: border-box;
    border: 1px solid #D9D9D9;
    color: #111;
}
.calender_top {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
}
.calender_arrow {
    width: 10px;
    height: 16px;
    cursor: pointer;
}
.calender_arrow_prev {
    background: url(/images/icon/calender_arrow.svg);
}
.calender_arrow_next {
    background: url(/images/icon/calender_arrow.svg);
    transform: rotate(180deg);
}
.calender_month {
    color: #333;
    font-size: 20px;
    font-weight: 600;
    margin: 0 12px;
}
.calender_day_box {
    display: grid;
    grid-template-columns: repeat(7, 14.2%);
    grid-column-gap: 0px;
    margin-bottom: 12px;
    font-size: 15px;
    text-align: center;
}
.calender_date_box {
    display: grid;
    grid-template-columns: repeat(7, 14.2%);
    grid-column-gap: 0px;
    text-align: center;
}
/* .calender_date_box div {
    padding: 5px 10px;
    margin: 5px;
    border-radius: 5px;
    cursor: pointer;
} */
.lastday {
    color: #D9D9D9;
}
.saturday {
    color: #335EE0;
}
.sunday {
    color: #E74133;
}
.calender_date_box .today {
    background: #474747;
    color: #fff;
}
.price_schedule {
    width: 100%;
    margin-left: 62px;
}
.price_schedule_notice {
    color: #222;
    font-size: 18px;
    font-weight: 600;
    margin: 10px 0;
}
.price_schedule_day {
    color: #666;
}
.price_schedule_price_box {
    display: flex;
    margin: 35px 0 50px 0;
    color: #E83929;
    font-size: 20px;
    font-weight: 600;
}
.price_schedule_price_point {
    width: 99px;
    height: 25px;
    border: 1px solid #E83929;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    line-height: 25px;
    margin-right: 11px;
}
.price_schedule_gray_box {
    padding: 25px 34px 36px 34px;
    background: #F5F5F5;
    color: #222;
    line-height: 32px;
}
.price_schedule_gray_big {
    margin-top: 16px;
    font-size: 20px;
}
@media screen and (max-width:1280px) {
    .select_schedule_people .calender {
        width: 100%;
    }
    .select_schedule_people .calender_date_box div {
        /* margin: 5px;
        padding: 5px; */
    }
}
@media screen and (max-width:900px) {
    .select_schedule_people {
        flex-wrap: wrap;
    }
    .calender_gray_box {
        width: 100%;
    }
}
@media screen and (max-width:600px) {
    .calender_gray_box {
        padding: 20px;
    }
}






/* 숙소 예약 섬살이 유형  */
.stay_island_life_box{
    padding-top:60px;
    display: grid;
    grid-template-columns: repeat( 4 , calc(25% - 22.5px));
    grid-column-gap: 30px; /* 좌우 간격 */
    grid-row-gap: 100px;  /* 상하 간격 */
    border-top:1px solid #000;
}
.island_life_box_item{
    cursor: pointer;
}
.island_life_box_item .gray-bg{
    background-color:#eee;
    padding-top:50px;
    margin-bottom:10px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    overflow: hidden;
    width:100%;
}
.island_life_box_item .text-area{
    margin-bottom:25px;
}
.island_life_box_item .text-area h4{
    font-family: 'tenada';
    font-size:55px;
    font-weight:800;
    text-align: center;
    -webkit-text-stroke: 1px black;
    color:#fff;
    margin-bottom:15px;
}
.island_life_box_item .text-area p{
    text-align: center;
    line-height:26px;
    font-size:16px;
    color:#333;
    width:80%;
    margin:0 auto;
    height:78px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}
.island_life_box_item .img-area{
    position:relative;
}
.island_life_box_item .img-area .img-box{
    overflow: hidden;
    width:101%;
    aspect-ratio: 1/0.88;
    border-top-left-radius:160px;
}
.island_life_box_item .img-area .island_life_thumbnail_img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition: all 0.3s;
}
.island_life_box_item .img-area .arrow{
    position:absolute;
    top:-25px;
    right:20px;
    width:50px;
    height:50px;
    border-radius: 50%;
    background-color:#222;
    display:flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
}
.island_life_box_item .btn{
    width:100%;
    height:55px;
    border:1px solid #aaa;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    font-family: 'pretendard';
    font-size:16px;
    color:#333;
    cursor: pointer;
}
@media (hover: hover) {
    .island_life_box_item:hover .text-area p{
        color:#fff;
    }
    .island_life_box_item:hover .img-area .island_life_thumbnail_img{
        transform: scale(1.1);
    }
    .island_life_box_item:hover .btn{
        background-color:#333;
        color:#fff;
        border:1px solid #333;
    }
    .island_life_box_item:hover .img-area .arrow{
        opacity: 1;
    }
}

.island_card_noline_box{
    display:flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.island_card_noline_box p{
    font-weight:700;
    color:#333;
    margin-bottom:10px;
}
.island_card_noline_box button{
    display:flex;
    align-items: center;
    color:#056FB8;
    background-color:unset;
    border:unset;
    cursor: pointer;
}
.island_card_noline_box button img{
    margin-right:5px;
}

@media screen and (max-width:1280px) {
    .stay_island_life_box{
        grid-template-columns: repeat( 3 , calc(33.3% - 20px));
    }
    .island_life_box_item .text-area h4{
        font-size:42px;
    }
}
@media screen and (max-width:1024px) {
    .stay_island_life_box{
        grid-template-columns: repeat( 2 , calc(50% - 15px));
    }
}
@media screen and (max-width:768px) {
    .stay_island_life_box{
        padding-top:60px;
        grid-template-columns: repeat( 1 , calc(100%));
        grid-column-gap: 00px; /* 좌우 간격 */
        grid-row-gap: 40px;  /* 상하 간격 */
    }
    .island_life_box_item .gray-bg{
        padding-top:30px;
    }
    .island_life_box_item .text-area{
        margin-bottom:30px;
    }
    .island_life_box_item .text-area h4{
        font-size:40px;
        margin-bottom:5px;
    }
    .island_life_box_item .text-area p{
        height:unset;
        overflow: unset;
        text-overflow: unset;
        word-break: unset;
        display: block;
        -webkit-line-clamp: unset;
        -webkit-box-orient: unset;
        width:90%;
    }
    .island_life_box_item .img-area .img-box{
        border-top-left-radius: 140px;
    }
    .island_life_box_item:hover .img-area .arrow{
        opacity: 1;
        width:36px;
        height:36px;
        top:-18px;
        right:20px;
    }
    .island_life_box_item .btn{
        height:50px;
        background-color:#333;
        border:1px solid #333;
        color:#fff;
    }
}




/*잠시섬 숙소 소개_예약 전 숙지*/
.line_title_box p{
    color: #222;
    font-size: 28px;
    font-weight: 600;
}
.before_reservation_final_payment {
    padding-top:60px;
    display: flex;
    justify-content: space-between;
    border-top:1px solid #000;
}

.before_reservation_checklist {
    width: 100%;
}

.scroll_chk_box {
    margin-top: 30px;
    margin-bottom: 60px;
}

.scroll_box { 
    box-sizing: border-box;
    border: 1px solid #AAA;
}

.scroll_contents {
    padding: 20px 20px;
    box-sizing: border-box;
    overflow-y: scroll;
    height: 360px;
}

/* 스크롤바 설정*/
.scroll_contents::-webkit-scrollbar{
    width: 11px;
}

/* 스크롤바 막대 설정*/
.scroll_contents::-webkit-scrollbar-thumb{
    background-color: #CBCBCB;
}

/* 스크롤바 뒷 배경 설정*/
.scroll_contents::-webkit-scrollbar-track{
    background-color: #F1F1F1;
}


.argee_secession {
    display: flex;
    align-items: center;
    margin-top: 17px;
    margin-bottom:47px;
}

#agree_chk_01, #agree_chk_02, #agree_chk_03 {display: none;}

.argee_secession .agree {
    display: flex;
    align-items: center;
    margin-right:20px;
}

.argee_secession .agree_chk {
    width: 24px;
    height: 24px;
    border: 1px solid #aaa;
    box-sizing: border-box;
    margin-right: 13px;
    position: relative;
    cursor: pointer;
}

.argee_secession  .agree_chk_txt {
    color: #222;
    font-size: 18px;
}

#agree_chk_01:checked + label::after,
#agree_chk_02:checked + label::after,
#agree_chk_03:checked + label::after,
#agree_chk_21:checked + label::after,
#agree_chk_22:checked + label::after,
#agree_chk_23:checked + label::after,
#agree_chk_24:checked + label::after{
    content:'✔';
    font-size: 18px;
    width: 24px;
    height: 24px;
    text-align: center;
    position: absolute;
    left: 0;
    top:0;
}
.final_payment_details {
    position: relative;
    top: 0; right: 0;
    margin-left: 60px;
}

.final_payment_details_fix {
    position: sticky;
    top: 60px;
    width: 600px;
    padding: 46px 46px 56px 46px;
    box-sizing: border-box;
    background: #F5F5F5;
}

.final_payment_details_title {
    color: #222;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 46px;
}

.final_payment_details .stay_info_room_txt {
    margin-bottom:15px;
}

.final_payment_details_img_info {
    display: flex;
}

.final_payment_details_img {
    width: 140px;
    height: 108px;
    margin-right: 35px;
}

.final_payment_details_img img {
    width: 100%; height: 100%;
    object-fit: cover;
}

.schedule_final_payment_price {
    margin-top: 40px;
    padding-top: 32px;
    border-top: 1px solid #aaa;
}

.schedule_final_payment_price_title {
    color: #222;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 15px;
}

.schedule_final_payment_price_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 65px;
    padding: 0 30px;
    box-sizing: border-box;
    background: #FFF;
    color: #222;
    font-size: 18px;
    font-weight: 400;
    margin-bottom: 25px;
}

.schedule_final_payment_price_box .bold {
    font-size: 26px;
    font-weight: 600;
}

.final_payment_btn {
    height: 53px;
    background: #222;
    color: #FFF;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    line-height: 53px;
    margin-top: 42px;
    cursor: pointer;
}
.use-point-area{
    display:flex;
    align-items: center;
    justify-content: space-between;
}
.use-point-area .use-point{
    display:flex;
    align-items: center;
    justify-content: space-between;
}
.use-point-area .use-point span{
    font-size:16px;
    font-weight:700;
    color:#007BD0;
    margin-right:5px;
}
.use-point-area .use-point div{
    display:flex;
    align-items: center;
}
.use-point-area .use-point div .remain-point{
    font-size:16px;
    color:#222;
    font-weight:400;
}
.use-point-area .use-point div {
    font-size:16px;
    color:#222;
    font-weight:400;
}

@media screen and (max-width:768px) {
    .use-point-area{
        flex-direction: column;
        align-items: flex-start;
    }
    .use-point-area .title{
        margin-bottom:10px;
    }
}



@media (hover:hover) {
    .final_payment_btn:hover {
        color:#222;
        background-color:#fff;
        border:1px solid #222;
    }
}
@media screen and (max-width:1024px) {
    .before_reservation_final_payment {
        flex-wrap: wrap;
    }
    .before_reservation_checklist {
        order: 2;
    }
    .final_payment_details {
        width: 100%;
        order: 1;
        margin-left: 0;
        margin-bottom: 50px;
    }
    .final_payment_details_fix {
        width: 100%;
    }
}
@media screen and (max-width:768px) {
    .before_reservation_final_payment{
        padding-top:40px;
    }
    .final_payment_details_title{
        margin-bottom:20px;
        font-size:20px;
    }
    .final_payment_details .stay_info_room_txt{
        font-size:24px;
    }
    .scroll_contents {
        padding: 10px;
    }
    .final_payment_details_img_info {
        flex-direction: column;
    }
    .final_payment_details_fix {
        padding: 30px 20px;
    }
    .final_payment_details_img {
        margin:0;
        margin-bottom: 20px;
        width:100%;
        height:unset;
        aspect-ratio: 1/0.771;
    }
    .final_payment_details_txt {
        width: 100%;
    }
    .before_reservation_checklist .scroll_chk_box:last-child{
        margin-bottom:0;
    }
    .before_reservation_checklist .argee_secession{
        margin-bottom:0;
    }
    .schedule_final_payment_price{
        margin-top:30px;
    }
    .schedule_final_payment_price_box{
        padding:0 15px;
    }
    .line_title_box p{
        font-size:24px;
    }
    .scroll_chk_box{
        margin-top:20px;
        margin-bottom:40px;
    }
    .total-price h5{
        font-size:22px;
    }
    .schedule_final_payment_price_box .bold{
        font-size:22px;
    }
}



/*잠시섬_예약완료*/
.complete-reserve-wrap{
    padding:110px 0;
}
.common_title_small {
    color: #222;
    font-family: 'Tenada';
    font-size: 45px;
    font-weight: 800;
}
.common_txt_center {
    text-align: center;
}
.common_txt_center_ex {
    color: #333;
    margin-top: 30px;
}
.reservation_completed {
    width: 744px;
    padding: 40px 20px;
    box-sizing: border-box;
    border: 1px solid #AAA;
    margin: 50px auto;
    text-align: center;
    color: #222;
}
.reservation_completed .stay_info_room {
    justify-content: center;
}
.reservation_completed_list_box {
    display: flex;
    justify-content: center;
}
.reservation_completed_list {
    display: flex;
    align-items: center;
}
.reservation_completed_list_day {
    margin-bottom: 15px;
}
.reservation_completed_list .bold {
    font-size: 26px;
    font-weight: 600;
    margin-right:5px;
}
.reservation_completed_list .div-point {
    font-size: 16px;
}
.reservation_completed_list .point {
    margin-right:5px;
}
.reservation_completed_list div{
    display:flex;
    align-items: center;
}
.reservation_completed_list div em{
    width:1px;
    height:15px;
    background-color:#aaa;
    margin:0 15px;
}
.reservation_completed_sub_title {
    width: 92px;
    margin-right: 55px;
    font-weight: 600;
    text-align: left;
}
.mypage_go_btn {
    width: 220px;
    height: 45px;
    border-radius: 4px;
    border: 1px solid #222;
    background: #FFF;
    color: #222;
    text-align: center;
    line-height: 45px;
    margin: 0 auto;
    cursor: pointer;
}


@media screen and (max-width:768px) {
    .complete-reserve-wrap{
        padding:60px 0;
    }
    .common_title_small{
        font-size:32px;
    }
    .common_txt_center_ex{
        margin-top:20px;
    }
    .reservation_completed {
        width: 100%;
        padding-bottom:10px;
    }
    .reservation_completed_list {
        flex-wrap: wrap;
        justify-content: center;
        margin-bottom:30px;
    }
    .reservation_completed_sub_title {
        width: 100%;
        text-align: center;
        margin-right: 0;
        margin-bottom:10px;
    }
    .reservation_completed .stay_info_room {
        display: block;
    }
    .reservation_completed .stay_info_room_txt {
        margin-right: 0;
    }
    .reservation_completed .stay_info_room_people {
        margin: 0 auto;
        margin-top: 20px;
    }
    .mypage_go_btn{
        width:100%;
    }
}






/* 다른 프로그램 추천 리스트 */
.other-program-wrap{
    background:url(/images/other-program-bg.jpg) center center no-repeat;
    background-size:cover;
    padding:100px 0 140px 0;
}
.other-program-title{
    font-size:30px;
    color:#fff;
    font-weight:700;
    margin-bottom:50px;
}
.other-program-swiper-wrap{
    position:relative;
}
.other-program-swiper .swiper-slide .img-area{
    width:100%;
    aspect-ratio: 1/1;
    overflow: hidden;
    border:1px solid #000;
    margin-bottom:20px;
}
.other-program-swiper .swiper-slide .img-area img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition: 0.3s all;
}
@media (hover : hover) {
    .other-program-swiper .swiper-slide .img-area img:hover{
        transform: scale(1.1);
    }
}
.other-program-swiper .swiper-slide .text-area h5{
    font-size:20px;
    color:#222;
    font-weight:600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.other-program-swiper-wrap .swiper-button-prev{
    width:55px;
    height:56px;
    transform: rotate(180deg);
    left:-75px;
}
.other-program-swiper-wrap .swiper-button-prev::after{
    content:'';
    width:55px;
    height:56px;
    background:url(/images/icon/slide_arrow_prev.svg) center center no-repeat;
}
.other-program-swiper-wrap .swiper-button-next{
    width:55px;
    height:56px;
    right:-75px;
}
.other-program-swiper-wrap .swiper-button-next::after{
    content:'';
    width:55px;
    height:56px;
    background:url(/images/icon/slide_arrow_prev.svg) center center no-repeat;
}
@media screen and (max-width:1600px) {
    .other-program-swiper-wrap .swiper-button-next{
        right:10px;
    }
    .other-program-swiper-wrap .swiper-button-prev{
        left:10px;
    }
}
@media screen and (max-width:768px) {
    .other-program-title{
        font-size:24px;
        margin-bottom:20px;
    }
    .other-program-wrap{
        padding:40px 0 100px 0;
    }
    .other-program-swiper-wrap .swiper-button-next,
    .other-program-swiper-wrap .swiper-button-prev
    {
        display:none;
    }
}





/* 커뮤니티 커뮤니티 커뮤니티  */
/* 잠미섬 미션 */
.community-wrap{
    display:flex;
}
.sub-nav-wrap{
    position:relative;
    height: auto;
    width:265px;
    margin-right:90px;
}
.sub-nav-wrap .mo-depth03{
    display:none;
}
.sub-lnb {
    position: sticky;
    top:50px;
    border:1px solid #000;
}
.sub-lnb .lnb-item p{
     color:#444;
     font-size:22px;
     line-height:32px;
     cursor: pointer;
}
.sub-lnb .lnb-item.on p{
    background:url(/images/icon/subnavarrow01.svg) center right 20px no-repeat #222;
    color:#fff;
    font-weight:600;
}
.sub-lnb .lnb-item p{
    padding:12px 25px;
}
.sub-lnb .lnb-item .depth03{
    background-color:#F2F2F2;
}
.sub-lnb .lnb-item .depth03 li{
    line-height:35px;
    font-size:18px;
    color:#444;
    cursor: pointer;
    background-color:#F2F2F2;
    padding:10px 35px;
    line-height:1.3;
}
.sub-lnb .lnb-item .depth03:nth-child(2) li{
    padding-top:20px;
}
.sub-lnb .lnb-item .depth03:last-child li{
    padding-bottom:20px;
}
.sub-lnb .lnb-item .depth03 li.on{
    font-weight:800;
    color:#222;
}
.community-contents-wrap{
    width:calc(100% - 265px - 90px);
}
.community-title{
    display:flex;
    align-items: center;
    justify-content: space-between;
    width:100%;
    border-bottom:1px solid #000;
    padding-bottom:10px;
    margin-bottom:40px;
}
.community-title h4{
    font-family: 'tenada';
    color:#222;
    font-weight:700;
    font-size:50px;
}
.community-title .go-btn button{
    padding:10px 28px;
    text-align: center;
    font-size:18px;
    background-color:#0AAC52;
    border:1px solid #000;
    color:#fff;
    cursor: pointer;
    border-radius: 25px;

}
.community-title .go-btn span{
    width:24px;
    height:24px;
    display:flex;
    align-items: center;
    justify-content: center;
    background-color:#05A54B;
    border-radius: 50%;
    margin-left:6px;
}
@media (hover : hover) {
    .community-title .go-btn button:hover{
        background-color:#fff;
        color:#009944;
        font-weight:600;
    }
}
.mission-ranking-list{
    display:flex;
    margin-bottom:50px;
}
.mission-ranking-list .ranking-list{
    width:calc(50% - 15px);
    border:1px solid #000;
    background-color:#FFFAEF;
}
.mission-ranking-list .ranking-list:first-child{
    margin-right:30px;
}
.mission-ranking-list .ranking-list h5{
    background-color:#FFF3D8;
    border-bottom:1px solid #000;
    padding:11px 0;
    text-align: center;
    font-size:21px;
    color:#222;
    font-weight:500;
}
.ranking-list .list-wrap ul{
    padding:20px 30px 20px 45px;
    position:relative;
    min-height:240px;
}
.ranking-list .list-wrap ul li{
    display:flex;
    align-items: center;
    height:40px;
}
.ranking-list .list-wrap ul li span{
    font-size:20px;
    font-weight:500;
    color:#333;
    margin-right:8px;
    width:15px;
}
.ranking-list .list-wrap ul li:first-child span{
    color:#E83929;
    font-weight:800;
}
.mission-ranking-list .ranking-list:first-child ul li p{
    width:100%;
}
.ranking-list .list-wrap ul li p{
    font-size:18px;
    color:#222;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width:calc(100% - 66px);
}
.ranking-list .list-wrap ul li p.no-member{
    width:100%;
}
.ranking-list.list-member .list-wrap ul li p{
    cursor: pointer;
}
.ranking-list .list-wrap ul li div{
    width:33px;
    height:33px;
    margin-right:10px;
    border-radius: 50%;
    overflow: hidden;
}
.ranking-list .list-wrap ul li div img{
    width:100%;
    height:100%;
    object-fit: cover;
}
.ranking-list .list-wrap ul > img{
    position:absolute;
    top:18px;
    left:26px;
    width:19px;
}

.mission-list .list-wrap li .img-area{
    width:100%;
    aspect-ratio: 1/1;
    margin-bottom:5px;
}
.mission-list .list-wrap li .text-area h6{
    font-size:18px;
    color:#444;
    line-height:23px;
    height:46px;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-word;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-bottom:5px;
    font-weight:400;
}
.mission-list .list-wrap li .text-area p{
    font-size:16px;
    color:#888;
    font-weight:300;
    margin-bottom:7px;
}
.mission-list .list-wrap li .text-area span{
    font-size:16px;
    color:#888;
    font-weight:300;
}

.community-contents-wrap .tab{
    width:100%;        
}
.community-contents-wrap .tab ul{
    width:100%;
    overflow-x: scroll;
    padding-bottom:10px;
}
.community-contents-wrap .tab ul::-webkit-scrollbar{
    display:block;
    width:7px;
    height:10px;
}
.community-contents-wrap .tab ul::-webkit-scrollbar-thumb{
    border-radius: 10px;
    background-color:#888;
    height:5px;
}
.community-contents-wrap .tab ul::-webkit-scrollbar-track{
    display:none;
}
@media screen and (max-width:1400px) {
    .community-title h4{
        font-size:40px;
    }
}
@media screen and (max-width:1280px) {
    .sub-nav-wrap{
        margin-right:50px;
    }
    .community-contents-wrap {
        width: calc(100% - 265px - 50px);
    }
    .community-title h4{
        font-size:24px;
    }
    .community-title .go-btn button{
        padding:10px 10px;
    }
}
@media screen and (max-width:1024px) {
    .community-title h4{
        margin-bottom:10px;
        font-size:32px;
    }
    .community-title{
        flex-direction: column;
        align-items: flex-start;
    }
    .community-title .go-btn{
        width:100%;
        display:flex;
        justify-content: flex-end;
    }
    .community-title .go-btn button{
        font-size:16px;
    }
}
@media screen and (max-width:1024px) {
    .sub-nav-wrap{
        width:180px;
        margin-right:40px;
    }
    .sub-lnb .lnb-item p{
        font-size:18px;
        padding:12px 15px;
    }
    .mission-ranking-list .ranking-list h5{
        font-size:18px;
    }
    .community-contents-wrap{
        width:calc(100% - 240px);
    }
    .ranking-list .list-wrap ul{
        padding:20px 15px 20px 25px;
    }
    .sub-lnb .lnb-item .depth03{
        padding:10px 22px;
    }
    .sub-lnb .lnb-item .depth03 li{
        padding:2px 0;
    }
    .ranking-list .list-wrap ul > img{
        left:10px;
    }
    .mission-ranking-list .ranking-list{
        width:calc(50% - 5px);
    }
    .mission-ranking-list .ranking-list:first-child{
        margin-right:10px;
    }
}

@media screen and (max-width:768px) {
    .community-wrap{
        flex-direction: column;
    }
    .community-contents-wrap{
        width:100%;
    }
    .sub-nav-wrap{
        margin-right:0;
        width:100%;
        margin-bottom:60px;
    }
    .sub-nav-wrap .mo-depth03{
        display:block;
    }
    .sub-nav-wrap .mo-depth03{
        display:flex;
        justify-content: space-between;
    }
    .sub-nav-wrap .mo-depth03 li{
        width:48%;
        padding:15px 0px;
        text-align: center;
        vertical-align: middle;
        line-height:1.3;
        background-color:#F2F2F2;
        color:#444;
        display:flex;
        align-items: center;
        justify-content: center;
    }
    .sub-nav-wrap  .mo-depth03 li.on{
        color:#222;
        font-weight:700;
        font-size:18px;
    }
    .sub-lnb .lnb-item.on p{
        height:60px;
        background:url(/images/icon/subnavarrow02.svg) center right 20px no-repeat #222;
    }
    .sub-lnb{
        position:relative;
        top:0;
        left:0;
        border:unset;
        margin-bottom:15px;
    }
    .sub-lnb li{
        width:100%;
        background-color:#fff;
        height:60px;
        border-bottom:1px solid #ccc;
        border-left:1px solid #ccc;
        border-right:1px solid #ccc;
        box-sizing: border-box;
        position:absolute;
        left:0;
    }
    .sub-lnb li.on{
        border:unset;
        position:unset;
    }
    .sub-lnb li:nth-child(2){
        top:60px;
    }
    .sub-lnb li:nth-child(3){
        top:120px;
    }
    .sub-lnb li:nth-child(4){
        top:180px;
    }
    .sub-lnb li:nth-child(5){
        top:240px;
    }

    .community-title{
        flex-direction: column;
        align-items: flex-start;
        margin-bottom:20px;
        padding-bottom:0;
    }
    .community-title h4{
        font-size:36px;
    }
    .community-title .go-btn button{
        position:fixed;
        bottom:20px;
        left:50%;
        transform: translateX(-50%);
        width:calc(100% - 40px);
        height:50px;
        background-color:#009944;
        color:#fff;
        border-radius: 10px;
        z-index: 999999;
        padding:0 20px;
        display:flex;
        align-items: center;
        justify-content: center;
    }
    .community-title .go-btn span{
        background-color:#fff;
    }
    .mission-ranking-list{
        flex-direction: column;
    }
    .mission-ranking-list .ranking-list{
        width:100%;
    }
    .mission-ranking-list .ranking-list:first-child{
        margin-right:0;
        margin-bottom:15px;
    }
    .ranking-list .list-wrap ul{
        padding:20px 20px;
    }
    .ranking-list .list-wrap ul > img{
        top:12px;
        left:12px;
    }
}






/* 잠시섬 커뮤니티 미션 상세 */
.mission-view .view-top h4{
    margin-bottom:20px;
}
.mission-view .view-top div{
    display:flex;
    align-items: center;
    margin-bottom:20px;
    font-size:16px;
    color:#666;
}
.mission-view .view-top div em{
    margin:0 10px;
    width:1px;
    height:14px;
    background-color:#666;
}
.mission-view .view-top div p{
    margin:0;
}






/* 커뮤ㅜ니티 미션 인증쓰기 */
.community-mission-write{
    padding-bottom:200px;
}
.community-mission-write .title-area{
    border-bottom:1px solid #222;
    margin-bottom:30px;
}
.community-mission-write .title-area h4{
    font-size:36px;
    color:#222;
    margin-bottom:20px;
}
.all-mission-list li{
    display:flex;
    align-items: center;
    justify-content: space-between;

}
.all-mission-list li p{
    max-width:calc(100% - 75px);
    min-width:calc(100% - 100px);
    width:unset;
}
.all-mission-list li span{
    padding:3px 4px;
    line-height: 1.4;
    font-size:14px;
    margin-left:15px;
    border-radius: 2px;
    font-weight:500;
    text-align: center;
    width:unset;
    height:unset;
}
.all-mission-list li p.possible{
    color:#444;
}
.all-mission-list li span.possible{
    color:#fff;
    background-color:#007BD0;

}
.all-mission-list li p.complete{
    color:#aaa;
}
.all-mission-list li span.complete{
    background-color:#DBDBDB;
    color:#444;
}
.mission-select-list-wrap{
    display:flex;
    justify-content: space-between;
    padding-bottom:40px;
    margin-bottom:40px;
    border-bottom:1px solid #222;
}
.mission-select-list-wrap .mission-select-list{
    width:calc(50% - 15px);
}
.mission-select-list-wrap .mission-select-list h5{
    font-size:21px;
    color:#333;
    font-weight:700;
    margin-bottom:15px;
}
.mission-select-list-wrap .mission-select-list div{
    border:1px solid #ccc;
    height:200px;

    overflow: hidden;
}
.mission-select-list-wrap .mission-select-list div ul{
    padding:20px 30px;
    height:200px;
    overflow-y: scroll;
}
.mission-select-list-wrap .mission-select-list div ul li{
    margin-bottom:15px;
    color:#444;
    font-size:18px;
}
.community-mission-write textarea{
    width:100%;
    height:500px;
    padding:12px 20px;
    border:1px solid #ccc;
}

.possible-mission-list ul li label{
    display:flex;

}
.possible-mission-list ul li label input{
    display:none;
}
.possible-mission-list ul li label span{
    width:16px;
    height:16px;
    border:1px solid #aaa;
    margin-top:4px;
    margin-right:7px;
    cursor: pointer;
}
.possible-mission-list ul li label p{
    line-height:1.3;
    cursor: pointer;
}
.possible-mission-list ul li label input:checked ~ span{
    border:2px solid #056FB8;
    display:flex;
    justify-content: center;
    align-items: center;
}
.possible-mission-list ul li label input:checked ~ span em{
    width:6px;
    height:6px;
    background-color:#056FB8;
}
.possible-mission-list ul li label input:checked ~ p{
    color:#056FB8;
}
.possible-mission-list ul li label p{
    width:calc(100% - 25px);
}

@media screen and (max-width:768px) {
    .community-mission-write{
        padding-bottom:80px;
    }
    .community-mission-write .title-area h4{
        font-size:30px;
        line-height:1.3;
    }
    .mission-select-list-wrap{
        flex-direction: column;
        padding-bottom:10px;
        margin-bottom:30px;
    }
    .mission-select-list-wrap .mission-select-list{
        width:100%;
        margin-bottom:20px;
    }
    .mission-select-list-wrap .mission-select-list div{
        height:185px;
    }
    .mission-select-list-wrap .mission-select-list div ul{
        padding:20px 20px;
        height:185px;
    }
    .possible-mission-list ul li label{
        align-items: center;
    }
    .possible-mission-list ul li label span{
        margin-top:0;
    }
    .community-mission-write textarea{
        height:300px;
    }
}
















/* 잠시섬 커뮤니티 영감 모임 리스트 */
.meeting-list-wrap{
    margin-bottom:200px;
}
.meeting-list-wrap .list-wrap li{
    cursor: pointer;
    padding:20px 30px 20px 0;
    border-bottom:1px solid #aaa;
}
.meeting-list-wrap .list-wrap li:first-child{
    padding-top:0;
}
.meeting-list-wrap .list-wrap .img-area{
    width:150px;
    aspect-ratio: 1/1;
    margin-right:30px;
}
.meeting-list-wrap .list-wrap .text-area{
    width:calc(100% - 150px - 30px);
}
.meeting-list-wrap .list-wrap .text-area h4{
    font-size:20px;
    margin-bottom:12px;
    line-height:1.5;
    font-weight:600;
}
.meeting-list-wrap .list-wrap .text-area h5{
    font-size:16px;
    font-weight:400;
    margin-bottom:15px;
    white-space: nowrap;      /* 텍스트를 한 줄로 표시 */
    overflow: hidden;         /* 넘친 텍스트를 숨김 */
    text-overflow: ellipsis;  /* 넘친 부분을 '...'으로 표시 */
}
.meeting-list-wrap .list-wrap .text-area .date{
    display:flex;
    align-items: center;
    margin-bottom:20px;
}
.meeting-list-wrap .list-wrap .text-area .date span{
   padding:2px 5px;
   color:#fff;
   background-color:#007BD0;
   font-size:18px;
   font-weight:600;
   margin-right:10px;
}
.meeting-list-wrap .list-wrap .text-area .date p{
    font-size:18px;
    color:#056FB8;
    font-weight:600;
}
@media screen and (max-width:768px) {
    .meeting-list-wrap .list-wrap li{
        flex-direction: column;
        padding-right:0;
    }
    .meeting-list-wrap .list-wrap .img-area{
        width:100%;
        margin-right:0;
        margin-bottom:20px;
    }
    .meeting-list-wrap .list-wrap .text-area{
        width:100%;
    }
}












/* 잠시섬 커뮤니티 영감 모임 상세 */
.meeting-view .view-top h4{
    margin-bottom:20px;
    font-size:36px;
    font-weight:800;
    color:#222;
}
.meeting-view .view-top div{
    display:flex;
    align-items: center;
    margin-bottom:20px;
    font-size:16px;
    color:#666;
}
.meeting-view .view-top div em{
    margin:0 10px;
    width:1px;
    height:14px;
    background-color:#666;
}
.meeting-view .view-top div p{
    margin:0;
}
.meeting-view-date{
    display:flex;
    padding:20px 0;
    border-bottom:1px solid #888;
}
.meeting-view-date h6{
    font-size:20px;
    color:#007BD0;
    font-weight:600;
    margin-right:35px;
}
.meeting-view-date div{
    margin-right:25px;
    display:flex;
    align-items: center;
}
.meeting-view-date div span{
    font-size:20px;
    color:#333;
    font-weight:600;
    margin-right:5px;
}
.meeting-view-date div p{
    font-size:20px;
    color:#333;
    font-weight:300;
}
.delete-image{
    position:absolute;
    width:16px;
    height:16px;
    top:5px;
    right:5px;
    background:url(/images/icon/popup_close.svg) center center no-repeat #222;
    background-size:10px 10px;
    border:none;
    cursor: pointer;
}
@media screen and (max-width:768px) {
    .meeting-view .view-top h4{
        font-size:30px;
        line-height:1.3;
    }
    .meeting-view-date{
        flex-direction: column;
    }
    .meeting-view-date h6{
        margin-right:0;
        margin-bottom:10px;
    }
    .meeting-view-date div{
        margin-right:0;
        margin-bottom:5px;
    }
    .meeting-view-date div span{
        font-size:18px;
    }
    .meeting-view-date div p{
        font-size:16px;
    }
}

/* 잠시섬 커뮤니티 영감 모임 상세 댓글 */
.comment-wrap{
    margin-top:50px;
    padding-bottom:50px;
    border-bottom:1px solid #222;
}
.write-wrap{
   display:flex;
   align-items: center;
   justify-content: space-between;
}
.write-wrap input{
   width:calc(100% - 75px);
   height:60px;
   padding:14px 35px;
   border:none;
   background-color:#F3F3F3;
   border-radius: 40px;
}
.write-wrap button{
   width:60px;
   height:60px;
   border-radius: 50%;
   border:none;
   background:url(/images/icon/review-enter.svg) center center no-repeat #222;
   cursor: pointer;
}
.comment-list-wrap .list-item{
   display:flex;
}
.comment-list-wrap .list-item.first-item{
   margin-bottom:30px;
}
.comment-list-wrap .list-item.first-item:first-child{
    margin-top:55px;
}
.comment-list-wrap .list-item.first-item:last-child{
    margin-bottom:0px;
 }
.comment-list-wrap .list-item .profile-img-area{
   width:50px;
   height:50px;
   border-radius: 50%;
   overflow: hidden;
   display:flex;
   align-items: center;
   justify-content: center;
   margin-right:30px;
}
.comment-list-wrap .list-item .profile-img-area img{
   width:100%;
   height:100%;
   object-fit: cover;
}
.comment-list-wrap .list-item .comment-contents{
   width:calc(100% - 80px);
}
.comment-list-wrap .list-item .comment-contents span{
   color:#0f994b;
   margin-right:2px;
   margin-left:2px;
}
.comment-list-wrap .list-item .comment-contents span:first-child{
    margin-left:0;
}
.comment-list-wrap .list-item .comment-contents .title{
   display:flex;
   align-items: center;
   justify-content: space-between;
   width:100%;
   margin-bottom:12px;
}
.comment-list-wrap .list-item .comment-contents .title h4{
   font-size:16px;
   color:#333;
   font-weight:600;
}
.comment-list-wrap .list-item .comment-contents .title p{
   font-size:16px;
   color:#888;
}
.comment-list-wrap .list-item .comment-contents .comment-con{
   line-height:1.5;
   color:#444;
   margin-bottom:15px;
}
.comment-list-wrap .list-item .comment-contents .btn-area{
   display:flex;
}
.comment-list-wrap .list-item .comment-contents .btn-area button{
   background:none;
   border:none;
   font-size:15px;
   color:#888;
   cursor: pointer;
}
.comment-list-wrap .list-item .comment-contents .btn-area .login-btn{
   display:flex;
   align-items: center;
   margin-left:30px;
}
.comment-list-wrap .list-item .comment-contents .btn-area .login-btn span{
   display:block;
   margin:0 10px;
   width:1px;
   height:12px;
   background-color:#888;
}
.comment-second-list-wrap{
   padding-left:80px;
}
.comment-second-list-wrap .list-item.second-item{
   margin-bottom:30px;
}
.comment-second-list-wrap .list-item.second-item:last-child{
    margin-bottom:40px;
 }
.write-wrap.list-write-wrap{
   margin-top:20px;
   margin-bottom:0px;
}


.load-more-comments {
    margin-top: 30px;
    height:50px;
    line-height:50px;
    background-color: #f5f5f5;
    border: unset;
    font-size:16px;
    color:#555;
    border-radius: 5px;
    cursor: pointer;
    text-align: center;
}

.more-replies-btn {
    border:none;
    cursor: pointer;
    background-color: unset;
    margin-left:80px;
    margin-bottom:30px;
    color:#555;
    font-size:16px;
}
.more-replies-btn img{
    margin-right:5px;
}
.write-box-hidden {
    display: none;
}
.btn-hidden {
    display: none;
}
/* 멘션 드롭다운 스타일 */
.mention-dropdown {
    position: absolute; /* 절대 위치 */
    z-index: 10;       /* 다른 요소 위에 표시 */
    background-color: white;
    border: 1px solid #ccc;
    max-height: 150px; /* 최대 높이 제한 */
    overflow-y: auto;  /* 스크롤 가능 */
    display: none;    /* 기본적으로 숨김 */
}
.mention-dropdown ul {
    list-style: none;
    padding: 0;
    margin: 0;
}
.mention-dropdown li {
    padding: 5px;
    cursor: pointer;
}
.mention-dropdown li:hover {
    background-color: #f0f0f0;
}
/* 멘션 스타일 */
.mention {
    color: blue;
    font-weight: bold;
    cursor: pointer; /* 마우스 오버 시 커서 변경 */
}


@media (hover:hover) {
   .comment-list-wrap .list-item .comment-contents .btn-area button:hover{
       color:#333;
    }
    .write-wrap button:hover{
        background-color:#009944;
    }
    .load-more-comments:hover{
        background-color:#eee;
        color:#222;
        text-decoration: underline;
    } 
    .more-replies-btn:hover{
        color:#222;
    }
}


@media screen and (max-width:768px) {
    .comment-wrap{
        margin-top:30px;
        padding-bottom:30px;
    }
    .write-wrap input{
        height:40px;
        width:calc(100% - 50px);
        padding:10px 15px;
    }
    .write-wrap button{
        width:40px;
        height:40px;
        background-size:50%;
    }
    .comment-list-wrap .list-item .profile-img-area{
        width:36px;
        height:36px;
        margin-right:20px;
    }
    .comment-list-wrap .list-item .comment-contents{
        width:calc(100% - 56px);
    }
    .comment-list-wrap .list-item .comment-contents .comment-con {
         margin-bottom:10px;
    }
    .comment-list-wrap .list-item .comment-contents .title{
         margin-bottom:5px;
    }
    .comment-list-wrap .list-item .comment-contents .title p{
        font-size:14px;
    }
    .comment-list-wrap .list-item.first-item{
        margin-bottom:20px;
    }
    .comment-list-wrap .list-item.first-item:first-child{
         margin-top:30px;
    }
    .comment-second-list-wrap{
        padding-left:20px;
    }
    .comment-second-list-wrap .list-item.second-item{
        margin-bottom:20px;
    }
    .comment-second-list-wrap .list-item.second-item:last-child{
         margin-bottom:30px;
    }
    .more-replies-btn{
         margin-left:60px;
    }
 
 }




/* 잠시섬 커뮤니티 영감 모임 쓰기 */
.meeting-write-date{
    width:100%;
    margin-top:40px;
    margin-bottom:30px;
    display:flex;
    align-items: center;
    justify-content: space-between;
}
.meeting-write-date div span{
    font-size:21px;
    color:#333;
    font-weight:700;
    margin-right:30px;
}
.meeting-date input{
    width:300px;
    height:45px;
    padding-left:20px;
    border:1px solid #bbb;
    background:url(/images/icon/meeting-date.svg) center right 20px no-repeat #fff;
    position: relative;
    color:#222;
}
.meeting-date input[type="date"]::-webkit-clear-button,
.meeting-date input[type="date"]::-webkit-inner-spin-button{
    display:none;
}
.meeting-date input[type="date"]::-webkit-calendar-picker-indicator{
    position:absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    color: transparent;
}
.meeting-date input[type="date"]::before{
    content: attr(data-placeholder);
    width: 100%;
}
.meeting-date input[type='date']:valid::before{
    display: none;
}
.meeting-time select{
    -webkit-appearance: none;  /* 크롬 화살표 없애기 */
    -moz-appearance: none; /* 파이어폭스 화살표 없애기 */
    appearance: none;  /* 화살표 없애기 */
    width:160px;
    height:45px;
    border:1px solid #bbb;
    padding-left:10px;
    background:url(/images/icon/meeting-arrow.svg) center right 10px no-repeat;
    margin-left:15px;
    color:#222;
}
.meeting-time select option{
    color:#aaa;
}
.meeting-view textarea{
    width:100%;
    height:500px;
    padding:12px 20px;
    border:1px solid #bbb;
}
.meeting-view textarea::placeholder{
    color:#888;
}
@media screen and (max-width:1280px) {
    .meeting-date input{
        width:320px;
    }
}
@media screen and (max-width:1024px) {
    .meeting-write-date div span{
        margin-right:20px;
    }
    .meeting-date input{
        width:230px;
    }
    .meeting-time select{
        width:100px;
    }
}
@media screen and (max-width:768px) {
    .meeting-write-date{
        flex-direction: column;
        align-items: flex-start;
        margin-bottom:0;
    }
    .meeting-date,
    .meeting-time{
        width:100%;
        margin-bottom:20px;
    }
    .meeting-date input{
        width:100%;
    }
    .meeting-write-date div span{
        display:block;
        width:100%;
        margin:0;
        margin-bottom:10px;
    }
    .meeting-time select{
        margin:0;
        margin-right:10px;
        width:calc(50% - 7px);
        display:inline-block;
    }
    .meeting-time select:last-child{
        margin-right:0;
    }
    .meeting-view textarea{
        height:300px;
    }
}






/* 사진 올리기 */
.photo-area{
    border:1px solid #ccc;
    padding:15px 20px;
    margin-bottom:20px;
}
.photo-area .phto-title{
    margin-bottom:20px;
}
.photo-area .phto-title h5{
    font-size:18px;
    font-weight:500;
    color:#333;
    margin-bottom:10px;
}
.photo-area .phto-title p{
     font-size:16px;
     color:#333;
     margin-bottom:10px;
}
.photo-area .photo-upload{
    display:flex;
}
.photo-area .photo-upload input{
    display:none;
}
.photo-area .photo-upload label{
    border:1px dashed #ccc;
    background-color:#f5f5f5;
    display:flex;
    align-items: center;
    justify-content: center;
    width:85px;
    height:85px;
    cursor: pointer;
    margin-right:20px;
}
.photo-area .photo-upload label div{
    display:flex;
    align-items: center;
    justify-content: center;
    width:28px;
    height:28px;
    border:1px solid #ccc;
    background-color:#d9d9d9;
    border-radius: 50%;
}
.photo-area .photo-upload label div img{
    opacity: 0.75;
}
.photo-area .img-list {
    width:calc(100% - 105px);
    display:flex;
    flex-wrap: wrap
}
.photo-area .img-list li{
    width:85px;
    height:85px;
    overflow: hidden;
    margin-right:10px;
    margin-bottom:10px;
    border:1px solid #ccc;
}
.photo-area .img-list li img{
   width:100%;
   height:100%;
   object-fit: cover;
}
.mission-coution-p {
    font-size: 15px;
    color: #333;
    margin-top: 5px;
}
@media screen and (max-width:768px) {
    .photo-area .photo-upload label{
        width:66px;
        height:66px;
        margin-right:10px;
    }
    .photo-area .img-list{
        width:calc(100% - 66px - 10px);
    }
    .photo-area .img-list li{
        width:66px;
        height:66px;
    }
}












/* 잠시섬 커뮤니티 이용후기 */
.review-title{
    display:flex;
    border:none;
    margin-bottom:0;
    justify-content: flex-end
}
.community-review-wrap .tab{
    margin-bottom:20px;
    margin-top:20px;
}
.community-review-wrap .tab ul{
    justify-content: flex-start;
}
.community-review-wrap .tab ul li{
    padding:16px 22px;
}
.community-review-list{
    margin-bottom:200px;
}
.community-review-list .list-wrap{
    border-top:1px solid #222;
    padding-top:10px;
}
.community-review-list .list-wrap li{
     padding:20px 0;
     border-bottom:1px solid #ccc;
}
.community-review-list .list-wrap .img-area{
    width:192px;
    aspect-ratio: 1/1;
    margin-right:50px;
}
.community-review-list .list-wrap .text-area{
    width:calc(100% - 192px - 50px);
}
.community-review-list .list-wrap .text-area h4{
    font-size:21px;
    color:#333;
    font-weight:600;
    line-height:1.4;
    margin-bottom:20px;
}
.community-review-list .list-wrap .text-area h5{
    font-size:18px;
    font-weight:400;
    color:#444;
    line-height:28px;
    height:84px;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-word;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    margin-bottom:40px;
}


@media screen and (max-width:1400px) {
    .community-review-wrap .tab ul li{
        font-size:18px;
        padding:16px 14px;
    }
}
@media screen and (max-width:1280px) {
    .community-review-wrap .tab {
        width:100%;
    }
    .community-review-wrap .tab ul{
        overflow-x: scroll;
    }
}
@media screen and (max-width:1024px) {
    .community-review-list .list-wrap .img-area{
        margin-right:20px;
    }
    .community-review-list .list-wrap .text-area{
        width:calc(100% - 192px - 30px);
    }

}
@media screen and (max-width:768px) {
    .community-review-wrap .tab ul li {
        padding:10px 15px;
        margin-right:10px;
    }
    .community-review-list .list-wrap li{
        flex-direction: column;
    }
    .community-review-wrap .tab{
        margin-bottom:20px;
    }
    .common-list02 .list-wrap li .img-area{
        width:100%;
        margin-right:0;
        margin-bottom:20px;
    }
    .community-review-list .list-wrap .text-area{
        width:100%;
    }
    .community-review-list .list-wrap .text-area h4{
        margin-bottom:10px;
    }
    .community-review-list .list-wrap .text-area h5{
        margin-bottom:20px;
        font-size:16px;
        line-height:24px;
        height:72px;
    }
}









/* 잠시섬 커뮤니티 이용후기 쓰기 폼*/
.community-review-write{
    padding-bottom:200px;
}
.community-review-write-title{
    font-size:36px;
    color:#222;
    font-weight:800;
    border-bottom:1px solid #222;
    padding-bottom:20px;
    margin-bottom:40px;
}
.form-area01 .form01{
    margin-bottom:30px;
    display:flex;
    align-items: center;
}
.form-area01 .form01 span{
    font-size:21px;
    color:#333;
    font-weight:700;
    margin-right:35px;
}
.form-area01 .form01 select{
    -webkit-appearance: none;  /* 크롬 화살표 없애기 */
    -moz-appearance: none; /* 파이어폭스 화살표 없애기 */
    appearance: none;  /* 화살표 없애기 */
    width:400px;
    height:45px;
    border:1px solid #bbb;
    padding-left:10px;
    background:url(/images/icon/meeting-arrow.svg) center right 10px no-repeat;
    margin-left:15px;
    color:#222;
}
.form-area01 .form02{
    margin-bottom:20px;
}
.form-area01 .form02 input{
    border:1px solid #bbb;
    height:45px;
    padding:0 20px;
    width:100%;
}
.form-area01 .form03 textarea{
    width:100%;
    height:500px;
    border:1px solid #bbb;
    padding:10px 14px;
}
@media screen and (max-width:768px) {
    .community-review-write{
        padding-bottom:80px;
    }
    .community-review-write-title{
        font-size:30px;
        line-height:1.3;
        margin-bottom:30px;
    }
    .form-area01 .form01{
        flex-direction: column;
        align-items: flex-start;
        margin-bottom:20px;
    }
    .form-area01 .form01 select{
        width:100%;
        margin:0;
    }
    .form-area01 .form01 span{
        margin-bottom:10px;
    }
    .form-area01 .form02 input{
        padding:0 10px;
    }
    .form-area01 .form03 textarea{
        height:300px;
    }
}





/* 커뮤니티 강화유니버스피플 리스트*/
.people-list-wrap{
    margin-bottom:200px;
}
.people-list-wrap .list-wrap{
    width:100%;
    display:grid;
    grid-template-columns: repeat(3, calc(33% - 20px));
    grid-column-gap: 30px;
    grid-row-gap: 40px;
}
.people-list-wrap .list-wrap li{
    cursor: pointer;
}
.people-list-wrap .list-wrap li .img-area{
    width:100%;
    aspect-ratio: 1/1;
    margin-bottom:15px;
    border-radius: 50%;
    overflow: hidden;
}
.people-list-wrap .list-wrap li .text-area h4{
    font-size:20px;
    color:#333;
    font-weight:600;
    margin-bottom:15px;
}
.people-list-wrap .list-wrap li .text-area p{
    font-size:16px;
    line-height:25px;
    height:50px;
    color:#444;
    font-weight:400;
    margin-bottom:15px;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-word;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
.people-list-wrap .list-wrap li .text-area span{
    width:34px;
    height:34px;
    display:flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color:#eee;
}

@media screen and (max-width:1024px) {
    .people-list-wrap .list-wrap{
        grid-template-columns: repeat(2, calc(50% - 20px));
        grid-column-gap: 40px;
        grid-row-gap: 40px;
    }
}
@media screen and (max-width:768px) {
    .people-list-wrap{
        margin-bottom:80px;
    }
    .people-list-wrap .list-wrap{
        grid-template-columns: repeat(1, calc(100%));
        grid-column-gap: 0px;
        grid-row-gap: 40px;
    }
}




/* 커뮤니티 info*/
.community-info-wrap{
    padding-top:80px;
    padding-bottom:200px;
    background-color:#eee;
}

.back-btn{
    width:100%;
    display:flex;
    justify-content: flex-end;
    margin-bottom:20px;
}
.back-btn button{
    color:#444;
    font-size:16px;
    border:none;
    background:unset;
    display:flex;
    align-items:center;
    cursor: pointer;

}
.back-btn button span{
    display:flex;
    align-items: center;
    justify-content: center;
    width:30px;
    height:30px;
    border-radius: 50%;
    background-color:#222;
    margin-right:10px;
}
.my-info-list > div{
    background-color:#fff;
    border-radius: 10px;
    margin-bottom:30px;
}
.my-info{
    padding:50px 50px;
    display:flex;
}
.my-info .img-area{
    width:165px;
    height:165px;
    overflow: hidden;
    border-radius: 50%;
    margin-right:35px;
}
.my-info .img-area img{
    width:100%;
    height:100%;
    object-fit: cover;
}
.my-info .text-area{
    width:calc(100% - 250px);
}
.my-info .text-area h4{
    font-size:22px;
    color:#222;
    font-weight:700;
    margin-bottom:18px;
    margin-top:15px;
}
.my-info .text-area span{
    font-size:15px;
    color:#666;
    font-weight:400;
    margin-bottom:18px;
    display:block;
}
.my-info .text-area p{
    font-size:15px;
    color:#444;
    line-height:30px;
}

.my-badge{
    padding:50px 50px;
}
.my-badge .title{
    display:flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom:70px;
}
.my-badge .title h4{
    font-size:26px;
    color:#222;
    font-weight:700;
}
.my-badge .title button{
    background:none;
    border:none;
    color:#444;
    font-size:15px;
    font-weight:400;
    display:flex;
    align-items: center;
    cursor: pointer;
}
.my-badge .title button img{
    margin-right:5px;
    width:13px;
    height:13px;
}
.my-badge-list ul{
    display:flex;
    text-align: center;
    overflow: hidden;
}
.my-badge-list ul li{
    min-width:calc(16.6% - 16.6px);
    width:calc(16.6% - 16.6px);
    margin-right:20px;
}
.my-badge-list ul li:last-child{
    margin-right:0;
}
.my-badge-list ul li .img-area{
    width:100%;
    aspect-ratio: 1/1;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom:15px;
}
.my-badge-list ul li .img-area img{
    width:100%;
    height:100%;
    object-fit: cover;
}
.my-badge-list ul li p{
    padding:0 10px;
    font-size:15px;
    color:#444;
    line-height:1.4;
}

.my-mission{
    padding:50px 50px;
}
.my-mission .title{
    display:flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom:50px;
}
.my-mission .title h4{
    font-size:26px;
    color:#222;
    font-weight:700;
}
.my-mission .title button{
    background:none;
    border:none;
    color:#444;
    font-size:15px;
    font-weight:400;
    display:flex;
    align-items: center;
    cursor: pointer;
}
.my-mission .title button img{
    margin-right:5px;
    width:13px;
    height:13px;
}
.my-mission-list ul{
    display:flex;
    overflow: hidden;
}
.my-mission-list ul li{
    min-width: calc(20% - 19.2px);
    width: calc(20% - 19.2px);
    margin-right:24px;
}
.my-mission-list ul li:last-child{
    margin-right:0;
}
.my-mission-list ul li .img-area{
    width:100%;
    aspect-ratio: 1/1;
    overflow: hidden;
    margin-bottom:10px;
}
.my-mission-list ul li .img-area img{
    width:100%;
    height:100%;
    object-fit: cover;
}
.my-mission-list ul li p{
    font-size:15px;
    color:#444;
    line-height:23px;
}

.my-link{
    padding:40px 50px;
    display:flex;
    align-items: center;
    position:relative;
}
.my-link h4{
    font-size:26px;
    color:#222;
    font-weight:700;
    margin-right:45px;
    width:114px;
}
.my-link input{
    border:none;
    border-bottom:1px solid #ccc;
    width:calc(100% - 45px - 114px);
    height:50px;
    padding:0 10px;
}
.my-link input::placeholder{
    font-size:16px;
    color:#333;
}
.my-link span{
    position:absolute;
    top:50%;
    right:50px;
    transform: translateY(-50%);
    width:34px;
    height:34px;
    display:flex;
    align-items: center;
    justify-content: center;
    background-color:#eee;
    border-radius: 50%;
    cursor: pointer;
}

@media screen and (max-width:1024px) {
    .my-info .text-area{
        width:calc(100% - 200px);
    }
    .my-badge-list ul li{
        min-width:calc(20% - 16px);
        width:calc(20% - 16px);
        text-align: center;
    }
    .my-badge-list ul li:nth-child(5){
        margin-right:0;
    }
    .my-badge-list ul li:nth-child(6){
        display:none;
    }
    .my-badge-list ul li .img-area{
        width:100%;
        height:unset;
        aspect-ratio: 1/1;
    }
    .my-mission-list ul li{
        min-width:calc(25% - 18px);
        width:calc(25% - 18px);
    }
    .my-mission-list ul li:nth-child(4){
        margin-right:0;
    }
    .my-mission-list ul li:nth-child(5){
        display:none;
    }
    .my-mission-list ul li .img-area{
        width:100%;
        height:unset;
        aspect-ratio: 1/1;
    }
}
@media screen and (max-width:768px) {
    .community-info-wrap{
        padding-top:40px;
        padding-bottom:80px;
    }
    .my-info{
        padding:40px 20px;
        flex-direction: column;
        align-items: center;
    }
    .my-info .img-area{
        margin-right:0;
    }
    .my-info .img-area{
        width:64%;
        height:unset;
        aspect-ratio: 1/1;
    }
    .my-info .text-area{
        width:100%;
        text-align: center;
    }
    .my-info-list > div{
        padding:40px 20px;
    }
    .my-badge .title{
        margin-bottom:55px;
    }
    .my-badge-list ul{
        flex-wrap: wrap;
    }
    .my-badge-list ul li{
        min-width:calc(50% - 10px);
        width:calc(50% - 10px);
        margin-right:20px;
        margin-top:30px;
    }
    .my-badge-list ul li:nth-child(-n+2){
        margin-top:0;
    }
    .my-badge-list ul li:nth-child(even){
        margin-right:0;
    }
    .my-badge-list ul li:nth-child(5){
        display:none;
    }
    .my-mission-list ul{
        flex-wrap: wrap;
    }
    .my-mission-list ul li{
        min-width:calc(50% - 10px);
        width:calc(50% - 10px);
        margin-right:20px;
        margin-top:30px;
        text-align: center;
    }
    .my-mission-list ul li:nth-child(-n+2){
        margin-top:0;
    }
    .my-mission-list ul li:nth-child(even){
        margin-right:0;
    }
    .my-link{
        flex-direction: column;
        align-items: flex-start;
    }
    .my-link h4{
        width:100%;
        margin-right:0;
        margin-bottom:20px;
    }
    .my-link input{
        width:100%;
    }
    .my-link span{
        top:unset;
        bottom:52px;
        right:20px;
        transform: unset;
    }
}












/* 커뮤니티 배지현황*/
.badge-present{
    padding:50px 50px;
    background-color:#fff;
    border-radius: 10px;
}
.badge-present ul {
    width:100%;
    display:grid;
    grid-template-columns: repeat(5, calc(20% - 36px));
    grid-column-gap: 45px;
    grid-row-gap: 25px;
    margin-bottom:120px;
    min-height:377px;
}
.badge-present ul li{
    text-align: center;
}
.badge-present ul li .img-area{
    width:144px;
    height:144px;
    overflow: hidden;
    margin-bottom:10px;
}
.badge-present ul li .img-area img{
    width:100%;
    height:100%;
    object-fit: cover;
}
.badge-present ul li p{
    font-size:16px;
    color:#444;
    padding:0 10px;
}
.badge-present .title{
    font-size:26px;
    color:#222;
    font-weight:700;
    margin-bottom:50px;
}
.badge-present .small-title{
    font-size:20px;
    color:#222;
    font-weight:600;
    margin-bottom:40px;
}

@media screen and (max-width:1024px) {
    .badge-present ul {
        grid-template-columns: repeat(4, calc(25% - 18.75px));
        grid-column-gap: 25px;
        grid-row-gap: 25px;
        margin-bottom: 120px;
    }
    .badge-present ul li .img-area{
        width:100%;
        height:unset;
        aspect-ratio: 1/1;
    }
}

@media screen and (max-width:768px) {
    .badge-present{
        padding:40px 20px;
    }
    .badge-present ul {
        grid-template-columns: repeat(2, calc(50% - 10px));
        grid-column-gap: 20px;
        grid-row-gap: 30px;
        margin-bottom: 60px;
        min-height:200px;
    }
}











/* 커뮤니티 미션현황*/
.mission-present{
    padding:50px 50px;
    background-color:#fff;
    border-radius: 10px;

}
.mission-present .common-list{
    margin-bottom:40px;
}
.mission-present .title{
    font-size:26px;
    font-weight:700;
    color:#222;
    margin-bottom:50px;
}
.mission-present-list .list-wrap .img-area{
    width:100%;
    aspect-ratio: 1/1;
    margin-bottom:10px;
}
.mission-present-list .list-wrap .text-area h5{
    font-size:18px;
    color:#444;
    line-height:23px;
    height:46px;
    font-weight:400;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-word;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-bottom:10px;
}
.mission-present-list .list-wrap .text-area p{
    font-size:15px;
    color:#888;
    font-weight:300;
}
@media screen and (max-width:768px) {
    .mission-present{
        padding:40px 20px;
    }
}















/* 커뮤니티 미션현황*/
.program-swiper{
    margin-bottom:70px;
}
.program-wrap .tab{
    padding:0 20px;
}

.radio-wrap{
    padding:20px 0;
    border-top:1px solid #aaa;
    border-bottom:1px solid #aaa;
}
.radio-wrap{
    display:flex;
    align-items: center;
    justify-content: center;
}
.radio-wrap div{
    margin-right:20px;
}
.radio-wrap div:last-child{
    margin-right:0;
}
.radio-wrap div input{
    display:none;
}
.radio-wrap div label{
    cursor: pointer;
    display:flex;
    align-items: center;
    font-size:20px;
    font-weight:600;
    color:#222;
}
.radio-wrap div label span{
    display:block;
    width:16px;
    height:16px;
    border-radius: 50%;
    background-color:#dcdcdc;
    border:1px solid #000;
    margin-right:8px;
}
.radio-wrap div input:checked ~ label span{
    background-color:#05A54B;
}

.program_contents_list{
    cursor: pointer;
}
.program_contents_list_wrap {
    margin: 50px 0 200px 0;
}
.program_contents_list_box {
    display: grid;
    grid-template-columns: repeat(4, calc(25% - 21.75px));
    grid-column-gap: 29px;
    grid-row-gap: 50px;
}
.program_contents_list:hover .product_img img {
    transform: scale(1.1);
}
.product_img {
    width: 100%;
    aspect-ratio: 1/1;
    overflow: hidden;
    border: 1px solid #000;
    max-width: unset;
    max-height: unset;
}
.product_img img {
    display: block;
    width: 100%; height: 100%;
    object-fit: cover;
    transition: all 0.3s;
}
.category_box {
    display: flex;
    margin: 20px 0 15px 0;
}
.category {
    padding:3px 15px;
    border-radius: 50px;
    border: 1px solid #222;
    color: #FFF;
    font-size: 15px;
    font-weight: 600;
    line-height: unset;
    text-align: center;
}
.category_blak {
    border: 1px solid #222;
    background: #FFF;
    color: #222;
}
.contents_list_title {
    font-size: 20px;
    font-weight: 600;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

@media screen and (max-width:1024px) {
    .circle_tab {
        overflow-x: scroll;
    }
    .program_contents_list_box {
        grid-template-columns: repeat(2, calc(50% - 10px));
        grid-column-gap: 20px;
        grid-row-gap: 50px;
    }
}
@media screen and (max-width:768px) {
    .program-swiper{
        margin-bottom:55px;
    }
    .radio-wrap div label span{
        width:20px;
        height:20px;
    }
    .program-swiper .text-area h4,
    .program-swiper .text-area h6,
    .program-swiper .text-area p{
        text-align: center;
    }
    .program_contents_list_wrap{
        margin-bottom:60px;
    }
    .program_contents_list_box {
        grid-template-columns: repeat(1, calc(100%));
        grid-column-gap: 0px;
        grid-row-gap: 40px;
    }
}















/*강화유니버스 커뮤니티 프로그램_상세*/
.program_detail_contents {
    position: relative;
    display: flex;
}
.program_detail_info {
    width: calc(100% - 442px - 60px);
    padding: 90px 0 200px 0;
}
.program_detail-swiper.swiper{
    width:840px;
    height:840px;
    overflow: hidden;
    margin-right: 60px;
    margin-bottom:50px;
}
.program_detail-swiper.swiper .swiper-slide{
    width:100%;
    height:unset;
    aspect-ratio: 1/1;
    overflow: hidden;
    border:1px solid #000;
}
.program_detail-swiper.swiper .swiper-slide img{
    width:100%;
    height:100%;
    object-fit: cover;
}
.program_detail-swiper .swiper-button-prev{
    position: absolute;
    top: 50%; left:10px; transform: translateY(-50%);
    width: 36px; height: 36px;
    background: url(/images/icon/slide_arrow02_prev.svg) center center no-repeat;
}
.program_detail-swiper .swiper-button-prev::after,
.program_detail-swiper .swiper-button-next::after{
    content:'';
}
.program_detail-swiper .swiper-button-next {
    position: absolute;
    top: 50%; right: 10px; transform: translateY(-50%) rotate(180deg);;
    width: 36px; height: 36px;
    background: url(/images/icon/slide_arrow02_prev.svg) center center no-repeat;
}
.program_detail_fix_box {
    position: relative;
    top: 0; right: 0;
    padding: 0 0 0 60px;
    border-left: 1px solid #aaa;
    background: #fff;
}
.program_detail_fix {
    width: 442px;
    position: sticky;
    top: 0px;
    padding: 90px 0 50px 0;
}
.program_detail_category {
    color: #05A54B;
    font-size: 18px;
}
.program_detail_title {
    color: #222;
    font-size: 30px;
    font-weight: 700;
    margin: 30px 0;
}
.program_detail_ex {
    color: #333;
    line-height: 28px;
}
.program_detail_price {
    color: #222;
    font-size: 18px;
    font-weight: 500;
    margin: 42px 0 26px 0;
}
.program_detail_day_time {
    color: #444;
    line-height: 30px;
}

.program_detail_reservation_btn {
    height: 53px;
    background: #222;
    color: #FFF;
    font-size: 18px;
    font-weight: 600;
    padding: 16px 0;
    box-sizing: border-box;
    text-align: center;
    margin: 63px 0 28px 0;
    cursor: pointer;
}

@media (hover:hover) {
    .program_detail_reservation_btn:hover{
        border:1px solid #222;
        color:#222;
        background-color:#fff;
    }
}

.program_detail_caution_txt {
    height: 56px;
    border-radius: 5px;
    border: 1px dashed #7D7D7D;
    color: #222;
    text-align: center;
    font-size: 15px;
    padding: 20px 0;
    box-sizing: border-box;
}

.program_detail_con{
    padding:35px 20px 35px 0;
    border-top:1px solid #aaa;
}
.program_detail_con h5{
    font-size:30px;
    font-weight:700;
    color:#222;
    margin-bottom:30px;
}
.program_detail_con .con-list li{
    position:relative;
    padding-left:10px;
    padding-right:60px;
    font-size:16px;
    color:#333;
    line-height:1.4;
    margin-bottom:10px;
}
.program_detail_con .con-list li::before{
    content:'';
    width:4px;
    height:4px;
    background-color:#333;
    position:absolute;
    top:8px;
    left:0;
    border-radius: 50%;
}
.program_detail_con .con-list li:last-child{
    margin-bottom:0;
}
.program_detail_con pre {
    white-space: pre-wrap;
}
.other-program-swiper02-wrap{
    padding:0 50px;
    position:relative;
}
.other-program-swiper02.swiper{
    width:730px;
    margin-right:60px;
}

.other-program-swiper02.swiper .swiper-wrapper .swiper-slide{
    overflow: hidden;
    aspect-ratio: 1/1;
    cursor: pointer;
    border:1px solid #000;
}

.other-program-swiper02.swiper .swiper-wrapper .swiper-slide img{
    width:100%;
    height:100%;
    object-fit: cover;
}
.other-program-swiper02-wrap .swiper-button-prev{
    left:0px;
    width:36px;
    height:36px;
    background: url(/images/icon/slide_arrow02_prev.svg) center center no-repeat;
}
.other-program-swiper02-wrap .swiper-button-prev:after{
    content:'';
}
.other-program-swiper02-wrap .swiper-button-next{
    right:50px;
    width:36px;
    height:36px;
    background: url(/images/icon/slide_arrow02_prev.svg) center center no-repeat;
    transform: rotate(180deg);
}
.other-program-swiper02-wrap .swiper-button-next::after{
    content:'';
}


@media screen and (max-width:1400px) {
    .program_detail-swiper.swiper{
        width:calc(100% - 40px);
        height:unset;
    }
    .program_detail_fix_box{
        padding-left:40px;
    }
    .other-program-swiper02-wrap{
        padding:0 80px 0 50px;
    }
    .other-program-swiper02.swiper{
        width:100%;
    }
    .other-program-swiper02-wrap .swiper-button-next{
        right:30px;
    }
}



@media screen and (max-width:1024px) {
    .program_detail_info {
        width:60%;
        padding: 90px 0 200px 0;
    }
    .program_detail-swiper.swiper{
        margin-right:40px;
    }

    .program_detail_con .con-list li{
        padding-right:40px;
    }
    .program_detail_fix {
        width:100%;
    }
    .program_detail_fix_box {
        width: 40%;
        padding-left:20px;
    }
    .program_detail_img_box {
        margin-right: 30px;
    }

    .program_info_contents {
        margin-right: 30px;
    }
}

@media screen and (max-width:768px) {
    .program_detail_fix{
        padding-top:60px;
    }
    .program_detail_contents {
        flex-wrap: wrap;
    }
    .program_detail_info {
        width: 100%;
        order: 2;
        padding: 0 0 100px 0;
    }
    .program_detail_fix_box {
        width: 100%;
        order: 1;
        padding: 0;
        border: none;
    }
    .program_detail_fix_box.mo-block .program_detail_fix{
        padding-top:0;
    }
    .program_detail_fix {
        position: unset;
        width: 100%;
    }
    .program_detail_img_box {
        margin-right: 0px;
    }
    .program_info_contents {
        margin-right: 0px;
    }
    .program_detail-swiper.swiper{
        margin:60px 0 20px 0;
        width:100%;
    }
    .program_detail_title{
        font-size:20px;
        margin-bottom:10px;
    }
    .program_detail_price{
        margin:30px 0 20px 0;
    }
    .program_detail_reservation_btn{
        margin:40px 0 20px 0;
    }
    .program_detail_con{
        padding-right:0;   
    }
    .program_detail_con .con-list li{
        padding-right:0;
    }
    .program_detail_con h5{
        font-size:24px;
        margin-bottom:20px;
    }
    .other-program-swiper02-wrap{
        padding:0 46px;
    }
    .other-program-swiper02-wrap .swiper-button-next{
        right:0;
    }
}





/* 프로그램 예약 상세 */
.program-view-wrap{
    padding-top:110px;
    padding-bottom:200px;
}

.people_select_box {
    width: 135px;
    height: 50px;
    border: 1px solid #CCC;
    padding: 0 15px;
    color: #333;
    font-family: Pretendard;
    font-size: 18px;
    margin-right: 20px;
    appearance:none;
    background:url(/images/icon/select_drop.svg) no-repeat right 15px center;
}
.program-view-wrap .calender_gray_box{
    width:calc(100% - 440px);
}
.program-view-wrap .how_many_people{
    width:440px;
}
.program-view-wrap .stay_info_img{
    width:280px;
    height:unset;
    aspect-ratio:1/1 ;
}
.program-view-wrap .final_payment_details_img{
    width:140px;
    height:unset;
    aspect-ratio: 1/1;
}
.program-view-wrap .final_payment_details_txt{
    width:calc(100% - 140px - 35px);
}
.program-view-wrap .final_payment_details_txt span{
    font-size:18px;
    color:#05A54B;
    margin-bottom:10px;
    display:block;
}
@media screen and (max-width:1024px) {
    .program-view-wrap .calender_gray_box{
        width:60%;
    }
    .program-view-wrap .how_many_people{
        width:40%;
        padding:30px 30px;
    }
}
@media screen and (max-width:768px) {
    .program-view-wrap{
        padding-top:60px;
        padding-bottom:80px;
    }
    .program-view-wrap .select_schedule_people{
        flex-direction: column;
    }
    .program-view-wrap .calender_gray_box{
        width:100%;
    }
    .program-view-wrap .stay_info_room_txt{
        margin-right:0;
    }

    .program-view-wrap .how_many_people{
        width:100%;
        padding:40px 0 30px 0;
    }
    .program-view-wrap .select_schedule_title{
        margin-bottom:20px;
    }
    .select_schedule_title{
        font-size:16px;
        text-align: center;
    }
    .program-view-wrap .people_select_box{
        width:80%;
    }
    .next_btn{
        width:100%;
        margin-top:60px;
    }
    .program-view-wrap .final_payment_details_img{
        width:100%;
    }
    .program-view-wrap .final_payment_details_txt{
        width:100%;
    }
}





/* b2b 문의하기 / 비즈니스 문의하기 */
.inquiry-swiper .text-area{
    top:45%;
    transform: translate(-50%, -50%);
}
.inquiry-swiper .text-area h4{
    text-align: center;
}
.down-wrap{
    background-color:#FFC960;
    border-bottom:1px solid #222;
}
.down-con{
    padding:50px 0;
    display:flex;
    align-items: center;
    justify-content: space-between;
}
.down-con h5{
    font-size:28px;
    font-weight:600;
    color:#222;
}
.down-con a{
    display:flex;
    align-items: center;
    font-size:22px;
}
.down-con a span{
    display:flex;
    align-items: center;
    justify-content: center;
    width:30px;
    height:30px;
    border-radius: 50%;
    background-color:#222;
    margin-left:10px;
}

.inquiry-wrap{
    margin-top:80px;
    margin-bottom:200px;
}

.inquiry-title{
    font-size:30px;
    color:#222;
    font-weight:700;
    margin-bottom:60px;
}
.inquiry-list{
    display:flex;
    justify-content: space-between;
}
.inquiry-item{
    margin-bottom:60px;
    width:600px;
}
.inquiry-item h6{
    font-size:18px;
    color:#222;
    font-weight:600;
    margin-bottom:15px;
}
.inquiry-item input,
.inquiry-item select{
    height:40px;
    border:none;
    border-bottom:1px solid #aaa;
    width:100%;
    padding:0 10px;
}

.inquiry-item.w-100{
    width:100%;
}
.inquiry-item textarea{
    width:100%;
    padding:15px 15px;
    width:100%;
    border:1px solid #aaa;
    height:200px;
}
.inquiry-item select{
    -webkit-appearance: none;  /* 크롬 화살표 없애기 */
    -moz-appearance: none; /* 파이어폭스 화살표 없애기 */
    appearance: none;  /* 화살표 없애기 */
    background:url(/images/icon/meeting-arrow.svg) center right 10px no-repeat;
    color:#222;
}
.inquiry-item select option{
    color:#aaa;
}
.inquiry-agree-area h5{
    font-size:18px;
    color:#444;
    font-weight:700;
    margin-bottom:25px;
}
.inquiry-agree-area .kor-la h6{
    font-size:16px;
    color:#333;
    font-weight:400;
    margin-bottom:5px;
}
.inquiry-agree-area .kor-la p{
    font-size:16px;
    color:#333;
    font-weight:400;
    position:relative;
    padding-left:15px;
    margin-bottom:8px;
}
.inquiry-agree-area .kor-la p:before{
    content:'';
    position:absolute;
    top:8px;
    left:5px;
    width:3px;
    height:3px;
    background-color:#333;
    border-radius: 50%;
}
.inquiry-agree-area .eng-la h6{
    font-size:15px;
    color:#aaa;
    font-weight:400;
    margin-bottom:5px;
}
.inquiry-agree-area .eng-la p{
    font-size:15px;
    color:#aaa;
    font-weight:400;
    position:relative;
    padding-left:15px;
}
.inquiry-agree-area .eng-la p:before{
    content:'';
    position:absolute;
    top:8px;
    left:5px;
    width:3px;
    height:3px;
    background-color:#aaa;
    border-radius: 50%;
}
.inquiry-agree-area .checkbox-area{
    display:flex;
    align-items: center;
    margin-bottom: 100px;
    margin-top:25px;
}
.inquiry-agree-area .checkbox-area input{
    width:24px;
    height:24px;
    margin-right:10px;
}
.inquiry-agree-area .checkbox-area p{
    color:#444;
    font-size:15px;
}

.inquiry-btn{
    text-align: center;
}
.inquiry-btn button{
    cursor: pointer;
    padding:15px 90px;
    color:#333;
    font-size:18px;
    font-weight:600;
    background:none;
    border:1px solid #333;
}
/*혜진*/
@media screen and (max-width:1280px) {
    .inquiry-item {
        width: calc(48%);
    }
}

@media screen and (max-width:1024px) {
    .down-con h5{
        font-size: 20px;
    }
}

@media screen and (max-width:768px) {
    .inquiry-swiper .text-area{
        top:50%;
        width:calc(100% - 40px);
    }
    .inquiry-wrap{
        margin-top:40px;
        margin-bottom:100px;
    }
    .inquiry-title{
        margin-bottom:30px;
    }
    .down-con h5{
        font-size: 23px;
    }
    .down-con a{
        font-size: 18px;
    }
    .down-con {
        flex-direction: column;
        align-items: flex-start;
        padding: 30px 0;
    }
    .down-con a{
        margin-top: 10px;
    }

    .down-con a span{
        width: 25px;
        height: 25px;
    }
    .down-con a span img{
        width: 30%;
    }
    .inquiry-list{
        flex-direction: column;
    }
    .inquiry-item {
        width: calc(100%);
        margin-bottom: 30px;
    }
    .inquiry-agree-area .checkbox-area{
        margin-bottom:40px;
    }
    .inquiry-btn button{
        width:100%;
        padding:0;
        height:50px;
        line-height:50px;
    }
}









/* 마이페이지 */
.mypage-banner {
    width:100%;
    height:440px;
    background:url(/images/mypage-bg.jpg) center center no-repeat;
    background-repeat:no-repeat;
    background-position: center center;
    background-size: cover;
}
.mypage-banner .text-area{
    padding-top:150px;
}
.mypage-banner .text-area h4{
    font-family: 'tenada';
    font-size:80px;
    color:#fff;
    text-shadow: 3px 3px 0 #000;
}
.my-page-wrap{
    background-color: #F3F3F3;
}
.my-page {
    display:flex;
    transform: translateY(-140px);
}
.my-page .my-profile-wrap{
    width:326px;
    margin-right:50px;
}
.my-page .my-profile-wrap .my-profile{
    background-color:#fff;
    border-radius: 10px;
    text-align: center;
    padding:80px 10px 26px 10px;
    position:relative;
}
.my-page .my-profile-wrap .my-profile button{
    position:absolute;
    top:26px;
    right:25px;
    border:none;
    background-color:#E0E0E0;
    display:flex;
    align-items: center;
    justify-content: center;
    width:32px;
    height:32px;
    border-radius: 50%;
    text-align: right;
    cursor: pointer;
}
.my-page .my-profile-wrap .my-profile .img-area{
    width:165px;
    height:165px;
    overflow: hidden;
    border-radius: 50%;
    margin:0 auto;
    margin-bottom:30px;
}
.my-page .my-profile-wrap .my-profile .img-area img{
    width:100%;
    height:100%;
    object-fit: cover;
}
.my-page .my-profile-wrap .my-profile h4{
    font-size:22px;
    color:#222;
    font-weight:700;
    margin-bottom:10px;
}
.my-page .my-profile-wrap .my-profile p{
    font-size:15px;
    color:#666;
}
.my-page .my-profile-list{
    width:1024px;
}
.my-page .my-profile-list > div{
    background-color: #fff;
    border-radius: 10px;
    margin-bottom: 30px;
}
.my-page .my-profile-list > div:last-child{
    margin-bottom:0;
}
.my-profile-list .list-item{
    display:flex;
    align-items: center;
    padding:40px 50px;
}
.my-profile-list .list-item .title{
    font-size:26px;
    color:#222;
    font-weight:700;
    word-break: break-all;
}
.my-profile-list .sumsari{
    justify-content: space-between;
}
.my-profile-list .sumsari div{
    display:flex;
    align-items: center;
}
.my-profile-list .sumsari div h4{
    margin-right:60px;
}
.my-profile-list .sumsari div p{
    font-size:22px;
    color:#333;
    font-weight:400;
}
.my-profile-list .sumsari div strong{
    font-weight:700;
    text-decoration: underline;
}
.my-profile-list .sumsari button{
    cursor: pointer;
    width:125px;
    height:35px;
    line-height:35px;
    background-color:#fff;
    border:1px solid #222;
    color:#222;
    border-radius: 4px;
}
.my-profile-list .point-wrap{
    justify-content: space-between;
}
.my-profile-list .point-wrap .point-btn{
    display: flex;
    align-items: flex-end;
    font-size:18px;
    color:#444;
    cursor: pointer;
}
.my-profile-list .point-wrap .point-btn p{
    margin-bottom:10px;
}
.my-profile-list .point-wrap .point-btn strong{
    font-size:40px;
    color:#333;
    font-weight:700;
    margin-right:10px;

}
.my-profile-list .my-history,
.my-profile-list .my-inqury,
.my-profile-list .my-write{
    justify-content: space-between;
}

.my-profile-list .my-history span,
.my-profile-list .my-inqury span,
.my-profile-list .my-write span{
    display:flex;
    align-items: center;
    justify-content: center;
    width:44px;
    height:44px;
    background-color:#222;
    cursor: pointer;
    border-radius: 50%;
}
.my-introduce{
    justify-content: space-between;
}
.my-introduce div input{
    width:500px;
    border:none;
    border-bottom:1px solid #222;
    height:45px;
    margin-right:20px;
    padding:0 10px;
}
.my-introduce div button{
    cursor: pointer;
    background-color:#fff;
    border:1px solid #222;
    color:#222;
    width:80px;
    height:35px;
    border-radius: 4px;
}

@media screen and (max-width:1400px) {
    .my-page .my-profile-wrap{
        width:23%;
    }
    .my-page .my-profile-list{
        width:calc(77% - 50px);
    }
    .my-introduce div input{
        width:400px;
    }

}
@media screen and (max-width:1280px) {
    .my-profile-list .sumsari div p{
        font-size:18px;
    }
    .my-introduce div{
        width:calc(100% - 140px);
    }
    .my-introduce div input{
        width:calc(100% - 104px);
    }
}
@media screen and (max-width:1024px) {
    .my-page {
        flex-direction: column;
    }
    .my-page .my-profile-wrap{
        width:100%;
        margin-bottom:20px;
    }
    .my-page .my-profile-list{
        width:100%;
    }
}
@media screen and (max-width:768px) {
    .mypage-banner{
        height:300px;
    }
    .mypage-banner .text-area{
        padding-top:80px;
    }
    .mypage-banner .text-area h4{
        font-size:48px;
    }
    .my-page .my-profile-list > div{
        padding:40px 20px;
        margin-bottom:20px;
    }
    .my-profile-list .list-item .title{
        font-size:24px;
        width:calc(100% - 51px);
    }
    .my-profile-list .sumsari{
        flex-direction: column;
        align-items: flex-start;
    }
    .my-profile-list .sumsari div{
        flex-direction: column;
        align-items: flex-start;
    }
    .my-profile-list .sumsari div h4{
        margin-bottom:20px;
    }
    .my-profile-list .sumsari div p{
        margin-bottom:30px;
        line-height:1.4;
    }
    .my-profile-list .sumsari button{
        width:100%;
        height:45px;
    }
    .my-profile-list .my-history .title,
    .my-profile-list .my-inqury .title,
    .my-profile-list .my-write .title
    {
        width:calc(100% - 51px);
    }
    .my-profile-list .my-badge .title{
        font-size:24px;

    }
    .my-profile-list .point-wrap .point-btn strong{
        font-size:32px;
        margin-right:4px;
    }
    .my-profile-list .my-history span, .my-profile-list .my-inqury span, .my-profile-list .my-write span{
        width:36px;
        height:36px;
        margin-left:15px;
    }
    .my-profile-list .my-history span img,
    .my-profile-list .my-inqury span img,
    .my-profile-list .my-write span img{
        width:8px;
        height:18px;
    }
    .my-profile-list .my-introduce{
        flex-direction: column;
        align-items: flex-start;
    }
    .my-profile-list .my-introduce .title{
        margin-bottom:20px;
    }
    .my-profile-list .my-introduce div{
        width:100%;
    }
    .my-profile-list .my-introduce div input{
        width:100%;
        margin-bottom:30px;
    }
    .my-profile-list .my-introduce div button{
        width:100%;
        height:45px;
    }
}









/* 프로필 설정 */
.mypage-banner02 {
    width:100%;
    height:440px;
    background:url(/images/mypage-bg02.jpg) center center no-repeat;
    background-repeat:no-repeat;
    background-position: center center;
    background-size: cover;
}
.mypage-banner02 .text-area{
    padding-top:120px;
}
.mypage-banner02 .text-area h4{
    font-family: 'tenada';
    font-size:50px;
    color:#fff;
    text-align: center;
}
.mypage_close_contents {
    width: 100%;
    background: #FFF;
    border-radius: 10px;
    padding: 60px 70px; box-sizing: border-box;
}
.mypage_close_contents_box{
    transform: translateY(-220px);
    width:1200px;
    margin:0 auto;
}
.mypage_close_contents_box .close-btn{
    width: 37px;
    height: 37px;
    background: url(/images/icon/popup_close.svg);
    border: 0; position: absolute;
    right: 0px;top: -90px;
    cursor: pointer;
}
.my_profile_img_change {
    display: flex;
    align-items: center;
    margin-bottom: 48px;
}
.my_profile_setting_img {
    width: 165px;
    height: 165px;
    border-radius: 50%;
    border: 1px solid #D9D9D9;
    overflow: hidden;
}
.my_profile_setting_img img {
    width: 100%; height: 100%;
    object-fit: cover;
}
.img_change_btn {
    padding:8px 20px;
    width:unset;
    height:unset;
    line-height:unset;
    border-radius: 4px;
    border: 1px solid #222;
    background: #FFF;
    color: #222;
    font-size: 16px;
    font-weight: 400;
    margin-left: 40px;
    text-align: center;
}
.img_change_btn:hover {
    cursor: pointer;
    background: #f5f5f5;
}
.mypage_setting_list {
    margin-bottom: 40px;
}
.pw_change_btn_box {
    position: relative;
}
.pw_change_btn_box input {
    padding-right: 129px;
    box-sizing: border-box;
}
.pw_change_btn {
    position: absolute;
    top: 0; right: 0;
    width: 109px;
    height: 28px;
    border-radius: 2px;
    background: #373737;
    border: none;
    color: #FFF;
    font-size: 15px;
    line-height: 28px;
    text-align: center;
}
.pw_change_btn:hover {
    cursor: pointer;
    opacity: 0.9;
}
.mypage_setting_grid_box {
    display: grid;
    grid-template-columns: repeat(3, calc(33.3% - 22px));
    grid-column-gap: 33px;
}
#men, #women, #no {display: none;}
#men:checked ~ .mypage_setting_men{color: #fff; background: #222;}
#women:checked ~ .mypage_setting_women{color: #fff; background: #222;}
#no:checked ~ .mypage_setting_no{color: #fff; background: #222;}

.mypage_setting_gender {
    height: 40px;
    border: 1px solid #AAA;
    background: #F9F9F9;
    color: #888;
    font-size: 15px;
    line-height: 40px;
    text-align: center;
}
.mypage_setting_gender:hover {
    cursor: pointer;
    color: #222;
    border: 1px solid #222;
}
.mypage_setting_flex_box {
    display: flex;
}
.mypage_setting_flex_box .b2b_contact_us_form_list_select {
    width: 397px;
    margin-right: 32px;
}
.mypage_setting_flex_box .b2b_contact_us_form_list_input {
    width: calc(100% - 397px - 32px);
}

.line_small_btn {
    width: fit-content;
    height: unset;
    padding: 16px 20px;
    box-sizing: border-box;
    border-radius: 50px;
    border: 1px solid #444;
    background: #FFF;
    color: #222;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    margin: 0 auto;
    cursor: pointer;
}
.pw_popup {
    position: fixed;
    width: 716px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    padding: 40px 50px;
    box-sizing: border-box;
    display: none;
    background: #fff;
    z-index: 9999999999999999;
    border-radius: 10px;
}
.pw_popup_contents {
    width: 100%; height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}
.pw_popup_contents>div {
    width: 100%;
}
.pw_popup_line {
    border-top: 1px solid #000;
    margin-top: 26px;
    padding-top: 63px;
    margin-bottom: 60px;
}
.pw_popup_line_contents {
    display: flex;
    align-items: center;
}
.pw_popup_line_contents_mg_b {
    margin-bottom: 40px;
}
.pw_popup_line_contents .b2b_contact_us_form_list_title {
    min-width: 123px;
    margin-right: 60px;
    margin-bottom: 0;
}


.b2b_contact_us_form_list_title{
    margin-bottom:10px;
}
.b2b_contact_us_form_list_input{
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    border: none;
    border-bottom: 1px solid #aaa;
    color: #888;
    font-family: Pretendard;
    font-size: 15px;
    font-weight: 400;
}
.b2b_contact_us_form_list_select{
    width: 100%;
    padding: 10px;
    border: none;
    border-bottom: 1px solid #aaa;
    color: #888;
    font-family: Pretendard;
    font-size: 15px;
    font-weight: 400;
    appearance: none;
    background: url(/images/icon/select_drop.svg) no-repeat right 8px center;
}
.b2b_contact_us_form_list_select  option[disabled]{display: none;}
.b2b_contact_us_form_list_title {
    color: #222;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
}
.b2b_contact_us_info {
    color: #333;
    line-height: 26px;
    margin-bottom: 10px;
}

.live-area{
    display:flex;
    margin-bottom:20px;
}
.live-area .live-input{
    padding: 10px;
    box-sizing: border-box;
    border: none;
    border-bottom: 1px solid #aaa;
    color: #888;
    font-size: 15px;
    font-weight: 400;
    margin-right:20px;
    width:calc(100% - 150px);
}
.live-input02{
    padding: 10px;
    box-sizing: border-box;
    border: none;
    border-bottom: 1px solid #aaa;
    color: #888;
    font-size: 15px;
    width:100%;
    font-weight: 400;
}

@media screen and (max-width: 768px) {
    .live-area{
        flex-direction: column-reverse;
    }
    .live-area .check_btn{
        width:100%;
        margin-bottom:10px;
    }
    .live-area .live-input{
        width:100%;
        margin-right:0;
    }
}


@media screen and (max-width:1400px) {
    .mypage_close_contents_box{
        width:100%;
    }
}
@media screen and (max-width:768px) {
    .mypage-banner02{
        height:250px;
    }
    .mypage-banner02 .text-area{
        padding-top: 80px;
    }
    .mypage-banner02 .text-area h4 {
       font-size: 30px;
       text-align: left;
    }
    .mypage_close_contents_box{
        transform: translateY(-100px);
    }
    .mypage_close_contents_box .close-btn{
        top:-70px;
        background-size: 25px;
        width:25px;
        height: 25px;
    }
    .my_profile_img_change{
        flex-direction: column;
    }
    .img_change_btn{
        margin-left:0;
        width:100%;
        margin-top:20px;
    }
    .mypage_close_contents{
        padding:30px 20px;
    }
    .mypage_setting_grid_box{
        grid-template-columns: repeat(3, calc(33.3% - 6.66px));
        grid-column-gap: 10px;
    }
    .mypage_setting_flex_box{
        flex-direction: column;
    }
    .mypage_setting_flex_box .b2b_contact_us_form_list_select{
        width:100%;
        margin-right:0;
        margin-bottom:10px;
    }
    .mypage_setting_flex_box .b2b_contact_us_form_list_input{
        width:100%;
    }
    .pw_popup{
        width:calc(100% - 40px);
        padding:30px 20px;
    }


}













/* 나의 배지 현황 */
.mypage_close_contents .badge-present{
    padding:0;
}
.mypage_close_contents .badge-present ul{
    grid-template-columns: repeat(5, calc(20% - 48px));
    grid-column-gap: 60px;
    grid-row-gap: 25px;
}
@media screen and (max-width:1024px) {
    .mypage_close_contents .badge-present ul{
        grid-template-columns: repeat(4, calc(25% - 22.5px));
        grid-column-gap: 30px;
        grid-row-gap: 25px;
    }
}

@media screen and (max-width:768px) {
    .badge-present .title{
        margin-bottom:30px;
    }
    .mypage_close_contents .badge-present ul{
        grid-template-columns: repeat(2, calc(50% - 10px));
        grid-column-gap: 20px;
        grid-row-gap: 30px;
    }
    .badge-present .small-title{
        margin-bottom:30px;
    }

}


/* 나의 완료 미션 */
.mypage_close_contents .mission-present{
    padding:0;
}
.mypage_close_contents .mission-present-list .list-wrap{
    grid-template-columns: repeat(4, calc(25% - 18.75px));
    grid-column-gap: 25px;
    grid-row-gap: 25px;
}
@media screen and (max-width:1024px) {
    .mypage_close_contents .mission-present-list .list-wrap{
        grid-template-columns: repeat(3, calc(33% - 15px));
        grid-column-gap: 30px;
        grid-row-gap: 25px;
    }
}

@media screen and (max-width:768px) {
    .mission-present .title{
        margin-bottom:30px;
    }
    .mypage_close_contents .mission-present-list .list-wrap{
        grid-template-columns: repeat(2, calc(50% - 10px));
        grid-column-gap: 20px;
        grid-row-gap: 30px;
    }
    .mypage_close_contents .mission-present-list .list-wrap .text-area .view_day{
        flex-direction: column;
        align-items: flex-start;
        margin-top:5px;
    }
    .mypage_close_contents .mission-present-list .list-wrap .text-area .view_day .view_day_line{
        display:none;
    }
    .mypage_close_contents .mission-present-list .list-wrap .text-area p{
        margin-bottom:20px;
    }
}




/* 나의 섬살이 유형 */

.mypage-stayisland-title{
    font-size:22px;
    color:#333;
    font-weight:600;
    text-align: center;
}
.mypage_close_contents .stay_island_life_box{
    border-top:none;
    padding-top:50px;
    grid-template-columns: repeat(3, calc(33% - 26.66px));
    grid-column-gap: 40px;
    grid-row-gap: 40px;
}
@media screen and (max-width:1024px) {
    .mypage_close_contents .stay_island_life_box{
        grid-template-columns: repeat(2, calc(50% - 20px));
        grid-column-gap: 40px;
        grid-row-gap: 40px;
    }
}
@media screen and (max-width:768px) {
    .mypage_close_contents .stay_island_life_box{
        padding-top:30px;
        grid-template-columns: repeat(1, calc(100%));
        grid-column-gap: 0px;
        grid-row-gap: 40px;
    }
}



/*마이페이지_포인트*/
.my_point_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40px;
    border: 1px solid #000;
    border-radius: 10px;
}
.my_point {
    display: flex;
    align-items: center;
}
.my_point_title {
    color: #222;
    font-size: 26px;
    font-weight: 700;
}
.my_point_coin {
    display: flex;
    align-items: center;
}
.my_point_coin_flex {
    display: flex;
    align-items: center;
}
.my_point_coin_icon {
    min-width: 32px; height: 32px;
    background: url(/images/icon/my_point_coin_icon.svg);
    margin: 0 20px 0 40px;
}
.my_point_coin_txt {
    font-size: 40px;
    font-weight: 700;
    color: #222;
}
.my_point_line {
    width: 1px; height: 20px;
    border-right: 1px solid #444;
    margin: 0 20px;
}
.my_point_txt {
    font-size: 18px;
    font-weight: 500;
    color: #222;
}
.my_point_coin_green {
    font-size: 18px;
    font-weight: 600;
    color: #009944;
    margin-left: 20px;
}
.point_change {
    width: 147px; height: 35px;
    background: #222;
    color: #fff;
    border-radius: 4px;
    text-align: center;
    line-height: 35px;
    border: none;
    cursor: pointer;
    font-size:16px;
}
.point_change:hover {
    opacity: 0.9;
}
.my_point_ex_box {
    margin-top: 20px;
}
.my_point_ex {
    display: flex;
    align-items: center;
    color: #444;
    margin-bottom: 10px;
}
.my_point_ex_dot {
    min-width: 3px; height: 3px;
    background: #444;
    border-radius: 50%;
    margin: 0 10px;
}
.my_point_ex.red {
    color: #E83929;
}

.point_list_title_wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 17px;
}
.point_list_title {
    color: #222;
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 17px;
}
.point_list_box_question {
    display: flex;
    color: #444;
    cursor: pointer;
}
.point_list_box_question_mark {
    width: 17px; height: 17px;
    background: url(/images/icon/point_list_box_question_mark.svg);
    margin-left: 5px;
}

.mypage_border_list {
    display: flex;
    align-items: center;
    padding: 22px 0;
    border-bottom: 1px solid #aaa;
}
.mypage_border_list_day {
    min-width: 86px;
    margin-right: 60px;
    color: #888;
    font-size: 0.95em;
    text-align: center;
}
.mypage_border_list_day.expire{
    color: #E8392999;
}
.mypage_border_list_contents {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #333;
    font-size: 18px;
}
.mypage_border_list_contents span:last-child{
    padding-right:20px;
}
.mypage_border_list_contents span{
    width:calc(100% - 140px);
}
.mypage_border_list_contents span.point_color_blak,
.mypage_border_list_contents span.point_color_blue{
    width:110px;
}
.mypage_border_list_way{
    width:110px;
    text-align: center;
}
.mypage_border_list_situation{
    width:110px;
    text-align: center;
}
.point_color_blue {
    color: #056FB8;
    font-weight: 600;
}
.point_color_blak {
    color: #333;
    font-weight: 600;
}
.mypage_border_pay {
    display: flex;
    align-items: center;
}
.mypage_border_pay span {
    display: inline-block;
    width: 70px;
    text-align: center;
}
.mypage_border_pay_bold {
    color: #333;
    font-size: 18px;
    font-weight: 600;
}
.mypage_border_pay_mg_r {
    margin-right: 50px;
}
.mypage_border_pay_blue {
    color: #056FB8;
}
.mypage_border_pay_red {
    color: #E83929;
}
.point_popup_line {
    border-top: 1px solid #000;
    margin-top: 26px;
    padding-top: 63px;
    margin-bottom: 60px;
    color: #222;
    line-height: 28px;
    font-size: 18px;
}
.provision {
    padding: 50px 0;
}
.point_provision {
    border-bottom: 2px dashed #ccc;
}
.provision_title {
    font-size: 20px;
    font-weight: 700;
    color: #056FB8;
}
.provision_ex_box {
    margin-top: 20px;
}
.provision_ex {
    display: flex;
    align-items: center;
    color: #222;
    margin-bottom: 10px;
}
.provision_ex_dot {
    min-width: 3px; height: 3px;
    background: #222;
    border-radius: 50%;
    margin: 0 10px 0 0;
}
.bold_line_top_contents {
    border-top: 2px solid #000;
}

@media screen and (max-width:1280px) {
    .my_point_box{
        padding:40px 25px;
    }
    .my_point_coin_icon{
        margin:0 20px 0 20px;
    }
}

@media screen and (max-width:1024px) {
    .my_point_box{
        flex-direction: column;
        justify-content: unset;
        align-items: center;
    }
    .my_point {
        flex-direction: column;
        align-items: center;
    }
    .my_point_coin{
        margin: 20px 0;
    }
    .my_point_coin_icon{
        margin: 0 20px 0 0px;
    }
    .my_point_coin_txt {
        font-size: 30px;
    }
}

@media screen and (max-width:768px) {
    .point_list_title_wrap{
        align-items: flex-start;
        flex-direction: column;
    }
    .my_point_coin {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 10px 0;
    }
    .my_point_line{
        display: none;
    }
    .my_point_coin_flex{
        margin: 8px 0;
    }
    .my_point_title {
        font-size: 20px;
    }
    .mypage_border_list{
        padding: 15px 0;
        align-items: flex-start;
        flex-direction: column;
    }
    .mypage_border_list_contents{
        margin: 0;
        padding-top: 5px;
        flex-direction: column;
        align-items: flex-start;
        line-height: 30px;
    }
    .mypage_border_list_day{
        width:100%;
        margin-right:0;
    }
    .mypage_border_list_contents span{
        width:100%;
        line-height:1.4;
    }
    .mypage_border_list_contents span.point_color_blak, .mypage_border_list_contents span.point_color_blue{
        width:100%;
        padding:0;
        margin-top:10px;
    }
    .my_point_box {
        padding: 30px 20px;
    }
    .my_point_coin_txt{
        font-size: 22PX;
    }
    .my_point_txt{
        font-size: 16PX;
    }
    .my_point_coin_green{
        font-size: 16PX;
    }
    .point_change{
        width:100%;
    }
    .my_point_ex div{
        line-height:1.3;
    }
}




/*마이페이지_포인트 팝업*/
.point-popup-closed-btn{
    position: absolute;
    top: 100px;
    right: calc(50% - 506px);
    background: url(/images/icon/popup_close.svg) center center no-repeat;
    width: 48px;
    height: 48px;
    cursor: pointer;
}
.point-popup-wrap{
    display:none;
    position:fixed;
    top:0;
    left:0;
    width:100%;
    height:100%;
    background:rgba(0, 0, 0, 0.6);
}
.point-popup-wrap.on{
    display:block;
}
.point-popup-box{
    width:100%;
    height:100%;
    margin-top:40px;
    padding-top:120px;
}
.point-popup{
    width:1024px;
    height:600px;
    overflow-y: scroll;
    position:relative;
    background-color:#fff;
    padding:50px 40px;
    border-radius: 10px;
    margin:0 auto;
}
.point-title{
    border-bottom:1px solid #222;
    margin-bottom:40px;
}
.point-title h4{
    font-size:30px;
    color:#222;
    font-weight:600;
    margin-bottom:25px;
}
.con-item{
    margin-bottom:40px;
}
.con-item h5{
    font-size:24px;
    color:#222;
    font-weight:600;
    margin-bottom:15px;
}
.con-item ul li{
    font-size:18px;
    color:#222;
    line-height:24px;
    margin-bottom:10px;
    padding-left:20px;
    position:relative;
}
.con-item ul li::before{
    content:'';
    width:4px;
    height:4px;
    background-color:#222;
    border-radius: 50%;
    position:absolute;
    top:10px;
    left:10px;
}


@media screen and (max-width: 1024px){
    .point-popup{
        width:calc(100% - 40px);
    }
}
@media screen and (max-width: 768px){
    .point-popup-box{
        padding-top:60px;
        justify-content: center;
        align-items: unset;
    }
    .point-popup{
        padding:30px 20px;
        height:fit-content;
        max-height:calc(100vh - 120px);
        overflow-y: scroll;
    }
    .point-title h4{
        font-size:24px;
        margin-bottom:20px;
    }
    .point-title{
        margin-bottom:20px;
    }
    .con-item{
        margin-bottom:30px;
    }
    .con-item h5{
        font-size:20px;
        margin-bottom:10px;
    }
    .con-item ul li{
        padding-left:12px;
        font-size:16px;
        margin-bottom:5px;
    }
    .con-item ul li::before{
        left:2px;
        top:9px;
    }
    .point-popup-closed-btn{
        position:absolute;
        top:40px;
        right:20px;
    }
}





/* 마이페이지 나의 예약/결제 내역  */
.my-payment-list {
    border-top: 2px solid #222;
}
.my-payment-list .title-area{
    padding:22px 0;
    display:flex;
    align-items: center;
    border-top:2px solid #222;
    border-bottom:1px solid #aaa;
}
.my-payment-list .title-area .date{
    width:150px;
    margin-right:20px;
    color:#222;
    font-weight:600;
}
.my-payment-list .title-area .payment-list{
    width:calc(100% - 170px - 130px - 110px);
    margin-right:20px;
    color:#333;
    font-size:18px;
    font-weight:600;
}
.my-payment-list .title-area .payment-method{
    width:110px;
    margin-right:20px;
    text-align: center;
    color:#333;
    font-size:18px;
    font-weight:600;
}
.my-payment-list .title-area .payment-situation{
    width:110px;
    text-align: center;
    color:#333;
    font-size:18px;
    font-weight:600;
}
.my-payment-list .list-item{
    padding:22px 0;
    display:flex;
    align-items: center;
    border-bottom:1px solid #aaa;
}
.my-payment-list .list-item .date{
    width:150px;
    margin-right:20px;
    color:#888;
    font-weight:300;
}
.my-payment-list .list-item .payment-list{
    width:calc(100% - 170px - 130px - 110px);
    margin-right:20px;
    color:#333;
    font-size:18px;
    cursor: pointer;
}
.my-payment-list .list-item .payment-method{
    width:110px;
    margin-right:20px;
    text-align: center;
    color:#333;
    font-size:18px;
    cursor: pointer;
}
.my-payment-list .list-item .payment-situation{
    width:110px;
    text-align: center;
    cursor: pointer;
}

.payment-situation.situation01{
    color:#E83929;
}
.payment-situation.situation03{
    color:#056FB8;
}
.payment-situation.situation02{
    color:#222;
}
@media screen and (max-width: 1024px){
    .my-payment-list .title-area .date{
        width:100px;
    }
    .my-payment-list .list-item .date{
        width:100px;
    }
    .my-payment-list .title-area .payment-list{
        width:calc(100% - 100px - 130px - 110px);
    }
    .my-payment-list .list-item .payment-list{
        width:calc(100% - 100px - 130px - 110px);
    }
}

@media screen and (max-width: 768px){
    .argee_secession .agree{
        margin-right:18px;
    }
    .argee_secession .agree_chk{
        margin-right:4px;
    }
    .my-payment-list .title-area {
        display: none;
    }
    .my-payment-list .list-item{
        align-items :flex-start;
        flex-direction :column;
    }
    .my-payment-list .list-item .date{
        width: 100%;
        margin-right: 0;
        margin-bottom: 10px;
    }
    .my-payment-list .list-item .payment-list{
        width: 100%;
        margin-right: 0;
        margin-bottom: 20px;
    }
    .my-payment-list .list-item .payment-method{
        display: flex;
        width: 100%;
        margin-right: 0;
        margin-bottom: 5px;
    }
    .my-payment-list .list-item .payment-situation{
        display: flex;
        width: 100%;
        margin-right: 0;
    }
    .argee_secession .agree_chk{
        margin-right:6px;
    }
    .argee_secession .agree_chk_txt{
        font-size: 15px;
    }
}








/*마이페이지_예약내역 및 결제내역_리스트*/
.point_list_box_mg_b {
    margin-bottom: 100px;
}

.point_list_title_box {
    display: flex;
    justify-content: space-between;
}

.mypage_reservation_pd_box {
    padding: 25px 0;
    border-bottom: 1px solid #aaa;
}

/*마이페이지_예약내역 및 결제내역*/
.mypage_pay_contents_box .stay_info_room {
    margin: 14px 0 0 0;
}

.mypage_pay_contents_box .stay_info_name {
    color: #333;
}
.right-btn-area{
    display:flex;
    align-items: center;
}
.right-btn-area .cancel-payment-btn{
    height: 30px;
    padding: 5px 17px; box-sizing: border-box;
    border-radius: 17px;
    border: 1px solid #444;
    background: #e7e7e7;
    color: #222;
    text-align: center;
    margin-right:10px;
    margin-bottom:17px;
    cursor: pointer;
}
.right-btn-area .cancel-payment-btn:hover{
    text-decoration: underline;
}
.receipt_btn {
    height: 30px;
    padding: 5px 17px; box-sizing: border-box;
    border-radius: 17px;
    border: 1px solid #444;
    background: #FFF;
    color: #333;
    text-align: center;
}

.receipt_btn:hover {
    cursor: pointer;
    text-decoration: underline;
}

@media screen and (max-width: 768px) {

    .mypage_reservation_pd_box{
        padding: 30px 0 0 0;
    }

    .point_list_box_mg_b {
        margin-bottom: 50px;
    }
    .point_list_title{
        margin-bottom: 20px;
    }
    .point_list_title_box{
        flex-direction: column;
        margin-bottom: 20px;
    }
    .receipt_btn{
        height:40px;
        line-height:40px;
        padding:0;
        border-radius: 50px;
    }
    .point_list_title{
        text-align: left;
    }
    .right-btn-area .cancel-payment-btn{
    line-height: 40px;
    padding: 0 15px;
    border-radius: 50px;
    height: 40px;
    margin-bottom:0;
    }
    .receipt_btn{
        padding: 0 10px;
        margin-bottom:0;
    }
}





/* 나의 문의 내역 */
.my-inqurty-list{
    border-top: 2px solid #222;
}
.my-inqurty-list .title-area{
    padding:22px 0;
    display:flex;
    align-items: center;
    border-bottom:1px solid #aaa;
}
.my-inqurty-list .title-area .date{
    width:150px;
    margin-right:20px;
    color:#222;
    font-weight:600;
}
.my-inqurty-list .title-area .inquiry-list{
    width:calc(100% - 190px - 130px - 110px);
    margin-right:20px;
    color:#333;
    font-size:18px;
    font-weight:600;
}
.my-inqurty-list .title-area .inquiry-kind{
    width:110px;
    margin-right:20px;
    text-align: center;
    color:#333;
    font-size:18px;
    font-weight:600;
}
.my-inqurty-list .title-area .inquiry-situation{
    width:110px;
    text-align: center;
    color:#333;
    font-size:18px;
    font-weight:600;
    margin:0 auto;
}
.my-inqurty-list .list-item{
    padding:22px 0;
    display:flex;
    align-items: center;
    border-bottom:1px solid #aaa;
}
.my-inqurty-list .list-item .date{
    width:150px;
    margin-right:20px;
    color:#888;
    font-weight:300;
}
.my-inqurty-list .list-item .inquiry-list{
    width:calc(100% - 190px - 130px - 110px);
    margin-right:20px;
    color:#333;
    font-size:18px;
    cursor: pointer;
}
.my-inqurty-list .list-item .inquiry-kind{
    width:110px;
    margin-right:20px;
    text-align: center;
    color:#333;
    font-size:18px;
}
.my-inqurty-list .list-item .inquiry-situation{
    text-align: center;
    margin:0 auto;
}
.inquiry-situation.situation01{
    width:55px;
    height:26px;
    line-height:26px;
    background-color:#222;
    color:#fff;
    border-radius: 50px;
}
.inquiry-situation.situation02{
    width:55px;
    height:26px;
    line-height:26px;
    background-color:#fff;
    color:#222;
    border-radius: 50px;
    border:1px solid #222;
}
@media screen and (max-width:1280px) {
    .my-inqurty-list .list-item .date{
        width:110px;
    }
    .my-inqurty-list .title-area .date{
        width:110px;
    }
}
@media screen and (max-width:1024px) {
    .my-inqurty-list .title-area .inquiry-list{
        width: calc(100% - 350px);
    }
    .my-inqurty-list .list-item .inquiry-list{
        width: calc(100% - 350px);
    }
    .my-inqurty-list .title-area .inquiry-kind{
        width:80px;
    }
    .my-inqurty-list .list-item .inquiry-kind{
        width:80px;
    }
    .my-inqurty-list .title-area .inquiry-situation{
        width:80px;
    }
}
@media screen and (max-width:768px) {
    .my-inqurty-list .title-area{
        display: none;
    }
    .my-inqurty-list .list-item{
        flex-direction: column;
    }
    .my-inqurty-list .list-item .date {
        width:100%;
        margin-right: 0;
        margin-bottom :10px;
    }
    .my-inqurty-list .list-item .inquiry-list{
        width:100%;
        margin-right: 0;
        margin-bottom:20px;
    }
    .my-inqurty-list .list-item .inquiry-kind{
        width:100%;
        margin-right: 0;
        margin-bottom:10px;
        text-align: unset;
    }
    .my-inqurty-list .list-item .inquiry-situation{
        margin: 0;
    }
    .my-inqurty-list .list-item{
        align-items: flex-start;
    }

}





/* 나의문의내역답변(상세) */
.my-inqury-view-wrap .my-inqury-title{
    padding-bottom:35px;
    border-bottom:1px solid #aaa;
    margin-bottom:35px;
}
.my-inqury-view-wrap .my-inqury-title span{
    display:block;
    font-size:18px;
    color:#007BD0;
    margin-bottom:15px;
}
.my-inqury-view-wrap .my-inqury-title h4{
    font-size:18px;
    color:#333;
    font-weight:600;
    margin-bottom:20px;
}
.my-inqury-view-wrap .my-inqury-title p{
    font-size:16px;
    color:#888;
    font-weight:300;
}
.my-inqury-view-wrap .my-inqury-answer .answer{
    min-height:200px;
}







/*잠시섬 커뮤니티_잠시섬소식*/
.community_title_img_list {
    display: flex;
    justify-content: space-between;
    padding: 29px 10px;
    border-bottom: 1px solid #aaa;
}

.community_title_img_post_title_box {
    width: calc(100% - 170px - 30px);
}

.community_title_img_post_title {
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.community_title_img_post_ex {
    color: #444;
    line-height: 28px;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin: 25px 0;
}

.community_title_img_post_img {
    width: 170px; height: 170px;
    overflow: hidden;
}

.community_title_img_post_img img {
    width: 100%; height: 100%;
    object-fit: cover;
}


/*프로젝트_뷰페이지*/
.view_title_box {
    margin-bottom: 50px;
}

.view_title {
    color: #222;
    font-size: 30px;
    font-weight: 600;
    margin-bottom: 37px;
}

.view_day {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    color: #666;
}

.view_day_line {
    width: 1px;
    height: 12px;
    background: #888;
    margin: 0 10px;
}

.view_contents_box {
    border-top: 1px solid #000;
    border-bottom: 1px solid #000;
    margin: 0 0 50px 0;
}

.view_contents {
    padding: 50px 0 118px 0;
}

.view_contents_ex_bold {
    color: #333;
    font-size: 24px;
    font-weight: 600;
    line-height: 30px;
    margin-bottom: 15px;
}

.view_contents_ex {
    color: #333;
    font-size: 18px;
    font-weight: 400;
    line-height: 32px;
}

.view_contents_img {
    width: fit-content;
    max-width: 557px;
    margin: 118px auto 0 auto;
}

.view_contents_img img {
    width: 100%; height: auto;
}


/*마이페이지_게시글*/
.mypage_boder_contents_box .community_title_img_post_title_box {
    width: calc(100% - 96px - 50px);
}

.mypage_boder_contents_box .community_title_img_post_img {
    width: 96px; height: 96px;
    overflow: hidden;
}

.mypage_boder_contents_box .community_title_img_post_ex {
    color: #444;
    line-height: 28px;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin: 25px 0;
}
.like_box {
    display: flex;
    align-items: center;
    color: #888;
}
.like_icon {
    width: 14px;
    height: 13px;
    background: url(/images/icon/like.svg);
    margin-right: 6px;
}
.delete_box {
    display: flex;
    align-items: center;
    color: #333;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
}

.delete_icon {
    width: 20px;
    height: 20px;
    background: url(/images/icon/delete_icon.svg);
    margin-right: 6px;
}



@media screen and (max-width:768px) {
    .community_title_img_list {
        flex-direction: column-reverse;
        padding:20px 0;
    }
    .mypage_boder_contents_box .community_title_img_post_img{
        width:80%;
        height:unset;
        aspect-ratio: 1/1;
        margin: 0 auto;
        margin-bottom:20px;
    }
    .mypage_boder_contents_box .community_title_img_post_title_box{
        width:100%;
    }
    .mypage_boder_contents_box .community_title_img_post_ex{
        -webkit-line-clamp: 2;
    }
    .community_title_img_list .view_day_line{
        margin:0 8px;
    }
    /* .community_title_img_list .delete_icon{
        margin-left:16px;
    } */
    .community_title_img_list .view_day{
        font-size:15px;
    }
}


/* 마이페이지 모달 */
.shadow{
    position: fixed;
    left: 0;top: 0;
    background: rgba(0, 0, 0, 0.50);
    width: 100%;
    height: 100vh;
    display: none;
    z-index: 999999999999;
}
.pw_popup {
    position: fixed;
    width: 716px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    padding: 40px 50px;
    box-sizing: border-box;
    display: none;
    background: #fff;
    z-index: 9999999999999999;
    border-radius: 10px;
}

.pw_popup .close {
    width: 37px;
    height: 37px;
    background: url(/images/icon/popup_close.svg);
    border: 0;
    position: absolute;
    right: 0px;
    top: -47px;
    cursor: pointer;
}

.pw_popup_contents {
    width: 100%; height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.pw_popup_contents>div {
    width: 100%;
}

.pw_popup_line {
    border-top: 1px solid #000;
    margin-top: 26px;
    padding-top: 63px;
    margin-bottom: 60px;
}

.pw_popup_line_contents {
    display: flex;
    align-items: center;
}

.pw_popup_line_contents_mg_b {
    margin-bottom: 40px;
}

.pw_popup_line_contents .b2b_contact_us_form_list_title {
    min-width: 127px;
    margin-right: 60px;
    margin-bottom: 0;
}






/* 문의하기 모달 */
.inqury-modal-wrap{
    display:none;
    position: fixed;
    top:0;
    left:0;
    width:100%;
    height:100%;
    background:rgb(0, 0, 0 , 0.75);
    z-index: 9999999999;
}
.inqury-modal-wrap.on{
    display:block;
}
.inqury-modal-wrap .inqury-modal{
    padding:200px 0;
    width:100%;
    height:100%;
    overflow-y: scroll;
}
.inqury-modal-wrap .inqury-modal .modal-bg{
    background-color:#fff;
    border-radius: 10px;
    padding:50px 60px;
    position:relative;
}
.inqury-modal-title{
    font-size:30px;
    color:#222;
    font-weight:700;
    margin-bottom:75px;
}
.inqury-modal-wrap .inquiry-item{
    margin-bottom:40px;
}
.inqury-modal-wrap .inquiry-agree-area .checkbox-area{
    margin-bottom:70px;
}
.inquiry-modal-closed-btn{
    position:absolute;
    top:-68px;
    right:0;
    background: url(/images/icon/popup_close.svg) center center no-repeat;
    width:48px;
    height:48px;
    cursor: pointer;
}



@media screen and (max-width:768px) {
    .inqury-modal-wrap{
        background:rgb(0, 0, 0 , 0.85)
    }
    .inqury-modal-wrap .inqury-modal{
        padding:80px 0;
    }
    .inqury-modal-wrap .inqury-modal .modal-bg{
        padding:40px 20px;
    }
    .inqury-modal-wrap .inquiry-agree-area .checkbox-area{
        margin-bottom:40px;
    }
    .mo-inquiry-modal-btn{
        margin-bottom:10px;
    }
    .mo-inquiry-modal-btn button{
        width:100%;
        height:100%;
    }
    .inquiry-modal-closed-btn{
        width:36px;
        height:36px;
        background-size:36px 36px;
        top:-50px;
    }
}


/* .calender_date_box div {
    padding: 5px 10px;
    margin: 5px;
    border-radius: 15px;
    cursor: pointer;
} */
.calendar_normal_date {
    /* background-color: #DCDCDC; 활성화 bg */ color: #333333; font-weight: 700; opacity: 1; pointer-events: auto; cursor: pointer;
}

.calendar_pick_date {
    background-color: #007BD0;
    color: #fff;
    font-weight: 700;
}

.calender_pick_disabled_date {
    opacity: 0.3; pointer-events: none; cursor: default;
}
.calender_two_before_day_disabled_date {
    opacity: 0.5; pointer-events: none; cursor: default;
}
.calender_next_pick_possible_date {
    opacity: 1; pointer-events: auto; cursor: pointer;
}
.photo-area .img-list li.thumbnail{
	border: 1px solid #F00;
	position: relative;
}
.photo-area .img-list li.thumbnail::after{
	content:'대표';
	position: absolute;
	width: 36px;
	height: 19px;
	top:5px;
	right:5px;
	background:#00000080;
	font-size:14px;
	color:#fff;
	border-radius: 2px;
	text-align: center;
}
.view-con .view_image_list_box {
	margin-top: 20px; text-align: center;
}

.program_calender_date_box {
    display: grid;
    grid-template-columns: repeat(7, 14.2%);
    grid-column-gap: 0px;
    text-align: center;
}
.program_calender_date_box div {
    padding: 6px 0px;
    margin: 5px;
    border-radius: 15px;
    cursor: pointer;
}
.program_calender_date_box div p{
    padding: 5px 8px;
    border-radius: 15px;
    max-width: 37px;
    margin:0 auto;
}
.calendar_normal_date {
     /* background-color: #DCDCDC; 활성화 bg */ color: #333333; font-weight: 700; opacity: 1; pointer-events: auto; cursor: pointer;
}

.calendar_pick_date {
    background-color: #007BD0;
    color: #fff;
    font-weight: 700;
}

.calender_pick_disabled_date {
    opacity: 0.3; pointer-events: none; cursor: default;
}
.calender_two_before_day_disabled_date {
    opacity: 0.5; pointer-events: none; cursor: default;
}
.calender_next_pick_possible_date {
    opacity: 1; pointer-events: auto; cursor: pointer;
}

.lastday.pick-date-outter-div .prev_p{
    width: fit-content; padding-left: 10px;
}
.lastday.pick-date-outter-div .next_p{
    width: fit-content; padding-left: 15px;
}
.calender_gray_box .day_box{
    width: fit-content; padding-left: 9px;
}
.program_calender_date_box {
    display: grid ;
    grid-template-columns: repeat(7, 14.2%);
    grid-column-gap: 0px;
    text-align: center;
}
.program_calender_date_box .today {
    background-color: #474747;
    color: #fff;
}
.stay_list_circle .on {
    background: #fff;
    border: 1px solid #000;
}

.stay_list_circle li {
    width: 9px; height: 9px;
    border-radius: 50%;
    border: 1px solid #fff;
    margin-right: 10px;
}

.stay_list_circle li:last-child {
    margin-right: 0;
}

.stay_list_txt_box {
    width: 570px;
}

.stay_list_txt_box .stay_info_room {
    margin: 30px 0;
}

.stay_list_ex {
    color: #333;
    font-size: 18px;
    line-height: 32px;
}
.stay-loading_box {
    display: flex; justify-content: center; width: 100%; margin-top: 100px;
}
.stay-loading_box img {
    width: 100px; height: 100px;
}
.pick-date-outter-div {
    margin: 8px 0; position: relative; padding-top: 5px;
}
.pick-date-inner-div {
    height: 23px; margin: 0 !important;
}
.pick-date-inner-div-active-bg {
    background-color: #CAE7FB;
}
.pick-date-inner-div-active-bg .sunday.reservable:not(.pick-date-inner-p-active),
.pick-date-inner-div-active-bg .saturday.reservable:not(.pick-date-inner-p-active),
.pick-date-inner-div-active-bg .reservable:not(.pick-date-inner-p-active)  {
    color: #007BD0; font-weight: 600; font-size: 16px;
}
.pick-date-inner-div-active-bg-w15 {
    background-color: #CAE7FB; width: 15px;
}
.pick-date-inner-p {
    position: absolute; top: 1px; width: 39px; height: 30px; align-content: center; border-radius: 15px;
 }
.pick-date-inner-p-active {
   color: #FFFFFF !important;  background-color: #007BD0;
}
.pick-date-left-radius {
    border-radius: 15px 0 0 15px;
}
.pick-date-right-radius {
    border-radius: 0 15px 15px 0
}

@media screen and (max-width:768px) {
    .program_calender_date_box div{
        padding:6px 0;
    }
    .pw_popup{
        width:calc(100% - 40px);
        padding:30px 20px;
    }
}





/* 결제 처리중 모달 */
.payment-loading-wrap{
    min-height:calc(100vh - 93px - 196px);
    padding:190px 0;
}
.payment-loading h4{
    font-family: 'tenada';
    font-size:45px;
    color:#222;
    margin-bottom:25px;
    text-align: center;
}
.payment-loading p{
    font-size:16px;
    color:#333;
    line-height:26px;
    text-align: center;
    margin-bottom:50px;
}
.loading{
    text-align: center;
}
.loading span {
    display: inline-block;
    width: 20px;
    height: 20px;
    background-color: #007BD0;
    border-radius: 50%;
    animation: loading 1.5s linear infinite;
    margin-right:10px;
}
.loading span:last-child{
    margin-right:0;
}
@keyframes loading {
0%,
100% {
    opacity: 0;
    transform: scale(0.5);
}
50% {
    opacity: 1;
    transform: scale(1.1);
}
}
.loading span:nth-child(1) {
animation-delay: 0s;
}
.loading span:nth-child(2) {
animation-delay: 0.2s;
}
.loading span:nth-child(3) {
animation-delay: 0.4s;
}
.loading span:nth-child(4) {
animation-delay: 0.6s;
}


@media screen and (max-width:768px) {
    .payment-loading-wrap{
        padding:120px 0;
    }
    .payment-loading h4{
        font-size:36px;
        margin-bottom:20px;
    }
    .payment-loading p{
        font-size:15px;
        line-height:24px;
        margin-bottom:40px;
    }
    .loading span {
        width:16px;
        height:16px;
        margin-right:8px;
    }
}


/* 프로그램 캘린더 수정 */
.program-calender .big_calender_day_box .calender_circle_box{
    position:relative;
}
.program-calender .big_calender_day_box .calender_circle_box div{
    position:absolute;
    top:7px;
    left:0;
}
.program-calender .big_calender_day_box .calender_circle_box span{
    padding-left:10px;
}
@media screen and (max-width:768px) {
    .program-calender .big_calender_day_box .big_calender_day .calender_circle_box_contents span{
        padding-left:10px;
        white-space: unset;
        line-height: 1.3;
        word-break: keep-all;
    }
    .program-calender .big_calender_day_box .big_calender_day .calender_circle_box{
        width:240px;
    }
}