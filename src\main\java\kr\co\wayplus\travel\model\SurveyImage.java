package kr.co.wayplus.travel.model;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;

import kr.co.wayplus.travel.base.model.CommonFileInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SurveyImage extends CommonFileInfo {
	private Integer questionId;
	private Integer recommandId;
	private Integer sortOrder;


	public SurveyImage addQuestionId( Integer questionId ) {
		this.questionId = questionId;
		return this;
	}

	public SurveyImage addFileId( Integer fileId ) {
		this.setFileId(fileId);
		return this;
	}

	public SurveyImage addSortOrder( Integer sortOrder ) {
		this.sortOrder = sortOrder;
		return this;
	}



}
