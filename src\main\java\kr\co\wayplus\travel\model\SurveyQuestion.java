package kr.co.wayplus.travel.model;

import java.util.ArrayList;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@Data
public class SurveyQuestion extends CommonDataSet {
	private Integer id;	//고유 번호
	private Integer upperId;
	private String question;
	private String answerType;
	private String agreePoint;
	private String disagreePoint;
	private Integer sortNumber;

	private SurveyImage file;

	ArrayList<SurveyQuestionAnswer> listSurveyQuestionAnswers;


}
