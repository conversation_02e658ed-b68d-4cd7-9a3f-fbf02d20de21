package kr.co.wayplus.travel.mapper.manage;

import kr.co.wayplus.travel.model.BadgeAcquireHistory;
import kr.co.wayplus.travel.model.BadgeAttachImage;
import kr.co.wayplus.travel.model.BadgeContents;
import kr.co.wayplus.travel.model.LoginUser;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


@Mapper
@Repository
public interface BadgeManageMapper {

    int selectCountBadgeContents(HashMap<String, Object> paramMap);

    List<BadgeContents> selectListBadgeContents(HashMap<String, Object> paramMap);

    void insertBadgeAttachImage(BadgeAttachImage attachImage);

    void insertBadgeContents(BadgeContents badgeContents);

    BadgeContents selectOneBadgeContents(HashMap<String, Object> param);

    void updateBadgeContents(BadgeContents badgeContents);

    void updateBadgeContentsDelete(HashMap<String, Object> param);

    int selectCountBadgeAcquireHistory(HashMap<String, Object> param);

    List<BadgeAcquireHistory> selectListBadgeAcquireHistory(HashMap<String, Object> param);

    void insertBadgeAcquireHistory(BadgeAcquireHistory badgeAcquireHistory);

    void updateBadgeAcquireHistory(BadgeAcquireHistory badgeAcquireHistory);

    ArrayList<BadgeContents> selectBadgeListByAutomationType(HashMap<String, Object> param);

    int selectTotalLoginCount(LoginUser user);

    int selectContinuousLoginCount(LoginUser user);
}