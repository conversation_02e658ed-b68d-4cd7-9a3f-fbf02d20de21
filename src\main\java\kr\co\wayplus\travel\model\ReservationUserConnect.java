package kr.co.wayplus.travel.model;

import com.fasterxml.jackson.annotation.JsonInclude;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReservationUserConnect extends CommonDataSet {
	private Long reservationId;	//예약번호
	private String userEmail;	//사용자ID(이메일ID)

	public ReservationUserConnect addReservationId(Long id) {
		this.reservationId = id;
		return this;
	}
	public ReservationUserConnect addUserEmail(String userEmail) {
		this.userEmail = userEmail;
		return this;
	}
	public ReservationUserConnect addCreateId(String userEmail) {
		this.setCreateId(userEmail);
		return this;
	}
}
