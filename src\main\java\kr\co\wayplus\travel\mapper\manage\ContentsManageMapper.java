package kr.co.wayplus.travel.mapper.manage;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import kr.co.wayplus.travel.model.BoardAttachFile;
import kr.co.wayplus.travel.model.Contents;
import kr.co.wayplus.travel.model.ContentsItem;

@Mapper
@Repository
public interface ContentsManageMapper {
	int selectCountContents(HashMap<String, Object> paramMap);
	ArrayList<Contents> selectListContents(HashMap<String, Object> paramMap);
	Contents selectOneContents(HashMap<String, Object> paramMap);
	void insertContents(Contents cnt) throws SQLException;
	void updateContents(Contents cnt) throws SQLException;
	void deleteContents(Contents cnt) throws SQLException;

	//contentsItem
	int selectCountContentsItem(HashMap<String, Object> paramMap);
	ArrayList<ContentsItem> selectListContentsItem(HashMap<String, Object> paramMap);
	ContentsItem selectOneContentsItem(HashMap<String, Object> paramMap);
	void insertContentsItem(ContentsItem cnt) throws SQLException;
	void updateContentsItem(ContentsItem cnt) throws SQLException;
	void deleteContentsItem(ContentsItem cnt) throws SQLException;

	void insertContentsAttachFile(BoardAttachFile baf);
}