package kr.co.wayplus.travel.model;

import com.fasterxml.jackson.annotation.JsonInclude;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@EqualsAndHashCode(callSuper = false)
public class ProductRestPeriod extends CommonDataSet {
    private Integer id;
    private String useYn;
    private String productSerial;
    private String productRestStartDate;
    private String productRestEndDate;

    public void setCreateUserId(String userEmail) {
        super.setCreateId(userEmail);
    }

    public void setUpdateUserId(String userEmail) {
        super.setLastUpdateId(userEmail);
    }
}
