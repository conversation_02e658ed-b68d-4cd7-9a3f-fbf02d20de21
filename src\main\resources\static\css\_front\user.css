.user_contents {
	margin-top: 60px;
}

.title_ex_box {
	margin-bottom: 50px;
	text-align: center;
}

.title_ex {
	margin-top: 16px;
	color: #222;
}

.title_box {
	margin-bottom: 40px;
	text-align: center;
}

.user_list_box {
	padding: 40px 0;
	border-top: 1px solid #666;
	border-bottom: 1px solid #666;
}

.login_contents_box {
	width: fit-content;
	margin: 0 auto;
}

.login_contents_box {
	display: flex;
}

.id_contents_input {
	margin-bottom: 20px;
}

.d-flex {
	display: flex;
}

.d-flex.fd-c {
	flex-direction: column;
}

.login_contents_input span {
	display: inline-block;
	width: 65px;
	margin-right: 20px;
}

.re-password .login_contents_input span {
	margin-right: unset;
}

.login_contents_input input {
	width: 400px;
	height: 40px;
	border: 1px solid #CCC;
	color: #666;
	font-family: Pretendard;
	font-size: 16px;
	font-weight: 400;
	padding-left: 10px;
	box-sizing: border-box;
}

.login_contents_input .info-text {
	width: 400px;
}


.login_contents_btn {
	display: flex;
	width: 100px;
	height: auto;
	border-radius: 5px;
	background: #0062D4;
	margin-left: 20px;
	color: #FFF;
	justify-content: center;
	align-items: center;
	font-size: 18px;
	font-weight: 600;
}

.login_contents_btn:hover {
	cursor: pointer;
	opacity: 0.9;
}

.user_list {
	font-size: 0;
	line-height: 10px;
	width: fit-content;
	margin: 0 auto;
}

.user_list li {
	display: inline-block;
	font-size: 14px;
	padding: 0 10px;
	border-right: 1px solid #222;
	margin: 20px 0 0 0;
	color: #222;
}

.user_list li:last-child {
	border: none;
}

.user_list li:hover {
	color: #0062D4;
	font-weight: 500;
	cursor: pointer;
}

.user_btn_box {
	margin-top: 40px;
}

#agree_chk {
	display: none;
}

.agree {
	margin-top: 20px;
	margin-bottom: 50px;
	overflow: hidden;
}

.agree_chk {
	float: left;
	display: block;
	width: 18px;
	height: 18px;
	border: 1px solid #666;
	margin-right: 13px;
	position: relative;
	cursor: pointer;
}

.agree_chk_txt {
	float: left;
	color: #666;
	font-size: 16px;
	font-weight: 400;
}

#agree_chk:checked+label::after {
	content: '✔';
	font-size: 14px;
	width: 18px;
	height: 18px;
	text-align: center;
	position: absolute;
	left: 0;
	top: 0;
}

.underline {
	text-decoration: underline;
}

.find-result {
	display: none;
}

.simple_login {
	text-align: center;
	margin-top: 80px;
	color: #444;
	font-size: 18px;
	font-weight: 600;
}

.sns_box {
	display: flex;
	justify-content: center;
	margin-top: 15px;
}

.naver {
	width: 60px;
	height: 61px;
	background: url(/images/icon/sns/naver.svg);
	cursor: pointer;
	margin-left: 20px;
}

.kakao {
	width: 60px;
	height: 61px;
	background: url(/images/icon/sns/kakao.svg);
	cursor: pointer;
}

@media screen and (max-width:1024px) {
	.wrap {
		margin-top: 60px;
	}
}

@media screen and (max-width:768px) {
	.login_contents_input span {
		display: none;
	}
}

@media screen and (max-width:600px) {
	.wrap {
		margin-top: 0px;
	}

	.login_contents_box {
		width: 100%;
	}

	.login_contents_input_box {
		width: 100%;
	}

	.login_contents_box {
		flex-wrap: wrap;
	}

	.login_contents_btn {
		width: 100%;
		height: 50px;
		margin: 20px 0 0 0;
	}

	.login_contents_input input {
		width: 100%;
	}
}