package kr.co.wayplus.travel.util;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.imageio.ImageIO;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import kr.co.wayplus.travel.builder.ExcelDataBuilder;
import kr.co.wayplus.travel.model.excel.ExcelData;
import kr.co.wayplus.travel.model.excel.ExcelImgData;
import kr.co.wayplus.travel.model.excel.ExcelMetadata;
import kr.co.wayplus.travel.model.excel.HeaderInfo;
import kr.co.wayplus.travel.web.front.ExternalApiController;

public class ExcelUtil {
	private final Logger logger = LoggerFactory.getLogger(ExcelUtil.class);

	private XSSFWorkbook workbook;
	private XSSFSheet sheet;

	public ExcelUtil() {
		this.workbook = new XSSFWorkbook();
		this.sheet = workbook.createSheet("Sheet1");
	}

	// Grid 헤더 생성 (2차원 병합 지원)
	public void createHeader(List<List<HeaderInfo>> headerRows) {
		this.createHeader(headerRows, 0, 0);
	}
	public void createHeader(List<List<HeaderInfo>> headerRows, int startRow, int startCol) {
	            CellStyle headerStyle = createHeaderStyle();

        // 헤더 행 생성 (startRow부터 시작)
        for (int rowIdx = 0; rowIdx < headerRows.size(); rowIdx++) {
            XSSFRow headerRow = sheet.createRow(startRow + rowIdx);
            List<HeaderInfo> rowHeaders = headerRows.get(rowIdx);

            int colIdx = startCol;
            for (HeaderInfo header : rowHeaders) {
                // 이미 병합된 영역은 건너뛰기
                while (isPartOfMergedRegion(startRow + rowIdx, colIdx)) {
                    colIdx++;
                }

                Cell cell = headerRow.createCell(colIdx);
                cell.setCellValue(header.getTitle());
                cell.setCellStyle(headerStyle);

                // 병합이 필요한 경우
                if (header.getRowSpan() > 1 || header.getColSpan() > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(
                        startRow + rowIdx,
                        startRow + rowIdx + header.getRowSpan() - 1,
                        colIdx,
                        colIdx + header.getColSpan() - 1
                    ));
                }

                colIdx += header.getColSpan();
            }
        }
	}

	// 병합된 영역인지 확인
	private boolean isPartOfMergedRegion(int row, int col) {
		for (CellRangeAddress range : sheet.getMergedRegions()) {
			if (range.isInRange(row, col)) {
				return true;
			}
		}
		return false;
	}

	// 헤더 스타일 생성
	private CellStyle createHeaderStyle() {
		CellStyle headerStyle = workbook.createCellStyle();
		headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		headerStyle.setAlignment(HorizontalAlignment.CENTER);
		headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headerStyle.setBorderTop(BorderStyle.THIN);
		headerStyle.setBorderBottom(BorderStyle.THIN);
		headerStyle.setBorderLeft(BorderStyle.THIN);
		headerStyle.setBorderRight(BorderStyle.THIN);

		// 폰트 설정
		Font headerFont = workbook.createFont();
		headerFont.setBold(true);

		return headerStyle;
	}

	// Grid 데이터 추가 (ExcelData 리스트 버전)
    public void addGridData(List<ExcelData> dataList, int startRow) {
        CellStyle defaultStyle = createDataStyle();

        int rowNum = startRow;
        for (ExcelData excelData : dataList) {
            XSSFRow row = sheet.createRow(rowNum++);
            int colNum = 0;

            Map<String, Object> data = excelData.getData();
            Map<String, ExcelDataBuilder.CellStyle> styles = excelData.getStyles();

            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                Cell cell = row.createCell(colNum++);

                // 스타일 적용
                ExcelDataBuilder.CellStyle customStyle = styles.get(key);
                if (customStyle != null) {
                    CellStyle cellStyle = workbook.createCellStyle();
                    cellStyle.cloneStyleFrom(defaultStyle);

                    // 배경색 설정
                    if (customStyle.getBackgroundColor() != null) {
                        IndexedColors bgColor = getIndexedColor(customStyle.getBackgroundColor());
                        if (bgColor != null) {
                            cellStyle.setFillForegroundColor(bgColor.getIndex());
                            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                        }
                    }

                    // 글자색 설정 (기존 글꼴 속성 유지)
                    if (customStyle.getTextColor() != null) {
                        IndexedColors textColor = getIndexedColor(customStyle.getTextColor());
                        if (textColor != null) {
                            Font newFont = workbook.createFont();
                            Font currentFont = workbook.getFontAt(defaultStyle.getFontIndex());

                            // 기존 글꼴 속성 복사
                            newFont.setFontName(currentFont.getFontName());
                            newFont.setFontHeightInPoints(currentFont.getFontHeightInPoints());
                            newFont.setBold(currentFont.getBold());
                            newFont.setItalic(currentFont.getItalic());

                            // 색상만 변경
                            newFont.setColor(textColor.getIndex());

                            cellStyle.setFont(newFont);
                        }
                    }

                    cell.setCellStyle(cellStyle);
                } else {
                    cell.setCellStyle(defaultStyle);
                }

                 // 값 설정
				 if (value != null) {
                    if (value instanceof Number) {
                        cell.setCellValue(((Number) value).doubleValue());
                    } else {
                        cell.setCellValue(value.toString());
                    }
                }

//				// 컬럼 너비 자동 조정
//				for (int i = 0; i < sheet.getRow(0).getLastCellNum(); i++) {
//					sheet.autoSizeColumn(i);
//				}
            }
        }
    }

    private IndexedColors getIndexedColor(String colorName) {
        try {
            return IndexedColors.valueOf(colorName.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

	// 데이터 스타일 생성
	private CellStyle createDataStyle() {
        CellStyle dataStyle = workbook.createCellStyle();

        // 기본 글꼴 설정
        Font defaultFont = workbook.createFont();
        defaultFont.setFontName("맑은 고딕");  // 기본 글꼴을 맑은 고딕으로 설정
        defaultFont.setFontHeightInPoints((short) 12);  // 글꼴 크기
        dataStyle.setFont(defaultFont);

        // 테두리 설정
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
		// 가운데 정렬 설정
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		// 줄 바꿈 설정
		dataStyle.setWrapText(true);
		// 숫자 포맷 설정 (#,##0: 천 단위 구분 쉼표)
        DataFormat format = workbook.createDataFormat();
        dataStyle.setDataFormat(format.getFormat("#,##0"));

        return dataStyle;
    }

	public void addImage(String base64Image, int col1, int row1, int col2, int row2) {
	    try {
	        // base64 데이터 전처리
	        if (base64Image.startsWith("data:image/png;base64,")) {
	            base64Image = base64Image.replaceAll("data:image/png;base64,", "");
	        }

	        // 이미지 디코딩 및 처리
	        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
	        ByteArrayInputStream inputStream = new ByteArrayInputStream(
	            Base64.getDecoder().decode(base64Image)
	        );

	        // BufferedImage를 통한 이미지 처리
	        BufferedImage bufferImg = ImageIO.read(inputStream);
	        ImageIO.write(bufferImg, "png", outputStream);

	        // 워크북에 이미지 추가
	        int pictureIdx = workbook.addPicture(
	            outputStream.toByteArray(),
	            XSSFWorkbook.PICTURE_TYPE_PNG
	        );

	        // Drawing 생성
	        XSSFDrawing drawing = sheet.createDrawingPatriarch();

	        // Anchor 생성 및 위치 설정
	        XSSFClientAnchor anchor = drawing.createAnchor(
	            0, 0,    // dx1, dy1 - 시작 셀 내 좌표
	            0, 0,    // dx2, dy2 - 종료 셀 내 좌표
	            col1,    // 시작 열
	            row1,    // 시작 행
	            col2,    // 종료 열
	            row2     // 종료 행
	        );

	        // 이미지 생성
	        drawing.createPicture(anchor, pictureIdx);

	        // 리소스 정리
	        inputStream.close();
	        outputStream.close();

	    } catch (Exception e) {
	        e.printStackTrace();
	    }
	}

	// 엑셀 컬럼 너비를 조정하는 새로운 메소드
    private void adjustColumnWidth() {
        if (sheet == null || sheet.getPhysicalNumberOfRows() == 0) {
            return;
        }

        // 각 컬럼의 최대 문자열 길이를 저장할 맵
        Map<Integer, Integer> maxCharLengths = new HashMap<>();

        // 모든 행과 셀을 순회하면서 최대 문자열 길이 계산
        for (Row row : sheet) {
            if (row == null) continue;
            
            for (Cell cell : row) {
                if (cell == null) continue;
                
                int columnIndex = cell.getColumnIndex();
                int length = 0;
                
                switch (cell.getCellType()) {
                    case STRING:
                        String str = cell.getStringCellValue();
                        // 한글은 2자, 영문/숫자는 1자로 계산
                        length = str.replaceAll("[^\\x00-\\xff]", "**").length();
                        break;
                    case NUMERIC:
                        length = String.valueOf(cell.getNumericCellValue()).length();
                        break;
                    default:
                        continue;
                }
                
                // 현재 컬럼의 최대 길이 업데이트
                maxCharLengths.merge(columnIndex, length, Integer::max);
            }
        }

        // 각 컬럼의 너비 설정
        for (Map.Entry<Integer, Integer> entry : maxCharLengths.entrySet()) {
            int column = entry.getKey();
            int maxLength = entry.getValue();
            
            // 기본 여백 추가
            int width = (maxLength + 2) * 256;
            
            // 최소, 최대 너비 제한
            width = Math.max(width, 3000);    // 최소 너비
            width = Math.min(width, 15000);   // 최대 너비
            
            try {
                sheet.setColumnWidth(column, width);
            } catch (Exception e) {
                // 오류 발생 시 기본 너비 적용
                sheet.setColumnWidth(column, 3000);
            }
        }
    }

	// Excel 파일 저장
	public byte[] generateExcelFile() throws IOException {
		// 컬럼 너비 자동 조정
		/*
		for (int i = 0; i < sheet.getRow(0).getLastCellNum(); i++) {
			sheet.autoSizeColumn(i);
		}
		*/

		adjustColumnWidth();  // 컬럼 너비 조정

		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		try {
			workbook.write(bos);
		} finally {
			bos.close();
			workbook.close();
		}
		return bos.toByteArray();
	}

	public static byte[] generateExcel(ExcelMetadata metadata, List<ExcelData> dataList) throws IOException {
		return generateExcel(metadata, dataList, null);
    }

	public static byte[] generateExcel(ExcelMetadata metadata, List<ExcelData> dataList, String base64Image) throws IOException {
		ExcelUtil excelUtil = new ExcelUtil();

		// 헤더 생성
		excelUtil.createHeader(metadata.getHeaders());

		// 데이터 변환 및 추가
		/*
		List<Map<String, Object>> excelData = dataList.stream()
				.map(data -> data.getData())
				.collect(Collectors.toList());
		*/

		excelUtil.addGridData(dataList, metadata.getHeaders().size());

		// 이미지 추가 (base64Image가 있는 경우)
	    if (base64Image != null && !base64Image.isEmpty()) {
	        // base64 prefix 제거
	        if (base64Image.contains(",")) {
	            base64Image = base64Image.split(",")[1];
	        }

	        // 데이터 영역 아래에 이미지 추가
	        int imageStartRow = dataList.size() + metadata.getHeaders().size() + 2;
	        // 이미지 위치 및 크기 조정
	        // A열부터 I열까지로 제한 (0부터 8까지)
	        int startCol = 0;    // A열
	        int endCol = 9;      // I열
	        int rowSpan = 10;    // 이미지 높이

	        // 이미지 추가 시 엑셀 셀 단위로 크기 제한
	        excelUtil.addImage(
	            base64Image,
	            startCol,                // 시작 열 (A)
	            imageStartRow,           // 시작 행
	            endCol,                  // 종료 열 (I)
	            imageStartRow + rowSpan  // 종료 행
	        );
	    }

		return excelUtil.generateExcelFile();
	}

	public static byte[] generateExcel(List<ExcelMetadata> _metadata, List<List<ExcelData>> _dataList, List<ExcelImgData> _base64Image) throws IOException {
		ExcelUtil excelUtil = new ExcelUtil();

		for ( int i=0;i<_metadata.size();i++ ) {
			ExcelMetadata metadata = _metadata.get(i);
			List<ExcelData> dataList = _dataList.get(i);
			ExcelImgData img = _base64Image.get(i);

			// 헤더 생성
			excelUtil.createHeader(metadata.getHeaders(), metadata.getStartRow(), metadata.getStartCol());

			// 데이터 변환 및 추가
			/*
				List<Map<String, Object>> excelData = dataList.stream()
				.map(data -> data.getData())
				.collect(Collectors.toList());
			 */

			excelUtil.addGridData(dataList, metadata.getHeaders().size() + metadata.getStartRow() );

			// 이미지 추가 (base64Image가 있는 경우)
			if (img != null && !img.getBase64Image().isEmpty()) {

				// base64 prefix 제거
				String base64Image = img.getBase64Image();
				if (base64Image.contains(",")) {
					base64Image = base64Image.split(",")[1];
				}

				// 데이터 영역 아래에 이미지 추가
				// 이미지 위치 및 크기 조정
				// A열부터 I열까지로 제한 (0부터 8까지)
				int startCol = img.getCol1();    // A열
				int endCol = img.getCol2();      // I열
				int imageStartRow = img.getRow1();
				int rowSpan = img.getRow2();    // 이미지 높이

				// 이미지 추가 시 엑셀 셀 단위로 크기 제한
				excelUtil.addImage(
						base64Image,
						startCol,                // 시작 열 (A)
						imageStartRow,           // 시작 행
						endCol,                  // 종료 열 (I)
						imageStartRow + rowSpan  // 종료 행
						);
			}
		}


		return excelUtil.generateExcelFile();
	}
}
