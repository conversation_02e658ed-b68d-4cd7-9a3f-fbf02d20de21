package kr.co.wayplus.travel.model;

import com.fasterxml.jackson.annotation.JsonInclude;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BoardCategory extends CommonDataSet {
    private Integer id;
    private Integer boardId;
    private String title;
    private String note;
    private Integer orderNum;
    private String useYn;
    private String defaultYn;
    private String searchYn;
    private String startDate;
    private String expireDate;

    //
    private int categoryCount;
    //청풍용
    private Integer userPointSetId;
    private String pointUseYn;
}
