package kr.co.wayplus.travel.web.manage;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.ibatis.annotations.Param;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartRequest;
import org.springframework.web.servlet.ModelAndView;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.MenuUser;
import kr.co.wayplus.travel.model.ProductInfo;
import kr.co.wayplus.travel.model.Reservation;
import kr.co.wayplus.travel.model.ReservationQna;
import kr.co.wayplus.travel.model.ReservationUserConnect;
import kr.co.wayplus.travel.model.SortData;
import kr.co.wayplus.travel.model.UserAttachFile;
import kr.co.wayplus.travel.model.UserCustomerOrder;
import kr.co.wayplus.travel.model.UserCustomerOrderHistory;
import kr.co.wayplus.travel.model.UserCustomerPayment;
import kr.co.wayplus.travel.model.UserGroupConnect;
import kr.co.wayplus.travel.model.UserPointExchange;
import kr.co.wayplus.travel.service.manage.ManageService;
import kr.co.wayplus.travel.service.manage.MenuManageService;
import kr.co.wayplus.travel.service.manage.ProductManageService;
import kr.co.wayplus.travel.service.manage.ReservationManageService;
import kr.co.wayplus.travel.service.manage.UserManageService;
import kr.co.wayplus.travel.service.user.UserPointService;
import kr.co.wayplus.travel.service.user.UserService;
import kr.co.wayplus.travel.util.FileInfoUtil;
import kr.co.wayplus.travel.util.ReservationUtil;

@Controller
@RequestMapping("/manage/reservation")
public class ReservationManageController extends BaseController {
    private final Logger logger = LoggerFactory.getLogger(getClass());

	@Value("${upload.file.path}")
	String externalImageUploadPath;

	@Value("${key.crypto.encrypt}")
    private String encrypt;
    @Value("${key.crypto.iv}")
    private String iv;

    @Value("${upload.file.max-size}")
    int maxFileSize;
    @Value("${upload.file.path}")
	private String fileUploadPath;

    /* 기본 파라미터 셋팅 */
    @Value("${pg.tosspay.method}")
    private String payMethod;
	@Value("${pg.tosspay.client.key}")
	private String pgTosspayClientKey;
	@Value("${pg.tosspay.server.key}")
	private String pgTosspayServerKey;

	final String addPath = "tourist/";

    private final ReservationManageService svcReservation;
    private final ManageService svcManage;
    private final ProductManageService svcProduct;
    private final UserManageService svcUser;
	private final UserPointService userPointService;
	private final UserService userService;
	private final MenuManageService menuManageService;
	private final ReservationUtil reservationUtil;

    public ReservationManageController(
			ReservationManageService svcReservation,
			ManageService svcManage,
			ProductManageService svcProduct,
			UserManageService userManageService,
			UserPointService userPointService,
			UserService userService,
			MenuManageService menuManageService,
			ReservationUtil reservationUtil) {
        this.svcReservation = svcReservation;
        this.svcManage  = svcManage;
        this.svcProduct  = svcProduct;
        this.svcUser  = userManageService;
		this.userPointService = userPointService;
		this.userService = userService;
		this.menuManageService = menuManageService;
		this.reservationUtil = reservationUtil;
	}

//	<!--################################### Reservation ###################################-->
    @GetMapping("/list")
    public ModelAndView list( HttpServletRequest request ){
        ModelAndView mav = new ModelAndView();
        HashMap<String, Object> paramMap = new HashMap<>();

        String url = request.getRequestURL().toString();
        String strPath = url.substring( url.lastIndexOf("/")+1 );

        mav.setViewName("manage/sub/reservation/list");
        return mav;
    }

    @GetMapping("/form")
    public ModelAndView form(
    		 HttpServletRequest request,
    		@RequestParam(value="mode", defaultValue="V") String mode,
    		@RequestParam(value="id", defaultValue="0") String id){

        ModelAndView mav = new ModelAndView();
        HashMap<String, Object> paramMap = new HashMap<>();

        MenuUser userMenu = null;
        ProductInfo product = null;

        if(mode.equals("U")) {
        	paramMap.put("id", id);
        	Reservation data = svcReservation.selectOneReservation(paramMap);

			HashMap<String, Object> param = new HashMap<>();
			param.put("reservationId", id);

			if( svcUser.selectCountUserCustomerOrder(param) > 0){
				UserCustomerOrder orderInfo = svcUser.selectOneUserCustomerOrder(param);
				data.setOrderInfo( orderInfo );
			} else {
				data.setOrderInfo(new UserCustomerOrder());
			}
			
        	mav.addObject("reservation",  data);

        	paramMap.clear();
        	paramMap.put("userEmail",data.getUserEmail());

        	if( data.getUserEmail() != null ) {
	        	LoginUser user = svcUser.getUserDetail(paramMap);
	        	if(user != null) {
	        		mav.addObject("user", user);
	        	} else {
	        		user = new LoginUser().addGuestUser(data.getUserEmail());

	        		mav.addObject("user", user);
	        	}
        	}

        	if(data.getFileId() != null) {
        		HashMap<String, Object> paramMapF = new HashMap<>();
        		paramMapF.put("fileId", data.getFileId());

        		UserAttachFile file = svcUser.selectOneUserAttachFile(paramMapF);
        		mav.addObject("file", file);
        		mav.addObject("fileUrl", "/upload/"+addPath+file.getUploadFilename() );
        	}

        	if(data.getProductSerial() != null && data.getProductTourId() != null) {
        		paramMap.put( "productSerial", data.getProductSerial() );
        		paramMap.put( "productTourId", data.getProductTourId() );
        		product = svcProduct.selectProductInfo(paramMap);

        		HashMap<String, Object> paramsMenu = new HashMap<>();
				
        		paramsMenu.put("menuId", product.getProductMenuId());
				
        		userMenu =  menuManageService.selectOneMenuUser(paramsMenu);

				mav.addObject("product", product);
        	} else {
        		mav.addObject("product",      new ProductInfo());
        	}
        } else {
        	mav.addObject("reservation",  new Reservation());
        	mav.addObject("user",         new LoginUser());
        	mav.addObject("product",      new ProductInfo());
        }
        mav.addObject("mode", mode);
        mav.addObject("pgTosspayCKey", pgTosspayClientKey);
        mav.addObject("pgTosspaySKey", pgTosspayServerKey);


        /*
        if( userMenu != null) {
        	if( userMenu.getMenuSubType().equals("stay")) {
        		mav.addObject("fromMode", "stay");
        	} else  if( userMenu.getMenuSubType().equals("package") ) {
        		mav.addObject("fromMode", "program");
        	}
        }
        */

        mav.setViewName("manage/sub/reservation/form");

        return mav;
    }

  //<!--################################### ajax ###################################-->
    @GetMapping("/calendar-count")
    @ResponseBody
    public HashMap<String, Object> inquiry_calendar_count_ajax(
    		HttpServletRequest request,
    		Reservation data,
    		@RequestParam HashMap<String, Object> paramMap ){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		ArrayList<HashMap<String, Object> > list = svcReservation.selectListCountReservationContentByCalendar(paramMap);

    		retrunMap.put("data",  list);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

        return retrunMap;
    }

    @GetMapping("/calendar-check-list")
    @ResponseBody
    public HashMap<String, Object> inquiry_calendar_check_list_ajax(
    		HttpServletRequest request,
    		Reservation data,
    		@RequestParam HashMap<String, Object> paramMap ){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		ArrayList<HashMap<String, Object> > list = svcReservation.selectListReservationContentByCheckList(paramMap);

    		retrunMap.put("data",  list);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

        return retrunMap;
    }

    @GetMapping("/status-count")
    @ResponseBody
    public HashMap<String, Object> reservation_count_status_type_ajax(
    		HttpServletRequest request,
    		Reservation data ){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		HashMap<String, Object> paramMap = new HashMap<>();
    		paramMap.put("cancelYn",     data.getCancelYn());

    		retrunMap.put("data", svcReservation.selectListReservationCountStatusType(paramMap) );

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

        return retrunMap;
    }

    @PostMapping("/list")
    @ResponseBody
    public HashMap<String, Object> reservation_list_ajax(
    		HttpServletRequest request,
    		Reservation ic,
    		@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length,

    		@Param(value="dateType") String dateType,
    		@Param(value="dateFrom") String dateFrom,
    		@Param(value="dateTo") String dateTo,
    		@Param(value="reservationCode") String reservationCode,
    		@Param(value="searchKey") String searchKey,

    		@Param(value="userEmail") String userEmail,
    		@Param(value="groupId") String groupId,

    		@Param(value="categoryId") String categoryId,
    		@Param(value="applyCode") String applyCode,
			@Param(value="cancelYn") String cancelYn,
    		@Param(value="cancelCode") String cancelCode,
    		@Param(value="titleLike") String titleLike,
    		@Param(value="contentLike") String contentLike,
    		@Param(value="deleteYn") String deleteYn
    		){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		HashMap<String, Object> paramMap = new HashMap<>();

    		List<SortData> listSort = getListOrder(request);
    		paramMap.put("listSort", listSort);

    		if(length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
    		}

    		if( deleteYn != null ) {
    			paramMap.put("deleteYn", deleteYn);
    		}

    		paramMap.put("dateType", dateType);
    		paramMap.put("dateFrom", dateFrom);
    		paramMap.put("dateTo",   dateTo + " 23:59:59");
    		paramMap.put("reservationCode", reservationCode);
    		paramMap.put("searchKey", searchKey);

    		paramMap.put("userEmail", userEmail);
    		paramMap.put("groupId", groupId);

    		paramMap.put("applyCode", applyCode);
			paramMap.put("cancelYn", cancelYn);
    		paramMap.put("cancelCode", cancelCode);
			paramMap.put("categoryId", categoryId);
			paramMap.put("titleLike", titleLike);
			paramMap.put("contentLike", contentLike);

    		int totalCount = svcReservation.selectCountReservation(paramMap);

    		retrunMap.put("recordsTotal", totalCount);
    		retrunMap.put("recordsFiltered", totalCount);
    		retrunMap.put("data", svcReservation.selectListReservation(paramMap));

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

        return retrunMap;
    }

    @PostMapping("/save")
    @ResponseBody
    public HashMap<String, Object> inquiry_save_ajax(
    		HttpSession session,
    		@RequestParam(value="mode", defaultValue="I") String mode,
    		@RequestParam(value="isGuest", defaultValue="false") boolean isGuest,
			@RequestParam(value="isGroup", defaultValue="false") String isGroup,
			@RequestParam(value="originPickPeople", defaultValue="0") int originPickPeople,
			@RequestParam(value="changePickPeople", defaultValue="0") int changePickPeople,
    		Reservation data,
    		HttpServletRequest request,
    		BindingResult bindingResult
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			if (isGroup.equals("N")) { 
				HashMap<String, Object> capacityCheckResult = svcReservation.checkPossibleReservation(data);
				if (capacityCheckResult.get("result").equals("restDate")) {
					retrunMap.put("result", "restDate");
					retrunMap.put("failDate", capacityCheckResult.get("failDate"));
					return retrunMap;
				}

				if(!capacityCheckResult.get("result").equals("success")) {
					retrunMap.put("result", "duplicateFail");
					retrunMap.put("failOptionName", capacityCheckResult.get("failOptionName"));
					retrunMap.put("failDate", capacityCheckResult.get("failDate"));
					retrunMap.put("dupCapacity", capacityCheckResult.get("dupCapacity"));

					return retrunMap;
				}
			}

        	if(_user instanceof LoginUser) {
        		LoginUser manager = (LoginUser)_user;
        		HashMap<String, Object> paramMap = new HashMap<>();

        		String thumbnailUrl = "";

	    		if(mode.equals("I")) {
	    			if(data.getCreateId() == null) data.setCreateId(manager.getUserEmail());

	    			if(isGuest) {
	    				String uuidEmail = String.valueOf(UUID.randomUUID());

	    				data.setUserEmail( uuidEmail );
	    				data.setCreateId( uuidEmail );

	    				if(session.getAttribute("encrypt") != null) encrypt = (String) session.getAttribute("encrypt");
	    				if(session.getAttribute("iv") != null) iv = (String) session.getAttribute("iv");

	    				LoginUser userGuest = new LoginUser()
    						.addGuestUser( uuidEmail )
	    					.addUserName( data.getUserName())
	    					.addUserJoinType( "reservation" )
	    					.addEncrypt( encrypt )
	    					.addIv( iv )
	    					.addUserPassworad("Guest");
	    				;

    		            svcUser.createUser(userGuest, false);
	    			}

	    			svcReservation.insertReservation(data);

	    			if(data.getTotalAmount() != null) {
	    				UserCustomerPayment payData = new UserCustomerPayment()
	    					.addReservationId(data.getId())
	    					.addUserEmail(data.getUserEmail())
	    					.addPayAmount(data.getTotalAmount())
	    					.addPayDivision("G")
	    					.addPayComment("최초 결제금액 등록")
	    					.addCreateId(data.getCreateId())
	    				;

	    				svcUser.insertUserCustomerPayment(payData);
						/*
						//확정예약건에 따른 사용자 등급산정
						if(_user instanceof LoginUser) {
							LoginUser user = (LoginUser)_user;
							LoginUser userInfo = svcUser.getUserDetail(user.getUserEmail());
							String userGrade = userInfo.getUserGrade();

							HashMap<String, Object> rsvMap = new HashMap<>();
							rsvMap.put("userEmail", userInfo.getUserEmail());
							rsvMap.put("reservationCode", "3");
							rsvMap.put("cancelYn", "N");

							//확약개수
							int rsvCount = svcReservation.selectCountReservation(rsvMap);
							switch (rsvCount) {
								case 1 -> {
									if (userGrade.equals("Guest")) userInfo.setUserGrade("Friend");
									userPointService.createUpGradePoint(user, "Friend");
								}
								case 5 -> {
									if (userGrade.equals("Friend")) userInfo.setUserGrade("Master");
									userPointService.createUpGradePoint(user, "Master");
								}
								case 10 -> {
									UserCustomerPayment loginUser = userService.selectUserPayTotalSummary(rsvMap);
									if (userGrade.equals("Master") && userInfo.getMailingYn().equals("Y") && loginUser.getPayAmount() >= 3000000 ) userInfo.setUserGrade("VIP");
									userPointService.createUpGradePoint(user, "VIP");
								}
							}
							userService.updateUserInfo(userInfo, false);
						}
						*/
	    			}
	    		} else {
	    			if(data.getId() == null) throw new Exception("데이터 처리중 필수 파라미터가 없습니다.");

	    			svcReservation.updateReservation(data);
	    		}
	    		retrunMap.put("result", "success");
	    		retrunMap.put("message", "처리가 완료 되었습니다.");
	    		retrunMap.put("data", data);
        	} else {
        		retrunMap.put("result", "fail");
	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
        	}
		} catch (Exception e) {
			retrunMap.put("result", "error");
			e.printStackTrace();

			if(e.getMessage() != null) {
				retrunMap.put("message", e.getMessage());
			} else {
				retrunMap.put("message", "처리중 문제가 발생했습니다.");
			}
		}
        return retrunMap;
    }


    @PostMapping("/delete")
    @ResponseBody
    public HashMap<String, Object> inquiry_delete_ajax(
    		Reservation data
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		if(data.getId() == null) throw new Exception("파라미터가 없습니다.");

    		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser manager = (LoginUser)_user;
        		HashMap<String, Object> paramMap = new HashMap<>();

        		if(data.getCreateId() == null) data.setCreateId(manager.getUserEmail());

	    		svcReservation.deleteReservation(data);

	    		retrunMap.put("result", "success");
	    		retrunMap.put("message", "처리가 완료 되었습니다.");
        	} else {
        		retrunMap.put("result", "fail");
	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
        	}
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
		}

        return retrunMap;
    }

    @PostMapping("/restore")
    @ResponseBody
    public HashMap<String, Object> inquiry_restore_ajax(
    		Reservation data
    		){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		if(data.getId() == null) throw new Exception("파라미터가 없습니다.");

    		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser manager = (LoginUser)_user;
        		HashMap<String, Object> paramMap = new HashMap<>();

        		if(data.getCreateId() == null) data.setCreateId(manager.getUserEmail());

	    		svcReservation.restoreReservation(data);

	    		retrunMap.put("result", "success");
	    		retrunMap.put("message", "처리가 완료 되었습니다.");
        	} else {
        		retrunMap.put("result", "fail");
	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
        	}
    	} catch (Exception e) {
    		retrunMap.put("result", "fail");
    		retrunMap.put("message", "처리중 문제가 발생했습니다.");
    	}


    	return retrunMap;
    }

    @PostMapping("/tourist-file-upload")
	@ResponseBody
	public HashMap<String, Object> touristFileUpload(
			MultipartRequest request,
			@ModelAttribute LoginUser userreal,
			@RequestParam(value="service_type", defaultValue="tourist-list") String serviceType,
			@RequestParam(value="id") Long id){
    	HashMap<String, Object> resultMap = new HashMap<>();
    	LoginUser manage = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

    	try{
    		MultipartFile multipartFile = null;
			if(request.getFile("tourist") != null){

				multipartFile = request.getFile("tourist");
				if(multipartFile.getSize() > 0){
					if(multipartFile.getSize() > maxFileSize){
						throw new Exception(String.valueOf(maxFileSize/1024/1024) + "MB 이하의 파일만 첨부 가능합니다.");
					}

					String uploadName = UUID.randomUUID().toString();

					File file = new File(fileUploadPath+addPath);
					if(!file.exists()) file.mkdirs();
					//MultipartFile file = request.getFile("tourist");

					logger.debug("File Uploaded : " + multipartFile.getOriginalFilename());

					UserAttachFile uploadedFile = new UserAttachFile();
					//productDetailScheduleImage.setDetailId(0);
					uploadedFile.setUploadPath(fileUploadPath+addPath);
					uploadedFile.setServiceType(serviceType);
					uploadedFile.setUploadFilename(uploadName);
					uploadedFile.setOriginFilename(multipartFile.getOriginalFilename());
					uploadedFile.setFileSize((int) multipartFile.getSize());
					uploadedFile.setFileMimetype(multipartFile.getContentType());
					if (multipartFile.getOriginalFilename().contains(".")) {
						uploadedFile.setFileExtension(multipartFile.getOriginalFilename().substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
					}
					uploadedFile.setUserEmail(userreal.getUserEmail());
					uploadedFile.setUploadId(manage.getUserEmail());

					String extension = uploadedFile.getFileExtension();

					if (!extension.equals("xlsx") && !extension.equals("xls")) {
			            throw new IOException("엑셀파일만 업로드 해주세요.");
			        }

					HashMap<String, Object> paramMap = new HashMap<>();
					paramMap.put("userEmail", userreal.getUserEmail());
					paramMap.put("deleteYn", "N");

					Integer cnt = svcUser.selectCountUserAttachFile(paramMap);
					if(cnt > 0) {
						ArrayList<UserAttachFile> list = svcUser.selectListUserAttachFile(paramMap);

						for (UserAttachFile _old : list) {
							if(_old != null) {
		    					FileInfoUtil.deleteImageFile_real(_old); /*실제 파일 제거*/
		    					svcUser.deleteUserAttachFile(_old);
		    				}
						}
					}

					multipartFile.transferTo(new File(fileUploadPath+addPath+uploadName));
					String imgUrl = "/upload/"+addPath+ uploadName;
					//userreal.setUserPassportImgPath( imgUrl );
					svcUser.insertUserAttachFile(uploadedFile);

					svcReservation.updateReservation( new Reservation().addId(id).addFileId( uploadedFile.getFileId() ) );

					resultMap.put("fileUrl", imgUrl);
					resultMap.put("file_id", uploadedFile.getFileId());
					resultMap.put("file", uploadedFile);

					resultMap.put("result", "success");
					resultMap.put("message", "저장되었습니다.");
				}
			}else{
				throw new Exception("첨부된 파일이 없습니다.");
			}

    	} catch (Exception e){
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

    	return resultMap;
    }

	@PostMapping("/tourist-file-user-reg")
	@ResponseBody
	public HashMap<String, Object> touristFileUserReg(
			HttpSession session,
			Reservation data,
			@ModelAttribute LoginUser userreal,
			@RequestParam(value="id") Long id,
			@RequestParam(value="fileId") Integer fileId){
    	HashMap<String, Object> resultMap = new HashMap<>();

    	LoginUser manage = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

    	try{
    		svcReservation.deleteReservationUserConnect( new ReservationUserConnect().addReservationId(id) );

    		HashMap<String, Object> paramMap = new HashMap<>();

    		paramMap.put("id", data.getId());
    		data = svcReservation.selectOneReservation( paramMap );

    		paramMap.clear();
    		paramMap.put("fileId", fileId);
			paramMap.put("deleteYn", "N");

			UserAttachFile uaf = svcUser.selectOneUserAttachFile(paramMap);

			String extension = uaf.getFileExtension();
			File file = new File( uaf.getRealFilePath() );
			FileInputStream files = new FileInputStream( file );

			Workbook workbook = null;
	        if (extension.equals("xlsx")) {
	            workbook = new XSSFWorkbook( files );
	        } else if (extension.equals("xls")) {
	            workbook = new HSSFWorkbook( files );
	        }

	        Sheet worksheet = workbook.getSheetAt(0);

	        /**
			 * *이름, 연락처, email없으면 안됨..
			 * 1. email있으면 해당 email사용, 없으면 next...
			 * 2. 이름과 연락처 동일한게 있는지 체크 있으면 사용, 없으면 next...
			 * 3. uuid 처리...
			 */
	        for (int i = 1; i < worksheet.getPhysicalNumberOfRows(); i++) {
	            Row row = worksheet.getRow(i);
	            //System.out.println(  row.getCell(0) + " " + row.getCell(1)+ " " + row.getCell(2) );

	            Cell _cNameKr = row.getCell(0);
	            Cell _cNameEn = row.getCell(1);
	            Cell _cSex    = row.getCell(2);
	            Cell _cPassNo = row.getCell(3);
	            Cell _cBrithD = row.getCell(4);
	            Cell _cPassEx = row.getCell(5);
	            Cell _cphone  = row.getCell(6);
	            Cell _cemail  = row.getCell(7);

	            String userEmail = null, userName = "", userNameEn = "", userGander = "", userPassNo = "", userBirthday="", userPassportExpirationDay="", userMobile = "";

	            boolean isGuestReg = false;

	            HashMap<String, Object> paramMapUser = new HashMap<>();

	            /*기존계정 확언 절차*/
	            if(!_cphone.getStringCellValue().equals("")  ||
	               !_cNameKr.getStringCellValue().equals("") ||
	               !_cBrithD.getStringCellValue().equals("") ||
	               !_cemail.getStringCellValue().equals("")   ) {

	            	String searchKey = userName+userMobile;
	            	if(_cemail != null) {
	            		userEmail = _cemail.getStringCellValue();
 	            		paramMapUser.put("email", ( userEmail ));
 	            	}
	            	if(_cNameKr  != null ) {
	            		userName = _cNameKr.getStringCellValue();
	            	}
	            	if(_cphone != null ) {
	            		userMobile = _cphone.getStringCellValue();
	            	}
	            	if(_cBrithD != null ) {
	            		userBirthday = _cBrithD.getStringCellValue();

	            		if( !userBirthday.equals("") ) {
	            			paramMapUser.put("userBirthday",  userBirthday );
	            		}
	            	}

	            	if(_cNameEn  != null ) {
	            		userNameEn = _cNameEn.getStringCellValue();
	            	}
	            	if(_cSex  != null ) {
	            		userGander = _cSex.getStringCellValue();

	            		if( userGander.toUpperCase().equals("M") ) {
	            			userGander = "남자";
	            		} else if( userGander.toUpperCase().equals("F") ) {
	            			userGander = "여자";
	            		}
	            	}
	            	if(_cPassNo != null ) {
	            		userPassNo = _cPassNo.getStringCellValue();
	            	}
	            	if(_cPassEx != null ) {
	            		userPassportExpirationDay = _cPassEx.getStringCellValue();
	            	}

	            	if(searchKey.trim().length() > 0)
	            		paramMapUser.put("searchKey", searchKey );

	            	if( userEmail != "" ) {
		            	LoginUser regedUser = svcUser.getUserDetail(paramMapUser);

		            	if( regedUser != null ) {
		            		isGuestReg = false;

		            		userEmail = regedUser.getUserEmail();
		            	} else {
		            		isGuestReg = true;
		            	}
	            	} else {
	            		isGuestReg = true;
	            	}
	            } else {
	            	/*아무것도 없네?*/
	            	logger.info("---------------:: 처리할 Row데이터 없음 (종료). ::---------------");
	            	break;
	            }

	            /*신규 비회원계정 등록처리.*/
	            if(isGuestReg) {
	            	if(userEmail == null)
	            		userEmail = String.valueOf(UUID.randomUUID());

	            	if(userEmail == "")
	            		userEmail = String.valueOf(UUID.randomUUID());

    				if(session.getAttribute("encrypt") != null)
    					encrypt = (String) session.getAttribute("encrypt");
    				if(session.getAttribute("iv") != null)
    					iv = (String) session.getAttribute("iv");

    				LoginUser userGuest = new LoginUser()
						.addGuestUser( userEmail )
    					.addUserName( userName )
    					.addUserNameEn( userNameEn )
    					.addUserPassportNo( userPassNo )
    					.addUserBirthday( userBirthday )
    					.addUserPassportExpirationDay( userPassportExpirationDay )
    					.addUserMobile( userMobile )
    					.addUserJoinType( "reservation" )
    					.addEncrypt( encrypt )
    					.addIv( iv )
    					.addUserPassworad("Guest");
    				;

		            svcUser.createUser(userGuest, false);

		            /*예약처리 명단 등록 및 유저 그룹 등록 처리*/

		            if( data.getGroupId() != null ) {
		            	svcUser.insertUserGroupConnect(
	            			new UserGroupConnect()
	            			.addUserEmail(userEmail)
	            			.addGroupId(  Long.valueOf( data.getGroupId() ) )
	            		);
		            }


		            svcReservation.insertReservationUserConnect(
	            		new ReservationUserConnect()
	            		.addReservationId(id)
	            		.addUserEmail(userEmail)
	            		.addCreateId(manage.getUserEmail()));
	            	}
	            	isGuestReg = false;
	            }


			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");

    	} catch (Exception e){
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}

	@PostMapping("/customer-list")
    @ResponseBody
    public HashMap<String, Object> reservation_customer_list_ajax(
    		HttpServletRequest request,
    		ReservationUserConnect data,
    		@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		HashMap<String, Object> paramMap = new HashMap<>();

    		List<SortData> listSort = getListOrder(request);
    		paramMap.put("listSort", listSort);

    		if(length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
    		}

    		if(data.getReservationId() != null)
    			paramMap.put("reservationId", data.getReservationId());

    		int totalCount = svcReservation.selectCountReservationUserConnect(paramMap);

    		retrunMap.put("recordsTotal", totalCount);
    		retrunMap.put("recordsFiltered", totalCount);
    		retrunMap.put("data", svcReservation.selectListReservationUserConnect(paramMap));

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

        return retrunMap;
    }

	@PostMapping("/order/save")
    @ResponseBody
    public HashMap<String, Object> usercoustomer_save_ajax(
    		HttpSession session,
    		//Multipart
    		@ModelAttribute Reservation r,
    		@ModelAttribute UserCustomerOrder uco,
    		@Param(value="cancelAmount") Long cancelAmount,
    		HttpServletRequest request,
    		BindingResult bindingResult
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		HashMap<String, Object> param = new HashMap<>();
        		param.put("id", r.getId() );
        		Reservation r2 = svcReservation.selectOneReservation(param);

        		String orderId = r2.getPayMoid();
        		String txTid = r2.getTid();
        		Long amount = r2.getTotalAmount();

        		UserCustomerPayment payDataG = new UserCustomerPayment()
					.addUserEmail( r2.getUserEmail() )
					.addReservationId( r2.getId() )
					.addPayMoid( orderId )
					.addPayAmount( amount * -1 )
					.addPayDivision("G")
					.addPayComment("최초 결제금액 등록")
					.addCreateId( getBaseUserEmail() )
				;

        		UserCustomerOrderHistory _ucoh = new UserCustomerOrderHistory()
        				.addUserEmail( r2.getUserEmail())
        				.addPayMoid(orderId)
        				.addPayStatusType( "PAY" )
        				.addAmt( amount )
        				.addPayTid(txTid)
        				//.addStatusResultCode(status)
        				//.addStatusResultMsg(method)
    				;

				svcUser.updateUserCustomerOrder(uco);
        		svcUser.insertUserCustomerOrderHistory(_ucoh);

        		if( r2.getUsePoint() > 0) {
        			UserCustomerPayment payDataDbyUsePoint = new UserCustomerPayment()
						.addUserEmail(r2.getUserEmail())
						.addReservationId( r2.getId() )
						.addPayMoid( orderId )
						.addPayAmount( r2.getUsePoint() * -1 )
						.addPayDivision("D")
						.addPayComment("포인트 사용금액")
						.addCreateId( getBaseUserEmail() )
					;
        			svcUser.insertUserCustomerPayment(payDataDbyUsePoint); //토스페이 결제금액.

	                userPointService.refundUserUsedPoint(r2.getUserPointUsedLogId(), "예약취소 포인트 환불", getBaseUserEmail());
        		}
        		UserCustomerPayment payDataD = new UserCustomerPayment()
					.addUserEmail( r2.getUserEmail() )
					.addReservationId( r2.getId() )
					.addPayMoid( orderId )
					.addPayAmount( cancelAmount * -1 )
					.addPayDivision("D")
					.addPayComment("결제금액")
					.addCreateId( getBaseUserEmail() )
				;

        		svcUser.insertUserCustomerPayment(payDataD); //토스페이 결제금액.
        		svcUser.insertUserCustomerPayment(payDataG); //받을금액

        		svcReservation.updateReservation(r);

	    		retrunMap.put("result", "success");
	    		retrunMap.put("message", "처리가 완료 되었습니다.");
        	} else {
        		retrunMap.put("result", "fail");
	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
        	}
		} catch (Exception e) {
			retrunMap.put("result", "error");

			if(e.getMessage() != null) {
				retrunMap.put("message", e.getMessage());
			} else {
				retrunMap.put("message", "처리중 문제가 발생했습니다.");
			}
		}
        return retrunMap;
    }


    //<!--################################### ajax ###################################-->
      @PostMapping("/qna/list")
      @ResponseBody
      public HashMap<String, Object> reservation_qna_list_ajax(
      		HttpServletRequest request,
      		Reservation ic,
      		@RequestParam(value="start", defaultValue="0") int start,
      		@RequestParam(value="length", defaultValue="10") int length,

//      		@Param(value="dateType") String dateType,
//      		@Param(value="dateFrom") String dateFrom,
//      		@Param(value="dateTo") String dateTo,
//      		@Param(value="reservationCode") String reservationCode,
//      		@Param(value="searchKey") String searchKey,

      		@Param(value="reservationId") String reservationId,
      		@Param(value="userEmail") String userEmail,
      		@Param(value="categoryId") String categoryId,
      		@Param(value="applyCode") String applyCode,
      		@Param(value="cancelCode") String cancelCode,
      		@Param(value="titleLike") String titleLike,
      		@Param(value="contentLike") String contentLike
      		){
      	HashMap<String, Object> retrunMap = new HashMap<>();

      	try {
      		HashMap<String, Object> paramMap = new HashMap<>();

      		List<SortData> listSort = getListOrder(request);
      		paramMap.put("listSort", listSort);

      		if(length >= 0) {
  				paramMap.put("itemStartPosition", start);
  				paramMap.put("pagePerSize", length);
      		}


//      		paramMap.put("dateType", dateType);
//      		paramMap.put("dateFrom", dateFrom);
//      		paramMap.put("dateTo",   dateTo + " 23:59:59");
//      		paramMap.put("reservationCode", reservationCode);
//      		paramMap.put("searchKey", searchKey);

      		paramMap.put("reservationId", reservationId);
      		paramMap.put("userEmail", userEmail);

//      		paramMap.put("applyCode", applyCode);
//
//      		paramMap.put("cancelCode", cancelCode);
//  			paramMap.put("categoryId", categoryId);
//  			paramMap.put("titleLike", titleLike);
//  			paramMap.put("contentLike", contentLike);

      		int totalCount = svcReservation.selectCountReservationQna(paramMap);

      		retrunMap.put("recordsTotal", totalCount);
      		retrunMap.put("recordsFiltered", totalCount);
      		retrunMap.put("data", svcReservation.selectListReservationQna(paramMap));

      		retrunMap.put("result", "success");
      		retrunMap.put("message", "처리가 완료 되었습니다.");
  		} catch (Exception e) {
  			retrunMap.put("result", "fail");
  			retrunMap.put("message", "처리중 문제가 발생했습니다.");
  			logger.error(e.getMessage());
  		}

          return retrunMap;
      }

      @PostMapping("/qna/save")
      @ResponseBody
      public HashMap<String, Object> reservation_qna_save_ajax(
      		HttpSession session,
      		@RequestParam(value="mode", defaultValue="I") String mode,
      		ReservationQna data,
      		//Multipart
      		HttpServletRequest request,
      		BindingResult bindingResult
      	){
      	HashMap<String, Object> retrunMap = new HashMap<>();

      	try {
      		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

          	if(_user instanceof LoginUser) {
          		LoginUser manager = (LoginUser)_user;
          		HashMap<String, Object> paramMap = new HashMap<>();

          		String thumbnailUrl = "";

  	    		if(mode.equals("I")) {
  	    			if(data.getCreateId() == null) data.setCreateId(manager.getUserEmail());
  	    			svcReservation.insertReservationQna(data);
  	    		} else {
  	    			if(data.getId() == null) throw new Exception("데이터 처리중 필수 파라미터가 없습니다.");
  	    			svcReservation.updateReservationQna(data);
  	    		}
  	    		retrunMap.put("result", "success");
  	    		retrunMap.put("message", "처리가 완료 되었습니다.");
          	} else {
          		retrunMap.put("result", "fail");
  	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
          	}
  		} catch (Exception e) {
  			retrunMap.put("result", "error");

  			if(e.getMessage() != null) {
  				retrunMap.put("message", e.getMessage());
  			} else {
  				retrunMap.put("message", "처리중 문제가 발생했습니다.");
  			}
  		}
          return retrunMap;
      }


      @PostMapping("/qna/delete")
      @ResponseBody
      public HashMap<String, Object> reservation_qna_delete_ajax(
      		ReservationQna data
      	){
      	HashMap<String, Object> retrunMap = new HashMap<>();

      	try {
      		if(data.getId() == null) throw new Exception("파라미터가 없습니다.");

      		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

          	if(_user instanceof LoginUser) {
          		LoginUser manager = (LoginUser)_user;
          		HashMap<String, Object> paramMap = new HashMap<>();

          		if(data.getCreateId() == null) data.setCreateId(manager.getUserEmail());

  	    		svcReservation.deleteReservationQna(data);

  	    		retrunMap.put("result", "success");
  	    		retrunMap.put("message", "처리가 완료 되었습니다.");
          	} else {
          		retrunMap.put("result", "fail");
  	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
          	}
  		} catch (Exception e) {
  			retrunMap.put("result", "fail");
  			retrunMap.put("message", "처리중 문제가 발생했습니다.");
  		}

          return retrunMap;
      }

      @PostMapping("/qna/restore")
      @ResponseBody
      public HashMap<String, Object> reservation_qna_restore_ajax(
      		ReservationQna data
      		){
      	HashMap<String, Object> retrunMap = new HashMap<>();

      	try {
      		if(data.getId() == null) throw new Exception("파라미터가 없습니다.");

      		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

          	if(_user instanceof LoginUser) {
          		LoginUser manager = (LoginUser)_user;
          		HashMap<String, Object> paramMap = new HashMap<>();

          		if(data.getCreateId() == null) data.setCreateId(manager.getUserEmail());

  	    		svcReservation.restoreReservationQna(data);

  	    		retrunMap.put("result", "success");
  	    		retrunMap.put("message", "처리가 완료 되었습니다.");
          	} else {
          		retrunMap.put("result", "fail");
  	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
          	}
      	} catch (Exception e) {
      		retrunMap.put("result", "fail");
      		retrunMap.put("message", "처리중 문제가 발생했습니다.");
      	}


      	return retrunMap;
      }

	@GetMapping("/stock-info")
	@ResponseBody
    public HashMap<String, Object> getProductStockInfo(
		@RequestParam(value="startDate") String startDate,
		@RequestParam(value="endDate") String endDate,
		@RequestParam(value="productSerial") String productSerial) {
		HashMap<String, Object> retrunMap = new HashMap<>();

      	try {
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

          	if(_user instanceof LoginUser) {
          		HashMap<String, Object> rsvParam = new HashMap<>();
		  		rsvParam.put("startDate", startDate);
		  		rsvParam.put("endDate", endDate);
		  		rsvParam.put("productSerial", productSerial);
		  		
		  		String isHaveSpecialPrice = svcReservation.selectOneIsHaveSpecialPrice(rsvParam);
				ArrayList<Reservation> productRsvStockList = reservationUtil.selectListCalcReservation(rsvParam);
				ArrayList<Reservation> productRsvStockListResult = new ArrayList<>();

				for (Reservation reservation : productRsvStockList) {
					if (reservation.getIsRestDate() == 0) {
						productRsvStockListResult.add(reservation);
					}
				}

				retrunMap.put("isHaveSpecialPrice", isHaveSpecialPrice);
				retrunMap.put("data", productRsvStockListResult);
  	    		retrunMap.put("result", "success");
          	} else {
          		retrunMap.put("result", "fail");
  	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
          	}
      	} catch (Exception e) {
			e.printStackTrace();
      		retrunMap.put("result", "fail");
      		retrunMap.put("message", "처리중 문제가 발생했습니다.");
      	}


      	return retrunMap;
    }
}
