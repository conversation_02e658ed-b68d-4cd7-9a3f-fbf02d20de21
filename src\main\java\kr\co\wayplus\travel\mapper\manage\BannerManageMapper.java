package kr.co.wayplus.travel.mapper.manage;

import kr.co.wayplus.travel.model.BannerCategory;
import kr.co.wayplus.travel.model.MainBannerImage;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;

@Mapper
@Repository
public interface BannerManageMapper {

    int selectMainBannerImageListCount(HashMap<String, Object> param);

    ArrayList<MainBannerImage> selectMainBannerImageList(HashMap<String, Object> param);

    void insertMainBannerImage(MainBannerImage bannerImage);

    void updateMainBannerDelete(HashMap<String, Object> param);

    void updateMainBannerOrder(HashMap<String, Object> param);

    MainBannerImage selectMainBannerImage(HashMap<String, Object> param);

    void updateMainBannerImage(MainBannerImage bannerImage);

    ArrayList<MainBannerImage> selectMainBannerPreviewList(HashMap<String, Object> param);

    void updateMainBannerStatus(MainBannerImage bannerImage);

    ArrayList<BannerCategory> selectListBannerCategory(HashMap<String, Object> param);

	BannerCategory selectOneBannerCategory(HashMap<String, Object> param);

    void insertBannerCategory(BannerCategory pc);

    void updateBannerCategory(BannerCategory bc);

    void updateBannerCategorySort(BannerCategory bannerCategory);

    void deleteBannerCategory(BannerCategory pc);

    void updateBannerCategoryUseYn(BannerCategory pc);



}
