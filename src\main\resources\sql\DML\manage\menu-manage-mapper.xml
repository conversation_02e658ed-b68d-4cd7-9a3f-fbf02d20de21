<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.manage.MenuManageMapper">
	<!--################################### manageMenu ###################################-->
	 <select id="selectCountMenuUser" parameterType="HashMap" resultType="Integer">
		with list as (
			SELECT a.menu_id, b.board_id, c.place_code, upper_menu_id, menu_name, menu_url, menu_type, menu_sub_type, menu_sort, menu_acronym,menu_desc,
				   navbar_yn, use_yn, a.delete_yn, hide_yn, virtual_yn, main_expose_yn, main_expose_type,
				   a.create_id, a.create_date, a.last_update_id, a.last_update_date,
			       CASE WHEN upper_menu_id IS NULL THEN 0
					    ELSE (SELECT CASE WHEN upper_menu_id IS NULL THEN 1 ELSE 2 END FROM menu_user mu2 WHERE mu2.menu_id = a.upper_menu_id)
				    END menu_depth,
				   (select name from code_item where code = a.menu_type and upper_code in ('menuType')) menu_type_name,
				   (select name from code_item where upper_code in(select code from code_item where upper_code in ('menuType')) and code = a.menu_sub_type and upper_code = a.menu_type ) menu_sub_type_name,
			       (select count(*) from menu_user b where b.upper_menu_id = a.menu_id) subMenuCount
			  FROM menu_user a
			  left join menu_connect_board b on a.menu_id = b.menu_id
			  left join menu_connect_place c on a.menu_id = c.menu_id
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isRecommandOnly) and isRecommandOnly">inner join survey_recommand sr on a.menu_id = sr.menu_id</if>)
        SELECT count(menu_id) FROM list
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isNullUpperMenuId)" >
				<choose >
					<when test="isNullUpperMenuId">and upper_menu_id is null</when>
					<otherwise>and upper_menu_id is not null</otherwise>
				</choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isNullPlaceCode)" >
				<choose >
					<when test="isNullPlaceCode">and c.place_code is null</when>
					<otherwise>and c.place_code is not null</otherwise>
				</choose>
			</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperMenuId)" >	and upper_menu_id=#{upperMenuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuName)" >	and menu_name=#{menuName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuUrl)" >	and menu_url=#{menuUrl}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)" >	and menu_type=#{menuType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSubType)" >	and menu_sub_type=#{menuSubType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuDepth)" >	and menu_depth=#{menuDepth}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSort)" >	and menu_sort=#{menuSort}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuAcronym)" >	and menu_acronym=#{menuAcronym}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuDesc)" >	and menu_desc=#{menuDesc}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(navbarYn)" >	and navbar_yn=#{navbarYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(virtualYn)" >	and virtual_yn=#{virtualYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
         </where>
    </select>

	<select id="selectListMenuUser" parameterType="HashMap" resultType="MenuUser">
		with list as (
			SELECT a.menu_id, b.board_id, c.place_code, upper_menu_id, menu_name, menu_url, menu_type, menu_sub_type, menu_action, menu_sort, menu_acronym,menu_desc,
				   navbar_yn, use_yn, a.delete_yn, hide_yn, virtual_yn, main_expose_yn, main_expose_type, point_use_yn, board_point_id, comment_point_id, user_favorite_point_id,
				   a.create_id, a.create_date, a.last_update_id, a.last_update_date,
			       CASE WHEN upper_menu_id IS NULL THEN 0
						ELSE (SELECT CASE WHEN upper_menu_id IS NULL THEN 1 ELSE 2 END FROM menu_user mu2 WHERE mu2.menu_id = a.upper_menu_id)
				    END menu_depth,
				   (select name from code_item where code = a.menu_type and upper_code in ('menuType')) menu_type_name,
				   (select name from code_item where upper_code in(select code from code_item where upper_code in ('menuType')) and code = a.menu_sub_type and upper_code = a.menu_type ) menu_sub_type_name,
			       (select count(*) from menu_user b where b.upper_menu_id = a.menu_id) subMenuCount
			  FROM menu_user a
			  left join menu_connect_board b on a.menu_id = b.menu_id
			  left join menu_connect_place c on a.menu_id = c.menu_id
			  <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isRecommandOnly) and isRecommandOnly">inner join survey_recommand sr on a.menu_id = sr.menu_id</if>)
		SELECT *
		  FROM(
	        SELECT @rownum:=@rownum+1 AS rownum, a.*
	          FROM list a
	          join (SELECT @rownum:= 0) rnum
	         <where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isNullUpperMenuId)" >
					<choose >
						<when test="isNullUpperMenuId">and upper_menu_id is null</when>
						<otherwise>and upper_menu_id is not null</otherwise>
					</choose>
				</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isNullPlaceCode)" >
					<choose >
						<when test="isNullPlaceCode">and place_code is null</when>
						<otherwise>and place_code is not null</otherwise>
					</choose>
				</if>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperMenuId)" >	and upper_menu_id=#{upperMenuId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuName)" >	and menu_name=#{menuName}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuUrl)" >	and menu_url=#{menuUrl}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)" >	and menu_type=#{menuType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSubType)" >	and menu_sub_type=#{menuSubType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuAction)" >	and menu_action=#{menuAction}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuDepth)" >	and menu_depth=#{menuDepth}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSort)" >	and menu_sort=#{menuSort}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuAcronym)" >	and menu_acronym=#{menuAcronym}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuDesc)" >	and menu_desc=#{menuDesc}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(navbarYn)" >	and navbar_yn=#{navbarYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(virtualYn)" >	and virtual_yn=#{virtualYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mainExposeYn)" >	and main_expose_yn=#{mainExposeYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mainExposeType)" >	and main_expose_type=#{mainExposeType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
				and delete_yn='N'
	         </where>

		 	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort=='menuId'" >	menu_id	</when>
					<when test="sort=='upperMenuId'" >	upper_menu_id	</when>
					<when test="sort=='menuName'" >	menu_name	</when>
					<when test="sort=='menuUrl'" >	menu_url	</when>
					<when test="sort=='menuType'" >	menu_type	</when>
					<when test="sort=='menuSubType'" >	menu_sub_type	</when>
					<when test="sort=='menuAction'" >	menu_action	</when>
					<when test="sort=='menuIcon'" >	menu_icon	</when>
					<when test="sort=='menuSort'" >	menu_sort	</when>
					<when test="sort=='menuAcronym'" >	menuAcronym	</when>
					<when test="sort=='menuDesc'" >	menu_desc	</when>
					<when test="sort=='createId'" >	create_id	</when>
					<when test="sort=='createDate'" >	create_date	</when>
					<when test="sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="sort=='deleteYn'" >	delete_yn	</when>
					<when test="sort=='navbarYn'" >	navbar_yn	</when>
					<when test="sort=='useYn'" >	use_yn	</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort=='menuId'" >	menu_id	</when>
					<when test="item.sort=='upperMenuId'" >	upper_menu_id	</when>
					<when test="item.sort=='menuName'" >	menu_name	</when>
					<when test="item.sort=='menuUrl'" >		menu_url	</when>
					<when test="item.sort=='menuType'" >	menu_type	</when>
					<when test="item.sort=='menuSubType'" >	menu_sub_type	</when>
					<when test="item.sort=='menuAction'" >	menu_action	</when>
					<when test="item.sort=='menuIcon'" >	menu_icon	</when>
					<when test="item.sort=='menuSort'" >	menu_sort	</when>
					<when test="item.sort=='menuAcronym'" >	menuAcronym	</when>
					<when test="item.sort=='menuDesc'" >	menu_desc	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='navbarYn'" >	navbar_yn	</when>
					<when test="item.sort=='useYn'" >	use_yn	</when>
		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
		  ) a

		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneMenuUser" parameterType="HashMap" resultType="MenuUser">
        SELECT menu_id, upper_menu_id, menu_name, menu_url, menu_type, menu_sub_type, menu_action, menu_sort, menu_acronym,menu_desc,
               navbar_yn, use_yn, hide_yn, virtual_yn, main_expose_yn, main_expose_type, delete_yn,
               create_id, create_date, last_update_id, last_update_date
          FROM menu_user a
          join (SELECT @rownum:= 0) rnum
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperMenuId)" >	and upper_menu_id=#{upperMenuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuName)" >	and menu_name=#{menuName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuUrl)" >	and menu_url=#{menuUrl}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)" >	and menu_type=#{menuType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSubType)" >	and menu_sub_type=#{menuSubType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuAction)" >	and menu_action=#{menuAction}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSort)" >	and menu_sort=#{menuSort}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuAcronym)" >	and menu_acronym=#{menuAcronym}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuDesc)" >	and menu_desc=#{menuDesc}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(navbarYn)" >	and navbar_yn=#{navbarYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mainExposeYn)" >	and main_expose_yn=#{mainExposeYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mainExposeType)" >	and main_expose_type=#{mainExposeType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			and delete_yn='N'
         </where>
	</select>

	<!--
	<select id="selectOneMenuUserByUrl" parameterType="String" resultType="MenuUser">
		SELECT menu_id,
			   upper_menu_id,
			   menu_name,
			   menu_acronym,
			   menu_url,
			   menu_type,
			   menu_sub_type,
			   menu_sort,
			   menu_desc,
			   navbar_yn,
			   use_yn,
			   create_date
		  FROM menu_user
		 WHERE menu_url=#{value}
	</select>
	 -->

	<select id="selectListMenuUserSubListById" parameterType="HashMap" resultType="MenuUser">
		SELECT menu_id,
			   upper_menu_id,
			   menu_name,
			   menu_url,
			   menu_type,
			   menu_sub_type,
			   menu_action,
			   menu_sort,
			   menu_desc,
			   navbar_yn,
			   virtual_yn,
			   use_yn,
			   create_date,
			   (select count(*) from menu_user b where b.upper_menu_id = a.upper_menu_id) subMenuCount
		  FROM menu_user a
		 WHERE delete_yn='N'
		   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" > AND use_yn=#{useYn}	</if>
		   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(virtualYn)" > AND virtual_yn=#{virtualYn}	</if>
		   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" > AND upper_menu_id = #{menuId}</if>
		   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)" > AND menu_type = #{menuType}	</if>
		 order by menu_sort		   
	</select>

	<select id="selectReviewMenuUrl" resultType="String">
		SELECT CONCAT((SELECT menu_url FROM menu_user mu2 WHERE mu2.menu_id = mu.upper_menu_id), mu.menu_url)
		FROM board_setting AS bs 
		LEFT JOIN menu_connect_board AS mcb on mcb.board_id = bs.id 
		LEFT JOIN menu_user AS mu on mu.menu_id = mcb.menu_id
		WHERE bs.type_code = 'review'
	</select>

	<insert id="insertMenuUser" parameterType="MenuUser" useGeneratedKeys="true" keyProperty="menuId">
        INSERT INTO menu_user
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperMenuId)" >	upper_menu_id=#{upperMenuId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuName)" >	menu_name=#{menuName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuUrl)" >	menu_url=#{menuUrl},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)" >	menu_type=#{menuType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSubType)" >	menu_sub_type=#{menuSubType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuAction)" >	menu_action=#{menuAction},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSort)" >	menu_sort=#{menuSort},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuAcronym)" >	menu_acronym=#{menuAcronym},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuDesc)" >	menu_desc=#{menuDesc},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(navbarYn)" >	navbar_yn=#{navbarYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(virtualYn)" >	virtual_yn=#{virtualYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(hideYn)" >	hide_yn=#{hideYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pointUseYn)" >	point_use_yn=#{pointUseYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardPointId)" >	board_point_id=#{boardPointId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentPointId)" >	comment_point_id=#{commentPointId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userFavoritePointId)" >	user_favorite_point_id=#{userFavoritePointId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mainExposeYn)" >	main_expose_yn=#{mainExposeYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mainExposeType)" >	main_expose_type=#{mainExposeType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	delete_yn=#{deleteYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId}, last_update_id=#{lastUpdateId},	</if>
			create_date = now(), last_update_date = now()
		</set>
    </insert>

    <update id="updateMenuUser" parameterType="MenuUser">
        UPDATE menu_user
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperMenuId) and isMenuUpperId" >	upper_menu_id=#{upperMenuId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isMenuUpperId) and !isMenuUpperId" >	upper_menu_id=null,	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuName)" >	menu_name=#{menuName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuUrl)" >	menu_url=#{menuUrl},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)" >	menu_type=#{menuType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSubType)" >	menu_sub_type=#{menuSubType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuAction)" >	menu_action=#{menuAction},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSort)" >	menu_sort=#{menuSort},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuAcronym)" >	menu_acronym=#{menuAcronym},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuDesc)" >	menu_desc=#{menuDesc},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(navbarYn)" >	navbar_yn=#{navbarYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(virtualYn)" >	virtual_yn=#{virtualYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pointUseYn)" >	point_use_yn=#{pointUseYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardPointId)" >	board_point_id=#{boardPointId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentPointId)" >	comment_point_id=#{commentPointId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userFavoritePointId)" >	user_favorite_point_id=#{userFavoritePointId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mainExposeYn)" >	main_expose_yn=#{mainExposeYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mainExposeType)" >	main_expose_type=#{mainExposeType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	delete_yn=#{deleteYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	last_update_date=#{lastUpdateDate},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
		</where>
    </update>

    <update id="deleteMenuUser" parameterType="MenuUser">
        UPDATE menu_user
        <set>
			delete_yn='Y',last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
		</where>
    </update>
</mapper>