/* 팝업 기능 */
.popup-layer {z-index:1000;position:absolute;top:100px;left:20px;width:452px;height:542px;max-width:100%;overflow:hidden;}
.popup-layer .popup-notice {height:540px;width:450px;max-width:100%;border:1px solid #CCCCCC;display:none;}
.popup-layer .popup-notice.pc {display:block;}
.popup-layer .popup-notice .popup-content {width:450px;max-width:100%;height:500px;background-color:#ffffff;}
.popup-layer .popup-notice .popup-content .popup-image {aspect-ratio:9/10;width:100%;margin:0 auto;object-fit:fill;}
.popup-layer .popup-notice .popup-content .popup-text {width:calc(100% - 2rem);height: calc(500px - 2rem);padding:1rem; margin:0 auto;overflow-y:scroll;}
.popup-layer .popup-notice .popup-control {display: flex;width:calc(100%);height:38px;justify-content: space-evenly;padding:5px;background-color:#444444;position: relative;z-index: 9999;}
.popup-layer .popup-notice .popup-control-button {border:none; padding:3px; background-color: transparent; color:#fff;}
.popup-layer .popup-notice .popup-control-button:hover {cursor:pointer;color:#999;}
.popup-layer .popup-notice .popup-guide {display:block;position:relative;top:calc(-50% - 30px);background-color: #dddddd;}
.popup-layer .popup-notice .popup-guide .guide-left{float:left;margin-left:10px;}
.popup-layer .popup-notice .popup-guide .guide-right{float:right;margin-right:10px;}
.popup-layer .popup-notice .popup-page-guide {border-radius:50%;height:30px;width:30px;border:1px solid #dddddd;opacity:0.9;background:#FFFFFF;}
.popup-layer .popup-notice .popup-page-guide:hover {opacity:1;border-color:#cccccc;}
.popup-layer .popup-notice .popup-page-guide img {height:100%;width:100%;}

.popup-notice-bar {z-index:1000;width:100%;height:101px;max-width:100%;overflow:hidden; position: relative;}
.popup-notice-bar .popup-bar {width:100%;overflow:hidden;}
.popup-notice-bar .popup-bar .popup-content {width:100%;text-align:center;margin:0 auto;border-bottom:1px solid #cccccc;}
.popup-notice-bar .popup-bar .popup-control .popup-control-button{background-color:transparent;color:#888888;padding-left:2rem;margin-left:1rem;font-size:0.75rem;border:none;}
.popup-notice-bar .popup-bar .popup-control .popup-control-button:hover {cursor:pointer;color:#444444;}
.popup-notice-bar .popup-bar .popup-guide {}
.popup-notice-bar .popup-bar .popup-guide .guide-left{float:left;margin-left:10px;}
.popup-notice-bar .popup-bar .popup-guide .guide-right{float:right;margin-right:10px;}
.popup-notice-bar .popup-bar .popup-page-guide {height:20px;width:20px;border:none;opacity:0.9;background:#FFFFFF;}
.popup-notice-bar .popup-bar .popup-page-guide:hover {opacity:1;border-color:#cccccc;cursor:pointer;}
.popup-notice-bar .popup-bar .popup-page-guide img {height:100%;width:100%;}
.popup-notice-bar.pc .popup-bar {height: 101px;}
.popup-notice-bar.pc .popup-bar .popup-content {height: 80px;}
.popup-notice-bar.pc .popup-bar .popup-content .popup-image {height:80px;object-fit:contain;}
.popup-notice-bar.pc .popup-bar .popup-control {height: 20px;display:flex;justify-content:right;}

.popup-notice-bar.mobile {display:none;}
.popup-notice-bar.mobile .popup-bar {height: 141px;}
.popup-notice-bar.mobile .popup-bar .popup-content {height: 120px;}
.popup-notice-bar.mobile .popup-bar .popup-content .popup-image {height:120px;object-fit:contain;}
.popup-notice-bar.mobile .popup-bar .popup-control {height: 20px;display:flex;justify-content:right;background: #fff;}

@media screen and (max-width: 960px) {

}
@media screen and (max-width: 768px) {
    
    .popup-layer  {z-index: 2; height:unset; left:15px;}
    .popup-layer .popup-notice.pc {display:none;}
    .popup-layer .popup-notice.mobile {display:block;}
    .popup-control .d-none, 
    .popup-notice-bar.pc {display:none;}
    .popup-notice-bar.mobile {height:60px;display:block;}
    .popup-notice-bar.mobile .popup-content{
        width:100%;
        height:60px;
    }
    .popup-notice-bar.mobile .popup-content .popup-image{
        width:100%;
        height:100%;
        object-fit: cover;
    }
    .popup-notice-bar.mobile .popup-bar .popup-control{
        position:absolute;
        top:50%;
        transform: translateY(-50%);
        right:15px;
        background-color:unset;
    }
    .popup-notice-bar.mobile .popup-bar .popup-control button.closed{
        font-size:0;
        padding:0;
        border:none;
        margin-left:0;
        background:unset;
    }
    .popup-notice-bar.mobile .popup-bar .popup-control button.closed img{
        width:16px;
        height:16px;
    }
    .popup-layer {
        width:calc(100% - 30px);
    }
    .popup-layer .popup-notice{
        height:unset;
    }
    .popup-layer .popup-notice .popup-content{
        width:100%;
        height:unset;
    }
    .popup-layer .popup-notice .popup-guide{
        display:none;
    }
    .popup-layer .popup-notice .popup-content .popup-text{
        height:unset;
    }


}
