package kr.co.wayplus.travel.model;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

@ToString
@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductDetailScheduleImage extends CommonDataSet {
    private int detailImageId;
    private int detailId;
    private String contentType;
    private String uploadPath;
    private String uploadFilename;
    private String fileExtension;
    private int fileSize;
    private String fileMimetype;
    private String originFilename;
    private List<MultipartFile> attach;
}
