package kr.co.wayplus.travel.model;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
@ToString
@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductDetailSchedule extends CommonDataSet {
    private int productTourId
                , productMenuId
                , detailImageId
                , detailId
                , detailSequence
                , tourspotId
                , tsId;
    private String detailCategory
                    , detailTitle
                    , address
                    , dayText
                    , attachImage
                    , uploadPath
                    , uploadFilename
                    , fileExtension
                    , fileSize
                    , fileMimetype
                    , originFilename
                    , detailNote
                    , timeText
                    , transportTime
                    , transportWay
                    , detailType
                    , tsTitle
                    , tsAppend
                    , registerType
                    , stayOption
                    , stayMinimumHeadCount
                    , stayMaximumHeadCount;
    private String[] attachImageNums;
    private ArrayList<PlaceSpotImage> imageSpotList;
    private List<ImageNumList> imageNumList;
    ArrayList<ProductDetailScheduleImage>  listProductDetailScheduleImage;

    @ToString
    @Setter
    @Getter
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ImageNumList {
        private int detailImageId
                , detailId;
        private String uploadPath
                , uploadFilename;
    }

    public void parseAndSetAttachImageNums() {
        this.attachImageNums = this.attachImage.split(",");
    }
}
