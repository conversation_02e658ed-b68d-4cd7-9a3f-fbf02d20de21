
/*main_banner*/
.main_banner {height: 400px;/*background: url(/images/front/demo/main_banner_01.png);*/background-size: cover;background-position: center;/*margin-top: 130px;*/box-sizing: border-box;}
.main_banner .link{cursor: pointer;}
.main-banner-item {width:100%;overflow:hidden;}
.main-banner-image {width:100%;height:400px;object-fit:cover;position: absolute;}
.main_banner_inner {color: #FFF;position: relative;}
.main_banner_title_box {position: absolute;top: 120px; left: 0;}
.main_banner_title_top {font-size: 40px;font-weight: 700;line-height: 40px;}
.main_banner_title_mid {font-size: 20px;font-weight: 500;line-height: 20px;margin: 20px 0 10px 0;}}
.main_banner_title_bot {color: #E3E3E3;font-weight: 500;line-height: 16px;}

.main-banner-overlap {width:100%;aspect-ratio:24 / 1;margin:0 auto;position:relative;top:-30%;}
.main-banner-control {width:calc(100% - 30px) !important;max-width:1200px;margin:0 auto;height:40px;overflow:hidden;position:relative !important;z-index:10;text-align: left !important; bottom:0 !important;}
.main-banner-indicator {display:inline-block;height:100%;margin:0 20px 0 0;padding:0;vertical-align:top;}
.main-banner-indicator li {margin-right:10px;}
.main-banner-indicator li:last-child {margin-right:0;}

.main-banner-indicator li.btn {display: inline-block;width: 40px;height: 40px; background:url('/images/icon/main_banner_arrow.svg') center center no-repeat;background-size: cover;cursor: pointer;}
/*.main-banner-indicator li.btn.btn_next {position: absolute; top: 435px; left: 54px;}*/
.main-banner-indicator li.btn.btn_prev {transform: rotateY(180deg);/* position: absolute; top: 435px; left: 0px;*/}
.dots, .swiper-pagination-bullet {width: 10px !important; height: 10px !important;border-radius: 50% !important;background: #E3E3E3 !important;margin-left: 0px !important;margin-right: 10px !important;cursor: pointer; opacity: unset !important;}
.dots_box .active, .swiper-pagination-bullet-active {background: #00F0FF !important;}


/*메인비주얼
.main_visual_box {width: 100%; height: 400px;background: url(/images/sample/main_visual_01.jpg) no-repeat;background-position: center center;background-size: cover;}
.main_visual {height: 100%;display: flex;align-items: center;}
.main_visual_title_box {color: #FFF;}
.main_visual_title {font-size: 40px;font-weight: 700;line-height: 40px;}
.main_visual_sub_title {font-size: 20px;font-weight: 500;line-height: 20px;margin: 20px 0 10px 0;}
.main_visual_ex {color: #E3E3E3;font-weight: 500;line-height: 16px;}
.dots_box {display: flex;margin-top: 40px;}
.dots_box li:last-child {margin: 0;}
.dots, .swiper-pagination-bullet {width: 10px !important; height: 10px !important;border-radius: 50% !important;background: #E3E3E3 !important;margin-left: 0px !important;margin-right: 10px !important;cursor: pointer; opacity: unset !important;}
.dots_box .active, .swiper-pagination-bullet-active {background: #00F0FF !important;}
*/
/*mice유니크베뉴*/
.mice_unique_contents {
    position: relative;
    height: 330px;
    display: flex;
    flex-wrap: nowrap;
    overflow: hidden;
    padding-top: 100px;
}

.mice_unique_title_box {
    width: calc(50% - 170px);
}

.mice_unique_ex {
    color: #222;
    font-size: 16px;
    font-weight: 300;
    line-height: 24px;
    margin: 24px 0 36px 0;
}

.mice_unique_img_box {
    position: absolute;
    left: 50%;
    margin-left: -80px;
}

.mice_unique_img {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    z-index: 99;
}

.mice_unique_img li {
    width: 486px;
    height: auto;
    margin-right: 24px;
}

.mice_unique_img li img {
    width: 100%; height: 100%;
}

.mice_unique_font {
    position: absolute;
    top: -68px; left: 0;
    color: #F3F3F3;
    font-family: "Alex Brush";
    font-size: 80px;
    z-index: 9;
}

/*공통_레이아웃*/
.main_contents {
    padding: 80px 0;
}

.main_title_box {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    margin-bottom: 40px;
}

.main_title {
    display: flex;
    align-items: center;
}

.main_title_bar {
    width: 1px;
    height: 11.5px;
    border-right: 1px solid #888;
    margin: 0 12px 0 18px;
}

.main_title_ex {
    color: #888;
    font-size: 18px;
}

.main_slide_wrap {
    position: relative;
}

.slide_arrow_prev {
    position: absolute;
    top: 50%; left: -87px;
}

.slide_arrow_next {
    position: absolute;
    top: 50%; right: -87px;
}

.main_slide_box {
    overflow: hidden;
}

.main_slide {
    width: 500%;
    /*overflow: hidden;*/
}

.main_slide_list {
    float: left;
    width: 282px;
    margin-right: 24px;
    cursor: pointer;
}

.main_slide_list:hover .product_img {
    transform: scale(1.1);
}

/*이색골프여행*/
.esc_golf {
    background: #FAFAFA;
}

/*파크골프*/
.park_golf {
    position: relative;
    padding: 42px 0 28px 0;
    background: #FAFAFA;
    min-height: 509px;
}

.park_golf_bg {
    width: 45%; height: 100%;
    background: #0062D4;
    position: absolute;
    top: 0; left: -20px;
    overflow: hidden;
}

.park_golf_bg_inner {
    position: relative;
}

.park_golf_bg_img {
    position: absolute;
    right: 0; top: 0;
}

.park_golf_contents {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    overflow: hidden;

}

.park_golf_title_wrap {
    padding-top: 38px;
}

.park_golf_title_ex {
    font-size: 20px;
    color: #fff;
}

.park_golf_title {
    margin: 10px 0 36px 0;
    color: #fff;
}

.all_view_box {
    overflow: hidden;
    height: 34px;
}

.all_view_box .all_view {
    margin-bottom: 14px;
    position: absolute;
    right: 10px;
}

.park_golf_slide_box {
    overflow: hidden !important;
    margin-left: 94px !important;
    max-width: 966px;
}

.park_golf_slide {
    width: 500%;
}

.park_golf_slide_list {
    float: left;
    width: 306px; min-width: 306px;
    height: 436px;
    padding: 17px 12px;
    margin-right: 24px;
    box-sizing: border-box;
    background: #FFF;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.10);
    cursor: pointer;
}

.park_golf_slide_list:hover .product_img {
    transform: scale(1.1);
}

.slide_paging_box {
    overflow: hidden;
    max-width: 1060px;
    height: 39px;
}

.slide_paging {
    position: absolute;
    margin-top: 18px;
    right: 10px;
}

/*여행 편의서비스*/
.convenience_service {
    padding-bottom: 80px;
}

.convenience_box {
    display: grid;
    grid-template-columns: repeat(3, calc(33.3% - 16px));
    grid-gap: 24px;
    cursor: pointer;
}

.convenience_img_box {
    position: relative;
    overflow: hidden;
}

.convenience_list {
    cursor: pointer;
}

.convenience_list:hover .convenience_img {
    transform: scale(1.1);
}

.convenience_category {
    position: absolute;
    top: unset;
    bottom: 0; left: 0;
    width: 120px;
    height: 40px;
    color: #FFF;
    font-size: 18px;
    text-align: center;
    font-weight: 500;
    line-height: 40px;
    z-index: 9;
    opacity: 0.9;
}

.convenience_img {
    transition: all 0.3s;
}

.convenience_img img {
    display: block;
    width: 100%; height: 100%;
}

.convenience_box .category_green {
    background: #31764D;
}

.convenience_box .category_blue {
    background: #0062D4;
}

.convenience_box .category_black {
    background: #333;
}

.convenience_box .main_slide_img {
    width: 100%; height: 384px;
    transition: all 0.3s;
}

/*공지사항_문의*/
.notice_inquiry {
    padding-bottom: 80px;
}

.notice_inquiry_contents {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
}

.notice_box {
    width: calc(100% - 384px - 24px);
}

.notice_list_box {
    border-top: 2px solid #666;
    border-bottom: 2px solid #666;
}

.notice_box_list {
   display: flex;
   justify-content: space-between;
    height: 58px;
    border-bottom: 1px solid #d9d9d9;
    box-sizing: border-box;
    line-height: 60px;
    cursor: pointer;
}

.notice_box_list:last-child {
    border: none;
}

.notice_box_list:hover .notice_box_list_title {
    text-decoration: underline;
}

.notice_box_list_title {
    width: calc(100% - 95px - 10px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.notice_box_list_day {
    width: 95px;
    padding: 0 5px;
}

.inquiry_box {
    width: 384px;
    padding: 60px 58px;
    box-sizing: border-box;
    background: #FAFAFA;
    color: #222;
}

.inquiry_box .look_btn {
    width: 100%;
}

.inquiry_sub_title {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 10px;
}

.inquiry_title {
    color: #333;
    font-size: 24px;
    font-weight: 600;
}

.inquiry_tel {
    font-size: 24px;
    font-weight: 300;
    margin: 10px 0 40px 0;
}

.inquiry_tel_bold {
    font-size: 30px;
    font-weight: 500;
}

/*반응형쿼리*/
@media screen and (max-width:1400px) {
    .main_banner_inner {width: 1200px; height: 100%;display: flex;align-items: center; }
    .slide_arrow_prev, .slide_arrow_next{display: none;}
}

@media screen and (max-width:1024px) {
    /*헤더영역만큼 메인비주얼 내림*/
    /*
    .main_visual_box {width: 100%;margin-top: 60px;}
    .main_contents {padding: 30px 0;}
    */
    .main_banner_title_box{padding: 0 20px;}
    .main-banner-control{padding:0 20px;}
}

@media screen and (max-width:768px) {
    /*mice유니크베뉴*/
    .mice_unique_contents {
        height: 600px;
        display: flex;
        flex-wrap: nowrap;
        padding-top: 30px;
        margin-bottom: 20px;
    }

    .mice_unique_title_box {
        width: 100%;
    }

    .mice_unique_img_box {
        position: absolute;
        bottom: -10px;
        left: 0px;
        margin-left: 0px;
        height: 330px;
    }

    /*파크골프*/
    .park_golf {
        padding: 0px 0 28px 0;
    }

    .park_golf_title_box {
        display: flex;
        align-items: center;
        color: #222;
    }

    .park_golf_title_ex {
        color: #222;
    }

    .park_golf_title {
        color: #222;
        margin: 0;
    }

    .park_golf_title_ex {
        margin-right: 20px;
    }

    .look_btn_box {
        display: flex;
        margin-top: 20px;
    }

    .park_golf_slide_wrap {
        position: relative;
        width: calc(100vw - 40px);
    }

    .park_golf_contents .all_view {
        position: absolute;
        top: -85px;
        right: 0;
    }

    .park_golf_bg {
        display: none;
    }

    .park_golf_contents {
        display: flex;
        flex-wrap: wrap;
    }

    .park_golf_contents .look_btn {
        margin-right: 10px;
    }

    .park_golf_slide_box {
        margin: 30px 0 0 0;
        margin-left: 0px !important;
    }

    /*숙박, 항공, 렌터카 카테고리 조정*/
    .convenience_category {
        width: 100%;
    }

    /*여행편의서비스*/
    .convenience_service {
        padding-bottom: 30px;
    }

    /*공지사항_문의 레이아웃 조정*/
    .notice_inquiry {
        padding-bottom: 50px;
    }
    .notice_inquiry_contents {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    .notice_box {
        width: 100%;
    }

    .inquiry_box {
        width: 100%;
        margin-top: 30px;
        padding: 30px 20px;
    }

    .inquiry_tel {
        margin: 10px 0 30px 0;
    }

    .inquiry_box .look_btn {
        margin-right: 10px;
    }
}

@media screen and (max-width:500px) {
    /*타이틀, 상품전체보기 레이아웃 조정*/
    .main_title_box {
        display: flex;
        flex-wrap: wrap;
    }

    /*타이틀 서브 텍스트 제거*/
    .main_title_bar, .main_title_ex {
        display: none;
    }
}

@media screen and (max-width:430px) {
    /*메인비주얼*/
    .main_visual {
        justify-content: center;
    }

    .main_visual_title_box {
        text-align: center;
    }
    .main_visual_title {
        font-size: 24px;
    }

    .main_visual_sub_title {
        font-size: 16px;
    }
    .main_visual_ex {
        font-size: 12px;
    }

    .dots_box {
        display: flex;
        justify-content: center;
    }

    /*유니크베뉴 설명문구 수정*/
    .mice_unique_contents {
        height: 520px;
    }

    .mice_unique_ex {
        margin: 20px 0;
    }

    .mice_unique_ex p {
        display: inline;
    }

    .mice_unique_font {
        font-size: 60px;
        top: -52px;
    }

    .mice_unique_img li {
        width: 425px; height: 250px;
    }

    .mice_unique_img_box {
        height: 250px;
    }

    /*상품 리스트 레이아웃 수정*/
    .main_slide {
        width: 100%;
        height: 410px;
    }

    .main_slide_list {
        width: 100%;
        margin-bottom: 10px;
    }

    /*파크골프_레이아웃 수정*/
    .park_golf_slide {
        width: 100%;
        height: 436px;
        overflow: hidden;
    }

    .park_golf_slide_list {
        width: 100%;
        margin-bottom: 10px;
    }

    .park_golf_title_ex {
        display: none;
    }

    /*숙박, 항공, 렌터카 레이아웃 조정*/
    .convenience_box {
        display: block;
    }

    .convenience_list {
        margin-bottom: 20px;
    }

    /*공지사항 및 문의*/
    .inquiry_sub_title {
        font-size: 18px;
    }

    .inquiry_sub_title {
        font-size: 18px;
    }

    .inquiry_tel {
        font-size: 20px;
    }

    .inquiry_tel_bold {
        font-size: 24px;
    }
}

@media screen and (max-width:375px) {
    /*파크골프_버튼_스타일_조정*/
    .park_golf_title_wrap {
        width: 100%;
    }

    .park_golf_contents .look_btn {
        width: 100%;
    }
}