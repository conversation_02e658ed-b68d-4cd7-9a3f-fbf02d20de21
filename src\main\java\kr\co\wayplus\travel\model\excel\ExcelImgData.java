package kr.co.wayplus.travel.model.excel;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import kr.co.wayplus.travel.builder.ExcelDataBuilder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ExcelImgData {
    private String id;                // 데이터 식별자
    private String base64Image;

    int col1,    // 시작 열
    row1,    // 시작 행
    col2,    // 종료 열
    row2;     // 종료 행

    public ExcelImgData() {
    }

    public void setSize(int col1,int col2,int row1,int row2) {
    	this.col1 = col1;
    	this.col2 = col2;
    	this.row1 = row1;
    	this.row2 = row2;
    }
}