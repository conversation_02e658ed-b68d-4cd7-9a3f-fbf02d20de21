package kr.co.wayplus.travel.web.manage;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.MultipartRequest;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.ServletRequest;
import jakarta.servlet.http.HttpServletRequest;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.BadgeAcquireHistory;
import kr.co.wayplus.travel.model.BoardAttachFile;
import kr.co.wayplus.travel.model.BoardCategory;
import kr.co.wayplus.travel.model.BoardComment;
import kr.co.wayplus.travel.model.BoardContents;
import kr.co.wayplus.travel.model.BoardSetting;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.ProductCategory;
import kr.co.wayplus.travel.model.SortData;
import kr.co.wayplus.travel.model.UserPointSet;
import kr.co.wayplus.travel.service.manage.BadgeManageService;
import kr.co.wayplus.travel.service.manage.BoardManageService;
import kr.co.wayplus.travel.service.manage.UserManageService;
import kr.co.wayplus.travel.service.user.UserPointService;
import kr.co.wayplus.travel.util.FileInfoUtil;
import kr.co.wayplus.travel.util.ImageUtil;
import kr.co.wayplus.travel.util.LoggerUtil;

@Controller
@RequestMapping("/manage/board")
public class BoardManageController extends BaseController {
    private final Logger logger = LoggerFactory.getLogger(getClass());

	@Value("${upload.file.path}")
	String externalImageUploadPath;

	final String addPath = "images/";

    @Value("${upload.file.max-size}")
    int maxFileSize;

    private final BoardManageService svcBorad;
    private final UserPointService userPointService;
    private final BadgeManageService badgeManageService;
	private final UserManageService userManageService;
	private final ImageUtil imageUtil;


    public BoardManageController(
    		BoardManageService svc1,
    		UserPointService svc2,
    		BadgeManageService svc3,
    		UserManageService svc4,
    		ImageUtil svc5) {
        this.svcBorad = svc1;
        this.userPointService = svc2;
        this.badgeManageService = svc3;
		this.userManageService = svc4;
		this.imageUtil = svc5;
    }
//	<!--################################### BoardSetting ###################################-->
    @GetMapping("/setting/list")
    public ModelAndView boardSetting_list(){
        ModelAndView mav = new ModelAndView();
        mav.setViewName("manage/sub/board/setting/list");
        return mav;
    }

    @GetMapping("/setting/form")
    public ModelAndView boardSetting_view(
    		@RequestParam(value="mode", defaultValue="I") String mode,
    		@RequestParam(value="id", defaultValue="0") String id){
        ModelAndView mav = new ModelAndView();
        mav.setViewName("manage/sub/board/setting/form");

        if(mode.equals("U")) {
        	HashMap<String, Object> paramMap = new HashMap<>();
        	paramMap.put("id", id);
        	mav.addObject("board",  svcBorad.selectOneBoardSetting(paramMap));
        	mav.addObject("p", paramMap);
        } else {
        	mav.addObject("board",  new BoardSetting());
        }
        mav.addObject("mode", mode);

        return mav;
    }

    @PostMapping("/setting-list")
    @ResponseBody
    public HashMap<String, Object> boardSetting_list_ajax(HttpServletRequest request, BoardSetting bc,
    		@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length,
    		@Param(value="boardId") String boardId,
    		@Param(value="useYn") String useYn,
    		@Param(value="titleLike") String titleLike,
    		@Param(value="contentLike") String contentLike,
    		@Param(value="contentsExis") String contentsExis,
    		@Param(value="notIds[]") Integer[] notIds
    		){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		HashMap<String, Object> paramMap = new HashMap<>();

    		ArrayList<Integer> menuIds = new ArrayList<Integer>();

    		List<SortData> listSort = getListOrder(request);
    		paramMap.put("listSort", listSort);

    		paramMap.put("sort", "menuName");
    		paramMap.put("sortOrder", "asc");

    		if(length > 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
    		}

    		paramMap.put("deleteYn", bc.getDeleteYn() );
			paramMap.put("boardId", boardId);
			paramMap.put("useYn", useYn);
			paramMap.put("titleLike", titleLike);
			paramMap.put("contentLike", contentLike);
			paramMap.put("contentsExis", contentsExis);

			if( notIds != null ) {
				paramMap.put("notIds", notIds);
			}

    		int totalCount = svcBorad.selectCountBoardSetting(paramMap);

    		retrunMap.put("recordsTotal", totalCount);
    		retrunMap.put("recordsFiltered", totalCount);
    		retrunMap.put("data", svcBorad.selectListBoardSetting(paramMap));

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

        return retrunMap;
    }

    @PostMapping("/setting-save")
    @ResponseBody
    public HashMap<String, Object> boardSetting_save_ajax(
    		@RequestParam(value="mode", defaultValue="I") String mode,
    		BoardSetting bc
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser user = (LoginUser)_user;
//        		HashMap<String, Object> paramMap = new HashMap<>();

	    		if(mode.equals("I")) {
	    			bc.setCreateId(user.getUserEmail());
	    			svcBorad.insertBoardSetting(bc);
	    		} else {
	    			bc.setLastUpdateId(user.getUserEmail());
	    			svcBorad.updateBoardSetting(bc);
	    		}

	    		retrunMap.put("result", "success");
	    		retrunMap.put("message", "처리가 완료 되었습니다.");
        	} else {
        		retrunMap.put("result", "fail");
	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
        	}
		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			retrunMap.put("info", e.getMessage());
			logger.debug(e.getCause().getLocalizedMessage());
		}
        return retrunMap;
    }

    @PostMapping("/setting-restore")
    @ResponseBody
    public HashMap<String, Object> boardSetting_restore_ajax(
    		BoardSetting bs
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		svcBorad.restoreBoardSetting(bs);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
		}


        return retrunMap;
    }

    @PostMapping("/setting-delete")
    @ResponseBody
    public HashMap<String, Object> boardSetting_delete_ajax(
    		BoardSetting bc
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		svcBorad.deleteBoardSetting(bc);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
		}

        return retrunMap;
    }
//	<!--################################### BoardContents ###################################-->
    @GetMapping("/contents/list")
    public ModelAndView board_list(
    		@RequestParam(value="boardId", defaultValue="0") Integer boardId,
    		@Param(value="titleLike") String titleLike,
    		@Param(value="contentLike") String contentLike){
		ModelAndView mav = new ModelAndView();
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("useYn","Y");
		paramMap.put("deleteYn","N");
		paramMap.put("subContentYn","N");
		ArrayList<BoardSetting> boardList = svcBorad.selectListBoardSetting(paramMap);
		mav.addObject("listBoard", boardList);
		if(boardId == 0 && !boardList.isEmpty()) {
			mav.addObject("board", boardList.get(0));
		}else{
			boolean init = false;
			for(BoardSetting board : boardList){
				if(board.getId() == boardId) {
					mav.addObject("board", board);
					init = true;
				}
			}
			if(!init && !boardList.isEmpty()) mav.addObject("board", boardList.get(0));
		}

		mav.setViewName("manage/sub/board/contents/list");
		return mav;
    }

    @GetMapping("/contents/form")
	public ModelAndView board_form(
			@RequestParam(value="mode", defaultValue="I") String mode,
			@RequestParam(value="bid", defaultValue="0") int bid,
			@RequestParam(value="id", defaultValue="0") String id){
		ModelAndView mav = new ModelAndView();
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("deleteYn","N");
		paramMap.put("boardId", bid);

		ArrayList<BoardSetting> listBoard = svcBorad.selectListBoardSetting(paramMap);

		ArrayList<BoardCategory> categorys = svcBorad.selectListBoardCategory(paramMap);

		if(bid == 0 && !listBoard.isEmpty()) {
			mav.addObject("setting", listBoard.get(0));
			mav.setViewName("manage/sub/board/contents/form");
		}else{
			boolean init = false;
			for(BoardSetting board : listBoard){
				if(board.getId() == bid) {
					mav.addObject("setting", board);
					init = true;
					switch (board.getTypeCode()){
						case "faq":
							paramMap.put("contentId", id);
							mav.addObject("comment", svcBorad.selectOneBoardComment(paramMap));
							mav.setViewName("manage/sub/board/contents/faq-form");
							break;
						case "inquiry":
						case "qna":
							paramMap.put("contentId", id);
							mav.addObject("comment", svcBorad.selectOneBoardComment(paramMap));
							mav.setViewName("manage/sub/board/contents/qna-form");
							break;
						case "gallery":
						case "review":
							mav.addObject("categoryList", svcBorad.selectListBoardCategory(paramMap));
							mav.setViewName("manage/sub/board/contents/review-form");
							break;
						case "board":
						case "common":
						case "press_news":
							mav.addObject("categoryList", svcBorad.selectListBoardCategory(paramMap));
							mav.setViewName("manage/sub/board/contents/press-news-form");
							break;
						case "mission":
							mav.addObject("categoryList", svcBorad.selectListBoardCategory(paramMap));
							mav.setViewName("manage/sub/board/contents/mission-form");
							break;
						default:
							mav.setViewName("manage/sub/board/contents/form");
							break;
					}
				}
			}
			if(!init && !listBoard.isEmpty()) mav.addObject("setting", listBoard.get(0));
		}
		mav.addObject("listBoard",  listBoard);
		mav.addObject("categorys",  categorys);

		if(mode.equals("U")) {
			paramMap.put("id", id);
			mav.addObject("board",  svcBorad.selectOneBoardContents(paramMap));
			paramMap.clear();
			paramMap.put("boardId", bid);
			paramMap.put("contentId", id);
			mav.addObject("attaches",  svcBorad.selectListBoardAttachFile(paramMap));
			mav.addObject("p", paramMap);
		} else {
			mav.addObject("board",  new BoardContents());
		}
		mav.addObject("mode", mode);
		return mav;
	}

    @GetMapping("/contents/view")
	public ModelAndView board_view(
			@RequestParam(value="mode", defaultValue="I") String mode,
			@RequestParam(value="bid", defaultValue="0") int bid,
			@RequestParam(value="id", defaultValue="0") String id){
		ModelAndView mav = new ModelAndView();
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("deleteYn","N");
		paramMap.put("boardId", bid);

		ArrayList<BoardSetting> listBoard = svcBorad.selectListBoardSetting(paramMap);

		ArrayList<BoardCategory> categorys = svcBorad.selectListBoardCategory(paramMap);

		if(bid == 0 && !listBoard.isEmpty()) {
			mav.addObject("setting", listBoard.get(0));
		}else{
			boolean init = false;
			for(BoardSetting board : listBoard){
				if(board.getId() == bid) {
					mav.addObject("setting", board);
					init = true;

					switch (board.getTypeCode()){
						case "faq":
							paramMap.put("contentId", id);
							mav.addObject("comment", svcBorad.selectOneBoardComment(paramMap));
							mav.setViewName("manage/sub/board/contents/faq-form");
							break;
						case "inquiry":
						case "qna":
							paramMap.put("contentId", id);
							mav.addObject("comment", svcBorad.selectOneBoardComment(paramMap));
							mav.setViewName("manage/sub/board/contents/qna-form");
							break;
						case "gallery":
						case "review":
							mav.addObject("categoryList", svcBorad.selectListBoardCategory(paramMap));
							mav.setViewName("manage/sub/board/contents/review-form");
							break;
						case "board":
						case "common":
						case "press_news":
							mav.addObject("categoryList", svcBorad.selectListBoardCategory(paramMap));
							mav.setViewName("manage/sub/board/contents/press-news-form");
							break;
						case "mission":
							mav.addObject("categoryList", svcBorad.selectListBoardCategory(paramMap));
							mav.setViewName("manage/sub/board/contents/mission-view");
							break;
					}
				}
			}
			if(!init && !listBoard.isEmpty()) mav.addObject("setting", listBoard.get(0));
		}
		mav.addObject("listBoard",  listBoard);
		mav.addObject("categorys",  categorys);

		paramMap.put("id", id);
		mav.addObject("board",  svcBorad.selectOneBoardContents(paramMap));
		paramMap.clear();
		paramMap.put("contentId", id);
		mav.addObject("attaches",  svcBorad.selectListBoardAttachFile(paramMap));
		mav.addObject("p", paramMap);

		return mav;
	}

    @GetMapping("/contents-attchlist")
    @ResponseBody
    public HashMap<String, Object> board_attachlist(
    		@RequestParam(value="id", defaultValue="0") String id
    		, @RequestParam(value="bid", defaultValue="0") String bid
    		){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		HashMap<String, Object> paramMap = new HashMap<>();
    		paramMap.put("boardId", bid);
    		paramMap.put("contentId", id);
    		retrunMap.put("listAttach",  svcBorad.selectListBoardAttachFile(paramMap));

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
		}

        return retrunMap;
    }

    @GetMapping("/contents-count")
    @ResponseBody
    public HashMap<String, Object> board_count_ajax(HttpServletRequest request, BoardContents bc,
    		@Param(value="boardId") String boardId,
    		@Param(value="boardIdLike") String boardIdLike,
    		@Param(value="isOnlyReview") Boolean isOnlyReview
    		){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		HashMap<String, Object> paramMap = new HashMap<>();

			paramMap.put("boardId", boardId);
			paramMap.put("boardIdLike", boardIdLike);
			paramMap.put("isOnlyReview", isOnlyReview);

    		int totalCount = svcBorad.selectCountBoardContents(paramMap);

    		retrunMap.put("count", totalCount);
    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

        return retrunMap;
    }
    @PostMapping("/contents-list")
    @ResponseBody
    public HashMap<String, Object> board_list_ajax(
    		HttpServletRequest request,
    		BoardContents bc,
    		@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length,
    		@Param(value="boardId") String boardId,
    		@Param(value="upperBoardId") String upperBoardId,
    		@Param(value="isUpperBoardId") Boolean isUpperBoardId,
    		@Param(value="titleLike") String titleLike,
    		@Param(value="contentLike") String contentLike,
			@Param(value="userEmail") String userEmail,
    		@Param(value="userName") String userName
    		, @Param(value="createId") String createId
    		, @Param(value="missionCheckYn") String missionCheckYn
    		, @Param(value="dateFrom") String dateFrom
    		, @Param(value="dateTo") String dateTo
    		){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		HashMap<String, Object> paramMap = new HashMap<>();

    		List<SortData> listSort = getListOrder(request);
    		paramMap.put("listSort", listSort);

    		if(length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
    		}

			paramMap.put("boardId", boardId);
			paramMap.put("upperBoardId", upperBoardId);
			paramMap.put("isUpperBoardId", isUpperBoardId);
			paramMap.put("titleLike", titleLike);
			paramMap.put("contentLike", contentLike);
			paramMap.put("missionCheckYn", missionCheckYn);
			paramMap.put("dateFrom", dateFrom);
			paramMap.put("dateTo", dateTo);
			paramMap.put("createId", createId);
			paramMap.put("deleteYn", bc.getDeleteYn() );
			paramMap.put("sort", "createDate");
			paramMap.put("sortOrder", "desc");

    		int totalCount = svcBorad.selectCountBoardContents(paramMap);

			ArrayList<BoardContents> boardContentsList = svcBorad.selectListBoardContents(paramMap);
			retrunMap.put("data", boardContentsList);

			HashMap<String, Object> boardSettingParamMap = new HashMap<>();
			boardSettingParamMap.put("useYn","Y");
			boardSettingParamMap.put("deleteYn","N");
			boardSettingParamMap.put("id",boardId);
			BoardSetting boardSetting = svcBorad.selectOneBoardSetting(boardSettingParamMap);
			ArrayList<LoginUser> users;
			if ( boardSetting.getUrl().toUpperCase().equals("PEOPLES") ) {
				HashMap<String, Object> userParam = new HashMap<String, Object>();
				if(length >= 0) {
					userParam.put("itemStartPosition", start);
					userParam.put("pagePerSize", length);
				}

				userParam.put("userEmail", userEmail);
				userParam.put("userName", userName);
				userParam.put("role", "USER");
				users = userManageService.selectUserListByRole(userParam);
				retrunMap.put("data", users);
				totalCount = userManageService.selectCountUserListByRole(userParam);
			}

			retrunMap.put("recordsTotal", totalCount);
    		retrunMap.put("recordsFiltered", totalCount);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
			e.printStackTrace();
		}

        return retrunMap;
    }

    @PostMapping("/contents-save")
    @ResponseBody
    public HashMap<String, Object> board_save_ajax(
    		@RequestParam(value="mode", defaultValue="I") String mode,
    		BoardContents bc,
    		MultipartHttpServletRequest request
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser user = (LoginUser)_user;
        		HashMap<String, Object> paramMap = new HashMap<>();

        		String thumbnailUrl = "";

	    		if(mode.equals("I")) {
	    			bc.setCreateId( user.getUserEmail());
	    			svcBorad.insertBoardContents(bc);
	    		} else {
	    			bc.setLastUpdateId( user.getUserEmail());
	    			svcBorad.updateBoardContents(bc);
	    		}
	    		List<MultipartFile> multipartFiles = null;

	    		Object obj = request.getParameterValues("deletes");

	    		if(request.getParameterValues("deletes") != null) {
	    			String[] deletes = request.getParameterValues("deletes");

	    			for (String strDeletes : deletes) {
	    				logger.debug(strDeletes);
	    				paramMap.clear();
	    				paramMap.put("fileId", strDeletes);

	    				BoardAttachFile _old = svcBorad.selectOneBoardAttachFile( paramMap );
	    				if(_old != null) {
	    					FileInfoUtil.deleteImageFile_real(_old); /*실제 이미지 파일 제거*/
	    					svcBorad.deleteBoardAttachFile(_old);
	    				}
					}
	    		}

	    		if(request.getFile("attachs") != null) {
	    			multipartFiles = request.getFiles("attachs");

	    			MultipartFile multipartFilesThumbnail = request.getFile("thumbnail");

	                if (multipartFiles.size() > 0) {
	                	File file = new File(externalImageUploadPath + addPath);
	                	if (!file.exists()) file.mkdirs();

	                    for (MultipartFile multipartFile : multipartFiles) {
	                        String uploadName = UUID.randomUUID().toString();
	                        multipartFile.transferTo(new File(externalImageUploadPath+ addPath + uploadName));
	                        logger.debug("User Question File Uploaded : " + multipartFile.getOriginalFilename());

	                        BoardAttachFile baf = new BoardAttachFile();
	                        baf.setBoardId(bc.getBoardId());
	                        baf.setContentId(bc.getId());
	                        baf.setServiceType("board");
	                        baf.setUploadPath(externalImageUploadPath+ addPath);
	                        baf.setUploadFilename(uploadName);
	                        baf.setOriginFilename(multipartFile.getOriginalFilename());
	                        baf.setFileSize((int) multipartFile.getSize());
	                        baf.setFileMimetype(multipartFile.getContentType());
	                        if (multipartFile.getOriginalFilename().contains(".")) {
	                            baf.setFileExtension(multipartFile.getOriginalFilename().substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
	                        }

	                        paramMap.clear();
	                        paramMap.put("contentId", bc.getId());

	                        if (user != null) {
	                            baf.setUploadId(String.valueOf(user.getUserEmail()));
	                        }

	                        svcBorad.insertBoardAttachFile(baf);

	                        if(multipartFilesThumbnail != null) {
	                        	if(multipartFile.getOriginalFilename().equals( multipartFilesThumbnail.getOriginalFilename() )) {
//			                        bc.setThumbnailUrl("/upload/images/"+uploadName);
//			            			svcBorad.updateBoardContents(bc);
			            			thumbnailUrl = "/upload/"+ addPath +uploadName;
	                        	}
	                        }
	                    }
	                }
	    		}

	    		boolean isThum = false;

	    		if(request.getParameter("thumbnail") != null ) {
	    			isThum = true;
        			thumbnailUrl = request.getParameter("thumbnail");
	    		} else if( thumbnailUrl.length() != 0 ) {
	    			isThum = true;
	    		}

        		if(isThum) {
        			bc.setThumbnailUrl(thumbnailUrl);
        			svcBorad.updateBoardContents(bc);
        		}

        		retrunMap.put("data", bc);
	    		retrunMap.put("result", "success");
	    		retrunMap.put("message", "처리가 완료 되었습니다.");
        	} else {
        		retrunMap.put("result", "fail");
	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
        	}
		} catch (Exception e) {

			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			retrunMap.put("info", e.getMessage());
			logger.error(e.getCause().getMessage());
		}
        return retrunMap;
    }

    @PostMapping("/contents-restore")
    @ResponseBody
    public HashMap<String, Object> board_restore_ajax(
    		BoardContents bc
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		svcBorad.restoreBoardContents(bc);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
		}


        return retrunMap;
    }

    @PostMapping("/contents-delete")
    @ResponseBody
    public HashMap<String, Object> board_delete_ajax(
    		BoardContents bc
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		svcBorad.deleteBoardContents(bc);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
		}

        return retrunMap;
    }

	@PostMapping("/contents/ck-image-upload")
	@ResponseBody
	public HashMap<String, Object> ckImageUpload(MultipartRequest request, @RequestParam(value="service_type", defaultValue="ck-editor") String serviceType){
		HashMap<String, Object> resultMap = new HashMap<>();
		LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		try{
			MultipartFile multipartFile = null;
			if(request.getFile("upload") != null){
				multipartFile = request.getFile("upload");
				if(multipartFile.getSize() > 0){
					if(multipartFile.getSize() > maxFileSize){
						throw new Exception(String.valueOf(maxFileSize/1024/1024) + "MB 이하의 파일만 첨부 가능합니다.");
					}

					String uploadName = UUID.randomUUID().toString();

					File file = new File(externalImageUploadPath+addPath);
					if(!file.exists()) file.mkdirs();

					multipartFile.transferTo(new File(externalImageUploadPath+addPath+uploadName));
					logger.debug("File Uploaded : " + multipartFile.getOriginalFilename());

					BoardAttachFile baf = new BoardAttachFile();
                    //baf.setBoardId(bc.getBoardId());
                    //baf.setContentId(bc.getId());
                    baf.setServiceType(serviceType);
                    baf.setUploadPath(externalImageUploadPath+ addPath);
                    baf.setUploadFilename(uploadName);
                    baf.setOriginFilename(multipartFile.getOriginalFilename());
                    baf.setFileSize((int) multipartFile.getSize());
                    baf.setFileMimetype(multipartFile.getContentType());
                    if (multipartFile.getOriginalFilename().contains(".")) {
                        baf.setFileExtension(multipartFile.getOriginalFilename().substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
                    }

                    if (user != null) {
                        baf.setUploadId(String.valueOf(user.getUserEmail()));
                    }

                    svcBorad.insertBoardAttachFile(baf);

					resultMap.put("result", "success");
					resultMap.put("url", "/upload/"+addPath+ uploadName);
					resultMap.put("file_id", baf.getFileId());
				}
			}else{
				throw new Exception("첨부된 파일이 없습니다.");
			}
		}catch (Exception e){
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}
//	<!--################################### BoardCategory ###################################-->
    @GetMapping("/category-list")
    @ResponseBody
    public HashMap<String, Object> category_list(
    		@RequestParam HashMap<String, Object> paramMap
    		){

    	HashMap<String, Object> retrunMap = new HashMap<>();
		try {
			ArrayList<BoardCategory> boardList = svcBorad.selectListBoardCategory(paramMap);

			retrunMap.put("categories", boardList);

			retrunMap.put("result", "success");
			retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			retrunMap.put("info", e.getMessage());
			logger.error(e.getCause().getMessage());
		}
		return retrunMap;
    }

    @PostMapping("/category-save")
    @ResponseBody
    public HashMap<String, Object> category_save_ajax(
    		@RequestParam(value="mode", defaultValue="I") String mode,
    		BoardCategory bc,
    		//MultipartHttp
    		ServletRequest request
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser user = (LoginUser)_user;
        		HashMap<String, Object> paramMap = new HashMap<>();

        		String thumbnailUrl = "";

	    		if(mode.equals("I")) {
	    			bc.setCreateId( user.getUserEmail());
	    			svcBorad.insertBoardCategory(bc);
	    		} else {
					//청풍만
	    			if(bc.getDefaultYn() != null ) {
	    				BoardCategory all_bc = new BoardCategory();
	    				all_bc.setBoardId( bc.getBoardId() );
	    				all_bc.setDefaultYn( "N" );
	    				svcBorad.updateBoardCategory(all_bc);
	    			}

	    			bc.setLastUpdateId( user.getUserEmail());
	    			svcBorad.updateBoardCategory(bc);
	    		}

	    		retrunMap.put("result", "success");
	    		retrunMap.put("message", "처리가 완료 되었습니다.");
        	} else {
        		retrunMap.put("result", "fail");
	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
        	}
		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			retrunMap.put("info", e.getMessage());
			logger.error(e.getCause().getMessage());
		}
        return retrunMap;
    }

    @PostMapping("/category-del")
	@ResponseBody
	public HashMap<String,Object> category_del(
			@ModelAttribute BoardCategory pc,
			@RequestParam(value="isReal", defaultValue="false") boolean isReal){
			HashMap<String,Object> retMap = new HashMap<String, Object>();

		try {
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			if(_user instanceof LoginUser) {
				LoginUser user = (LoginUser)_user;
				pc.setDeleteId( user.getUserEmail() );

				if(isReal) {
					svcBorad.deleteBoardCategoryReal(pc);
				} else {
					svcBorad.deleteBoardCategory(pc);
				}


				retMap.put("result", "success");
				retMap.put("message", "처리가 완료 되었습니다.");
			} else {
				retMap.put("result", "fail");
				retMap.put("message", "로그인 문제가 발생되었습니다.");
			}
		} catch (Exception e) {
			retMap.put("result","ERROR");
			retMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getCause().getMessage());
		}

		return retMap;
	}

	@PostMapping("/category-resotre")
	@ResponseBody
	public HashMap<String,Object> category_resotre(@ModelAttribute BoardCategory pc){
		HashMap<String,Object> retMap = new HashMap<String, Object>();

		try {
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			if(_user instanceof LoginUser) {
				LoginUser user = (LoginUser)_user;
				pc.setLastUpdateId( user.getUserEmail() );
				svcBorad.restoreBoardCategory(pc);

				retMap.put("result", "success");
				retMap.put("message", "처리가 완료 되었습니다.");
			} else {
				retMap.put("result", "fail");
				retMap.put("message", "로그인 문제가 발생되었습니다.");
			}

		} catch (Exception e) {
			retMap.put("result","ERROR");
			retMap.put("message","처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getCause().getMessage());
		}

		return retMap;
	}

//	<!--################################### BoardComment ###################################-->
	@PostMapping("/comment")
	@ResponseBody
	public HashMap<String, Object> boardCommentSave(
			@RequestParam(value="mode", defaultValue="I") String mode,
			BoardComment bc, BindingResult bindingResult,
			MultipartHttpServletRequest request
	){
		HashMap<String, Object> retrunMap = new HashMap<>();
		try {
			LoggerUtil.writeBindingResultErrorLog(bindingResult, logger);

			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			if(_user instanceof LoginUser) {
				LoginUser user = (LoginUser)_user;
				HashMap<String, Object> paramMap = new HashMap<>();
				if( bc.getSecretYn() != null )
					if(bc.getSecretYn().isEmpty() || bc.getSecretYn().equals("A"))
						bc.setSecretYn(null);

				if(mode.equals("I")) {
					bc.setCreateId( user.getUserEmail());
					svcBorad.insertBoardComment(bc);
				} else {
					bc.setLastUpdateId(user.getUserEmail());
					svcBorad.updateBoardComment(bc);
				}

				retrunMap.put("result", "success");
				retrunMap.put("message", "처리가 완료 되었습니다.");
			} else {
				retrunMap.put("result", "fail");
				retrunMap.put("message", "로그인이 필요합니다.");
			}
		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			retrunMap.put("info", e.getMessage());
			logger.error(e.getCause().getMessage());
		}
		return retrunMap;
	}
//	<!--################################### BoardMission ###################################-->
    @GetMapping("/mission/list")
    public ModelAndView misson_list(
    		@RequestParam(value="boardId", defaultValue="0") Integer boardId,
    		@Param(value="titleLike") String titleLike,
    		@Param(value="contentLike") String contentLike){
		ModelAndView mav = new ModelAndView();
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("useYn","Y");
		paramMap.put("deleteYn","N");
		paramMap.put("id",7);
		//ArrayList<BoardSetting> boardList = svcBorad.selectListBoardSetting(paramMap);
		BoardSetting board = svcBorad.selectOneBoardSetting(paramMap);
		mav.addObject("board", board);
		/*
		mav.addObject("listBoard", boardList);
		if(boardId == 0 && !boardList.isEmpty()) {
			mav.addObject("board", boardList.get(0));
		}else{
			boolean init = false;
			for(BoardSetting board : boardList){
				if(board.getId() == boardId) {
					init = true;
				}
			}
			if(!init && !boardList.isEmpty()) mav.addObject("board", boardList.get(0));

		}
		*/

		mav.setViewName("manage/sub/board/mission/list");
		return mav;
    }


    @GetMapping("/mission/form")
	public ModelAndView mission_form(
			@RequestParam(value="mode", defaultValue="I") String mode,
			@RequestParam(value="bid", defaultValue="0") int bid,
			@RequestParam(value="id", defaultValue="0") String id){
		ModelAndView mav = new ModelAndView();
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("deleteYn","N");
		paramMap.put("boardId", bid);

		ArrayList<BoardSetting> listBoard = svcBorad.selectListBoardSetting(paramMap);

		ArrayList<BoardCategory> categorys = svcBorad.selectListBoardCategory(paramMap);

		if(bid == 0 && !listBoard.isEmpty()) {
			mav.addObject("setting", listBoard.get(0));
			mav.setViewName("manage/sub/board/contents/form");
		}else{
			boolean init = false;
			for(BoardSetting board : listBoard){
				if(board.getId() == bid) {
					mav.addObject("setting", board);
					init = true;
					switch (board.getTypeCode()){
						case "mission":
							mav.addObject("categoryList", svcBorad.selectListBoardCategory(paramMap));
							mav.setViewName("manage/sub/board/mission/mission-form");
							break;
						default:
							mav.setViewName("manage/sub/board/mission/form");
							break;
					}
				}
			}
			if(!init && !listBoard.isEmpty()) mav.addObject("setting", listBoard.get(0));
		}
		mav.addObject("listBoard",  listBoard);
		mav.addObject("categorys",  categorys);

		if(mode.equals("U")) {
			paramMap.put("id", id);
			mav.addObject("board",  svcBorad.selectOneBoardContents(paramMap));
			paramMap.clear();
			paramMap.put("contentId", id);
			mav.addObject("attaches",  svcBorad.selectListBoardAttachFile(paramMap));
			mav.addObject("p", paramMap);
		} else {
			mav.addObject("board",  new BoardContents());
		}
		mav.addObject("mode", mode);
		return mav;
	}

    @PostMapping("/mission/check")
	@ResponseBody
	public HashMap<String, Object> missionCheck(
			BoardContents bc, BindingResult bindingResult
	){
		HashMap<String, Object> retrunMap = new HashMap<>();
		try {
			LoggerUtil.writeBindingResultErrorLog(bindingResult, logger);


			if( bc.getUserPointSetId() != 0  ) {
				UserPointSet up = userPointService.getUserPointSettingById( bc.getUserPointSetId().toString() );
				userPointService.createUserPoint(up, bc.getCreateId() , getBaseUserEmail(), bc.getId() );
			}

			if( bc.getUserBadgeSetId() != 0  ) {
				BadgeAcquireHistory badgeAcquireHistory = new BadgeAcquireHistory();

				HashMap<String, Object> param = new HashMap<>();
	            param.put("userEmail", bc.getCreateId());
	            param.put("badgeId", bc.getUserBadgeSetId());
	            if(badgeManageService.selectCountBadgeAcquireHistory(param) == 0){
	                badgeAcquireHistory.setUserEmail( bc.getCreateId() );
	                badgeAcquireHistory.setBadgeId( bc.getUserBadgeSetId() );
	                badgeAcquireHistory.setCreateId( getBaseUserEmail() );

	                badgeManageService.writeBadgeAcquireHistory(badgeAcquireHistory);
	            }
			}

			bc.setMissionCheckYn("Y");
			svcBorad.updateBoardContentsByMissionCheck( bc );

			if ( bc.getDeleteImageIds() != null && !bc.getDeleteImageIds().isEmpty() ) {
				String[] deleteImageIds = bc.getDeleteImageIds().split(",");
				BoardAttachFile baf = new BoardAttachFile();
				for(String id : deleteImageIds) {
					baf.setFileId(Integer.parseInt(id));
					svcBorad.deleteBoardAttachFile(baf);
				}
			}


			retrunMap.put("result", "success");
			retrunMap.put("message", "처리가 완료 되었습니다.");

			/*
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			if(_user instanceof LoginUser) {
				LoginUser user = (LoginUser)_user;
				HashMap<String, Object> paramMap = new HashMap<>();
				if( bc.getSecretYn() != null )
					if(bc.getSecretYn().isEmpty() || bc.getSecretYn().equals("A"))
						bc.setSecretYn(null);

				if(mode.equals("I")) {
					bc.setCreateId( user.getUserEmail());
					svcBorad.insertBoardComment(bc);
				} else {
					bc.setLastUpdateId(user.getUserEmail());
					svcBorad.updateBoardComment(bc);
				}

				retrunMap.put("result", "success");
				retrunMap.put("message", "처리가 완료 되었습니다.");
			} else {
				retrunMap.put("result", "fail");
				retrunMap.put("message", "로그인이 필요합니다.");
			}
			*/
		} catch (Exception e) {
			e.printStackTrace();
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			retrunMap.put("info", e.getMessage());
			logger.error(e.getCause().getMessage());

		}
		return retrunMap;
	}

    @GetMapping("/mission/view")
	public ModelAndView mission_view(
			@RequestParam(value="mode", defaultValue="I") String mode,
			@RequestParam(value="bid", defaultValue="0") int bid,
			@RequestParam(value="id", defaultValue="0") String id){
		ModelAndView mav = new ModelAndView();
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("deleteYn","N");
		paramMap.put("boardId", bid);

		ArrayList<BoardSetting> listBoard = svcBorad.selectListBoardSetting(paramMap);

		ArrayList<BoardCategory> categorys = svcBorad.selectListBoardCategory(paramMap);

		if(bid == 0 && !listBoard.isEmpty()) {
			mav.addObject("setting", listBoard.get(0));
		}else{
			boolean init = false;
			for(BoardSetting board : listBoard){
				if(board.getId() == bid) {
					mav.addObject("setting", board);
					init = true;

					switch (board.getTypeCode()){
						case "mission":
							mav.addObject("categoryList", svcBorad.selectListBoardCategory(paramMap));
							mav.setViewName("manage/sub/board/mission/mission-view");
							break;
					}
				}
			}
			if(!init && !listBoard.isEmpty()) mav.addObject("setting", listBoard.get(0));
		}
		mav.addObject("listBoard",  listBoard);
		mav.addObject("categorys",  categorys);

		paramMap.put("id", id);
		mav.addObject("board",  svcBorad.selectOneBoardContents(paramMap));
		paramMap.clear();
		paramMap.put("contentId", id);
		mav.addObject("attaches",  svcBorad.selectListBoardAttachFile(paramMap));
		mav.addObject("p", paramMap);

		return mav;
	}
    
    @GetMapping("/mission/list-thumb")
    public ModelAndView boardTumb_list(){
        ModelAndView mav = new ModelAndView();
        HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("useYn","Y");
		paramMap.put("deleteYn","N");
		paramMap.put("id",7);
        BoardSetting board = svcBorad.selectOneBoardSetting(paramMap);
		mav.addObject("board", board);
		
        mav.setViewName("manage/sub/board/mission/list-thumb");
        return mav;
    }
    
    @PostMapping("/mission/thumb-resize")
	@ResponseBody
	public HashMap<String, Object> missionThumb_resize(
			@RequestParam(value="id", defaultValue="0") String id
			//, BindingResult bindingResult
		){
		HashMap<String, Object> retrunMap = new HashMap<>();
		try {
			//LoggerUtil.writeBindingResultErrorLog(bindingResult, logger);
			HashMap<String, Object> paramMap = new HashMap<>();
			paramMap.put("id",id);
			BoardContents bc = svcBorad.selectOneBoardContents(paramMap);
						
			String[] parts = bc.getThumbnailUrl().split("/");
			String uuid = parts[parts.length - 1];
			//System.out.println("split() 메소드로 추출한 UUID: " + lastPart);
			
			paramMap.clear();
			paramMap.put("uploadFilename", uuid);
			BoardAttachFile file = svcBorad.selectOneBoardAttachFile(paramMap);
			
			String fullPath = externalImageUploadPath+ addPath +file.getUploadFilename(); 
			
			imageUtil.resizeImage(fullPath,externalImageUploadPath+ addPath + "thumb/"+ uuid );
			
			bc.setThumbnailUrl( "/upload/"+ addPath + "thumb/"+ uuid );
			
			svcBorad.updateBoardContents(bc);
			
			retrunMap.put("result", "success");
			retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			e.printStackTrace();
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			retrunMap.put("info", e.getMessage());
			logger.error(e.getCause().getMessage());

		}
		return retrunMap;
	}
    

    @GetMapping("/missionby/list")
    public ModelAndView misson_missionByList(
    		@RequestParam(value="boardId", defaultValue="0") Integer boardId,
    		@Param(value="titleLike") String titleLike,
    		@Param(value="contentLike") String contentLike){
		ModelAndView mav = new ModelAndView();
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("useYn","Y");
		paramMap.put("deleteYn","N");
		paramMap.put("id",7);
		//ArrayList<BoardSetting> boardList = svcBorad.selectListBoardSetting(paramMap);
		BoardSetting board = svcBorad.selectOneBoardSetting(paramMap);
		mav.addObject("board", board);
		/*
		mav.addObject("listBoard", boardList);
		if(boardId == 0 && !boardList.isEmpty()) {
			mav.addObject("board", boardList.get(0));
		}else{
			boolean init = false;
			for(BoardSetting board : boardList){
				if(board.getId() == boardId) {
					init = true;
				}
			}
			if(!init && !boardList.isEmpty()) mav.addObject("board", boardList.get(0));

		}
		*/

		mav.setViewName("manage/sub/board/missionby/list");
		return mav;
    }

    @GetMapping("/missionby/view")
	public ModelAndView missionby_view(
			@RequestParam(value="mode", defaultValue="I") String mode,
			@RequestParam(value="bid", defaultValue="0") int bid,
			@RequestParam(value="id", defaultValue="0") String id){
		ModelAndView mav = new ModelAndView();
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("deleteYn","N");
		paramMap.put("boardId", bid);

		ArrayList<BoardSetting> listBoard = svcBorad.selectListBoardSetting(paramMap);

		ArrayList<BoardCategory> categorys = svcBorad.selectListBoardCategory(paramMap);

		if(bid == 0 && !listBoard.isEmpty()) {
			mav.addObject("setting", listBoard.get(0));
		}else{
			boolean init = false;
			for(BoardSetting board : listBoard){
				if(board.getId() == bid) {
					mav.addObject("setting", board);
					init = true;

					switch (board.getTypeCode()){
						case "mission":
							mav.addObject("categoryList", svcBorad.selectListBoardCategory(paramMap));
							mav.setViewName("manage/sub/board/missionby/view");
							break;
					}
				}
			}
			if(!init && !listBoard.isEmpty()) mav.addObject("setting", listBoard.get(0));
		}
		mav.addObject("listBoard",  listBoard);
		mav.addObject("categorys",  categorys);

		paramMap.put("id", id);
		BoardContents bc = svcBorad.selectOneBoardContents(paramMap);
		mav.addObject("board",  bc );

		if( bc.getUpperBoardId() != null ) {
			paramMap.clear();
			paramMap.put("id", bc.getUpperBoardId());
			paramMap.put("isUpperBoardId", false);
			mav.addObject("uboard",  svcBorad.selectOneBoardContents(paramMap));
		}
		paramMap.clear();
		paramMap.put("contentId", id);
		mav.addObject("attaches",  svcBorad.selectListBoardAttachFile(paramMap));
		mav.addObject("p", paramMap);

		return mav;
	}

    @GetMapping("/userby/list")
    public ModelAndView misson_userByList(
    		@RequestParam(value="boardId", defaultValue="0") Integer boardId,
    		@Param(value="titleLike") String titleLike,
    		@Param(value="contentLike") String contentLike){
		ModelAndView mav = new ModelAndView();
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("useYn","Y");
		paramMap.put("deleteYn","N");
		paramMap.put("id",7);
		//ArrayList<BoardSetting> boardList = svcBorad.selectListBoardSetting(paramMap);
		BoardSetting board = svcBorad.selectOneBoardSetting(paramMap);
		mav.addObject("board", board);
		/*
		mav.addObject("listBoard", boardList);
		if(boardId == 0 && !boardList.isEmpty()) {
			mav.addObject("board", boardList.get(0));
		}else{
			boolean init = false;
			for(BoardSetting board : boardList){
				if(board.getId() == boardId) {
					init = true;
				}
			}
			if(!init && !boardList.isEmpty()) mav.addObject("board", boardList.get(0));

		}
		*/

		mav.setViewName("manage/sub/board/userby/list");
		return mav;
    }

    @GetMapping("/userby/view")
	public ModelAndView userby_view(
			@RequestParam(value="mode", defaultValue="I") String mode,
			@RequestParam(value="bid", defaultValue="0") int bid,
			@RequestParam(value="id", defaultValue="0") String id){
		ModelAndView mav = new ModelAndView();
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("deleteYn","N");
		paramMap.put("boardId", bid);

		ArrayList<BoardSetting> listBoard = svcBorad.selectListBoardSetting(paramMap);

		ArrayList<BoardCategory> categorys = svcBorad.selectListBoardCategory(paramMap);

		if(bid == 0 && !listBoard.isEmpty()) {
			mav.addObject("setting", listBoard.get(0));
		}else{
			boolean init = false;
			for(BoardSetting board : listBoard){
				if(board.getId() == bid) {
					mav.addObject("setting", board);
					init = true;

					switch (board.getTypeCode()){
						case "mission":
							mav.addObject("categoryList", svcBorad.selectListBoardCategory(paramMap));
							mav.setViewName("manage/sub/board/userby/view");
							break;
					}
				}
			}
			if(!init && !listBoard.isEmpty()) mav.addObject("setting", listBoard.get(0));
		}
		mav.addObject("listBoard",  listBoard);
		mav.addObject("categorys",  categorys);

		paramMap.put("id", id);
		BoardContents bc = svcBorad.selectOneBoardContents(paramMap);
		mav.addObject("board",  bc);

		if( bc.getUpperBoardId() != null ) {
			paramMap.clear();
			paramMap.put("id", bc.getUpperBoardId());
			paramMap.put("isUpperBoardId", false);
			mav.addObject("uboard",  svcBorad.selectOneBoardContents(paramMap));
		}
		paramMap.clear();
		paramMap.put("contentId", id);
		mav.addObject("attaches",  svcBorad.selectListBoardAttachFile(paramMap));
		mav.addObject("p", paramMap);

		return mav;
	}
}
