_fnGenJson = function(targetId){
	var _json = {};
	var _data ={}
	var _mp = new MapCustom();
	var _keyset = new Array();

	if( $(`.jsonGroup`, `#${targetId}`).length > 0 ){
		$(`.jsonGroup`, `#${targetId}`).each(function(i,e){
			var arr = new Array();
			var _subdata ={};

		    $(`input:not([disabled='disabled']), select:not([disabled='disabled'])`, $(e)).each(function(j,e2){
				_subdata[$(e2).attr('name')] = $(e2).val();
		    });

		    _data[i] = _subdata;
		});
	} else {
		$(`input:not([disabled='disabled']), select:not([disabled='disabled'])`, `#${targetId}`).each(function(i,e){
			var type = $(this).attr('type');

			//console.log(type);
			if( type =='radio' || type =='check'){
				//console.log(type, $(e).is(':checked'));
				if( $(e).is(':checked') ){
					_data[$(e).attr('name')] = $(e).val();
				}
			} else {
				var size = $(`${$(e).prop('tagName')}[name='${$(e).attr('name')}']`).length;
				if( size > 1 ){
					var _key = $(e).attr('name');
					var arr;

					if( _mp.get(_key) === undefined  ){
						_keyset.push( _key );
						arr = new Array();
						_mp.put(_key, arr);
					} else {
						arr = _mp.get(_key);
					}

					arr.push($(e).val());
				} else {
					_data[$(e).attr('name')] = $(e).val();
				}
			}
		});

		for(var i in _keyset){
			var _key = _keyset[i];
			var arr = _mp.get(_key);
			var _subData ={};

			for(var j in arr){
				_subData[ j ] = arr[j];
			}
			_data[ _key ] = _subData;
		}
	}

	_json["data"] = _data;

	return _json;
}

fnGenJson = function(targetId){
	var _json = _fnGenJson( targetId );

	if( _json != null ||
	    _json != undefined ){
		_json['id']=targetId;
	}

	return _json;
}

fnGenJsonToStr = function(targetId){
	var _json = fnGenJson( targetId );

	return JSON.stringify(_json);
}

fnGenJsonToStrNoneId =function(targetId){
	var _json = _fnGenJsonToStr( targetId );
	return JSON.stringify(_json);

}

fnInitJsonData = function( json ){
	if(json !== null){
		if(json !== undefined && json.length > 0 ){
	    	var _data = JSON.parse( json );
	    	//console.log(_data);

			var id   = _data['id'];
			var data = _data['data'];

			$.each( data, function( key, value ){
			    //console.log(`$('#${key}','#${id}')` );
			    //console.log('key:' + key + ' / ' + 'value:' + value );
			    $(`#${key}`,`#${id}`).val( value );
			});
		}
	}
	//$('#divProductPriceOption').html();
}

fnInitJsonData = function( json, cbFun ){
	//console.log('fnInitJsonData');
	if(json !== null &&
	   json !== undefined &&
	   json.length > 0 ){
    	var _data = JSON.parse( json );
    	//console.log(_data);

		var id   = _data['id'];
		var data = _data['data'];

		if(data !== undefined){
			//console.log( data, typeof(data) );

			if( typeof(data) == 'string' ){
				if(data.Vehicle != '') cbFun( data.Vehicle );
			} else {
				$.each( data, function( key, value ){
					//console.log(`$('#${key}','#${id}')`, value, cbFun );
					if(value != '' && typeof(cbFun) == 'function' ){
						cbFun( value );
					} else {
						//console.log(`$('#${key}','#${id}')`, value );
						$(`#${key}`,`#${id}`).val(value);
					}
				})
			}
		}
	} else {
		if(typeof(cbFun) == 'function' ){
			cbFun();
		}
	}
}