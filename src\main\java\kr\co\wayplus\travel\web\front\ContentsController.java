//package kr.co.wayplus.travel.web.front;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.servlet.ModelAndView;
//
//import jakarta.servlet.http.HttpServletRequest;
//import kr.co.wayplus.travel.base.web.BaseController;
//import kr.co.wayplus.travel.model.MainBannerImage;
//import kr.co.wayplus.travel.model.MenuUser;
//import kr.co.wayplus.travel.model.PagingDTO;
//import kr.co.wayplus.travel.model.PlaceSpot;
//import kr.co.wayplus.travel.model.SettingAllianceContents;
//import kr.co.wayplus.travel.service.front.ContentsService;
//import kr.co.wayplus.travel.service.front.PageService;
//
//@Controller
//@RequestMapping("/contents")
//public class ContentsController  extends BaseController {
//
//    @Value("${cookie-set.domain}")
//    private String cookieDomain;
//    @Value("${cookie-set.prefix}")
//    private String cookieName;
//
//    private final Logger logger = LoggerFactory.getLogger(getClass());
//
//    private final PageService pageService; /*common하게*/
//    private final ContentsService contentsService;
//
//    @Autowired
//    public ContentsController(PageService pageService, ContentsService contentsService) {
//    	this.pageService = pageService;
//        this.contentsService = contentsService;
//    }
//
//    @GetMapping(value = {"/{path1}"})
//    public ModelAndView dynamicPath2(
//    		HttpServletRequest request
//    		, @PathVariable String path1
//    		) {
//    	ModelAndView mav = new ModelAndView();
//
//    	commonPathFinder(mav, path1);
//
//    	return mav;
//    }
//
//    @GetMapping(value = {"/{path1}/{path2}"})
//    public ModelAndView dynamicPath3(
//    		HttpServletRequest request
//    		, @PathVariable String path1
//    		, @PathVariable String path2
//    		) {
//    	ModelAndView mav = new ModelAndView();
//
//    	commonPathFinder(mav, path1, path2);
//
//    	return mav;
//    }
//
//    private void commonPathFinder(ModelAndView mav, String ... arrPath) {
//    	HashMap<String, Object> param = new HashMap<>();
//
//    	String fullUrl = "";
//    	for (String string : arrPath) {
//			fullUrl += "/"+string;
//		}
//
//    	param.put("fullMenuUrl", fullUrl);
//
//    	MenuUser menu = pageService.selectOneMenuUser( param );
//
//    	mav.setViewName( "/front/sub/contents"+fullUrl );
//    }
//
////    @GetMapping(value = {"/{path1}"})
////    public ModelAndView dynamicPath2(HttpServletRequest request,
////    		@PathVariable String path1,
////    		@RequestParam(value="page", defaultValue="1") int page,
//////    		@RequestParam(value="subCategory", defaultValue="ALL") String subCategory,
////    		@RequestParam(value="contentType", defaultValue="ALL") String contentType,
////            @RequestParam(value="location", defaultValue="ALL") String location,
////            @RequestParam(value="titleLike", defaultValue="") String titleLike){
////    	ModelAndView mav = new ModelAndView();
////
////    	/*
////    	 * 1. menuId 알아내기
////    	 * 2. menuId통해 카태고리 표시용 정보 알아내기
////    	 * 3. 제품목록 정보 알아내기
////    	 */
////
////    	HashMap<String, Object> param = new HashMap<>();
//////    	param.put("subCategory", subCategory);
////
////    	param.put("contentType", contentType);
////    	param.put("location", location);
////    	param.put("titleLike", titleLike);
////
////    	HashMap<String, Object> param1 = new HashMap<>();
////    	HashMap<String, Object> param2 = new HashMap<>();
////    	HashMap<String, Object> param3 = new HashMap<>();
////    	HashMap<String, Object> param4 = new HashMap<>();
////
////    	param1.put("menuUrl", "/"+path1);
////    	MenuUser menu = pageService.selectOneMenuUser( param1 );
////
////    	if(menu != null) {
////    		param2.put("upperMenuId", menu.getMenuId());
////    		ArrayList<MenuUser> menuMenuList = pageService.selectListMenuUser( param2 );
////
////    		param3.put("menuId", menu.getMenuId());
////    		param3.put("bannerType", "menuBanner");
////    		ArrayList< MainBannerImage > bannerList = pageService.selectListMainBannerImage(param3);
////
////    		mav.addObject("menu", menu);
////    		mav.addObject("menuCategory", menuMenuList);
////    		mav.addObject("bannerList", bannerList);
////
////    		ArrayList< SettingAllianceContents > allianceList = pageService.selectListAllianceContents(param4);
////    		mav.addObject("allianceList", allianceList);
////
////
////    		if( menu.getMenuSubType().equals( "place" ) ) {
////	    		int totalCount = pageService.selectCountPlaceSpot(param);
////	            PagingDTO paging = new PagingDTO(totalCount, page, 0, 12);
////	            param.put("itemStartPosition", paging.getItemStartPosition());
////	            param.put("pagePerSize", paging.getPagePerSize());
////
////	            mav.addObject("totalCount", totalCount);
////	            mav.addObject("paging", paging);
////	            ArrayList<PlaceSpot> tourSpotList = pageService.selectListPlaceSpot(param);
////	            mav.addObject("tourSpotList", tourSpotList);
////	            mav.setViewName("/front/contents/list");
////    		} else if ( menu.getMenuSubType().equals( "custom" ) ) {
////    			mav.setViewName("/front/contents/"+menu.getMenuSubType() + "/"+path1);
////    		}
////
////    	} else {
////    		//오~~류~~인~~데~~
//////    		mav.setView(new RedirectView("/order/error"));
////    	}
////
////    	param.put("p1",param1);
////    	param.put("p2",param2);
////    	param.put("p3",param3);
////
////    	mav.addObject("p", param);
//////
////        return mav;
////    }
////
////    @GetMapping(value = {"/{path1}/{id}"})
////    public ModelAndView dynamicPath2(HttpServletRequest request,
////    		@PathVariable String path1,
////    		@PathVariable String id){
////    	ModelAndView mav = new ModelAndView();
////
////    	HashMap<String, Object> param = new HashMap<>();
////
////    	param.put("tsId", id);
////
////    	HashMap<String, Object> param1 = new HashMap<>();
////    	param1.put("menuUrl", "/"+path1);
////    	MenuUser menu = pageService.selectOneMenuUser( param1 );
////
////    	PlaceSpot touristSpot = pageService.selectOnePlaceSpot(param);
////
////    	mav.addObject("menu", menu);
////        mav.addObject("item", touristSpot);
////
////    	mav.setViewName("/front/contents/view");
////        return mav;
////    }
//}
