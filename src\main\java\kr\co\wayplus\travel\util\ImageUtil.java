package kr.co.wayplus.travel.util;

import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.color.ColorSpace;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.awt.image.ColorConvertOp;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Iterator;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.FileImageOutputStream;
import javax.imageio.stream.ImageInputStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

//EXIF 데이터 처리를 위한 라이브러리 (Gradle 의존성 필요)
import com.drew.imaging.ImageMetadataReader;
import com.drew.imaging.ImageProcessingException;
import com.drew.metadata.exif.ExifIFD0Directory;

@Component
public class ImageUtil {
	private final Logger logger = LoggerFactory.getLogger(getClass());

	// 최소 사이즈 상수
    private final int MIN_WIDTH = 250;
    private final int MIN_HEIGHT = 250;

    /**
     * 이미지를 리사이징하는 메소드
     *
     * @param originalPath 원본 이미지 경로
     * @param resizedPath 리사이즈된 이미지가 저장될 경로
     * @return 성공 여부
     */
    public boolean resizeImage(String originalPath, String resizedPath) {
        try {
            // 원본 이미지 파일 확인
            File originalFile = new File(originalPath);
            if (!originalFile.exists()) {
                logger.error("원본 파일이 존재하지 않습니다: {}", originalPath);
                return false;
            }

            // 이미지 타입 결정 (UUID 형식이면 마임 타입 또는 매직 넘버로 확인)
            String imageType = determineImageType(originalFile);
            logger.debug("감지된 이미지 타입: {}", imageType);

            // EXIF 메타데이터 읽기
            int orientation = getExifOrientation(originalFile);
            logger.debug("EXIF Orientation: {}", orientation);

            // 이미지 읽기
            BufferedImage originalImage = readImage(originalFile);
            if (originalImage == null) {
                logger.error("이미지 파일이 아니거나 읽을 수 없습니다: {}", originalPath);
                return false;
            }

            // EXIF 기반 이미지 회전 적용
            BufferedImage orientedImage = applyExifOrientation(originalImage, orientation);

            // 원본 이미지 크기
            int originalWidth = orientedImage.getWidth();
            int originalHeight = orientedImage.getHeight();
            logger.debug("원본 이미지 크기(회전 적용 후): {}x{}", originalWidth, originalHeight);

            // 타겟 크기 계산 (비율 유지)
            int[] dimensions = calculateDimensions(originalWidth, originalHeight);
            int newWidth = dimensions[0];
            int newHeight = dimensions[1];
            logger.debug("리사이징 후 크기: {}x{}", newWidth, newHeight);

            // 고품질 이미지 리사이징
            BufferedImage resizedImage = resizeImageWithHighQuality(orientedImage, newWidth, newHeight);

            // 파일 확장자 결정
            String formatName = determineOutputFormat(resizedPath, imageType);

            // 압축률 지정하여 저장
            float quality = "png".equals(formatName) ? 1.0f : 0.85f;
            saveImageWithCompression(resizedImage, resizedPath, formatName, quality);

            return true;

        } catch (Exception e) {
            logger.error("이미지 리사이징 중 오류가 발생했습니다: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 이미지 타입 결정 (UUID 형식 파일명 지원)
     *
     * @param file 이미지 파일
     * @return 이미지 타입 (jpeg, png 등)
     */
    private String determineImageType(File file) throws IOException {
        // 파일 매직 넘버로 타입 확인
        byte[] fileHeader = new byte[8];
        try {
            Files.newInputStream(file.toPath()).read(fileHeader);

            // PNG 시그니처: 89 50 4E 47 0D 0A 1A 0A
            if (fileHeader[0] == (byte) 0x89 &&
                fileHeader[1] == (byte) 0x50 &&
                fileHeader[2] == (byte) 0x4E &&
                fileHeader[3] == (byte) 0x47) {
                return "png";
            }

            // JPEG 시그니처: FF D8 FF
            if (fileHeader[0] == (byte) 0xFF &&
                fileHeader[1] == (byte) 0xD8 &&
                fileHeader[2] == (byte) 0xFF) {
                return "jpeg";
            }

            // GIF 시그니처: 47 49 46 38
            if (fileHeader[0] == (byte) 0x47 &&
                fileHeader[1] == (byte) 0x49 &&
                fileHeader[2] == (byte) 0x46 &&
                fileHeader[3] == (byte) 0x38) {
                return "gif";
            }
        } catch (IOException e) {
            logger.warn("파일 헤더 읽기 실패, 대체 방법 사용: {}", e.getMessage());
        }

        // 확장자로 확인 (대체 방법)
        String fileName = file.getName().toLowerCase();
        if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
            return "jpeg";
        } else if (fileName.endsWith(".png")) {
            return "png";
        } else if (fileName.endsWith(".gif")) {
            return "gif";
        }

        // 기본값 (대부분의 이미지가 JPEG이므로)
        return "jpeg";
    }

    /**
     * EXIF Orientation 값 읽기
     *
     * @param imageFile 이미지 파일
     * @return Orientation 값 (1-8, 기본값 1)
     */
    private int getExifOrientation(File imageFile) {
        try {
            com.drew.metadata.Metadata metadata = ImageMetadataReader.readMetadata(imageFile);
            ExifIFD0Directory directory = metadata.getFirstDirectoryOfType(ExifIFD0Directory.class);

            if (directory != null && directory.containsTag(ExifIFD0Directory.TAG_ORIENTATION)) {
                return directory.getInt(ExifIFD0Directory.TAG_ORIENTATION);
            }
        } catch (ImageProcessingException | IOException e) {
            logger.warn("EXIF 데이터 읽기 실패: {}", e.getMessage());
        } catch (Exception e) {
            logger.warn("예상치 못한 EXIF 처리 오류: {}", e.getMessage());
        }

        // 기본 방향 (회전 없음)
        return 1;
    }

    /**
     * 이미지 파일 읽기
     *
     * @param imageFile 이미지 파일
     * @return BufferedImage 객체
     */
    private BufferedImage readImage(File imageFile) throws IOException {
        try {
            // 표준 방식
            BufferedImage image = ImageIO.read(imageFile);
            if (image != null) {
                return image;
            }

            // 대체 방식
            ImageInputStream input = ImageIO.createImageInputStream(imageFile);
            if (input == null) {
                throw new IOException("이미지 스트림을 열 수 없습니다");
            }

            try {
                Iterator<ImageReader> readers = ImageIO.getImageReaders(input);
                if (!readers.hasNext()) {
                    throw new IOException("이미지 리더를 찾을 수 없습니다");
                }

                ImageReader reader = readers.next();
                try {
                    reader.setInput(input);
                    return reader.read(0);
                } finally {
                    reader.dispose();
                }
            } finally {
                input.close();
            }
        } catch (Exception e) {
            logger.error("이미지 읽기 실패: {}", e.getMessage(), e);
            throw new IOException("이미지를 읽을 수 없습니다", e);
        }
    }

    /**
     * EXIF Orientation 값에 따라 이미지 회전 적용
     *
     * @param image 원본 이미지
     * @param orientation EXIF Orientation 값 (1-8)
     * @return 회전이 적용된 이미지
     */
    private BufferedImage applyExifOrientation(BufferedImage image, int orientation) {
        if (orientation == 1) {
            return image; // 회전 필요 없음
        }

        AffineTransform transform = new AffineTransform();

        switch (orientation) {
            case 2: // 수평 뒤집기
                transform.scale(-1.0, 1.0);
                transform.translate(-image.getWidth(), 0);
                break;

            case 3: // 180도 회전
                transform.translate(image.getWidth(), image.getHeight());
                transform.rotate(Math.PI);
                break;

            case 4: // 수직 뒤집기
                transform.scale(1.0, -1.0);
                transform.translate(0, -image.getHeight());
                break;

            case 5: // 수직 뒤집기 + 90도 시계 방향 회전
                transform.rotate(-Math.PI / 2);
                transform.scale(-1.0, 1.0);
                break;

            case 6: // 90도 시계 방향 회전
                transform.translate(image.getHeight(), 0);
                transform.rotate(Math.PI / 2);
                break;

            case 7: // 수평 뒤집기 + 90도 시계 방향 회전
                transform.scale(-1.0, 1.0);
                transform.translate(-image.getHeight(), 0);
                transform.translate(0, image.getWidth());
                transform.rotate(Math.PI / 2);
                break;

            case 8: // 270도 시계 방향 회전
                transform.translate(0, image.getWidth());
                transform.rotate(-Math.PI / 2);
                break;

            default:
                return image;
        }

        // 새 이미지 크기 결정 (90/270도 회전 시 가로/세로 변경)
        int newWidth = image.getWidth();
        int newHeight = image.getHeight();
        if (orientation == 5 || orientation == 6 || orientation == 7 || orientation == 8) {
            newWidth = image.getHeight();
            newHeight = image.getWidth();
        }

        // 회전된 이미지 생성
        BufferedImage rotatedImage = new BufferedImage(newWidth, newHeight, image.getType());
        Graphics2D g2d = rotatedImage.createGraphics();
        g2d.transform(transform);

        // 회전에 따라 그리기 위치 조정
        if (orientation == 5 || orientation == 6 || orientation == 7 || orientation == 8) {
            g2d.drawImage(image, 0, 0, null);
        } else {
            g2d.drawImage(image, 0, 0, null);
        }
        g2d.dispose();

        return rotatedImage;
    }

    /**
     * 원본 이미지 비율을 유지하면서 새로운 크기 계산
     *
     * @param originalWidth 원본 이미지 너비
     * @param originalHeight 원본 이미지 높이
     * @return 새로운 [너비, 높이] 배열
     */
    private int[] calculateDimensions(int originalWidth, int originalHeight) {
        int newWidth = originalWidth;
        int newHeight = originalHeight;

        // 비율 계산
        double ratio = (double) originalHeight / originalWidth;

        // 이미지 크기가 큰 경우 축소 (MB -> KB)
        if (originalWidth > 1000 || originalHeight > 1000) {
            // 가로 세로 중 큰 쪽을 기준으로 축소
            if (originalWidth >= originalHeight) {
                newWidth = 800;
                newHeight = (int) Math.round(newWidth * ratio);
            } else {
                newHeight = 800;
                newWidth = (int) Math.round(newHeight / ratio);
            }
        }

        // 최소 크기 확인
        if (newWidth < MIN_WIDTH) {
            newWidth = MIN_WIDTH;
            newHeight = (int) Math.round(newWidth * ratio);
        }

        if (newHeight < MIN_HEIGHT) {
            newHeight = MIN_HEIGHT;
            newWidth = (int) Math.round(newHeight / ratio);
        }

        return new int[] {newWidth, newHeight};
    }

    /**
     * 출력 형식을 결정하는 메소드
     *
     * @param outputPath 출력 파일 경로
     * @param sourceImageType 원본 이미지 타입
     * @return 이미지 포맷 이름 (jpeg 또는 png)
     */
    private String determineOutputFormat(String outputPath, String sourceImageType) {
        // 출력 파일 확장자 확인
        String lowerPath = outputPath.toLowerCase();
        if (lowerPath.endsWith(".png")) {
            return "png";
        } else if (lowerPath.endsWith(".jpg") || lowerPath.endsWith(".jpeg")) {
            return "jpeg";
        } else {
            // 확장자가 없으면 원본 타입 사용
            return sourceImageType;
        }
    }

    /**
     * 고품질 이미지 리사이징
     *
     * @param originalImage 원본 이미지
     * @param newWidth 새 너비
     * @param newHeight 새 높이
     * @return 리사이즈된 고품질 이미지
     */
    private BufferedImage resizeImageWithHighQuality(BufferedImage originalImage, int newWidth, int newHeight) {
        // 투명도 유지 여부 확인
        int type = originalImage.getTransparency() == BufferedImage.OPAQUE ?
                   BufferedImage.TYPE_INT_RGB : BufferedImage.TYPE_INT_ARGB;

        BufferedImage resizedImage = new BufferedImage(newWidth, newHeight, type);
        Graphics2D g2d = resizedImage.createGraphics();

        // 고품질 렌더링 설정
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);

        // 이미지 그리기
        g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g2d.dispose();

        // 색상 관리 (선택사항)
        if (type == BufferedImage.TYPE_INT_RGB) {
            ColorConvertOp colorOp = new ColorConvertOp(ColorSpace.getInstance(ColorSpace.CS_sRGB), null);
            resizedImage = colorOp.filter(resizedImage, null);
        }

        return resizedImage;
    }

    /**
     * 압축률이 적용된 이미지 저장 메소드
     *
     * @param image 저장할 이미지
     * @param outputPath 저장 경로
     * @param formatName 이미지 포맷 (jpeg, png 등)
     * @param compressionQuality 압축 품질 (0.0f~1.0f)
     * @throws IOException 파일 저장 중 오류 발생 시
     */
    private void saveImageWithCompression(BufferedImage image, String outputPath, String formatName, float compressionQuality) throws IOException {
        File outputFile = new File(outputPath);

        // 디렉토리가 없으면 생성
        if (!outputFile.getParentFile().exists()) {
            outputFile.getParentFile().mkdirs();
        }

        // PNG 파일은 압축 대신 직접 저장
        if (formatName.equalsIgnoreCase("png")) {
            ImageIO.write(image, formatName, outputFile);
            return;
        }

        // JPEG 등 압축 가능한 포맷
        Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName(formatName);
        if (!writers.hasNext()) {
            throw new IOException("해당 포맷을 지원하는 ImageWriter가 없습니다: " + formatName);
        }

        ImageWriter writer = writers.next();
        ImageWriteParam param = writer.getDefaultWriteParam();

        // 압축 품질 설정
        if (param.canWriteCompressed()) {
            param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
            param.setCompressionQuality(compressionQuality);
        }

        // 파일에 이미지 쓰기
        try (FileImageOutputStream output = new FileImageOutputStream(outputFile)) {
            writer.setOutput(output);
            writer.write(null, new IIOImage(image, null, null), param);
        } finally {
            writer.dispose();
        }
    }
}
