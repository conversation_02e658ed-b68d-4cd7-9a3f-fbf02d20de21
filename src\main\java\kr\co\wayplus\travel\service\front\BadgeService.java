package kr.co.wayplus.travel.service.front;

import kr.co.wayplus.travel.mapper.front.BadgeMapper;
import kr.co.wayplus.travel.model.BadgeContents;
import kr.co.wayplus.travel.model.BoardContents;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Service
public class BadgeService {

    private final BadgeMapper mapper;

    @Autowired
    public BadgeService(BadgeMapper mapper) {
        this.mapper = mapper;
    }
	//	<!--################################### #{BadgeContents} ################################### -->
	public BadgeContents selectOneBadgeContents(HashMap<String, Object> param) {
		return mapper.selectOneBadgeContents(param);
	}
    public void insertUserBagdeContents(BoardContents boardContents) {
        mapper.insertUserBagdeContents(boardContents);
    }
	//	<!--################################### #{badgeAcquireHistory} ################################### -->
	public void insertUserBadgeAcquireHistory(HashMap<String, Object> badgeParam) {
		mapper.insertUserBadgeAcquireHistory(badgeParam);
	}
}
