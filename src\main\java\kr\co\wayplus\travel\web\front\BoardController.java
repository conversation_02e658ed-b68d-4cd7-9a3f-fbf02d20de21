package kr.co.wayplus.travel.web.front;

import java.io.File;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.BadgeAcquireHistory;
import kr.co.wayplus.travel.model.BadgeContents;
import kr.co.wayplus.travel.model.BoardAttachFile;
import kr.co.wayplus.travel.model.BoardComment;
import kr.co.wayplus.travel.model.BoardContents;
import kr.co.wayplus.travel.model.BoardSetting;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.UserPointAccrued;
import kr.co.wayplus.travel.model.UserPointSet;
import kr.co.wayplus.travel.service.front.BoardService;
import kr.co.wayplus.travel.service.front.PageService;
import kr.co.wayplus.travel.service.manage.BadgeManageService;
import kr.co.wayplus.travel.service.user.UserPointService;
import kr.co.wayplus.travel.util.FileInfoUtil;
import kr.co.wayplus.travel.util.ImageUtil;
import kr.co.wayplus.travel.util.MappingHttpDownloadView;


@Controller
@RequestMapping("/board")
public class BoardController  extends BaseController {

    @Value("${cookie-set.domain}")
    private String cookieDomain;
    @Value("${cookie-set.prefix}")
    private String cookieName;

    @Value("${upload.file.path}")
	String externalImageUploadPath;

	final String addPath = "images/";
	final String addThumbnail = "thumb/";

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final PageService pageService;
    private final BoardService boardService;
    private final UserPointService userPointService;
    private final BadgeManageService badgeManageService;
    private final ImageUtil imageUtil;

    @Autowired
    public BoardController(
    		PageService pageService,
    		BoardService boardService,
    		UserPointService svc2,
    		BadgeManageService svc3,
    		ImageUtil imageUtil) {
    	this.pageService = pageService;
        this.boardService = boardService;
        this.userPointService = svc2;
        this.badgeManageService = svc3;
        this.imageUtil = imageUtil;
    }
	
    @PostMapping("/file")
	public ModelAndView awards_list_ajax(
			HttpServletRequest request,
			@RequestParam(value="id") int id,
            @RequestParam(value="filename") String filename ){
		ModelAndView mav= new ModelAndView(new MappingHttpDownloadView());

		try{
            HashMap<String, Object> param = new HashMap<>();
            param.put("fileId", id);
            param.put("filename", filename);
            BoardAttachFile attach = boardService.selectOneBoardAttachFile(param);
            mav.addObject("storeFileName", attach.getUploadFilename());
            mav.addObject("downloadFileName", attach.getOriginFilename());
            mav.addObject("uploadUrl", attach.getUploadPath());
            mav.addObject("deleteFlag", "N");
        }catch (Exception e){
            e.printStackTrace();
        }

		return mav;
	}

    @PostMapping("/save")
    @ResponseBody
    public HashMap<String, Object> board_save_ajax(
    		@RequestParam(value="mode", defaultValue="I") String mode,
    		BoardContents bc,
			HttpServletRequest request
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
			boardService.insertBoardContentsService(bc, mode, request);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리되었습니다.");

		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 에러가 발생하였습니다.");
			e.printStackTrace();
			retrunMap.put("info", e.getMessage());
			logger.error(e.getCause().getMessage());

		}
        return retrunMap;
    }
    
    


	@GetMapping("/list-comment")
	@ResponseBody
	public HashMap<String, Object> getBoardComments(
	        @RequestParam(value = "id", defaultValue = "0") String id,
	        @RequestParam(value = "length", defaultValue = "10") int length,
	        @RequestParam(value = "start", defaultValue = "0") int start,
			@RequestParam(value = "upperId ", required = false) Integer upperId,
			@RequestParam(value = "contentId", defaultValue = "0") Integer contentId,
			@RequestParam(value = "tabIndex", required = false, defaultValue = "0") Integer tabIndex) {
	    HashMap<String, Object> resultMap = new HashMap<>();

	    try {
			HashMap<String, Object> paramMap = new HashMap<>();
			//<<이 프로젝트용
	        /*
			Object boardObject = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			if( boardObject instanceof LoginUser _user) {
				paramMap.put("likeUserEmail", _user.getUsername());
			}
			*/
			paramMap.put("contentId", contentId);
			int totalCount = boardService.selectCountBoardComment(paramMap);

	        paramMap.put("length", length);
	        paramMap.put("start", start);
			if ( length > 0 ) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
			}
	       	List<BoardComment> comments = boardService.selectListBoardComment(paramMap);

	        resultMap.put("result", "success");
	        resultMap.put("data", comments);
	        resultMap.put("recordsTotal", totalCount);
	        resultMap.put("message", "조회되었습니다.");
	    } catch (Exception e) {
			e.printStackTrace();
	        resultMap.put("result", "error");
	        resultMap.put("message", "조회중 오류가 발생했습니다.");
	        logger.error("조회 오류", e);
	    }

	    return resultMap;
	}

	@PostMapping("/save-comment")
    @ResponseBody
    public HashMap<String, Object> save_comment_ajax(
			BoardComment boardComment
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

    		if(_user instanceof LoginUser) {
				boardComment.setCreateId( ((LoginUser)_user).getUserEmail() );
				boardComment.setUserName( ((LoginUser)_user).getUserName() );
				boardComment.setUserRole( ((LoginUser)_user).getUserRole() );

				boardService.insertBoardComment(boardComment);
				retrunMap.put("result", "success");
				retrunMap.put("message", "등록되었습니다.");
				return retrunMap;
			}
			else {
				retrunMap.put("result", "error");
				retrunMap.put("message", "로그인 후 이용해주세요.");
				return retrunMap;
			}
		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "등록중 에러가 발생하였습니다.");
			e.printStackTrace();
			retrunMap.put("info", e.getMessage());
			logger.error(e.getCause().getMessage());
		}
        return retrunMap;
    }

	/**
	 *
	 * @param id
	 * @param itemStartPosition
	 * @param pagePerSize
	 * @param boardId
	 * @return
	 */
	@PostMapping("/list-contents")
	@ResponseBody
	public HashMap<String, Object> board_contents_ajax(
	        @RequestParam(value = "id", defaultValue = "0") String id,
	        @RequestParam(value = "itemStartPosition", defaultValue = "0") int itemStartPosition,
	        @RequestParam(value = "pagePerSize", defaultValue = "10") int pagePerSize,
			@RequestParam(value = "boardId", defaultValue = "0") int boardId,
			//@RequestParam(value = "missionType", defaultValue = "0") String missionType,
			@RequestParam(value = "userEmail", defaultValue = "") String userEmail
			) {
	    HashMap<String, Object> resultMap = new HashMap<>();

	    try {
	        HashMap<String, Object> paramMap = new HashMap<>();
			//<<이 프로젝트용
	        /*
			Object boardObject = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			if( boardObject instanceof LoginUser _user) {
				paramMap.put("likeUserEmail", _user.getUsername());
			}
			*/
			paramMap.put("boardId", boardId);
			//paramMap.put("missionType", missionType);

			paramMap.put("isUpperBoardId", false);
			paramMap.put("useYn", "Y");
			paramMap.put("deleteYn", "N");
			paramMap.put("sort", "id");
			paramMap.put("sortOrder", "desc");

			//if( boardId == 7 ) {
				//paramMap.put("userEmail", getBaseUserEmail() );
			//}

			if(!userEmail.equals("")) {
				//paramMap.put("createId", "admin");
				paramMap.put("possibleUserEmail", userEmail);
			}

			int totalCount = boardService.selectCountBoardContents(paramMap);

	        //paramMap.put("itemStartPosition", itemStartPosition);
	        //paramMap.put("pagePerSize", pagePerSize);
	       	List<BoardContents> contents = boardService.selectListBoardContents(paramMap);

			for (BoardContents cont : contents) {
				paramMap.clear();
				paramMap.put("contentId", cont.getId());
				cont.setImageList(boardService.selectListBoardAttachFile(paramMap));
			}

	        resultMap.put("result", "success");
	        resultMap.put("data", contents);
	        resultMap.put("recordsTotal", totalCount);
	        resultMap.put("message", "조회되었습니다.");
	    } catch (Exception e) {
	        resultMap.put("result", "error");
	        resultMap.put("message", "조회중 오류가 발생했습니다.");
	        logger.error("조회 오류", e);
	    }

	    return resultMap;
	}

	@GetMapping("/list-contents")
	@ResponseBody
	public HashMap<String, Object> getBoardContents(
	        @RequestParam(value = "id", defaultValue = "0") String id,
	        @RequestParam(value = "itemStartPosition", defaultValue = "0") int itemStartPosition,
	        @RequestParam(value = "pagePerSize", defaultValue = "10") int pagePerSize,
			@RequestParam(value = "boardId", defaultValue = "0") int boardId,
			@RequestParam(value = "categoryId", defaultValue = "0") int categoryId,
			@RequestParam(value = "boardSettingTypeCode", defaultValue = "") String boardSettingTypeCode,
			@RequestParam(value = "userEmail", defaultValue = "") String userEmai) {
	    HashMap<String, Object> resultMap = new HashMap<>();

	    try {
	        HashMap<String, Object> paramMap = new HashMap<>();
			//<<이 프로젝트용
			Object boardObject = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			if( boardObject instanceof LoginUser _user) {
				paramMap.put("likeUserEmail", _user.getUsername());
			}

			paramMap.put("boardId", boardId);
			if ( boardId == 0 && !boardSettingTypeCode.isEmpty() ) {
				paramMap.put("typeCode", boardSettingTypeCode );
				BoardSetting boardSetting = boardService.selectOneBoardSetting( paramMap );
				paramMap.put("boardId", boardSetting.getId() );
			}

			if ( categoryId != 0 ) {
				paramMap.put("categoryId", categoryId);
			}

			if ( !userEmai.equals("") ) {
				paramMap.put("userEmail", userEmai);
			}
			paramMap.put("useYn", "Y");
			paramMap.put("deleteYn", "N");
			paramMap.put("sort", "createDate");
			paramMap.put("sortOrder", "desc");
			int totalCount = boardService.selectCountBoardContents(paramMap);

	        paramMap.put("itemStartPosition", itemStartPosition);
	        paramMap.put("pagePerSize", pagePerSize);
	       	List<BoardContents> contents = boardService.selectListBoardContents(paramMap);
			   for (BoardContents cont : contents) {
				paramMap.clear();
				paramMap.put("contentId", cont.getId());
				cont.setImageList(boardService.selectListBoardAttachFile(paramMap));
			}

	        resultMap.put("result", "success");
	        resultMap.put("data", contents);
	        resultMap.put("recordsTotal", totalCount);
	        resultMap.put("message", "조회되었습니다.");
	    } catch (Exception e) {
	        resultMap.put("result", "error");
	        resultMap.put("message", "조회중 오류가 발생했습니다.");
	        logger.error("조회 오류", e);
	    }

	    return resultMap;
	}

	@PostMapping("/delete")
    @ResponseBody
    public HashMap<String, Object> board_delete_ajax(
    		BoardContents bc
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			String userEmail = ((LoginUser)_user).getUserEmail();

			bc.setCreateId(userEmail);
			bc.setUserRole( ((LoginUser)_user).getUserRole() );
			bc.setUserName( ((LoginUser)_user).getUserName() );

			boardService.deleteBoardContents(bc);

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리되었습니다.");

		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 에러가 발생하였습니다.");
			e.printStackTrace();
			retrunMap.put("info", e.getMessage());
			logger.error(e.getCause().getMessage());

		}
        return retrunMap;
    }


    // 댓글 목록 조회 (페이징)
    @GetMapping("/list-comments")
	@ResponseBody
    public HashMap<String, Object> getComments(
		@RequestParam int contentId
		, @RequestParam int page
		, @RequestParam int size) {
        HashMap<String, Object> response = new HashMap<>();
        try {
			
			int offset = (page - 1) * size;
			HashMap<String, Object> paramMap = new HashMap<>();
			paramMap.put("contentId", contentId);
			paramMap.put("size", size);
			paramMap.put("offset", offset);

			int totalComments = boardService.selectTotalCommentCount(paramMap);
			int totalPages = (int) Math.ceil((double) totalComments / size);

			List<Map<String, Object>> comments = boardService.selectListTotalComment(paramMap);
			// mentions 문자열 -> List<String> 변환
			for (Map<String, Object> comment : comments) {
				parseMentions(comment);
			}

			HashMap<String, Object> data = new HashMap<>();
			data.put("comments", comments);
			data.put("totalPages", totalPages); // 전체 페이지 수
			response.put("result", "success");
			response.put("data", data);

        } catch (Exception e) {
            response.put("result", "error");
            response.put("message", "댓글 목록 조회 실패: " + e.getMessage());
        }
        return response;
    }

    // 댓글 등록
    @PostMapping("/save-comments")
	@ResponseBody
    public HashMap<String, Object> addComment(BoardComment boardComment) {
        HashMap<String, Object> retrunMap = new HashMap<>();
        try {
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

    		if(_user instanceof LoginUser) {
				String mentionsStr = createMentionsString(boardComment.getMentionsParam()); 
				boardComment.setCreateId( ((LoginUser)_user).getUserEmail() );
				boardComment.setUserName( ((LoginUser)_user).getUserName() );
				boardComment.setUserRole( ((LoginUser)_user).getUserRole() );
				boardComment.setMentions(mentionsStr);

				boardService.insertBoardComment(boardComment);

				HashMap<String, Object> paramMap = new HashMap<>();
				paramMap.put("id", boardComment.getId());
				Map<String, Object> newComment = boardService.selectOneCommentReply(paramMap);


				retrunMap.put("data", newComment);
				retrunMap.put("result", "success");
				retrunMap.put("message", "등록되었습니다.");
				return retrunMap;
			}
			else {
				retrunMap.put("result", "error");
				retrunMap.put("message", "로그인 후 이용해주세요.");
				return retrunMap;
			}
        } catch (Exception e) {
            retrunMap.put("result", "error");
            retrunMap.put("message", "댓글 등록 실패: " + e.getMessage());
        }
        return retrunMap;
    }


    // 댓글 수정
    @PutMapping("/modify-comments/{id}")
	@ResponseBody
    public HashMap<String, Object> updateComment(@PathVariable int id, @RequestParam String content,
	@RequestParam(required = false) List<String> mentionsParam) {
        HashMap<String, Object> retrunMap = new HashMap<>();
        try {
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

    		if(_user instanceof LoginUser) {
				String mentionsStr = createMentionsString(mentionsParam);

				HashMap<String, Object> paramMap = new HashMap<>();
				paramMap.put("id", id);
				paramMap.put("note", content);
				paramMap.put("mentions", mentionsStr);
				paramMap.put("lastUpdateId", ((LoginUser)_user).getUserEmail());

				boardService.updateBoardComments(paramMap);

				retrunMap.put("result", "success");
				retrunMap.put("message", "수정되었습니다.");
				return retrunMap;
			}
			else {
				retrunMap.put("result", "error");
				retrunMap.put("message", "로그인 후 이용해주세요.");
				return retrunMap;
			}
        } catch (Exception e) {
            retrunMap.put("result", "error");
            retrunMap.put("message", "댓글 수정 실패: " + e.getMessage());
        }
        return retrunMap;
    }


    // 댓글 삭제
    @PutMapping("/remove-comments/{id}")
	@ResponseBody
    public HashMap<String, Object> deleteComment(@PathVariable int id) {
        HashMap<String, Object> retrunMap = new HashMap<>();
        try {
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

    		if(_user instanceof LoginUser) {
				HashMap<String, Object> paramMap = new HashMap<>();
				paramMap.put("id", id);
				paramMap.put("lastUpdateId", ((LoginUser)_user).getUserEmail());

				boardService.deleteBoardComments(paramMap);

				retrunMap.put("result", "success");
				retrunMap.put("message", "삭제되었습니다.");
				return retrunMap;
			}
			else {
				retrunMap.put("result", "error");
				retrunMap.put("message", "로그인 후 이용해주세요.");
				return retrunMap;
			}
        } catch (Exception e) {
            retrunMap.put("result", "error");
            retrunMap.put("message", "댓글 삭제 실패: " + e.getMessage());
        }
        return retrunMap;
    }

    // 대댓글 목록 조회
    @GetMapping("/list-replies")
	@ResponseBody
    public HashMap<String, Object> getReplies(
		@RequestParam int upperId
		, @RequestParam int start
		, @RequestParam int size) {
       HashMap<String, Object> resultMap = new HashMap<>();
        try{
			HashMap<String, Object> paramMap = new HashMap<>();
			paramMap.put("upperId", upperId);	
			paramMap.put("size", size);
			paramMap.put("offset", start);
			List<Map<String, Object>> replies = boardService.selectCommentReply(paramMap);
			for (Map<String, Object> reply : replies) {
                parseMentions(reply);
            }

            resultMap.put("result", "success");
            resultMap.put("data", replies);

        } catch(Exception e){
            resultMap.put("result", "error");
            resultMap.put("message", "대댓글 목록 조회 실패 : " + e.getMessage());
        }
        return resultMap;
    }


    // 대댓글 등록
    @PostMapping("/save-replies")
	@ResponseBody
    public HashMap<String, Object> addReply(BoardComment boardComment) {
		HashMap<String, Object> retrunMap = new HashMap<>();
		try {
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			if(_user instanceof LoginUser) {
				String mentionsStr = createMentionsString(boardComment.getMentionsParam()); 
				boardComment.setCreateId( ((LoginUser)_user).getUserEmail() );
				boardComment.setUserName( ((LoginUser)_user).getUserName() );
				boardComment.setUserRole( ((LoginUser)_user).getUserRole() );
				boardComment.setMentions(mentionsStr);

				boardService.insertBoardComment(boardComment);

				HashMap<String, Object> paramMap = new HashMap<>();
				paramMap.put("id", boardComment.getId());

				Map<String, Object> newReply = boardService.selectOneCommentReply(paramMap);
				parseMentions(newReply);

				retrunMap.put("data", newReply);
				retrunMap.put("result", "success");
				retrunMap.put("message", "등록되었습니다.");
				return retrunMap;
			}
			else {
				retrunMap.put("result", "error");
				retrunMap.put("message", "로그인 후 이용해주세요.");
				return retrunMap;
			}
		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "댓글 등록 실패: " + e.getMessage());
		}
		return retrunMap;
    }


    //대댓글 수정
    @PutMapping("/modify-replies/{id}")
	@ResponseBody
    public HashMap<String, Object> updateReply(@PathVariable int id, @RequestParam String content,
	@RequestParam(required = false) List<String> mentionsParam){
		HashMap<String, Object> retrunMap = new HashMap<>();
        try {
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

    		if(_user instanceof LoginUser) {
				String mentionsStr = createMentionsString(mentionsParam);

				HashMap<String, Object> paramMap = new HashMap<>();
				paramMap.put("id", id);
				paramMap.put("note", content);
				paramMap.put("mentions", mentionsStr);
				paramMap.put("lastUpdateId", ((LoginUser)_user).getUserEmail());

				boardService.updateBoardComments(paramMap);

				retrunMap.put("result", "success");
				retrunMap.put("message", "수정되었습니다.");
				return retrunMap;
			}
			else {
				retrunMap.put("result", "error");
				retrunMap.put("message", "로그인 후 이용해주세요.");
				return retrunMap;
			}
        } catch (Exception e) {
            retrunMap.put("result", "error");
            retrunMap.put("message", "댓글 수정 실패: " + e.getMessage());
        }
        return retrunMap;
    }

    //대댓글 삭제
    @PutMapping("/remove-replies/{id}")
	@ResponseBody
    public HashMap<String, Object> deleteReply(@PathVariable int id){
		HashMap<String, Object> retrunMap = new HashMap<>();
        try {
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

    		if(_user instanceof LoginUser) {
				HashMap<String, Object> paramMap = new HashMap<>();
				paramMap.put("id", id);
				paramMap.put("lastUpdateId", ((LoginUser)_user).getUserEmail());

				boardService.deleteBoardComments(paramMap);

				retrunMap.put("result", "success");
				retrunMap.put("message", "삭제되었습니다.");
				return retrunMap;
			}
			else {
				retrunMap.put("result", "error");
				retrunMap.put("message", "로그인 후 이용해주세요.");
				return retrunMap;
			}
        } catch (Exception e) {
            retrunMap.put("result", "error");
            retrunMap.put("message", "댓글 삭제 실패: " + e.getMessage());
        }
        return retrunMap;
    }

	//대댓글 개수 가져오기
    @GetMapping("/count-replies")
	@ResponseBody
    public HashMap<String, Object> countReplies(
		@RequestParam int upperId) {
        HashMap<String, Object> response = new HashMap<>();
        try {
            HashMap<String, Object> paramMap = new HashMap<>();
			paramMap.put("upperId", upperId);	
			int count = boardService.selectCommentReplyCount(paramMap);

            response.put("result", "success");
            response.put("data", count);
        } catch (Exception e) {
            response.put("result", "error");
            response.put("message", "대댓글 개수 조회 실패: " + e.getMessage());
        }
        return response;
    }

	// 특정 최상위 댓글에 참여한 사용자들의 닉네임 목록 가져오기 (멘션 기능) - 변경 없음
    @GetMapping("/mentions")
	@ResponseBody
    public HashMap<String, Object> getMentions(@RequestParam int upperId) {
        HashMap<String, Object> response = new HashMap<>();
        try {
			HashMap<String, Object> paramMap = new HashMap<>();
			paramMap.put("upperId", upperId);
			List<String> mensions = boardService.selectOneCommentMentions(paramMap);

            response.put("result", "success");
            response.put("data", mensions);
        } catch (Exception e) {
            response.put("result", "error");
            response.put("message", "멘션 사용자 목록 조회 실패: " + e.getMessage());
        }
        return response;
    }

	// 멘션 문자열 파싱 (쉼표로 구분된 문자열 -> List<String>) - 도우미 함수
    private void parseMentions(Map<String, Object> comment) {
        Object mentionsObj = comment.get("mentions");
        if (mentionsObj != null && mentionsObj instanceof String) {
            String mentionsStr = (String) mentionsObj;
            if (StringUtils.hasText(mentionsStr)) { // 빈 문자열이 아닌 경우에만
                List<String> mentions = Arrays.asList(StringUtils.commaDelimitedListToStringArray(mentionsStr));
                comment.put("mentions", mentions);
            } else {
                comment.put("mentions", null); // 빈 문자열이면 null (또는 빈 List)
            }
        } else {
            comment.put("mentions", null); // mentions 컬럼이 없으면 null (또는 빈 List)
        }
    }

    // 멘션 List<String> -> 쉼표로 구분된 문자열 변환 - 도우미 함수
    private String createMentionsString(List<String> mentions) {
        if (mentions == null || mentions.isEmpty()) {
            return null;
        }
        return StringUtils.collectionToCommaDelimitedString(mentions);
    }

	@PostMapping("/mission-already-check")
	@ResponseBody
	public HashMap<String, Object> ajax_mission_already_check(
	        @RequestParam(value = "boardId", defaultValue = "0") int boardId,
	        @RequestParam(value = "upperBoardId", defaultValue = "0") int upperBoardId
			) {
	    HashMap<String, Object> resultMap = new HashMap<>();

	    try {
			boolean isAlreadyCheck = false;
	        HashMap<String, Object> paramMap = new HashMap<>();
			
			paramMap.put("boardId", boardId);
			paramMap.put("upperBoardId", upperBoardId);
			paramMap.put("createId", getBaseUserEmail());

	       	int contents = boardService.isMissionAlreadyCheck(paramMap);
			if (contents > 0) {
				isAlreadyCheck = true;
			}

	        resultMap.put("result", "success");
	        resultMap.put("isAlreadyCheck", isAlreadyCheck);
	        resultMap.put("message", "조회되었습니다.");
	    } catch (Exception e) {
	        resultMap.put("result", "error");
	        resultMap.put("message", "조회중 오류가 발생했습니다.");
	        logger.error("조회 오류", e);
	    }

	    return resultMap;
	}
}
