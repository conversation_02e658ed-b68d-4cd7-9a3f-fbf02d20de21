package kr.co.wayplus.travel.model;

import java.util.ArrayList;

import com.fasterxml.jackson.annotation.JsonInclude;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BoardContents extends CommonDataSet {
    private int id;
    private int productTourId;
    private Integer boardId;
    private String boardType;
    private Integer menuId; //청풍용
    private Integer upperBoardId; //청풍용
    private String missionType; //청풍용
    private String missionTeam; //청풍용
    private String missionArea; //청풍용
    private String missionCheckYn; //청풍용
    private Integer missionCompleteCount; //청풍용
    private Integer userPointSetId; //청풍용
    private Integer userBadgeSetId; //청풍용
    private String boardUrl;
    private String boardName;
    private Integer categoryId;
    private String categoryName;
    private String boardLink;
    private String title;
    private String content;
    private String note;
    private String applyCode;
    private Integer seriesId;
    private String seriesName;
    private String tags;
    private String thumbnailUrl;
    private int favoriteCount;
    private Integer scrapCount;
    private int commentCount;
    private Integer viewCount;
    private Integer attachmentCount;
    private int attachFileCount;
    private String useYn;
    private String fixYn;
    private String secretYn;
    private String cancelYn;
    private String guestName;
    private String guestPass;
    private String startDate;
    private String expireDate;

    private String comment;
    private String commentTime;

    private String userNickName;
    private String userName;
	private String boardThumbnail;
    private String userType;

    private String applyCodeName;
    private String userFavorite;
    private String userFavoriteId;

    private String fullMenuUrl;
    private String boardFavoriteType;
    private String boardCommentFavoriteType;
    private String answerCreateDate;
    private String answerLastUpdateDate;
    private ArrayList<BoardAttachFile> imageList;

    private Integer idPrev;
    private Integer idNext;

    private ArrayList<BoardComment> listComment;

    //청풍용
    private String pointAddFlag;
    private String pointAddType;
    private String pointType;
    private String userRole;
    private String userProfileImage;
    private String favoriteCalcType;
    private String assembleDate;
    private String assembleTime;
    private String isPossible;
    private String userEmail;
    private String deleteImageIds;
//    private String titleLike;
//    private String contentLike;
//    private String searchKey;

    private String accruedReason, accruedPoint, badgeName, badgeFileUrl;

    public BoardContents(){
        this.id = 0;
        this.boardId = 0;
        this.categoryId = 0;
        this.seriesId = 0;
        this.favoriteCount = 0;
        this.scrapCount = 0;
        this.commentCount = 0;
        this.viewCount = 0;
        this.attachmentCount = 0;
        this.pointAddType = "board";
    }


    public void setImageList(ArrayList<BoardAttachFile> imageList) {
        this.imageList = imageList;
    }
}
