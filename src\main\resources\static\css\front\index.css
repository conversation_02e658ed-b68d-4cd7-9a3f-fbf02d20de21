/*메인배너*/
.main_banner_wrap {
    position: relative;
    overflow: hidden;
}

.main_banner_swiper {
    position: relative;
}

.main_banner.swiper-slide {
    position: relative;
    width: 100%;
    height: 800px;
    overflow: hidden;
}

.main_banner_img_box {
    width: 100%;
    height: 100%;
}

.main_banner_img {
    width: 100%;
    height: 100%;
}

.main_banner_img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.main_banner_txt_box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    text-align: center;
    margin-top: -35PX;
}

.main_banner_title_box {
    position: relative;
    width: fit-content;
    margin: 0 auto;
}

.main_banner_title {
    color: #FFF;
    text-shadow: 3px 3px 0px #000;
    font-family: Tenada;
    font-size: 100px;
    font-weight: 800;
}

.main_banner_icon {
    position: absolute;
    top: -140px; left: -50px;
    width: 229px;
    height: 192px;
    background: url(/images/main_banner_icon.png);
}

.main_banner_ex {
    color: #FFF;
    font-size: 26px;
    font-weight: 600;
    line-height: 38px;
}
.main_banner_ex_text {
    word-break: keep-all;
}

.page_pause_arrow {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translate(-50%, 0);
    z-index: 99999;
}

.main_banner_line {
    display: flex;
    justify-content: center;
    margin-bottom: 10px;
}

.main_banner_line span {
    width: 8px; height: 8px;
    background: #808080;
    border-radius: 50%;
    border: 1px solid #000;
    margin: 0 2px;
}

.main_banner_line span:hover {
    cursor: pointer;
    background: #fff;
}

.main_banner_line .on {
    background: #fff;
}

.main_banner_page {
    display: block !important;
    width: unset !important;
    text-align: center;
    color: #A5A5A5;
}

@media screen and (max-width:600px) {
    .main_banner.swiper-slide {
        height: 515px;
    }

    .main_banner_title {
        font-size: 48px;
    }

    .main_banner_ex {
        font-size: 16px;
    }

    .main_banner_icon {
        position: absolute;
        top: -90px; left: -30px;
        width: 127px;
        height: 74px;
        background: url(/images/main_banner_icon.png);
        background-repeat: no-repeat;
        background-size: cover;
    }
}

/*프로그램*/
.main_program {
    padding: 150px 0;
}

.main_title_box {
    color: #222;
}

.main_title {
    display: block;
    width: fit-content; height: 63px;
    min-width: 218px;
    padding: 15px 23px;
    box-sizing: border-box;
    border-radius: 40px;
    border: 1px solid #000;
    font-family: Tenada;
    font-size: 32px;
    font-weight: 800;
    text-align: center;
    margin: 0 auto;
}

.main_title_ex {
    text-align: center;
    font-size: 18px;
    margin: 30px 0 50px 0;
}

.slide_contents_wrap {
    position: relative;
}

.slide_arrow {
    position: absolute;
    top: 50%;
    margin-top: -55px;
    width: 56px;
    height: 55px;
    border-radius: 50%;
    cursor: pointer;
    z-index: 99999;
}

.slide_arrow_prev {
    left: -76px;
    background: url(/images/icon/slide_arrow_prev.svg);
    background-size: cover;
}


.slide_arrow_next {
    right: -76px;
    background: url(/images/icon/slide_arrow_prev.svg);
    rotate: 180deg;
    background-size: cover;
}

.slide_contents {
    width: 1430px;
    margin-left: -15px;
    display: flex;
    flex-wrap: nowrap;
}

.slide_contents_list {
    width: 33.3%;
    margin: 0 15px;
    cursor: pointer;
}

.slide_contents_list:hover .product_img img {
    transform: scale(1.1);
}

.product_img {
    width: 100%; height: auto;
    overflow: hidden;
}

.product_img img {
    width: 100%; height: auto;
    transition: all 0.3s;
}

.category_box {
    display: flex;
    margin: 30px 0 20px 0;
}

.category {
    width: 55px;
    height: 26px;
    border-radius: 50px;
    border: 1px solid #222;
    color: #FFF;
    font-size: 15px;
    font-weight: 600;
    line-height: 26px;
    text-align: center;
}

.category_mg_r {
    margin-right: 7px;
}

.category_red {
    background: #F25353;
}

.category_blue {
    background: #007BD0;
}

.category_green {
    background: #094;
}

.category_gray {
    background: #ECECEC;
    color: #222;
}

.contents_list_title {
    font-size: 20px;
    font-weight: 600;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

@media screen and (max-width:1660px) {
    .slide_arrow_prev {
        left: 20px;
    }

    .slide_arrow_next {
        right: 20px;
    }
}

@media screen and (max-width:600px) {
    .main_title {
        min-width: 175px; height: 50px;
        padding: 12px 20px;
        font-size: 26px;
    }

    .main_title_ex span {
        display: block;
    }
    .main_program {
        padding: 70px 0;
    }

    .slide_arrow {
        display: none;
    }

    .slide_contents {
        margin-left: -10px;
    }

    .slide_contents_list {
        width: 298px;
        margin: 0 10px;
    }

    .contents_list_title {
        font-size: 18px;
    }
}

/*잠시섬*/
.main_jamsisum {
    padding: 130px 0 150px 0;
    background: url(/images/main_jamsisum_bg.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    overflow: hidden;
}

.main_jamsisum_contents {
    display: flex;
    justify-content: space-between;
}

.main_jamsisum_title_box {
    color: #fff;
    position: relative;
    z-index: 9;
}

.main_title_top {
    text-shadow: 2px 2px 0px #000;
    font-family: Tenada;
    font-size: 36px;
    font-weight: 800;
    margin-bottom: 15px;
}

.main_big_title {
    text-shadow: 2px 2px 0px #000;
    font-family: Tenada;
    font-size: 65px;
    font-weight: 800;
}

.main_common_ex {
    margin: 40px 0;
    font-size: 20px;
    font-weight: 400;
    line-height: 30px;
}

.main_jamsisum_btn {
    display: block;
    width: 184px;
    height: 45px;
    border-radius: 22.5px;
    border: 1px solid #000;
    background: #0AAC52;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    line-height: 45px;
}

.main_jamsisum_btn:hover {
    cursor: pointer;
    background: #000;
    opacity: 0.9;
}

.main_jamsisum_img_box {
    position: relative;
    width: 700px;
    height: 390px;
    z-index: 2;
}

.main_jamsisum_img {
    position: relative;
    z-index: 999;
}

.main_jamsisum_img.swiper-wrapper {
    position: relative;
    z-index: 2;
}

.main_jamsisum_img img {
    width: 100%; height: 100%;
    object-fit: cover;
    z-index: 2;
}
.main_jamsisum_img_box .main_jamsisum_img_btn.swiper-pagination-bullets{
    position:absolute;
    bottom:-40px;
    z-index: 999;
    text-align: right;
}
.main_jamsisum_img_btn li {
    position: relative;
    width: 8px; height: 8px;
    border-radius: 50%;
    border: 1px solid #000;
    margin: 0 2px;
    z-index: 9999;
}

.main_jamsisum_img_btn li:hover {
    cursor: pointer;
    background: #fff;
}

.main_jamsisum_img_btn .on {
    background: #fff;
}

.main_jamsisum_img_line {
    position: absolute;
    bottom: -50px; right: -40px;
    width: 710px;
    height: 400px;
    border: 1px solid rgba(27, 27, 27, 0.50);
    z-index: 1;
}

.main_jamsisum_img_green {
    position: absolute;
    left: -153px; bottom: -96px;
    width: 340px; height: 340px;
    background: url(/images/main_jamsisum_img_green.png);
    background-size: cover;
    animation-name: circlegreen;
    animation-duration: 1s;
    animation-iteration-count: infinite;
}

@keyframes circlegreen {
    0% {bottom: -96px;}
    50% {bottom: -86px;}
    100% {bottom: -96px;}
}

.main_jamsisum_img_blue {
    position: absolute;
    right: -90px; top: -64px;
    width: 150px; height: 150px;
    background: url(/images/main_jamsisum_img_blue.png);
    background-size: cover;
    animation-name: circleblue;
    animation-duration: 1s;
    animation-iteration-count: infinite;
}

@keyframes circleblue {
    0% {top: -64px;}
    50% {top: -74px;}
    100% {top: -64px;}
}

.main_jamsisum_img_red {
    position: absolute;
    right: -190px; bottom: -75px;
    width: 68px; height: 68px;
    background: url(/images/main_jamsisum_img_red.png);
    background-size: cover;
    animation-name: circlered;
    animation-duration: 1s;
    animation-iteration-count: infinite;
}

@keyframes circlered {
    0% {bottom: -75px;}
    50% {bottom: -65px;}
    100% {bottom: -75px;}
}

@media screen and (max-width:1300px) {
    .main_jamsisum_img_box {
        width: 500px;
        height: 278px;
        margin-right: 40px;
    }

    .main_jamsisum_img_line {
        position: absolute;
        bottom: -50px; right: -40px;
        width: 510px;
        height: 288px;
        border: 1px solid rgba(27, 27, 27, 0.50);
        z-index: 99;
    }
}

@media screen and (max-width:1024px) {
    .main_jamsisum_contents {
        align-items: center;
    }

    .main_jamsisum_img_box {
        width: 320px;
        height: 178px;
        margin-right: 20px;
    }

    .main_jamsisum_img_line {
        position: absolute;
        bottom: -50px; right: -20px;
        width: 300px;
        height: 185px;
        border: 1px solid rgba(27, 27, 27, 0.50);
        z-index: 99;
    }

    .main_jamsisum_img_green {
        bottom: -250px;
    }
}

@media screen and (max-width:768px) {
    .main_jamsisum_contents {
        flex-wrap: wrap;
    }

    .main_jamsisum_title_box {
        width: 100%;
    }

    .main_jamsisum_img_box {
        width: 100%;
        height: auto;
        margin-right: 0px;
    }

    .main_jamsisum_img_line {
        position: absolute;
        bottom: -15px; right: -10px;
        width: 100%;
        height: 100%;
        border: 1px solid rgba(27, 27, 27, 0.50);
        z-index: 1;
    }

    .main_jamsisum_img_box .main_jamsisum_img_btn.swiper-pagination-bullets {
        text-align: center;
    }

    .main_jamsisum_img_green {
        position: absolute;
        left: -15px; bottom: -176px;
        width: 148px; height: 148px;
        background: url(/images/main_jamsisum_img_green.png);
        background-size: cover;
    }

    .main_jamsisum_img_blue {
        position: absolute;
        right: -40px; top: -130px;
        width: 150px; height: 150px;
        background: url(/images/main_jamsisum_img_blue.png);
        background-size: cover;
    }

    .main_jamsisum_img_red {
        position: absolute;
        right: 5px; bottom: -86px;
        width: 68px; height: 68px;
        background: url(/images/main_jamsisum_img_red.png);
        background-size: cover;
    }
}

@media screen and (max-width:600px) {
    .main_jamsisum {
        padding: 70px 0 145px 0;
    }

    .main_title_top {
        font-size: 24px;
    }

    .main_big_title {
        font-size: 52px;
    }

    .main_common_ex {
        margin: 26px 0 56px 0;
        font-size: 18px;
        line-height: 26px;
        word-break: keep-all;
    }
    .main_common_ex p{
        margin-bottom:10px;
    }

    .main_jamsisum_btn {
        margin: 0 auto;
        margin-bottom: 50px;
    }
    .main_common_ex_enter p br{
        display:none;
    }
}


/*강화유니버스*/
.main_universe {
    padding: 176px 0;
    background: url(/images/main_universe_bg.jpg);
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-size: cover;
    color: #fff;
}

.main_universe_title_box {
    text-align: center;
}

.main_universe .main_common_ex {
    text-align: center;
}

.main_universe_btn {
    display: block;
    width: 184px;
    height: 45px;
    margin: 0 auto;
    border-radius: 22.5px;
    border: 1px solid #000;
    background: #007ED6;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    line-height: 45px;
}

.main_universe_btn:hover {
    cursor: pointer;
    background: #000;
    opacity: 0.9;
}

@media screen and (max-width:600px) {
    .main_universe {
        padding: 73px 0 110px 0;
    }

    .main_universe_title_box {
        text-align: left;
    }
}

/*커뮤니티 프로그램*/
.main_community_program {
    padding: 150px 0;
}

.slide_contents_list_ver {
    /* width: 25%; */
    /* margin: 0 15px; */
    cursor: pointer;
}

.slide_contents_list_ver:hover .product_img img {
    transform: scale(1.1);
}

.category_blak {
    border: 1px solid #222;
    background: #FFF;
    color: #222;
}

@media screen and (max-width:600px) {
    .main_community_program {
        padding: 80px 0;

    }
    .slide_contents_list_ver {
        width: 298px;
        margin: 0 10px;
    }
}

/*잠시섬 소식*/
.main_notice {
    padding: 80px 0;
    border-top: 1px solid #000;
    border-bottom: 1px solid #000;
}

.main_notice_contents {
    display: flex;
    justify-content: space-between;
}

.main_title_plus_box {
    margin-right: 20px;
}

.main_title_plus {
    width: 180px;
    color: #222;
    font-family: Tenada;
    font-size: 32px;
    font-weight: 800;
    margin-bottom: 45px;
}

.main_title_plus_icon {
    display: block;
    width: 33px;
    height: 34px;
    background: url(/images/icon/more.svg);
    transition: all 0.3s;
}

.main_title_plus_icon:hover {
    cursor: pointer;
    transform: rotate(180deg);
}

.main_border_width {
    width: 1040px;
}

.main_notice_list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.main_notice_list:hover .main_notice_list_title {
    cursor: pointer;
    text-decoration: underline;
}

.main_notice_list_title {
    width: calc(100% - 100px - 20px);
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    color: #333;
    font-size: 22px;
    font-weight: 500;
}

.main_notice_list_day {
    color: #666;
    font-size: 16px;
    font-weight: 400;
}

@media screen and (max-width:600px) {
    .main_notice_contents {
        flex-wrap: wrap;
    }

    .main_title_plus_box {
        margin-right: 0px;
        margin-bottom: 25px;
    }

    .main_title_plus_box {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .main_title_plus {
        margin-bottom: 0;
        width:calc(100% - 40px);
    }

    .main_notice_list {
        flex-wrap: wrap;
    }

    .main_notice_list_title {
        width: 100%;
        -webkit-line-clamp: 2;
        font-size: 18px;
    }

    .main_notice_list_day {
        margin-top: 10px;
    }
}

/*강화 뉴스레터*/
.main_newsletter {
    padding: 140px 0;
}

.main_newsletter_list_box {
    display: flex;
    justify-content: space-between;
}

.main_newsletter_list {
    width: calc(33.3% - 20px);
    border: 1px solid #000;
    transition-duration: 0.3s;
}

.main_newsletter_list:hover {
    cursor: pointer;
    transform: translateY(-10px);
}

.main_newsletter_list_title {
    padding: 46px 40px 0 40px;
    margin-bottom: 40px;
    box-sizing: border-box;
    min-height: 190px;
    color: #333;
    font-size: 22px;
    font-weight: 600;
    line-height: 35px;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.main_newsletter_list_day {
    padding: 21px 40px;
    border-top: 1px solid #ccc;
    color: #666;
}

@media screen and (max-width:900px) {
    .main_newsletter_list_box {
        flex-wrap: wrap;
    }

    .main_newsletter_list {
        width: 100%;
        margin-bottom: 20px;
    }

    .main_newsletter_list:hover {
        cursor: pointer;
        transform: unset;
    }

    .main_newsletter_list:last-child {
        margin-bottom: 0;
    }

    .main_newsletter_list_title {
        padding: 30px 40px 0 40px;
        margin-bottom: 30px;
        min-height: 130px;
        -webkit-line-clamp: 3;
    }

    .main_newsletter_list_day {
        padding: 14px 40px;
        border-top: 1px solid #ccc;
        color: #666;
    }
}

@media screen and (max-width:600px) {
    .main_newsletter {
        padding: 80px 0;
    }
}

/*B2B 문의하기*/
.main_b2b {
    padding: 157px 0 126px 0;
    background: url(/images/main_b2b_bg.jpg) center center no-repeat;
    background-attachment: fixed;
    background-size:cover;
    color: #fff;
}

.main_b2b_contents {
    display: flex;
    align-items: center;
    justify-content: center;
}

.main_b2b_title_box {
    text-align: center;
}

.main_b2b_title {
    font-family: Tenada;
    font-size: 32px;
    font-weight: 800;
    margin-bottom: 19px;
}

.main_b2b_ex {
    font-size: 18px;
}

.main_b2b_circle_box {
    display: flex;
    margin-left: 145px;
}

.main_b2b_circle {
    width: 97px;
    height: 97px;
    border-radius: 48.732px;
    font-size: 22px;
    font-weight: 600;
    text-align: center;
    line-height: 97px;
}

.main_b2b_circle_red {
    background: #F25353;
}

.main_b2b_circle_green {
    background: #094;
    margin: 0 25px;
}

.main_b2b_circle_blue {
    background: #007BD0;
}

@media screen and (max-width:768px) {
    .main_b2b {
        padding:80px 0 64px 0;
    }
    .main_b2b_circle_box {
        display: flex;
        margin-left: 20px;
    }
}

@media screen and (max-width:600px) {
    .main_b2b_contents {
        flex-wrap: wrap;
    }

    .main_b2b_title_box {
        width: 100%;
        margin-bottom: 30px;
    }

    .main_b2b_ex span {
        display: block;
    }

    .main_b2b_circle_box {
        margin-left: 0;
    }

    .main_b2b_circle {
        width: 85px;
        height: 85px;
        font-size: 20px;
        line-height: 85px;
    }

    .main_b2b_circle_green {
        margin: 0 15px;
    }

    .main_banner_ex_text {
        line-height: 23px;
    }
}

.main_banner_title_ani span {
    position:relative;
    display: inline-block;
    animation:txtup 1.5s infinite;
    -webkit-animation:txtup 1.5s infinite;
    -ms-animation:txtup 1.5s infinite;
    -moz-animation: txtup 1.5s infinite;
}
.main_banner_title_ani span:nth-of-type(1){animation-delay:0.1s;}
.main_banner_title_ani span:nth-of-type(2){animation-delay:0.2s;}
.main_banner_title_ani span:nth-of-type(3){animation-delay:0.3s;}
.main_banner_title_ani span:nth-of-type(4){animation-delay:0.4s;}
.main_banner_title_ani span:nth-of-type(5){animation-delay:0.5s;}
.main_banner_title_ani span:nth-of-type(6){animation-delay:0.6s;}
.main_banner_title_ani span:nth-of-type(7){animation-delay:0.7s;}

@-webkit-keyframes txtup {
    0%{top:0;}
    20% {top:-0.5rem;}
    40% {top:0;}
    60% {top:0;}
    80% {top:0;}
    100% {top:0}
}
@keyframes txtup {
    0% {top:0;}
    20% {top:-0.5rem;}
    40% {top:0}
    60% {top:0}
    80% {top:0}
    100% {top:0}
}

/*배너*/
.guide_banner {
    display: flex;
    align-items: center;
    justify-content: center;
    background: url(/images/guide_banner.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    color: #fff;
    padding: 165px 0;
}

.sub_banner_title {
    color: #FFF;
    text-shadow: 1.849px 1.849px 0px #000;
    text-align: center;
    font-family: Tenada;
    font-size: 60px;
    font-weight: 800;
    margin-bottom: 30px;
}

.sub_banner_ex_box {
    text-align: center;
    font-size: 20px;
    font-weight: 400;
    line-height: 32px;
}

@media screen and (max-width:600px) {
    .guide_banner {
        padding: 50px 0;
    }

    .sub_banner_title {
        font-size: 30px;
    }

    .sub_banner_ex_box {
        font-size: 18px;
    }
}

/*두근두근 사전준비*/
.ready {
    padding: 115px 0 200px 0;
}

.guide_title_box {
    display: flex;
    align-items: center;
    justify-content: center;
}

.guide_title_icon {
    min-width: 46px; height: 47px;
    background: url(/images/icon/title_check.svg);
    margin-right: 15px;
}

.guide_title {
    color: #222;
    font-family: Tenada;
    font-size: 42px;
    font-weight: 800;
    padding-top: 15px;
}

.ready_content {
    margin-top: 80px;
}

.ready_three_circle_bg {
    width: 812px;
    height: 110px;
    background: url(/images/ready_three_circle_bg.png);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.ready_three_circle {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    color: #FFF;
    text-align: center;
    font-size: 20px;
    font-weight: 700;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
}

.ready_three_circle_red {
    background: #E83929;
}

.ready_three_circle_green {
    background: #094;
    margin: 0 79px;
}

.ready_three_circle_blue {
    background: #056FB8;
}

.ready_list_contents {
    margin-top: 174px;
}

.ready_list_title {
    color: #333;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 20px;
}

.ready_list_box {
    border-top: 1px solid #222;
}

.ready_list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 26px 0;
    border-bottom: 1px solid #aaa;
    color: #333;
    font-size: 20px;
    font-weight: 400;
}

.ready_list_btn {
    padding: 10px 28px;
    border-radius: 50px;
    border: 1px solid #000;
    background: #222;
    color: #FFF;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    margin-left: 20px;
    text-wrap: nowrap;
}

.ready_list_btn:hover {
    cursor: pointer;
    background: #fff;
    color: #222;
}

@media screen and (max-width:1024px) {
    .ready_list {
        flex-direction: column;
    }
    
    .ready_list_btn {
        margin-left: 0;
        margin-top: 20px;
    }
}


@media screen and (max-width:860px) {
    .ready_three_circle_bg {
        width: unset;
        height: unset;
        background: unset;
    }
}

@media screen and (max-width:768px) {
    .ready_three_circle_green {
        margin: 0 30px;
    }
}

@media screen and (max-width:600px) {
    .guide_title_icon {
        min-width: 30px;
        height: 31px;
        background: url(/images/icon/title_check.svg);
        background-size: cover;
    }

    .guide_title {
        font-size: 26px;
        padding-top: 10px;
    }
    .ready {
        padding: 40px 0;
    }

    .ready_content {
        margin-top: 30px;
    }

    .ready_three_circle_bg {
        flex-wrap: wrap;
        width:100%;
    }

    .ready_three_circle_green {
        margin: 20px 0;
    }

    .ready_three_circle{
        border-radius: 50px;
        width:100%;
        height:60px;
    }

    .ready_list_contents {
        margin-top: 50px;
    }

    .ready_list_title {
        font-size: 20px;
    }

    .ready_list {
        font-size: 16px;
    }
    .ready_list span{
        line-height:1.5;
    }
    .ready_list_btn {
        width:100%;
        font-size: 16px;
        padding: 10px 10px;
    }
    .ready_three_circle > div > p br{
        display:none;
    }

}


/*체크인_체크아웃*/
.checkin_checkout {
    padding: 0 0 170px 0;
}

.checkin_checkout_contents_box {
    margin-top: 96px;
    display: flex;
    justify-content: space-between;
}

.checkin_checkout_contents {
    width: calc(50% - 15px);
    min-height: 343px;
    border-radius: 20px;
    background: #EEF8FF;
    padding: 40px;
    box-sizing: border-box;
}

.checkin_checkout_green_bg {
    background: #EEF6F2;
}

.checkin_checkout_title {
    color: #333;
    text-align: center;
    font-size: 28px;
    font-weight: 800;
    margin: 20px 0;
}

.checkin_checkout_ex {
    color: #333;
    text-align: center;
    font-size: 20px;
    font-weight: 400;
    line-height: 32px;
}

.checkin_room_list_box {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40px;
}

.checkin_room_list {
    display: flex;
    align-items: center;
    color: #333;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
}

.checkin_room_list:hover {
    cursor: pointer;
    font-weight: 700;
}

.checkin_room_list img {
    margin-left: 15px;
}

.checkin_room_line {
    width: 1px; height: 18px;
    border-right: 1px solid #929292;
    margin: 0 25px;
}

@media screen and (max-width:1280px) {
    .checkin_room_list_box {
        flex-wrap: wrap;
    }

    .checkin_room_list {
        width: 100%;
        justify-content: center;
        margin-bottom: 10px;
    }

    .checkin_room_line {display: none;}
}

@media screen and (max-width:600px) {
    .checkin_checkout {
        padding: 0 0 50px 0;
    }

    .checkin_checkout_contents_box {
        margin-top: 30px;
        min-height:unset;
    }

    .checkin_checkout_title {
        font-size: 22px;
        text-align: left;
    }

    .checkin_checkout_ex {
        font-size: 16px;
        text-align: left;
        min-height:unset;
    }
    .checkin_checkout_ex p{
        line-height:1.5;
        margin-bottom:10px;
    }

    .checkin_checkout_contents_box {
        flex-wrap: wrap;
    }

    .checkin_checkout_contents {
        width: 100%;
        padding: 20px;
        margin-bottom: 20px;
        min-height: unset;
    }

    .checkin_room_list_box {
        margin-top: 20px;
        border-top:1px solid #222;
        border-bottom:1px solid #222;
    }
    .checkin_room_list{
        padding:20px 10px;
        justify-content: space-between;
        border-bottom:1px dashed #ccc;
        margin-bottom:0;
    }

    .checkin_room_list:last-child{
        border-bottom:unset;
    }
}

/*자유롭게 강화여행*/
.free_tour {
    display: flex;
    align-items: center;
    justify-content: center;
    background: url(/images/free_tour_bg.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    color: #fff;
    padding: 150px 0 145px 0;
}

.guide_title_icon_wh {
    min-width: 46px; height: 47px;
    background: url(/images/icon/title_check_wh.svg);
    margin-right: 15px;
}

.guide_title_wh {
    color: #fff;
}
.free-tour-wrap{
    display:flex;
    margin-top:60px;
    box-sizing: border-box;
}
.free-tour-list.list01{
    display:flex;
    width:calc(100% - 215px);
    flex-direction:column;
}
.free-tour-list.list01 div{
    display:flex;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50px;
    height: 56px;
    margin-bottom:10px;
    padding:0px 30px;
    display:flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}
.free-tour-list.list01 div span{
    display:block;
    width:24px !important;
    height:17px;
    background: url(/images/icon/free_tour_list_arrow.svg) center center no-repeat;
}
.free-tour-list.list02{
    width:185px;
    margin-left:30px;
    display:flex;
    flex-direction:column;
}
.free-tour-list.list02 div{
    background: rgba(5, 111, 184, 0.6);
    border-radius: 50px;
    height: 56px;
    display:flex;
    align-items: center;
    justify-content: center;
    margin-bottom:10px;
    cursor: pointer;
}

@media  (hover : hover) {
    .free-tour-list.list01 div:hover{
        background: rgba(0, 0, 0);
    }
    .free-tour-list.list02 div:hover{
        background: rgba(5, 111, 184);
    }
}

.free-tour-list.list01 div:last-child,
.free-tour-list.list02 div:last-child
{
    margin-bottom:0;
}


@media screen and (max-width:768px) {
    .free-tour-wrap {
        margin-top: 30px;
        flex-direction: column;
    }
    .free-tour-list.list01{
        width:100%;
        margin-bottom:30px;
    }
    .free-tour-list.list01 div,
    .free-tour-list.list02 div
    {
        height:unset;
        padding: 15px 30px;
    }
    .free-tour-list.list01 div p{
        width:calc(100% - 34px);
    }
    .free-tour-list.list02{
        width:100%;
        margin-left:0;
    }
}


@media screen and (max-width:600px) {
    .free_tour {
        padding: 50px 0;
    }


    .guide_title_icon_wh{
        width:30px;
        height:30px;
        background-size:30px 30px;
        min-width: unset;
    }



}

/*회고*/
.retrospect {
    padding: 120px 0 210px 0;
}

.retrospect_contents {
    margin-top: 80px;
}

.retrospect_list_box {
    display: flex;
    justify-content: space-between;
}

.retrospect_list {
    width: calc(33.3% - 26.6px);
    min-height: 221px;
    border: 1px solid #222;
    padding: 47px 40px; box-sizing: border-box;
    color: #333;
    font-size: 20px;
    font-weight: 400;
    line-height: 32px;
}

.retrospect_list:hover {
    cursor: pointer;
    text-decoration: underline;
    font-weight: 600;
}

.retrospect_list_arrow {
    width: 28px; height: 28px;
    background: url(/images/icon/retrospect_list_arrow.svg);
    margin-top: 30px;
}

@media screen and (max-width:970px) {
    .retrospect_list p {
        display: inline;
    }
}

@media screen and (max-width:768px) {
    .retrospect_list {
        width: calc(33.3% - 15px);
        padding: 30px;
    }
    .retrospect {
        padding: 50px 0;
    }

    .retrospect_contents {
        margin-top: 30px;
    }

    .retrospect_list_box {
        flex-wrap: wrap;
    }

    .retrospect_list {
        width: 100%;
        margin-bottom: 20px;
        min-height: unset;
        font-size: 16px;
        padding: 20px;
        display:flex;
        justify-content: space-between;
        align-items: center;
        line-height:1.6;
    }

    .retrospect_list p {
        display: block;
    }
    .retrospect_list_arrow{
        margin-top:0;
    }
}


/*숙박과 재충전*/
.lodgment_recharge {
    padding: 0 0 130px 0;
}

.lodgment_recharge_contents {
    margin-top: 80px;
    display: flex;
    align-items: center;
}

.lodgment_recharge_img {
    margin-right: 70px;
}

.lodgment_recharge_img img {
    width: 100%;
}

.lodgment_recharge_ex {
    color: #333;
    text-align: center;
    font-size: 20px;
    font-weight: 400;
    line-height: 32px;
    margin-bottom: 40px;
}

.lodgment_recharge_btn_box {
    width: fit-content;
    margin: 0 auto;
}

.lodgment_recharge_btn {
    width: 225px;
    height: 38px;
    border-radius: 4px;
    border: 1px solid #222;
    background: #222;
    color: #FFF;
    text-align: center;
    line-height: 38px;
    margin-bottom: 10px;
}

.lodgment_recharge_btn:hover {
    cursor: pointer;
    background: #fff;
    color: #222;
}

@media screen and (max-width:1280px) {
    .lodgment_recharge_img {
        width: 600px;
    }

    .lodgment_recharge_ex_btn {
        width: calc(100% - 600px);
    }
}

@media screen and (max-width:1180px) {
    .lodgment_recharge_img {
        margin-right: 30px;
    }
}

@media screen and (max-width:1024px) {
    .lodgment_recharge_img {
        width: 500px;
    }

    .lodgment_recharge_ex_btn {
        width: calc(100% - 500px);
    }
}

@media screen and (min-width:769px) and (max-width:965px) {
    .lodgment_recharge_ex p {
        display: inline;
    }
}

@media screen and (max-width:768px) {
    .lodgment_recharge_contents {
        flex-wrap: wrap;
    }

    .lodgment_recharge_img {
        margin-right: 0;
        width: 100%;
    }

    .lodgment_recharge_ex_btn {
        width: 100%;
        margin-top: 30px;
    }
}

@media screen and (max-width:600px) {
    .lodgment_recharge {
        padding: 0 0 50px 0;
    }

    .lodgment_recharge_contents {
        margin-top: 30px;
    }

    .lodgment_recharge_ex {
        font-size: 16px;
        margin-bottom: 30px;
    }
    .lodgment_recharge_ex p{
        line-height:1.5;
        margin-bottom:10px;
    }

    .lodgment_recharge_btn_box{
        margin:0;
        width:100%;
    }
    .lodgment_recharge_btn{
        width:100%;
    }
}

/*안녕 또 만나요*/
.bye {
    display: flex;
    align-items: center;
    justify-content: center;
    background: url(/images/bye_bg.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    color: #fff;
    padding: 130px 0 95px 0;
    margin-bottom: -1px;
}

.bye_title {
    color: #FFF;
    text-shadow: 1.849px 1.849px 0px #000;
    text-align: center;
    font-family: Tenada;
    font-size: 60px;
    font-weight: 800;
    margin-bottom: 30px;
}

.bye_ex_box {
    text-align: center;
    font-size: 20px;
    font-weight: 400;
    line-height: 32px;
}

.bye_btn {
    padding: 10px 28px;
    border-radius: 22.5px;
    border: 1px solid #000;
    background: #056FB8;
    color: #FFF;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    margin-top:40px;
}

.bye_btn:hover {
    cursor: pointer;
    background: #000;
}

@media screen and (max-width:600px) {
    .bye {
        padding: 50px 0;
    }

    .bye_title {
        font-size: 30px;
    }

    .bye_ex_box {
        font-size: 18px;
    }

    .bye_btn {
        font-size: 16px;
    }
}

.banner-slide-count-box {
    display: flex; justify-content: center; position: absolute; bottom: 34px; left: 0; width: 100%; z-index: 1;
}
.main-banner-left-count {
    font-size: 15px; color: #ffffff;
}
.main-banner-right-count {
    font-size: 15px; color: #A5A5A5;
}




/* 개편 후 */

/* 메인 배너 */

.main-banner-swiper .swiper-slide{
    position:relative;
    height:900px;
}
.main-banner-swiper .swiper-slide img{
    width:100%;
    height:100%;
    object-fit: cover;
}
.main-banner-swiper .text-area{
    position:absolute;
    top:50%;
    left:50%;
    transform: translate(-50%,-50%);
}
.main-banner-swiper .text-area h4{
    font-family: 'tenada';
    font-size:65px;
    color:#222;
    margin-bottom:40px;
    text-align: center;
    font-weight:600;

}
.main-banner-swiper .text-area h6{
    font-size:30px;
    line-height:42px;
    color:#222;
    margin-bottom:40px;
    text-align: center;

}
.main-banner-swiper .text-area p{
    font-size:24px;
    line-height:38px;
    color:#222;
    margin-bottom:90px;
    text-align: center;
}
.main-banner-swiper .text-area button{
    padding:18px 70px;
    text-align: center;
    color:#222;
    border:1px solid #222;
    display:block;
    margin:0 auto;
    background-color:unset;
    cursor: pointer;
    font-size:18px;
    font-weight:600;
}
.main-banner-swiper .swiper-horizontal>.swiper-pagination-bullets,
.main-banner-swiper .swiper-pagination-bullets.swiper-pagination-horizontal,
.main-banner-swiper .swiper-pagination-custom,
.main-banner-swiper .swiper-pagination-fraction{
    bottom:65px;
}
.main-banner-swiper  .swiper-pagination-bullet{
    width:9px;
    height:9px;
    background-color:#7a7a7a;
    border:1px solid #000;
    opacity: 1;
}
.main-banner-swiper  .swiper-pagination-bullet.swiper-pagination-bullet-active{
    background-color:#fff;
}

.main-banner-swiper .swiper-button-prev,
.main-banner-swiper .swiper-button-next{
    display:none;
}

@media (hover : hover) {
    .main-banner-swiper .text-area button:hover{
        background-color:#EFEFEF;
        color:#222;
        text-decoration: underline;
    }
}

@media screen and (max-width:768px) {
    .main-banner-swiper .swiper-slide{
        width:100%;
        height:600px;
    }
    .main-banner-swiper .text-area h4{
        font-size:30px;
        line-height:48px;
        text-align: center;
        margin-bottom:25px;
    }
    .main-banner-swiper .text-area h6{
        font-size:20px;
        line-height:28px;
        text-align: center;
        margin-bottom:30px;
    }
    .main-banner-swiper .text-area p{
        font-size:16px;
        line-height:26px;
        text-align: center;
        margin-bottom:40px;
    }
    .main-banner-swiper .text-area button{
        display:block;
        padding:15px 15px;
        width:100%;
    }
    .main-banner-swiper .text-area br{
        display:block;
    }
    .main-banner-swiper .swiper-pagination{
        display:none;
    }
}






/*  content01 */
.content01{
    padding:145px 0 140px 0;
    overflow: hidden;
}

.con01-area{
    display:flex;
}
.con01-area .img-area{
    width:615px;
    aspect-ratio: 1/1;
    margin-right:80px;
    overflow: hidden;
    border:1px solid #ccc;
}
.con01-area .img-area img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition: 0.3s all;
}
.con01-area .text-area{
    width:calc(100% - 615px - 80px);
    background-color: #fff;
    position:relative;
}
.con01-area .text-area h4{
    font-size:28px;
    line-height:44px;
    color:#222;
    margin-bottom:40px;
}
.con01-area .text-area .explain-area{
    height:385px;
    padding-right:50px;
    overflow-y: scroll;
    margin-bottom:45px;
}
.con01-area .text-area .explain-area::-webkit-scrollbar{
    width:7px;
}
.con01-area .text-area .explain-area::-webkit-scrollbar-thumb{
    width:7px;
    background-color:#aaa;
    border-radius: 10px;
}
.con01-area .text-area .explain-area::-webkit-scrollbar-track{
    display:none;
}
.con01-area .text-area .explain-area p{
    font-size:21px;
    line-height:32px;
    color:#222;
}
.content01 .text-area .content01-img01{
    position:absolute;
    top:40%;
    transform: translateY(-50%);
    right:-420px;
}
.content01 .text-area .content01-img02{
    position:absolute;
    bottom:50px;
    right:-127px;
}
.con01-btn{
    margin-top:30px;
}
.con01-btn button{
    width:100%;
    height:53px;
    line-height:53px;
    text-align: center;
    background-color:#007BD0;
    border:1px solid #000;
    color:#fff;
    font-size:18px;
    font-weight:600;
    border-radius: 50px;
    cursor: pointer;
    display:block;
}

@media (hover:hover) {
    .con01-btn button:hover{
        background-color:#fff;
        color:#007BD0;
    }  
    .con01-area .img-area img:hover{
        transform: scale(1.05);
    }
}

@media screen and (max-width:1024px) {
    .con01-area {
        flex-direction: column;
    }
    .con01-area .img-area{
        width:100%;
        aspect-ratio: 1/1;
        height:unset;
        margin-right:0;
        margin-bottom:40px;
    }
    .con01-area .text-area{
        width:100%;
    }
    .con01-btn button{
        margin:0 auto;
    }
}


@media screen and (max-width:768px) {
    .content01{
        padding:50px 0;
    }
    .con01-area .img-area{
        margin-bottom:20px;
    }
    .con01-area .text-area h4{
         font-size:22px;
         line-height:30px;
         margin-bottom:25px;
         text-align: center;
    }
    .con01-area .text-area .explain-area{
        height:unset;
        overflow: visible;
        padding:0 0;
    }
    .con01-area .text-area .explain-area p{
        font-size:18px;
        line-height:28px;
        text-align: center;
    }
}


/* 잠시섬 미션 보드  */
.content02{
    padding:150px 0;
    background:url(/images/main-mission-bg.jpg) center center no-repeat;
    background-size:cover;
}
.mission-contents{
    width:1200px;
    margin:0 auto;
}
.mission-contents .title h4{
    font-family: 'tenada';
    color:#222;
    font-weight:700;
    font-size:50px;
    text-align: center;
    margin-bottom:38px;
}
.misssion-con-area{
    display:flex;
    justify-content: space-between;
    margin-bottom:38px;
}
.mission-con{
    width:calc(50% - 15px);
}
.mission-con .con-title h5{
    text-align: center;
    line-height:50px;
    font-size:21px;
    color:#222;
    font-weight:500;
    background-color:#FFF3D8;
    border:1px solid #000;
}
.mission-con .list-wrap{
    border-bottom:1px solid #222;
    border-right:1px solid #222;
    border-left:1px solid #222;
    background-color:#FFFAEF;
    padding:20px 40px;
}
.mission-con .list-wrap .list{
    position:relative;
}
.mission-con .list-wrap .list li{
    width:100%;
    border-radius: 35px;
    border:1px dashed #000;
    padding:0 30px 0 40px;
    color:#222;
    position:relative;
    margin-bottom:10px;
    display:flex;
    align-items: center;
    font-size:18px;
    line-height:1.4;
    height:50px;
}
.mission-con .list-wrap .list li:last-child{
    margin-bottom:0;
}
.mission-con .list-wrap .list li:first-child{
    font-weight:600;
    background-color:#FFC960;
}
.mission-con .list-wrap .list .crown-img{
    position:absolute;
    top:-10px;
    left:10px;
    z-index: 9;
}
.mission-con .list-wrap .list li span{
    position:absolute;
    top:50%;
    left:20px;
    transform: translateY(-50%);
}
.mission-con .list-wrap .list li p{
    width:calc(100% - 39px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; 
}
.mission-con .list-wrap .list li p.no-member{
    width:100%;
}
.mission-con .list-wrap .list02 li p{
    cursor: pointer;
}
.mission-con .list-wrap .list li div{
    width:29px;
    height:29px;
    overflow: hidden;
    margin-right:10px;
    display:flex;
    align-items: center;
    border-radius: 50%;
}
.mission-con .list-wrap .list li div img{
    width:100%;
    height:100%;
    object-fit: cover;
}
.mission-con .list-wrap .list.list02 li:first-child{
    background-color:#2DAA63;
}

@media screen and (max-width:1400px) {
    .mission-contents{
        width:calc(100% - 40px);
    }
}
@media screen and (max-width:1024px) {
    .mission-con .con-title h5{
        font-size:20px;
    }
}
@media screen and (max-width:768px) {
    .content02{
        padding:60px 0;
    }
    .mission-contents .title h4{
        font-size:40px;
        margin-bottom:20px;
    }
    .misssion-con-area {
        flex-direction: column;
    }
    .mission-con{
        width:100%;
        margin-top:20px;
    }
    .mission-con .list-wrap{
        padding:30px 20px;
    }
    .mission-con .list-wrap .list li p{
        width:100%;
    }
    .mission-con .list-wrap .list02 li p{
        width:calc(100% - 39px);
    }
    .mission-con .list-wrap .list02 li p.none-member{
        width:100%;
    }
}



/* 강화유니버스 협업 프로젝트 / 탐방 문의  */
.content03{
    padding:150px 0;
}
.project-ask-wrap h4{
    font-size:50px;
    color:#222;
    font-weight:800;
    text-align: center;
    margin-bottom:51px;
    font-family: 'tenada';
}

.project-ask{
    display:flex;
}
.project-ask .con{
    width:50%;
    cursor: pointer;
}
.project-ask .con .img-area{
    width:100%;
    aspect-ratio: 1/0.6;
    overflow: hidden;
    border:1px solid #222;
}
.project-ask .con .img-area img{
    width:100%;
    height:100%;
    object-fit: cover;
    transition: 0.3s all;
}
.project-ask .con .text-area{
    border-right:1px solid #222;
    border-left:1px solid #222;
    border-bottom:1px solid #222;
    width:100%;
    padding:20px 30px;
    background-color:#1191E9;
}
.project-ask .con .text-area p{
    font-size:23px;
    font-weight:600;
    color:#222;
}
.project-ask .con:first-child .img-area,
.project-ask .con:first-child .text-area
{
    border-right:none;
}



@media (hover : hover) {
    .project-ask .con .img-area img:hover{
        transform: scale(1.1);
    }
}
@media screen and (max-width:1400px) {
    .project-ask .con .text-area{
        padding:20px 18px;
    }
}

@media screen and (max-width:1024px) {
    .project-ask .con .text-area{
        height:97px;
    }
}
@media screen and (max-width:1024px) {
    .project-ask-wrap h4{
        font-size:40px;
        word-break: keep-all;
    }
    .project-ask .con .text-area{
        padding:12px 12px;
        height:71px;
    }
    .project-ask .con .text-area p{
        font-size:19px;
    }
}

@media screen and (max-width:768px) {
    .content03{
        padding:60px 0;
    }
    .project-ask-wrap h4{
        font-size:32px;
        line-height:40px;
        margin-bottom:25px;
    }
    .project-ask {
        flex-direction: column;
    }
    .project-ask .con{
        width:100%;
    }
    .project-ask .con:first-child{
        margin-bottom:20px;
    }
    .project-ask .con:first-child .img-area,
    .project-ask .con:first-child .text-area
    {
        border-right:1px solid #222;
    }
    .project-ask .con .text-area{
        height:unset;
    }
    .con01-btn button{
        font-size:16px;
    }

}















































