package kr.co.wayplus.travel.model;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BadgeContents extends CommonDataSet {
	private Integer badgeId;	//배지 ID
	private Integer badgeCategoryId;	//배지 카테고리 ID
	private Integer badgeImageFileId;	//배지 이미지 파일 ID
	private String badgeType;	//배지 유형
	private String badgeCategory;	//배지 카테고리 이름
	private String badgeName;	//배지 이름
	private String badgeImage;	//배지 이미지
	private String badgeDesc;	//배지 설명
	private String badgeAutomationType; //배지 자동적용방식
	private Integer badgeAutomationCount; //배지 자동적용 기준 숫자

	private String useYn;	//사용 여부
	private String uploadFilename;	//배지 이미지 URL
	
}
