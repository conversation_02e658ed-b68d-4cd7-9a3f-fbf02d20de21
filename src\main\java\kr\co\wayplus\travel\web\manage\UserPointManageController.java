package kr.co.wayplus.travel.web.manage;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.websocket.server.PathParam;
import kr.co.wayplus.travel.model.*;
import kr.co.wayplus.travel.service.user.UserPointService;

import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Controller
@RequestMapping("/manage/point")
public class UserPointManageController {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final UserPointService userPointService;

    public UserPointManageController(UserPointService userPointService) {
        this.userPointService = userPointService;
    }

    @GetMapping("/setting")
    public ModelAndView pointSetting(@RequestParam(value="startDate",defaultValue="") String startDate,
                                  @RequestParam(value="endDate",defaultValue="") String endDate,
                                  @RequestParam(value="page",defaultValue="1") int page,
                                  @RequestParam(value="pageSize",defaultValue="10") int pageSize,
                                  @RequestParam(value="useType",defaultValue="") String useType){
        ModelAndView mav = new ModelAndView();

        HashMap<String, Object> param = new HashMap<>();
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("useType", useType);

        int totalCount = userPointService.getUserPointSettingListCount(param);
        PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
        param.put("itemStartPosition", paging.getItemStartPosition());
        param.put("pagePerSize", paging.getPagePerSize());
        param.put("upperCode", "pointType");
        mav.addObject("p", param);
        mav.addObject("paging", paging);
        mav.addObject("pointSetList", userPointService.getUserPointSettingList(param));
        mav.setViewName("manage/sub/user/point/setting");
        return mav;
    }

    @GetMapping("/setting/{id}")
    public ModelAndView pointSettingDetail(@PathVariable(value="id") String id){
        ModelAndView mav = new ModelAndView();
        mav.addObject("point", userPointService.getUserPointSettingById(id));
        mav.addObject("pointCode",  userPointService.getCodeItemList("pointType"));
        mav.setViewName("manage/sub/user/point/setting-form");
        return mav;
    }

    @GetMapping("/setting/list")
    @ResponseBody
    public HashMap<String, Object> pointSettingList(
                                  @RequestParam(value="startDate",defaultValue="") String startDate,
                                  @RequestParam(value="endDate",defaultValue="") String endDate,
                                  @RequestParam(value="useType",defaultValue="") String useType){
        HashMap<String, Object> param = new HashMap<>();
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("useType", useType);
        //param.put("useYn", "Y");

        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("result", "success");
        resultMap.put("data", userPointService.getUserPointSettingList(param));

        return resultMap;
    }

    @PostMapping("/setting/insert")
    @ResponseBody
    public HashMap<String, Object> pointSettingInsert(@ModelAttribute UserPointSet pointSet, BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            pointSet.setCreateId(user.getUserEmail());
            if(!pointSet.getExpiredDay().isEmpty() && !pointSet.getExpiredDayType().isEmpty()) {
                pointSet.setExpiredDay(pointSet.getExpiredDay() + pointSet.getExpiredDayType());
            }else{
                pointSet.setExpiredDay(null);
            }
            if(!pointSet.getDuplicatePeriod().isEmpty() && !pointSet.getDuplicateDayType().isEmpty()) {
                pointSet.setDuplicatePeriod(pointSet.getDuplicatePeriod() + pointSet.getDuplicateDayType());
            }else{
                pointSet.setDuplicatePeriod(null);
            }
            if(pointSet.getStartDate() != null && pointSet.getStartDate().isEmpty()) pointSet.setStartDate(null);
            if(pointSet.getExpireDate() != null && pointSet.getExpireDate().isEmpty()) pointSet.setExpireDate(null);
            if(pointSet.getUseYn() == null) pointSet.setUseYn("N");
            userPointService.insertUserPointSetting(pointSet);

            resultMap.put("result", "success");
            resultMap.put("message", "저장됐습니다.");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }
        return resultMap;
    }

    @PutMapping("/setting/update")
    @ResponseBody
    public HashMap<String, Object> pointSettingUpdate(@ModelAttribute UserPointSet pointSet, BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            pointSet.setLastUpdateId(user.getUserEmail());
            if(!pointSet.getExpiredDay().isEmpty() && !pointSet.getExpiredDayType().isEmpty()) {
                pointSet.setExpiredDay(pointSet.getExpiredDay() + pointSet.getExpiredDayType());
            }else{
                pointSet.setExpiredDay(null);
            }
            if(pointSet.getStartDate() != null && pointSet.getStartDate().isEmpty()) pointSet.setStartDate(null);
            if(pointSet.getExpireDate() != null && pointSet.getExpireDate().isEmpty()) pointSet.setExpireDate(null);
            if(pointSet.getUseYn() == null) pointSet.setUseYn("N");
            userPointService.updateUserPointSetting(pointSet);

            resultMap.put("result", "success");
            resultMap.put("message", "저장됐습니다.");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }
        return resultMap;
    }

    @PutMapping("/setting/remove")
    @ResponseBody
    public HashMap<String, Object> pointSettingRemove(@ModelAttribute UserPointSet pointSet, BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            pointSet.setDeleteId(user.getUserEmail());
            userPointService.updateUserPointSettingDeleteYn(pointSet);

            resultMap.put("result", "success");
            resultMap.put("message", "삭제됐습니다.");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }
        return resultMap;
    }

    @GetMapping("/record/list")
    public ModelAndView pointRecordList(@RequestParam(value="accruedType",defaultValue="") String accruedType,
                                  @RequestParam(value="searchType",defaultValue="") String searchType,
                                  @RequestParam(value="startDate",defaultValue="") String startDate,
                                  @RequestParam(value="endDate",defaultValue="") String endDate,
                                  @RequestParam(value="page",defaultValue="1") int page,
                                  @RequestParam(value="pageSize",defaultValue="10") int pageSize,
                                  @RequestParam(value="searchKey",defaultValue="") String searchKey){
        ModelAndView mav = new ModelAndView();

        HashMap<String, Object> param = new HashMap<>();
        param.put("accruedType", accruedType);
        param.put("searchType", searchType);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("searchKey", searchKey);

        int totalCount = userPointService.getUserPointRecordListCount(param);
        PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
        param.put("itemStartPosition", paging.getItemStartPosition());
        param.put("pagePerSize", paging.getPagePerSize());
        mav.addObject("p", param);
        mav.addObject("paging", paging);
        mav.addObject("pointList", userPointService.getUserPointRecordList(param));
        mav.setViewName("manage/sub/user/point/record");
        return mav;
    }

    @GetMapping("/ajax-record/list")
    @ResponseBody
    public HashMap<String, Object> ajaxPointRecordList(@RequestParam(value="start", defaultValue="0") int start,
                                                        @RequestParam(value="length", defaultValue="10") int length,
                                                        @RequestParam(value="userEmail",defaultValue="") String userEmail){
        HashMap<String, Object> returnMap = new HashMap<>();

        try {
            HashMap<String, Object> param = new HashMap<>();
            param.put("searchType", "userEmail");
            param.put("searchKey", userEmail);

            if(length >= 0) {
                param.put("itemStartPosition", start);
                param.put("pagePerSize", length);
            }

            param.put("sort", "lastUpdateDate");
            param.put("sortOrder", "desc");

            int totalCount = userPointService.getUserPointRecordListCount(param);
            returnMap.put("recordsTotal", totalCount);
            returnMap.put("recordsFiltered", totalCount);

            returnMap.put("data", userPointService.getUserPointRecordList(param));
            returnMap.put("result", "success");
        } catch (Exception e) {
            returnMap.put("result", "error");
            returnMap.put("message", e.getMessage());
            logger.error(e.getMessage());
            e.printStackTrace();
        }

        return returnMap;
    }

    @GetMapping("/record/board")
    public ModelAndView pointRecordBoard(@RequestParam(value="page",defaultValue="1") int page,
                                  @RequestParam(value="pageSize",defaultValue="10") int pageSize,
                                  @RequestParam(value="searchKey",defaultValue="") String searchKey){
        ModelAndView mav = new ModelAndView();

        HashMap<String, Object> param = new HashMap<>();
        param.put("searchKey", searchKey);

        int totalCount = userPointService.getUserPointRecordGroupListCount(param);
        PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
        param.put("itemStartPosition", paging.getItemStartPosition());
        param.put("pagePerSize", paging.getPagePerSize());
        mav.addObject("p", param);
        mav.addObject("paging", paging);
        mav.addObject("pointList", userPointService.getUserPointRecordGroupList(param));
        mav.setViewName("manage/sub/user/point/record-group");
        return mav;
    }


    @GetMapping("/record/form")
    public ModelAndView pointRecordForm( @Param(value="userEmail") String userEmail ){
        ModelAndView mav = new ModelAndView();

        if( userEmail != null ) {
        	mav.addObject("userEmail", userEmail);

        }



        mav.addObject("pointCode",  userPointService.getCodeItemList("pointType"));
        mav.setViewName("manage/sub/user/point/record-form");
        return mav;
    }

    @PostMapping("/record/create")
    @ResponseBody
    public HashMap<String, Object> pointRecordCreate(@ModelAttribute UserPointAccrued pointAccrued, BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            pointAccrued.setCreateId(user.getUserEmail());
            userPointService.createUserPoint(pointAccrued);
            resultMap.put("result", "success");
            resultMap.put("message", "저장됐습니다.");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }
        return resultMap;
    }

    @PutMapping("/record/cancel")
    @ResponseBody
    public HashMap<String, Object> pointRecordCancel(@RequestParam(value="id", defaultValue="0") int id,
                                                     @RequestParam(value="accruedId", defaultValue="0") int accruedId,
                                                     @RequestParam(value="cancelCode", defaultValue="0") String code){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            UserPointAccrued pointAccrued = new UserPointAccrued();
            pointAccrued.setCancelId(user.getUserEmail());
            pointAccrued.setCancelCode(code);
            pointAccrued.setId(accruedId);
            userPointService.cancelUserPoint(id, pointAccrued);

            resultMap.put("result", "success");
            resultMap.put("message", "저장됐습니다.");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }
        return resultMap;
    }

    @GetMapping("/record/user")
    public ModelAndView pointRecordUser(@RequestParam(value="page",defaultValue="1") int page,
                                        @RequestParam(value="pageSize",defaultValue="10") int pageSize,
                                        @RequestParam(value="userEmail",defaultValue="") String userEmail,
                                        @RequestParam(value="userName",defaultValue="") String userName){
        ModelAndView mav = new ModelAndView();

        if(!userEmail.isEmpty()) {
            HashMap<String, Object> param = new HashMap<>();
            param.put("userEmail", userEmail);
            param.put("userName", userName);

            int totalCount = userPointService.getUserPointRecordListCount(param);
            PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
            param.put("itemStartPosition", paging.getItemStartPosition());
            param.put("pagePerSize", paging.getPagePerSize());
            mav.addObject("p", param);
            mav.addObject("paging", paging);
            mav.addObject("point", userPointService.getUserPointRecordSummary(param));
            mav.addObject("pointList", userPointService.getUserPointRecordList(param));
        }
        mav.setViewName("manage/sub/user/point/record-user");
        return mav;
    }

    @GetMapping("/accrued")
    public ModelAndView pointAccruedList(@RequestParam(value="searchType",defaultValue="") String searchType,
                                  @RequestParam(value="startDate",defaultValue="") String startDate,
                                  @RequestParam(value="endDate",defaultValue="") String endDate,
                                  @RequestParam(value="page",defaultValue="1") int page,
                                  @RequestParam(value="pageSize",defaultValue="10") int pageSize,
                                  @RequestParam(value="searchKey",defaultValue="") String searchKey){
        ModelAndView mav = new ModelAndView();

        HashMap<String, Object> param = new HashMap<>();
        param.put("searchType", searchType);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("searchKey", searchKey);

        int totalCount = userPointService.getUserPointAccruedListCount(param);
        PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
        param.put("itemStartPosition", paging.getItemStartPosition());
        param.put("pagePerSize", paging.getPagePerSize());
        mav.addObject("p", param);
        mav.addObject("paging", paging);
        mav.addObject("pointList", userPointService.getUserPointAccruedList(param));
        mav.setViewName("manage/sub/user/point/accrued");
        return mav;
    }

    @GetMapping("/used")
    public ModelAndView pointUsedList(@RequestParam(value="searchType",defaultValue="") String searchType,
                                  @RequestParam(value="startDate",defaultValue="") String startDate,
                                  @RequestParam(value="endDate",defaultValue="") String endDate,
                                  @RequestParam(value="page",defaultValue="1") int page,
                                  @RequestParam(value="pageSize",defaultValue="10") int pageSize,
                                  @RequestParam(value="searchKey",defaultValue="") String searchKey){
        ModelAndView mav = new ModelAndView();

        HashMap<String, Object> param = new HashMap<>();
        param.put("searchType", searchType);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("searchKey", searchKey);

        int totalCount = userPointService.getUserPointUsedListCount(param);
        PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
        param.put("itemStartPosition", paging.getItemStartPosition());
        param.put("pagePerSize", paging.getPagePerSize());
        mav.addObject("p", param);
        mav.addObject("paging", paging);
        mav.addObject("pointList", userPointService.getUserPointUsedList(param));
        mav.setViewName("manage/sub/user/point/used");
        return mav;
    }

    @GetMapping("/exchange/list")
    public ModelAndView pointExchangeList(@RequestParam(value="accruedType",defaultValue="ALL") String accruedType,
                                  @RequestParam(value="searchType",defaultValue="") String searchType,
                                  @RequestParam(value="startDate",defaultValue="") String startDate,
                                  @RequestParam(value="endDate",defaultValue="") String endDate,
                                  @RequestParam(value="page",defaultValue="1") int page,
                                  @RequestParam(value="pageSize",defaultValue="10") int pageSize,
                                  @RequestParam(value="searchKey",defaultValue="") String searchKey){
        ModelAndView mav = new ModelAndView();

        HashMap<String, Object> param = new HashMap<>();
        param.put("accruedType", accruedType);
        param.put("searchType", searchType);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("searchKey", searchKey);

        int totalCount = userPointService.getCountUserPointExchangeList(param);
        PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
        param.put("itemStartPosition", paging.getItemStartPosition());
        param.put("pagePerSize", paging.getPagePerSize());
        mav.addObject("p", param);
        mav.addObject("paging", paging);
        mav.addObject("exchangeList", userPointService.getUserPointExchangeList(param));
        mav.setViewName("manage/sub/user/point/exchange");
        return mav;
    }

    @PostMapping("/exchange/confirm")
    @ResponseBody
    public HashMap<String, Object> pointExchangeConfirm(@ModelAttribute UserPointExchange userPointExchange, BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            userPointExchange.setLastUpdateId(user.getUserEmail());
            userPointExchange.setPointExchangeConfirmYn("Y");
            userPointService.cancelConfirmUserPointExchange(userPointExchange);

            resultMap.put("result", "success");
            resultMap.put("message", "처리됐습니다.");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }
        return resultMap;
    }

    @PutMapping("/exchange/cancel")
    @ResponseBody
    public HashMap<String, Object> pointExchangeCancel(@ModelAttribute UserPointExchange userPointExchange, BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            userPointExchange.setCreateId(user.getUserEmail());
            userPointExchange.setLastUpdateId(user.getUserEmail());
            userPointExchange.setPointExchangeConfirmYn("N");

            userPointService.cancelConfirmUserPointExchange(userPointExchange);

            resultMap.put("result", "success");
            resultMap.put("message", "처리됐습니다.");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }
        return resultMap;
    }

    @GetMapping("/list")
    public ModelAndView point_list(){
        ModelAndView mav = new ModelAndView();
        mav.setViewName("manage/sub/user/point/list");
        return mav;
    }

    @GetMapping("/point-detail")
    public ModelAndView point_detail( @Param(value="userEmail") String userEmail ){
        ModelAndView mav = new ModelAndView();

        mav.addObject("userEmail", userEmail);
        mav.setViewName("manage/sub/user/point/point-detail");
        return mav;
    }

    @PostMapping("/user/list")
    @ResponseBody
    public HashMap<String, Object> pointUserList(
    		                      HttpServletRequest request,
    		                      //@RequestParam(value="page",defaultValue="1") int page,
                                  //@RequestParam(value="pageSize",defaultValue="10") int pageSize,
                                  @RequestParam(value="start", defaultValue="0") int start,
                                  @RequestParam(value="length", defaultValue="10") int length,
                                  @RequestParam(value="searchKey",defaultValue="") String searchKey){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		HashMap<String, Object> param = new HashMap<>();
            param.put("searchKey", searchKey);

            //param.put("sort", "menuName");
    		//param.put("sortOrder", "asc");

            if(length > 0) {
				param.put("itemStartPosition", start);
				param.put("pagePerSize", length);
    		}

			int totalCount = userPointService.getUserPointRecordGroupListCount(param);

    		retrunMap.put("recordsTotal", totalCount);
    		retrunMap.put("recordsFiltered", totalCount);
    		retrunMap.put("data", userPointService.getUserPointRecordGroupList(param));

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

        return retrunMap;
    }

	@PostMapping("/record/list")
	@ResponseBody
	public HashMap<String, Object> pointRecordList(HttpServletRequest request,
			@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length,
			@RequestParam(value = "userEmail",defaultValue="") String userEmail,
			@RequestParam(value = "searchKey",defaultValue="") String searchKey,
			@RequestParam(value = "accruedType", defaultValue = "") String accruedType,
			@RequestParam(value = "searchType", defaultValue = "") String searchType,
			@RequestParam(value = "startDate", defaultValue = "") String startDate,
			@RequestParam(value = "endDate", defaultValue = "") String endDate,
			@RequestParam(value = "searchKey", defaultValue = "") String searchKe) {
		HashMap<String, Object> retrunMap = new HashMap<>();

		try {
			HashMap<String, Object> param = new HashMap<>();
			param.put("accruedType", accruedType);
			param.put("searchType", searchType);
			param.put("startDate", startDate);
			param.put("endDate", endDate);
			param.put("searchKey", searchKey);
			param.put("userEmail", userEmail);

			if(length > 0) {
				param.put("itemStartPosition", start);
				param.put("pagePerSize", length);
    		}


			int totalCount = userPointService.getUserPointRecordListCount(param);
			//PagingDTO paging = new PagingDTO(totalCount, start, 0, length);
			//param.put("itemStartPosition", paging.getItemStartPosition());
			//param.put("pagePerSize", paging.getPagePerSize());

			retrunMap.put("recordsTotal", totalCount);
			retrunMap.put("recordsFiltered", totalCount);
			retrunMap.put("data", userPointService.getUserPointRecordList(param));

			retrunMap.put("result", "success");
			retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

		return retrunMap;
	}
}
