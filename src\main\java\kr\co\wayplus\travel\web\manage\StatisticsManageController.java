package kr.co.wayplus.travel.web.manage;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.builder.ExcelDataBuilder;
import kr.co.wayplus.travel.model.excel.ExcelData;
import kr.co.wayplus.travel.model.excel.ExcelImgData;
import kr.co.wayplus.travel.model.excel.ExcelMetadata;
import kr.co.wayplus.travel.model.excel.HeaderInfo;
import kr.co.wayplus.travel.service.manage.StatisticsManageService;
import kr.co.wayplus.travel.util.ExcelUtil;

@Controller
@RequestMapping("/manage/statistics")
public class StatisticsManageController extends BaseController {
	private final Logger logger = LoggerFactory.getLogger(getClass());

	@Value("${upload.file.path}")
	private String imageUploadPath;


	@Value("${upload.file.max-size}")
	int maxFileSize;

	private StatisticsManageService service;

	//private final MenuManageService menuManageService;

	@Autowired
	private StatisticsManageController(
			StatisticsManageService svc
			//, MenuManageService menuManageService
		) {
		this.service = svc;
		//this.menuManageService = menuManageService;
	}

	/*################################################statistics################################################*/
	@GetMapping("/connect")
	public ModelAndView statistics_connect(
		@RequestParam(value="page", defaultValue="1") int page,
		@RequestParam(value="pageSize", defaultValue="10") int pageSize){
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> paramMap = new HashMap<>();
		mav.addObject("p", paramMap);
		mav.setViewName("manage/sub/statistics/connect");
		return mav;
	}

	@PostMapping("/connect")
	@ResponseBody
	public Map<String, Object>	statistics_connect_ajax(
			@RequestParam Map<String, Object> paramMap){
		Map<String, Object> resultMap = new HashMap<>();
		try{
			resultMap.put("p", paramMap);

			resultMap.put("list", service.selectListStatisticConnectDate(paramMap));
			resultMap.put("info", service.selectListStatisticConnectInfo(paramMap));
			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		}catch (Exception e){
			logger.error(e.getMessage());
			e.printStackTrace();
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}


	@RequestMapping("/connect/excel")
	public ResponseEntity<byte[]> statistics_connect_Download(
			@RequestParam Map<String, Object> paramMap,
			@RequestBody(required = false) Map<String, Object> requestBody) {

		try {
//			// 0. service를 통한 데이터 로드

			Map<String, Object> Info = service.selectListStatisticConnectInfo(paramMap);

			Map<String, String> columnMapping1 = new LinkedHashMap<String, String>();
			List<HeaderInfo> headerRow1 = new ArrayList<HeaderInfo>();
			headerRow1.add( new HeaderInfo("조회기간", 1, 1) );
			headerRow1.add( new HeaderInfo("총 접속자 수", 1, 1) );
			headerRow1.add( new HeaderInfo("평균 접속자 수", 1, 1) );
			headerRow1.add( new HeaderInfo("오늘 접속자 수", 1, 1) );
			headerRow1.add( new HeaderInfo("어제 접속자 수", 1, 1) );

//			// 1. Excel 메타데이터 설정
//			ExcelMetadata metadata = createDepartmentMetadata();
			ExcelMetadata metadata1 = ExcelMetadata.builder()
	            .sheetName("접속자통계")
	            .addHeaderRow( Arrays.asList(new HeaderInfo( "접속자통계", 1, 1) ) )
	            .addHeaderRow( headerRow1 )
	            .addColumnMapping( columnMapping1 )
	            .startRow(0)
	            .startCol(0)
	            .build();

			ExcelMetadata metadata2 = ExcelMetadata.builder()
				.addHeaderRow( Arrays.asList(new HeaderInfo( "기간별 통계", 1, 1) ) )
				.startRow(5).startCol(0).build();
			ExcelMetadata metadata3 = ExcelMetadata.builder()
				.addHeaderRow( Arrays.asList(new HeaderInfo( "요일별 통계", 1, 1) ) )
				.startRow(6).startCol(9).build();

			// 2. 데이터 생성
			List<ExcelData> excelDataList1 = new ArrayList<ExcelData>();
			List<ExcelData> excelDataList2 = new ArrayList<ExcelData>();

			columnMapping1.put("title", "접속자통계");
			columnMapping1.put("sum", "총 접속자 수");
			columnMapping1.put("avg", "평균 접속자 수");
			columnMapping1.put("today", "오늘 접속자 수");
			columnMapping1.put("yesterday", "어제 접속자 수");
			ExcelData _edata = new ExcelData();

			_edata.addValue("title", paramMap.get("startDate").toString() +" ~ "+ paramMap.get("endDate").toString() );
			_edata.addValue("sum", Math.round(Float.parseFloat(Info.get("to_sum_cnt").toString())));
			_edata.addValue("avg", Math.round(Float.parseFloat(Info.get("to_avg_cnt").toString())));
			_edata.addValue("today", Math.round(Float.parseFloat(Info.get("to_day_count").toString())));
			_edata.addValue("yesterday", Math.round(Float.parseFloat(Info.get("to_yesterday_count").toString())));
			excelDataList1.add(_edata);


			// 3. 이미지 데이터 추출
			String base64Image1 = null;
			ExcelImgData img1 = new ExcelImgData();
	        if (requestBody != null && requestBody.containsKey("chartImage1")) {
	            base64Image1 = (String) requestBody.get("chartImage1");
	            img1.setBase64Image(base64Image1);
	            img1.setSize(0, 6, 6, 21);
	        }
			String base64Image2 = null;
			ExcelImgData img2 = new ExcelImgData();
	        if (requestBody != null && requestBody.containsKey("chartImage2")) {
	            base64Image2 = (String) requestBody.get("chartImage2");
	            img2.setBase64Image(base64Image2);
	            img2.setSize(9, 16, 7, 20);
	        }

			//size 추가용
			List<ExcelData> excelDataList3 = new ArrayList<ExcelData>();
			ExcelImgData img3 = new ExcelImgData();
			img3.setBase64Image(base64Image1);
			img3.setSize(0, 0, 0, 0);

	        List<ExcelMetadata> _metadata = Arrays.asList(metadata1, metadata2, metadata3);
	        List<List<ExcelData>> _dataList = Arrays.asList(excelDataList1, excelDataList2, excelDataList3);
	        List<ExcelImgData> _base64Image = Arrays.asList(img1, img2, img3);

//			// 4. Excel 생성
			byte[] excelFile = ExcelUtil.generateExcel(_metadata, _dataList, _base64Image );

			// 5. 응답 헤더 설정
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
			headers.setContentDisposition(ContentDisposition.builder("attachment")
					.filename("접속자 통계.xlsx", StandardCharsets.UTF_8).build());

			return new ResponseEntity<>(excelFile, headers, HttpStatus.OK);
		} catch (Exception e) {
			logger.error("Failed to generate excel file", e);
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}


	@GetMapping("/demography")
	public ModelAndView statistics_demography(
		@RequestParam(value="page", defaultValue="1") int page,
		@RequestParam(value="pageSize", defaultValue="10") int pageSize){
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> paramMap = new HashMap<>();
		mav.addObject("p", paramMap);
		mav.setViewName("manage/sub/statistics/demography");
		return mav;
	}

	@PostMapping("/demography")
	@ResponseBody
	public Map<String, Object>	statistics_demography_ajax(
			@RequestParam Map<String, Object> paramMap){
		Map<String, Object> resultMap = new HashMap<>();
		try{
			resultMap.put("list1", service.selectListStatisticDemographyGender(null));
			resultMap.put("list2", service.selectListStatisticDemographyAge(null));
			resultMap.put("list3", service.selectListStatisticDemographyRegion(null));

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		}catch (Exception e){
			logger.error(e.getMessage());
			e.printStackTrace();
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}


	@RequestMapping("/demography/excel")
	public ResponseEntity<byte[]> statistics_demography_Download(
			@RequestParam Map<String, Object> paramMap,
			@RequestBody(required = false) Map<String, Object> requestBody) {

		try {
//			// 0. service를 통한 데이터 로드

			ArrayList<Map<String, Object>> list1 = service.selectListStatisticDemographyGender(null);
			ArrayList<Map<String, Object>> list2 = service.selectListStatisticDemographyAge(null);
			ArrayList<Map<String, Object>> list3 = service.selectListStatisticDemographyRegion(null);

			Map<String, String> columnMapping1 = new LinkedHashMap<String, String>();
			List<HeaderInfo> headerRow1 = new ArrayList<HeaderInfo>();

			//ExcelData edata = ExcelDataBuilder.create().build();
			ExcelData edata1 = new ExcelData();

			headerRow1.add( new HeaderInfo("구분", 1, 1) );
			headerRow1.add( new HeaderInfo("명수", 1, 1) );

			columnMapping1.put("title", "인구학통계");
			columnMapping1.put("sum", "합계");

			edata1.addValue("title", "합계");
			edata1.addValue("sum", 0);

//			// 1. Excel 메타데이터 설정
//			ExcelMetadata metadata = createDepartmentMetadata();
			ExcelMetadata metadata1 = ExcelMetadata.builder()
				.addHeaderRow( Arrays.asList(new HeaderInfo( "인구학 통계", 1, 1) ) )
	            .addHeaderRow( Arrays.asList(new HeaderInfo( "성별통계", 1, 1) ) )
	            .addHeaderRow( headerRow1 )
	            .addColumnMapping( columnMapping1 )
	            .startRow(0)
	            .startCol(0)
	            .build();

			// 2. 데이터 생성
			List<ExcelData> excelDataList1 = new ArrayList<ExcelData>();
			excelDataList1.add(edata1);

			int sum1 = 0;
			for (Map<String, Object> map : list1) {
				//metadata.builder().addHeaderRow( Arrays.asList(new HeaderInfo(map.get("user_geder").toString(), 1, 1) ) );
				columnMapping1.put(map.get("user_geder").toString(), map.get("name").toString());
				ExcelData _edata = new ExcelData();

				_edata.addValue(map.get("user_geder").toString()+"_title", map.get("name").toString());
				_edata.addValue(map.get("user_geder").toString()+"_count", map.get("count"));
				excelDataList1.add(_edata);

				sum1 += Integer.valueOf(map.get("count").toString() );
			}

			edata1.addValue("sum", sum1); //값 보정


			// 3. 이미지 데이터 추출
			String base64Image1 = null;
			ExcelImgData img1 = new ExcelImgData();
	        if (requestBody != null && requestBody.containsKey("chartImage1")) {
	            base64Image1 = (String) requestBody.get("chartImage1");
	            img1.setBase64Image(base64Image1);
	            img1.setSize(3, 7, 0, 6);
	        }



	        //------------------------------------------------------------------------------------
	        Map<String, String> columnMapping2 = new LinkedHashMap<String, String>();
			List<HeaderInfo> headerRow2 = new ArrayList<HeaderInfo>();

			//ExcelData edata = ExcelDataBuilder.create().build();
			ExcelData edata2 = new ExcelData();

			headerRow2.add( new HeaderInfo("구분", 1, 1) );
			headerRow2.add( new HeaderInfo("합계", 1, 1) );
			columnMapping2.put("title", "구분");
			columnMapping2.put("sum", "합계");
			edata2.addValue("title", "개수");
			edata2.addValue("sum", 0);

			int sum2 = 0;
//			for (Map<String, Object> map : list2) {
//				headerRow2.add( new HeaderInfo(map.get("title").toString(), 1, 1) );
//				columnMapping2.put(map.get("id").toString(), map.get("title").toString());
//
//				edata2.addValue(map.get("id").toString(), map.get("count"));
//
//			}
			{
				Map<String, Object> map = list2.get(0);
				sum2 += Integer.valueOf(map.get("agSum").toString() );

				headerRow2.add( new HeaderInfo("10대이하", 1, 1) );
				headerRow2.add( new HeaderInfo("20대", 1, 1) );
				headerRow2.add( new HeaderInfo("30대", 1, 1) );
				headerRow2.add( new HeaderInfo("40대", 1, 1) );
				headerRow2.add( new HeaderInfo("50대", 1, 1) );
				headerRow2.add( new HeaderInfo("60대", 1, 1) );
				headerRow2.add( new HeaderInfo("70대", 1, 1) );
				headerRow2.add( new HeaderInfo("80대", 1, 1) );
				headerRow2.add( new HeaderInfo("90대", 1, 1) );
				headerRow2.add( new HeaderInfo("미상", 1, 1) );

				columnMapping2.put("ag01", "10대이하");
				columnMapping2.put("ag02", "20대");
				columnMapping2.put("ag03", "30대");
				columnMapping2.put("ag04", "40대");
				columnMapping2.put("ag05", "50대");
				columnMapping2.put("ag06", "60대");
				columnMapping2.put("ag07", "70대");
				columnMapping2.put("ag08", "80대");
				columnMapping2.put("ag09", "90대");
				columnMapping2.put("ag99", "미상");

				edata2.addValue( "ag01", map.get("ag01") );
				edata2.addValue( "ag02", map.get("ag02") );
				edata2.addValue( "ag03", map.get("ag03") );
				edata2.addValue( "ag04", map.get("ag04") );
				edata2.addValue( "ag05", map.get("ag05") );
				edata2.addValue( "ag06", map.get("ag06") );
				edata2.addValue( "ag07", map.get("ag07") );
				edata2.addValue( "ag08", map.get("ag08") );
				edata2.addValue( "ag09", map.get("ag09") );
				edata2.addValue( "ag99", map.get("ag99") );

				edata2.addValue("sum", sum2); //값 보정
			}

//			// 1. Excel 메타데이터 설정
//			ExcelMetadata metadata = createDepartmentMetadata();
			ExcelMetadata metadata2 = ExcelMetadata.builder()
				.addHeaderRow( Arrays.asList(new HeaderInfo( "연령별통계", 1, 1) ) )
	            .addHeaderRow( headerRow2 )
	            .addColumnMapping( columnMapping2 )
	            .startRow(8)
	            .startCol(0)
	            .build();
//
//			// 2. 데이터 생성
			List<ExcelData> excelDataList2 = new ArrayList<ExcelData>();

			excelDataList2.add(edata2);

			// 3. 이미지 데이터 추출
	        String base64Image2 = null;
	        ExcelImgData img2 = new ExcelImgData();
	        if (requestBody != null && requestBody.containsKey("chartImage2")) {
	            base64Image2 = (String) requestBody.get("chartImage2");
	            img2.setBase64Image(base64Image2);
	            img2.setSize(0, 12, 12, 8);
	        }

	        //------------------------------------------------------------------------------------
	        Map<String, String> columnMapping3 = new LinkedHashMap<String, String>();
			List<HeaderInfo> headerRow3 = new ArrayList<HeaderInfo>();

			//ExcelData edata = ExcelDataBuilder.create().build();
			ExcelData edata3 = new ExcelData();

			headerRow3.add( new HeaderInfo("구분", 1, 1) );
			headerRow3.add( new HeaderInfo("합계", 1, 1) );
			columnMapping3.put("name", "구분");
			columnMapping3.put("sum", "합계");
			edata3.addValue("name", "개수");
			edata3.addValue("sum", 0);

			int sum3 = 0;
			for (Map<String, Object> map : list3) {
				headerRow3.add( new HeaderInfo(map.get("name").toString(), 1, 1) );
				columnMapping3.put(map.get("name").toString(), map.get("name").toString());

				edata3.addValue(map.get("name").toString(), map.get("count"));

				sum3 += Integer.valueOf(map.get("count").toString() );
			}

			edata3.addValue("sum", sum3); //값 보정

//			// 1. Excel 메타데이터 설정
//			ExcelMetadata metadata = createDepartmentMetadata();
			ExcelMetadata metadata3 = ExcelMetadata.builder()
				.addHeaderRow( Arrays.asList(new HeaderInfo( "거주지별통계", 1, 1) ) )
	            .addHeaderRow( headerRow3 )
	            .addColumnMapping( columnMapping3 )
	            .startRow(20)
	            .startCol(0)
	            .build();
//
//			// 2. 데이터 생성
			List<ExcelData> excelDataList3 = new ArrayList<ExcelData>();

			excelDataList3.add(edata3);

			// 3. 이미지 데이터 추출
			String base64Image3 = null;
	        ExcelImgData img3 = new ExcelImgData();
	        if (requestBody != null && requestBody.containsKey("chartImage3")) {
	            base64Image3 = (String) requestBody.get("chartImage3");
	            img3.setBase64Image(base64Image3);
	            img3.setSize(0, 20, 24, 8);
	        }


	        List<ExcelMetadata> _metadata = Arrays.asList(metadata1, metadata2, metadata3);
	        List<List<ExcelData>> _dataList = Arrays.asList(excelDataList1, excelDataList2, excelDataList3);
	        List<ExcelImgData> _base64Image = Arrays.asList(img1, img2, img3);

//			// 4. Excel 생성
			byte[] excelFile = ExcelUtil.generateExcel(_metadata, _dataList, _base64Image );

			// 5. 응답 헤더 설정
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
			headers.setContentDisposition(ContentDisposition.builder("attachment")
					.filename("인구학 통계.xlsx", StandardCharsets.UTF_8).build());

			return new ResponseEntity<>(excelFile, headers, HttpStatus.OK);
		} catch (Exception e) {
			logger.error("Failed to generate excel file", e);
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping("/islandlife")
	public ModelAndView statistics_islandlife(
			@RequestParam(value="page", defaultValue="1") int page,
			@RequestParam(value="pageSize", defaultValue="10") int pageSize){
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> paramMap = new HashMap<>();
		mav.addObject("p", paramMap);
		mav.setViewName("manage/sub/statistics/islandlife");
		return mav;
	}
	@PostMapping("/islandlife")
	@ResponseBody
	public Map<String, Object>	statistics_islandlife_ajax(
			@RequestParam Map<String, Object> paramMap){
		Map<String, Object> resultMap = new HashMap<>();
		try{
			resultMap.put("list", service.selectListIslandlife(null));

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		}catch (Exception e){
			logger.error(e.getMessage());
			e.printStackTrace();
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}

	@RequestMapping("/islandlife/excel")
	public ResponseEntity<byte[]> statistics_islandlife_Download(
			@RequestParam Map<String, Object> paramMap,
			@RequestBody(required = false) Map<String, Object> requestBody) {

		try {
//			// 0. service를 통한 데이터 로드
			ArrayList<Map<String, Object>> data = service.selectListIslandlife(null);

			Map<String, String> columnMapping = new LinkedHashMap<String, String>();
			List<HeaderInfo> headerRow = new ArrayList<HeaderInfo>();
			List<ExcelData> dataList = new ArrayList<>();

			//ExcelData edata = ExcelDataBuilder.create().build();
			ExcelData edata = new ExcelData();

			headerRow.add( new HeaderInfo("섬살이유형", 1, 1) );
			headerRow.add( new HeaderInfo("합계", 1, 1) );
			columnMapping.put("title", "섬살이유형");
			columnMapping.put("sum", "합계");
			edata.addValue("title", "개수");
			edata.addValue("sum", 0);

			int sum = 0;
			for (Map<String, Object> map : data) {
				headerRow.add( new HeaderInfo(map.get("title").toString(), 1, 1) );
				columnMapping.put(map.get("id").toString(), map.get("title").toString());

				edata.addValue(map.get("id").toString(), map.get("count"));

				sum += Integer.valueOf(map.get("count").toString() );
			}

			edata.addValue("sum", sum); //값 보정

//			// 1. Excel 메타데이터 설정
//			ExcelMetadata metadata = createDepartmentMetadata();
			ExcelMetadata metadata = ExcelMetadata.builder()
				.addHeaderRow( Arrays.asList(new HeaderInfo( "섬살이유형 통계", 1, 1) ) )
	            .addHeaderRow( headerRow )
	            .addColumnMapping( columnMapping )
	            .startRow(1)
	            .startCol(0)
	            .build();
//
//			// 2. 데이터 생성
			List<ExcelData> excelDataList = new ArrayList<ExcelData>();

			excelDataList.add(edata);

			// 3. 이미지 데이터 추출
			String base64Image = null;
	        if (requestBody != null && requestBody.containsKey("chartImage")) {
	            base64Image = (String) requestBody.get("chartImage");
	        }
//
//			// 4. Excel 생성
			byte[] excelFile = ExcelUtil.generateExcel(metadata, excelDataList, base64Image);

			// 5. 응답 헤더 설정
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
			headers.setContentDisposition(ContentDisposition.builder("attachment")
					.filename("섬살이 유형통계.xlsx", StandardCharsets.UTF_8).build());

			return new ResponseEntity<>(excelFile, headers, HttpStatus.OK);
		} catch (Exception e) {
			logger.error("Failed to generate excel file", e);
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping("/product")
	public ModelAndView statistics_product(
		@RequestParam(value="page", defaultValue="1") int page,
		@RequestParam(value="pageSize", defaultValue="10") int pageSize){
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> paramMap = new HashMap<>();
		mav.addObject("p", paramMap);
		mav.setViewName("manage/sub/statistics/product");
		return mav;
	}
	@PostMapping("/product")
	@ResponseBody
	public Map<String, Object>	statistics_product_ajax(
			@RequestParam Map<String, Object> paramMap){
		Map<String, Object> resultMap = new HashMap<>();
		try{
			resultMap.put("p", paramMap);


			ArrayList<String> dateList         = service.selectListStatisticProductItemY(paramMap);

			//resultMap.put("x", productList);
			resultMap.put("Y", dateList);

			paramMap.put("dates", dateList);
			resultMap.put("list", service.selectListStatisticProductDate(paramMap));

			//resultMap.put("y", service.selectListStatisticProductY(paramMap));
			//resultMap.put("list", service.selectListStatisticProductDate(paramMap));
			//resultMap.put("info", service.selectListStatisticProductInfo(paramMap));
			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		}catch (Exception e){
			logger.error(e.getMessage());
			e.printStackTrace();
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}



	@GetMapping("/program")
	public ModelAndView statistics_program(
		@RequestParam(value="page", defaultValue="1") int page,
		@RequestParam(value="pageSize", defaultValue="10") int pageSize){
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> paramMap = new HashMap<>();
		mav.addObject("p", paramMap);
		mav.setViewName("manage/sub/statistics/program");
		return mav;
	}
	@PostMapping("/program")
	@ResponseBody
	public Map<String, Object>	statistics_program_ajax(
			@RequestParam Map<String, Object> paramMap){
		Map<String, Object> resultMap = new HashMap<>();
		try{
			resultMap.put("p", paramMap);


			ArrayList<String> dateList         = service.selectListStatisticDateY(paramMap);

			//resultMap.put("x", productList);
			resultMap.put("Y", dateList);

			paramMap.put("dates", dateList);
			resultMap.put("list", service.selectListStatisticProgramDate(paramMap));

			//resultMap.put("y", service.selectListStatisticProductY(paramMap));
			//resultMap.put("list", service.selectListStatisticProductDate(paramMap));
			//resultMap.put("info", service.selectListStatisticProductInfo(paramMap));
			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		}catch (Exception e){
			logger.error(e.getMessage());
			e.printStackTrace();
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}

	@RequestMapping("/program/excel")
	public ResponseEntity<byte[]> statistics_program_Download(
			@RequestParam Map<String, Object> paramMap,
			@RequestBody(required = false) Map<String, Object> requestBody) {

		try {
//			// 0. service를 통한 데이터 로드
			ArrayList<String> dateList         = service.selectListStatisticProgramItemY(paramMap);
			paramMap.put("dates", dateList);
			ArrayList<Map<String, Object>> programInfo = service.selectListStatisticProgramDate(paramMap);

			List<HeaderInfo> headerRow1 = new ArrayList<HeaderInfo>();
			List<HeaderInfo> headerRow2 = new ArrayList<HeaderInfo>();
			Map<String, String> columnMapping1 = new LinkedHashMap<String, String>();
			headerRow1.add( new HeaderInfo("상품명", 2, 1) );
			headerRow1.add( new HeaderInfo("합계", 1, 2) );
			headerRow2.add( new HeaderInfo("조회수", 1, 1) );
			headerRow2.add( new HeaderInfo("구매수", 1, 1) );

			List<ExcelData> excelDataList1 = new ArrayList<ExcelData>();

			// 2. 데이터 생성
			for (int i=0; i < programInfo.size(); i++) {
				Map<String, Object> map = programInfo.get(i);

				ExcelData _edata = new ExcelData();
				columnMapping1.put("v_sum"+i, "조회수");
				columnMapping1.put("s_sum"+i, "구매수");

				int vSum = 0, sSum = 0;
				for (int j=0; j < dateList.size(); j++) {
					_edata.addValue(map.get("product_title").toString(), map.get("product_title").toString());
					vSum += Math.round(Double.parseDouble(map.get(dateList.get(j)+"_v").toString()));
					sSum += Math.round(Double.parseDouble(map.get(dateList.get(j)+"_s").toString()));
				}
				_edata.addValue("v_sum"+i, vSum); //값 보정
				_edata.addValue("s_sum"+i, sSum); //값 보정
				for (int j=0; j < dateList.size(); j++) {
					if ( i == 0 ) {
						headerRow1.add( new HeaderInfo(dateList.get(j).toString(), 1, 2) );
						headerRow2.add( new HeaderInfo("조회수", 1, 1) );
						headerRow2.add( new HeaderInfo("구매수", 1, 1) );
					}
					_edata.addValue(dateList.get(j)+"_v", Math.round(Double.parseDouble( map.get(dateList.get(j)+"_v").toString())));
					_edata.addValue(dateList.get(j)+"_s", Math.round(Double.parseDouble(map.get(dateList.get(j)+"_s").toString())));
				}
				excelDataList1.add(_edata);
			}

			ExcelMetadata metadata1 = ExcelMetadata.builder()
				.addHeaderRow( Arrays.asList(new HeaderInfo( "프로그램 구매 통계", 1, 1) ) )
				.addHeaderRow( Arrays.asList(new HeaderInfo( "기간별 조회수 통계", 1, 1	)
					,  new HeaderInfo( "기간 : "+dateList.get(0)+" ~ "+dateList.get(dateList.size()-1), 1, 5 ) ) )
	            .addHeaderRow( headerRow1 )
				.addHeaderRow( headerRow2 )
	            .addColumnMapping( columnMapping1 )
	            .startRow(0)
	            .startCol(0)
	            .build();
			ExcelMetadata metadata2 = ExcelMetadata.builder()
				.addHeaderRow( Arrays.asList(new HeaderInfo( "기간별 조회수 통계", 1, 1) ) )
				.startRow(programInfo.size()+7).startCol(0).build();
			ExcelMetadata metadata3 = ExcelMetadata.builder()
				.addHeaderRow( Arrays.asList(new HeaderInfo( "기간별 구매수 통계", 1, 1) ) )
				.startRow(programInfo.size()+7+29).startCol(0).build();

			String base64Image1 = null;
			ExcelImgData img1 = new ExcelImgData();
	        if (requestBody != null && requestBody.containsKey("chartImage1")) {
	            base64Image1 = (String) requestBody.get("chartImage1");
	            img1.setBase64Image(base64Image1);
	            img1.setSize(0, 9, programInfo.size()+8, 26);
	        }
			String base64Image2 = null;
			ExcelImgData img2 = new ExcelImgData();
	        if (requestBody != null && requestBody.containsKey("chartImage2")) {
	            base64Image2 = (String) requestBody.get("chartImage2");
	            img2.setBase64Image(base64Image2);
	            img2.setSize(0, 9, programInfo.size()+8+29, 26);
	        }

			//size 차지용
			List<ExcelData> excelDataList2 = new ArrayList<ExcelData>();
			List<ExcelData> excelDataList3 = new ArrayList<ExcelData>();
			ExcelImgData img3 = new ExcelImgData();
			img3.setBase64Image(base64Image1);
			img3.setSize(0, 0,0, 0);

			List<ExcelMetadata> _metadata = Arrays.asList(metadata1, metadata2, metadata3);
			List<List<ExcelData>> _dataList = Arrays.asList(excelDataList1, excelDataList2, excelDataList3);
			List<ExcelImgData> _base64Image = Arrays.asList(img1, img2, img3);

			// 4. Excel 생성
			byte[] excelFile = ExcelUtil.generateExcel(_metadata, _dataList, _base64Image );

			// 5. 응답 헤더 설정
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
			headers.setContentDisposition(ContentDisposition.builder("attachment")
					.filename("프로그램 구매 통계.xlsx", StandardCharsets.UTF_8).build());

			return new ResponseEntity<>(excelFile, headers, HttpStatus.OK);
		} catch (Exception e) {
			logger.error("Failed to generate excel file", e);
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping("/jamsiisland")
	public ModelAndView statistics_jamsiisland(
			@RequestParam(value="page", defaultValue="1") int page,
			@RequestParam(value="pageSize", defaultValue="10") int pageSize){
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> paramMap = new HashMap<>();
		mav.addObject("p", paramMap);
		mav.setViewName("manage/sub/statistics/jamsiisland");
		return mav;
	}

	@PostMapping("/jamsiisland")
	@ResponseBody
	public Map<String, Object>	statistics_jamsiisland_ajax(
			@RequestParam Map<String, Object> paramMap){
		Map<String, Object> resultMap = new HashMap<>();
		try{
			resultMap.put("p", paramMap);


			ArrayList<String> dateList         = service.selectListStatisticJamsiislandItemY(paramMap);

			//resultMap.put("x", productList);
			resultMap.put("Y", dateList);

			paramMap.put("dates", dateList);
			resultMap.put("list1", service.selectListStatisticJamsiislandDate(paramMap));
			resultMap.put("listGender", service.selectListStatisticJamsiislandGender(paramMap));
			resultMap.put("list3", service.selectListStatisticJamsiislandRegion(paramMap));
			resultMap.put("list4", service.selectListStatisticJamsiislandAmount(paramMap));
			resultMap.put("listAge", service.selectListStatisticJamsiislandAge(paramMap));

			//resultMap.put("y", service.selectListStatisticProductY(paramMap));
			//resultMap.put("list", service.selectListStatisticProductDate(paramMap));
			//resultMap.put("info", service.selectListStatisticProductInfo(paramMap));
			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		}catch (Exception e){
			logger.error(e.getMessage());
			e.printStackTrace();
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}

	@RequestMapping("/jamsiisland/excel")
	public ResponseEntity<byte[]> statistics_jamsiisland_Download(
			@RequestParam Map<String, Object> paramMap,
			@RequestBody(required = false) Map<String, Object> requestBody) {

		try {
//			// 0. service를 통한 데이터 로드
			ArrayList<String> dateList         = service.selectListStatisticJamsiislandItemY(paramMap);
			paramMap.put("dates", dateList);
			ArrayList<Map<String, Object>> Info = service.selectListStatisticJamsiislandDate(paramMap);

			List<HeaderInfo> headerRow1 = new ArrayList<HeaderInfo>();
			List<HeaderInfo> headerRow2 = new ArrayList<HeaderInfo>();
			Map<String, String> columnMapping1 = new LinkedHashMap<String, String>();
			headerRow1.add( new HeaderInfo("상품명", 2, 1) );
			headerRow1.add( new HeaderInfo("합계", 1, 2) );
			headerRow2.add( new HeaderInfo("조회수", 1, 1) );
			headerRow2.add( new HeaderInfo("구매수", 1, 1) );

			List<ExcelData> excelDataList1 = new ArrayList<ExcelData>();

			// 2. 데이터 생성
			for (int i=0; i < Info.size(); i++) {
				Map<String, Object> map = Info.get(i);

				columnMapping1.put(map.get("product_serial").toString(), map.get("product_serial").toString());
				ExcelData _edata = new ExcelData();
				columnMapping1.put("v_sum"+i, "조회수");
				columnMapping1.put("s_sum"+i, "구매수");

				int vSum = 0, sSum = 0;
				for (int j=0; j < dateList.size(); j++) {
					_edata.addValue(map.get("product_title").toString(), map.get("product_title").toString());
					vSum += Math.round(Double.parseDouble(map.get(dateList.get(j)+"_v").toString()));
					sSum += Math.round(Double.parseDouble(map.get(dateList.get(j)+"_s").toString()));
				}
				_edata.addValue("v_sum"+i, vSum); //값 보정
				_edata.addValue("s_sum"+i, sSum); //값 보정
				for (int j=0; j < dateList.size(); j++) {
					if ( i == 0 ) {
						headerRow1.add( new HeaderInfo(dateList.get(j).toString(), 1, 2) );
						headerRow2.add( new HeaderInfo("조회수", 1, 1) );
						headerRow2.add( new HeaderInfo("구매수", 1, 1) );
					}
					_edata.addValue(dateList.get(j)+"_v", Math.round(Double.parseDouble( map.get(dateList.get(j)+"_v").toString())));
					_edata.addValue(dateList.get(j)+"_s", Math.round(Double.parseDouble(map.get(dateList.get(j)+"_s").toString())));
				}
				excelDataList1.add(_edata);
			}

			ExcelMetadata metadata1 = ExcelMetadata.builder()
				.addHeaderRow( Arrays.asList(new HeaderInfo( "잠시섬 구매 통계", 1, 1) ) )
				.addHeaderRow( Arrays.asList(new HeaderInfo( "기간별 조회수 통계", 1, 1	)
				,  new HeaderInfo( "기간 : "+dateList.get(0)+" ~ "+dateList.get(dateList.size()-1), 1, 5 ) ) )
	            .addHeaderRow( headerRow1 )
				.addHeaderRow( headerRow2 )
	            .addColumnMapping( columnMapping1 )
	            .startRow(0)
	            .startCol(0)
	            .build();
			//size 차지용
			ExcelMetadata metadataText1 = ExcelMetadata.builder()
				.addHeaderRow( Arrays.asList(new HeaderInfo( "상품별 조회수 통계", 1, 1) ) )
				.startRow(Info.size()+6).startCol(0).build();
			List<ExcelData> metadataText1ExcelDataList = new ArrayList<ExcelData>();
			ExcelMetadata metadataText2 = ExcelMetadata.builder()
				.addHeaderRow( Arrays.asList(new HeaderInfo( "상품별 구매수 통계", 1, 1) ) )
				.startRow(Info.size()+7).startCol(11).build();
			List<ExcelData> metadataText2ExcelDataList = new ArrayList<ExcelData>();


			//---상품별 성별 통계
			ArrayList<Map<String, Object>> genderInfo = service.selectListStatisticJamsiislandGender(paramMap);
			List<HeaderInfo> secondHeaderRow1 = new ArrayList<HeaderInfo>();
			List<HeaderInfo> secondHeaderRow2 = new ArrayList<HeaderInfo>();
			Map<String, String> secondColumnMapping = new LinkedHashMap<String, String>();
			secondHeaderRow1.add( new HeaderInfo("상품명", 2, 1) );
			secondHeaderRow1.add( new HeaderInfo("합계", 1, 3) );
			secondHeaderRow2.add( new HeaderInfo("남성", 1, 1) );
			secondHeaderRow2.add( new HeaderInfo("여성", 1, 1) );
			secondHeaderRow2.add( new HeaderInfo("미상", 1, 1) );

			secondColumnMapping.put("product_title", "상품명");
			secondColumnMapping.put("man", "남성");
			secondColumnMapping.put("woman", "여성");
			secondColumnMapping.put("etc", "미상");

			ExcelMetadata metadata2 = ExcelMetadata.builder()
	            .sheetName("상품별 성별 통계")
	            .addHeaderRow( Arrays.asList(new HeaderInfo( "상품별 성별 통계", 1, 1) ) )
	            .addHeaderRow( secondHeaderRow1 )
				.addHeaderRow( secondHeaderRow2 )
	            .addColumnMapping( secondColumnMapping )
	            .startRow(Info.size()+42)
	            .startCol(0)
	            .build();
			List<ExcelData> excelDataList2 = new ArrayList<ExcelData>();

			for (int i=0; i < genderInfo.size(); i++) {
				Map<String, Object> map = genderInfo.get(i);
				ExcelData edata = new ExcelData();

				edata.addValue("product_title", map.get("product_title").toString());
				edata.addValue("man", Math.round(Double.parseDouble( map.get("g1").toString())));
				edata.addValue("woman", Math.round(Double.parseDouble( map.get("g2").toString())));
				edata.addValue("etc", Math.round(Double.parseDouble( map.get("g3").toString())));
				excelDataList2.add(edata);
			}

			//---------------상품별 연령별 통계	---------------
			ArrayList<Map<String, Object>> ageInfo = service.selectListStatisticJamsiislandAge(paramMap);
			List<HeaderInfo> ageInfoHeaderRow1 = new ArrayList<HeaderInfo>();
			Map<String, String> ageInfoColumnMapping = new LinkedHashMap<String, String>();
			ageInfoHeaderRow1.add( new HeaderInfo("상품명", 1, 1) );
			ageInfoHeaderRow1.add( new HeaderInfo("10대 이하", 1, 1) );
			ageInfoHeaderRow1.add( new HeaderInfo("20대", 1, 1) );
			ageInfoHeaderRow1.add( new HeaderInfo("30대", 1, 1) );
			ageInfoHeaderRow1.add( new HeaderInfo("40대", 1, 1) );
			ageInfoHeaderRow1.add( new HeaderInfo("50대", 1, 1) );
			ageInfoHeaderRow1.add( new HeaderInfo("60대", 1, 1) );
			ageInfoHeaderRow1.add( new HeaderInfo("70대", 1, 1) );
			ageInfoHeaderRow1.add( new HeaderInfo("80대", 1, 1) );
			ageInfoHeaderRow1.add( new HeaderInfo("90대 이상", 1, 1) );
			ageInfoHeaderRow1.add( new HeaderInfo("미상", 1, 1) );
			ageInfoColumnMapping.put("product_title", "상품명");
			ageInfoColumnMapping.put("ag01", "10대 이하");
			ageInfoColumnMapping.put("ag02", "20대");
			ageInfoColumnMapping.put("ag03", "30대");
			ageInfoColumnMapping.put("ag04", "40대");
			ageInfoColumnMapping.put("ag05", "50대");
			ageInfoColumnMapping.put("ag06", "60대");
			ageInfoColumnMapping.put("ag07", "70대");
			ageInfoColumnMapping.put("ag08", "80대");
			ageInfoColumnMapping.put("ag09", "90대");
			ageInfoColumnMapping.put("ag99", "미상");

			ExcelMetadata metadata3 = ExcelMetadata.builder()
	            .sheetName("상품별 연령별 통계")
	            .addHeaderRow( Arrays.asList(new HeaderInfo( "상품별 연령별 통계", 1, 1) ) )
	            .addHeaderRow( ageInfoHeaderRow1 )
	            .addColumnMapping( ageInfoColumnMapping )
	            .startRow(Info.size()+57)
	            .startCol(0)
	            .build();
			List<ExcelData> excelDataList3 = new ArrayList<ExcelData>();

			for (int i=0; i < ageInfo.size(); i++) {
				ExcelData ageInfoEdata = new ExcelData();
				Map<String, Object> map = ageInfo.get(i);

				ageInfoEdata.addValue(map.get("product_title").toString(), map.get("product_title").toString());
				ageInfoEdata.addValue("ag01", map.get("ag01").toString());
				ageInfoEdata.addValue("ag02", map.get("ag02").toString());
				ageInfoEdata.addValue("ag03", map.get("ag03").toString());
				ageInfoEdata.addValue("ag04", map.get("ag04").toString());
				ageInfoEdata.addValue("ag05", map.get("ag05").toString());
				ageInfoEdata.addValue("ag06", map.get("ag06").toString());
				ageInfoEdata.addValue("ag07", map.get("ag07").toString());
				ageInfoEdata.addValue("ag08", map.get("ag08").toString());
				ageInfoEdata.addValue("ag09", map.get("ag09").toString());
				ageInfoEdata.addValue("ag99", map.get("ag99").toString());
				excelDataList3.add(ageInfoEdata);
			}

			//---------------상품별 연령별 통계	---------------
			ArrayList<Map<String, Object>> regionInfo = service.selectListStatisticJamsiislandRegion(paramMap);
			List<HeaderInfo> regionInfoHeaderRow1 = new ArrayList<HeaderInfo>();
			List<HeaderInfo> regionInfoHeaderRow2 = new ArrayList<HeaderInfo>();
			Map<String, String> regionInfoColumnMapping = new LinkedHashMap<String, String>();
			regionInfoHeaderRow1.add( new HeaderInfo("상품명", 2, 1) );
			regionInfoHeaderRow1.add( new HeaderInfo("합계", 1, 18) );
			regionInfoHeaderRow2.add( new HeaderInfo("서울", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("부산", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("대구", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("인천", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("광주", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("대전", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("울산", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("세종", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("경기", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("충북", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("충남", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("전북", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("전남", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("경북", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("경남", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("강원", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("제주", 1, 1) );
			regionInfoHeaderRow2.add( new HeaderInfo("기타", 1, 1) );

			regionInfoColumnMapping.put("product_title", "상품명");
			regionInfoColumnMapping.put("regionSum", "합계");
			regionInfoColumnMapping.put("서울", "서울");
			regionInfoColumnMapping.put("부산", "부산");
			regionInfoColumnMapping.put("대구", "대구");
			regionInfoColumnMapping.put("인천", "인천");
			regionInfoColumnMapping.put("광주", "광주");
			regionInfoColumnMapping.put("대전", "대전");
			regionInfoColumnMapping.put("울산", "울산");
			regionInfoColumnMapping.put("세종", "세종");
			regionInfoColumnMapping.put("경기", "경기");
			regionInfoColumnMapping.put("충북", "충북");
			regionInfoColumnMapping.put("충남", "충남");
			regionInfoColumnMapping.put("전북", "전북");
			regionInfoColumnMapping.put("전남", "전남");
			regionInfoColumnMapping.put("경북", "경북");
			regionInfoColumnMapping.put("경남", "경남");
			regionInfoColumnMapping.put("강원", "강원");
			regionInfoColumnMapping.put("제주", "제주");
			regionInfoColumnMapping.put("기타", "기타");

			ExcelMetadata metadata4 = ExcelMetadata.builder()
	            .sheetName("상품별 거주지 통계")
	            .addHeaderRow( Arrays.asList(new HeaderInfo( "상품별 거주지 통계", 1, 1) ) )
	            .addHeaderRow( regionInfoHeaderRow1 )
	            .addHeaderRow( regionInfoHeaderRow2 )
	            .addColumnMapping( regionInfoColumnMapping )
	            .startRow(Info.size()+75)
	            .startCol(0)
	            .build();
			List<ExcelData> excelDataList4 = new ArrayList<ExcelData>();

			for (int i=0; i < regionInfo.size(); i++) {
				ExcelData regionInfoEdata = new ExcelData();
				Map<String, Object> map = regionInfo.get(i);

				regionInfoEdata.addValue(map.get("product_title").toString(), map.get("product_title").toString());
				regionInfoEdata.addValue("서울", Math.round(Double.parseDouble(map.get("서울").toString())));
				regionInfoEdata.addValue("부산", Math.round(Double.parseDouble(map.get("부산").toString())));
				regionInfoEdata.addValue("대구", Math.round(Double.parseDouble(map.get("대구").toString())));
				regionInfoEdata.addValue("인천", Math.round(Double.parseDouble(map.get("인천").toString())));
				regionInfoEdata.addValue("광주", Math.round(Double.parseDouble(map.get("광주").toString())));
				regionInfoEdata.addValue("대전", Math.round(Double.parseDouble(map.get("대전").toString())));
				regionInfoEdata.addValue("울산", Math.round(Double.parseDouble(map.get("울산").toString())));
				regionInfoEdata.addValue("세종", Math.round(Double.parseDouble(map.get("세종").toString())));
				regionInfoEdata.addValue("경기", Math.round(Double.parseDouble(map.get("경기").toString())));
				regionInfoEdata.addValue("충북", Math.round(Double.parseDouble(map.get("충북").toString())));
				regionInfoEdata.addValue("충남", Math.round(Double.parseDouble(map.get("충남").toString())));
				regionInfoEdata.addValue("전북", Math.round(Double.parseDouble(map.get("전북").toString())));
				regionInfoEdata.addValue("전남", Math.round(Double.parseDouble(map.get("전남").toString())));
				regionInfoEdata.addValue("경북", Math.round(Double.parseDouble(map.get("경북").toString())));
				regionInfoEdata.addValue("경남", Math.round(Double.parseDouble(map.get("경남").toString())));
				regionInfoEdata.addValue("강원", Math.round(Double.parseDouble(map.get("강원").toString())));
				regionInfoEdata.addValue("제주", Math.round(Double.parseDouble(map.get("제주").toString())));
				regionInfoEdata.addValue("기타", Math.round(Double.parseDouble(map.get("기타").toString())));

				excelDataList4.add(regionInfoEdata);
			}

			//---------------상품별 이용정보 통계	---------------
			ArrayList<Map<String, Object>> amountInfo = service.selectListStatisticJamsiislandAmount(paramMap);
			List<HeaderInfo> amountInfoHeaderRow1 = new ArrayList<HeaderInfo>();
			List<HeaderInfo> amountInfoHeaderRow2 = new ArrayList<HeaderInfo>();
			Map<String, String> amountInfoColumnMapping = new LinkedHashMap<String, String>();
			amountInfoHeaderRow1.add( new HeaderInfo("상품명", 2, 1) );
			amountInfoHeaderRow1.add( new HeaderInfo("합계", 1, 3) );
			amountInfoHeaderRow2.add( new HeaderInfo("이용인원", 1, 1) );
			amountInfoHeaderRow2.add( new HeaderInfo("이용일자", 1, 1) );
			amountInfoHeaderRow2.add( new HeaderInfo("누적금액", 1, 1) );

			amountInfoColumnMapping.put("product_title", "상품명");
			amountInfoColumnMapping.put("이용인원", "이용인원");
			amountInfoColumnMapping.put("이용일자", "이용일자");
			amountInfoColumnMapping.put("누적금액", "누적금액");

			ExcelMetadata metadata5 = ExcelMetadata.builder()
	            .sheetName("상품별 이용정보 통계")
	            .addHeaderRow( Arrays.asList(new HeaderInfo( "상품별 이용정보 통계", 1, 1) ) )
	            .addHeaderRow( amountInfoHeaderRow1 )
	            .addHeaderRow( amountInfoHeaderRow2 )
	            .addColumnMapping( amountInfoColumnMapping )
	            .startRow(Info.size()+85)
	            .startCol(0)
	            .build();
			List<ExcelData> excelDataList5 = new ArrayList<ExcelData>();

			for (int i=0; i < amountInfo.size(); i++) {
				ExcelData amountInfoEdata = new ExcelData();
				Map<String, Object> map = amountInfo.get(i);

				amountInfoEdata.addValue(map.get("product_title").toString(), map.get("product_title").toString());
				amountInfoEdata.addValue("이용인원", Math.round(Double.parseDouble(map.get("order_count").toString())));
				amountInfoEdata.addValue("이용일자", Math.round(Double.parseDouble(map.get("stay_date").toString())));
				amountInfoEdata.addValue("누적금액", Math.round(Double.parseDouble(map.get("total_amount").toString())));

				excelDataList5.add(amountInfoEdata);
			}

			String base64Image1 = null;
			ExcelImgData img1 = new ExcelImgData();
	        if (requestBody != null && requestBody.containsKey("chartImage1")) {
	            base64Image1 = (String) requestBody.get("chartImage1");
	            img1.setBase64Image(base64Image1);
	            img1.setSize(0, 9, Info.size()+7, 26);
	        }
			String base64Image2 = null;
			ExcelImgData img2 = new ExcelImgData();
	        if (requestBody != null && requestBody.containsKey("chartImage2")) {
	            base64Image2 = (String) requestBody.get("chartImage2");
	            img2.setBase64Image(base64Image2);
	            img2.setSize(11, 21, Info.size()+8, 26);
	        }
			String base64Image3 = null;
			ExcelImgData img3 = new ExcelImgData();
	        if (requestBody != null && requestBody.containsKey("chartImage3")) {
	            base64Image3 = (String) requestBody.get("chartImage3");
	            img3.setBase64Image(base64Image3);
	            img3.setSize(5, 10, Info.size()+metadata2.getStartRow()-6, 10);
	        }
			String base64Image4 = null;
			ExcelImgData img4 = new ExcelImgData();
	        if (requestBody != null && requestBody.containsKey("chartImage4")) {
	            base64Image4 = (String) requestBody.get("chartImage4");
	            img4.setBase64Image(base64Image4);
	            img4.setSize(12, 20, Info.size()+metadata3.getStartRow()-6, 10);
	        }
			String base64Image5 = null;
			ExcelImgData img5 = new ExcelImgData();
	        if (requestBody != null && requestBody.containsKey("chartImage5")) {
	            base64Image5 = (String) requestBody.get("chartImage5");
	            img5.setBase64Image(base64Image5);
	            img5.setSize(20, 30, Info.size()+metadata4.getStartRow()-6, 10);
	        }
			ExcelImgData img6 = new ExcelImgData();
			img6.setBase64Image(base64Image1);
			img6.setSize(0, 0, 0, 0);
			ExcelImgData img7 = new ExcelImgData();
			img7.setBase64Image(base64Image1);
			img7.setSize(0, 0, 0, 0);


			List<ExcelMetadata> _metadata = Arrays.asList(metadata1, metadataText1, metadataText2, metadata2, metadata3, metadata4, metadata5);
			List<List<ExcelData>> _dataList = Arrays.asList(excelDataList1, metadataText1ExcelDataList, metadataText2ExcelDataList, excelDataList2, excelDataList3, excelDataList4, excelDataList5);
			List<ExcelImgData> _base64Image = Arrays.asList(img1, img2, img3, img4, img5, img6, img7);

//			// 4. Excel 생성
			byte[] excelFile = ExcelUtil.generateExcel(_metadata, _dataList, _base64Image );

			// 5. 응답 헤더 설정
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
			headers.setContentDisposition(ContentDisposition.builder("attachment")
					.filename("잠시섬 구매 통계.xlsx", StandardCharsets.UTF_8).build());

			return new ResponseEntity<>(excelFile, headers, HttpStatus.OK);
		} catch (Exception e) {
			logger.error("Failed to generate excel file", e);
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping("/board")
	public ModelAndView statistics_board(
		@RequestParam(value="page", defaultValue="1") int page,
		@RequestParam(value="pageSize", defaultValue="10") int pageSize){
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> paramMap = new HashMap<>();
		mav.addObject("p", paramMap);
		mav.setViewName("manage/sub/statistics/board");
		return mav;
	}

	@PostMapping("/board")
	@ResponseBody
	public Map<String, Object>	statistics_board_ajax(
			@RequestParam Map<String, Object> paramMap){
		Map<String, Object> resultMap = new HashMap<>();
		try{
			resultMap.put("p", paramMap);

			ArrayList<String> dateList         = service.selectListStatisticDateY(paramMap);

			paramMap.put("dates", dateList);

			resultMap.put("dates", dateList);
			resultMap.put("list", service.selectListStatisticBoard(paramMap));

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		}catch (Exception e){
			logger.error(e.getMessage());
			e.printStackTrace();
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}

	@RequestMapping("/board/excel")
	public ResponseEntity<byte[]> statistics_board_Download(
			@RequestParam Map<String, Object> paramMap,
			@RequestBody(required = false) Map<String, Object> requestBody) {

		try {
//			// 0. service를 통한 데이터 로드
			ArrayList<String> dateList         = service.selectListStatisticDateY(paramMap);
			paramMap.put("dates", dateList);
			ArrayList<Map<String, Object>> boardInfo = service.selectListStatisticBoard(paramMap);

			List<HeaderInfo> boardHeaderRow1 = new ArrayList<HeaderInfo>();
			List<HeaderInfo> boardHeaderRow2 = new ArrayList<HeaderInfo>();
			Map<String, String> boardColumnMapping1 = new LinkedHashMap<String, String>();
			boardHeaderRow1.add( new HeaderInfo("게시판", 2, 1) );
			boardHeaderRow1.add( new HeaderInfo("합계", 1, 2) );
			boardHeaderRow2.add( new HeaderInfo("조회수", 1, 1) );
			boardHeaderRow2.add( new HeaderInfo("작성수", 1, 1) );
//			boardHeaderRow2.add( new HeaderInfo("좋아요", 1, 1) );
//			boardHeaderRow2.add( new HeaderInfo("댓글수", 1, 1) );

			List<ExcelData> excelDataList1 = new ArrayList<ExcelData>();

			// 2. 데이터 생성
			for (int i=0; i < boardInfo.size(); i++) {
				Map<String, Object> map = boardInfo.get(i);

				ExcelData edata = new ExcelData();
				boardColumnMapping1.put("v_sum"+i, "조회수");
				boardColumnMapping1.put("w_sum"+i, "좋아요");
//				boardColumnMapping1.put("f_sum"+i, "좋아요");
//				boardColumnMapping1.put("c_sum"+i, "댓글수");

				int vSum = 0,wSum = 0/* fSum = 0, cSum = 0*/;
				for (int j=0; j < dateList.size(); j++) {
					edata.addValue(map.get("title").toString(), map.get("title").toString());
					vSum += Math.round(Double.parseDouble(map.get(dateList.get(j)+"_v").toString()));
					wSum += Math.round(Double.parseDouble(map.get(dateList.get(j)+"_w").toString()));
//					fSum += Math.round(Double.parseDouble(map.get(dateList.get(j)+"_f").toString()));
//					cSum += Math.round(Double.parseDouble(map.get(dateList.get(j)+"_c").toString()));
				}
				edata.addValue("v_sum"+i, vSum); //값 보정
				edata.addValue("v_sum"+i, wSum); //값 보정
//				edata.addValue("f_sum"+i, fSum); //값 보정
//				edata.addValue("c_sum"+i, cSum); //값 보정
				for (int j=0; j < dateList.size(); j++) {
					if ( i == 0 ) {
						boardHeaderRow1.add( new HeaderInfo(dateList.get(j).toString(), 1, 3) );
						boardHeaderRow2.add( new HeaderInfo("조회수", 1, 1) );
						boardHeaderRow2.add( new HeaderInfo("작성수", 1, 1) );
//						boardHeaderRow2.add( new HeaderInfo("좋아요", 1, 1) );
//						boardHeaderRow2.add( new HeaderInfo("댓글수", 1, 1) );
					}
					edata.addValue(dateList.get(j)+"_v", Math.round(Double.parseDouble( map.get(dateList.get(j)+"_v").toString())));
					edata.addValue(dateList.get(j)+"_w", Math.round(Double.parseDouble( map.get(dateList.get(j)+"_w").toString())));
//					edata.addValue(dateList.get(j)+"_f", Math.round(Double.parseDouble(map.get(dateList.get(j)+"_f").toString())));
//					edata.addValue(dateList.get(j)+"_c", Math.round(Double.parseDouble(map.get(dateList.get(j)+"_c").toString())));
				}
				excelDataList1.add(edata);
			}

			ExcelMetadata metadata1 = ExcelMetadata.builder()
	            .sheetName("게시글 통계")
	            .addHeaderRow( Arrays.asList(new HeaderInfo( "게시글 통계", 1, 1) ) )
				.addHeaderRow( Arrays.asList(new HeaderInfo( "기간별 조회수 통계", 1, 2	)
						,  new HeaderInfo( "기간 : "+dateList.get(0)+" ~ "+dateList.get(dateList.size()-1), 1, 5 ) ) )
	            .addHeaderRow( boardHeaderRow1 )
				.addHeaderRow( boardHeaderRow2 )
	            .addColumnMapping( boardColumnMapping1 )
	            .startRow(0)
	            .startCol(0)
	            .build();

			ExcelMetadata metadata2 = ExcelMetadata.builder()
	            .addHeaderRow( Arrays.asList(new HeaderInfo( "기간별 조회수 통계", 1, 1) ) )
				.startRow(boardInfo.size()+7).startCol(0) .build();
			ExcelMetadata metadata3 = ExcelMetadata.builder()
	            .addHeaderRow( Arrays.asList(new HeaderInfo( "기간별 작성수 통계", 1, 1) ) )
				.startRow(boardInfo.size()+6+29).startCol(0) .build();
//			ExcelMetadata metadata4 = ExcelMetadata.builder()
//	            .addHeaderRow( Arrays.asList(new HeaderInfo( "기간별 댓글수 통계", 1, 1) ) )
//				.startRow(boardInfo.size()+6+29+29).startCol(0) .build();

			String base64Image1 = null;
			ExcelImgData img1 = new ExcelImgData();
	        if (requestBody != null && requestBody.containsKey("chartImage1")) {
	            base64Image1 = (String) requestBody.get("chartImage1");
	            img1.setBase64Image(base64Image1);
	            img1.setSize(0, 12, boardInfo.size()+8, 25);
	        }
			String base64Image2 = null;
			ExcelImgData img2 = new ExcelImgData();
	        if (requestBody != null && requestBody.containsKey("chartImage2")) {
	            base64Image2 = (String) requestBody.get("chartImage2");
	            img2.setBase64Image(base64Image2);
	            img2.setSize(0, 12, boardInfo.size()+7+29, 25);
	        }
//			String base64Image3 = null;
			ExcelImgData img3 = new ExcelImgData();
//	        if (requestBody != null && requestBody.containsKey("chartImage3")) {
//	            base64Image3 = (String) requestBody.get("chartImage3");
//	            img3.setBase64Image(base64Image3);
//	            img3.setSize(0, 12, boardInfo.size()+7+29+29, 25);
//	        }
//			ExcelImgData img4 = new ExcelImgData();
//	        img4.setBase64Image(base64Image3);
//	            img4.setSize(0, 0, 0, 0);

			//size 차지용
			List<ExcelData> excelDataList2 = new ArrayList<ExcelData>();
			List<ExcelData> excelDataList3 = new ArrayList<ExcelData>();
			List<ExcelData> excelDataList4 = new ArrayList<ExcelData>();

			List<ExcelMetadata> _metadata = Arrays.asList(metadata1, metadata2, metadata3/*, metadata4*/);
			List<List<ExcelData>> _dataList = Arrays.asList(excelDataList1, excelDataList2, excelDataList3/*, excelDataList4*/);
			List<ExcelImgData> _base64Image = Arrays.asList(img1, img2, null/*,img3, img4*/);

			// 4. Excel 생성
			byte[] excelFile = ExcelUtil.generateExcel(_metadata, _dataList, _base64Image );

			// 5. 응답 헤더 설정
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
			headers.setContentDisposition(ContentDisposition.builder("attachment")
					.filename("게시글 통계.xlsx", StandardCharsets.UTF_8).build());

			return new ResponseEntity<>(excelFile, headers, HttpStatus.OK);
		} catch (Exception e) {
			logger.error("Failed to generate excel file", e);
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping("/mission")
	public ModelAndView statistics_mission(
			@RequestParam(value="page", defaultValue="1") int page,
			@RequestParam(value="pageSize", defaultValue="10") int pageSize){
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> paramMap = new HashMap<>();
		mav.addObject("p", paramMap);
		mav.setViewName("manage/sub/statistics/mission");
		return mav;
	}

	@PostMapping("/mission")
	@ResponseBody
	public Map<String, Object>	statistics_mission_ajax(
			@RequestParam Map<String, Object> paramMap){
		Map<String, Object> resultMap = new HashMap<>();
		try{
			resultMap.put("p", paramMap);

			ArrayList<String> dateList         = service.selectListStatisticDateY(paramMap);

			paramMap.put("dates", dateList);

			resultMap.put("dates", dateList);
			resultMap.put("list", service.selectListStatisticMission(paramMap));

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		}catch (Exception e){
			logger.error(e.getMessage());
			e.printStackTrace();
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}

	@RequestMapping("/mission/excel")
	public ResponseEntity<byte[]> statistics_mission_Download(
			@RequestParam Map<String, Object> paramMap,
			@RequestBody(required = false) Map<String, Object> requestBody) {

		try {
//			// 0. service를 통한 데이터 로드
			ArrayList<String> dateList         = service.selectListStatisticDateY(paramMap);
			paramMap.put("dates", dateList);
			ArrayList<Map<String, Object>> missionInfo = service.selectListStatisticMission(paramMap);

			List<HeaderInfo> missionHeaderRow1 = new ArrayList<HeaderInfo>();
			Map<String, String> missionColumnMapping1 = new LinkedHashMap<String, String>();
			missionHeaderRow1.add( new HeaderInfo("미션이름", 1, 1) );
			missionHeaderRow1.add( new HeaderInfo("합계", 1, 1) );
			missionColumnMapping1.put("badge_name", "미션");
			missionColumnMapping1.put("sum", "합계");

			List<ExcelData> excelDataList1 = new ArrayList<ExcelData>();

			// 2. 데이터 생성
			for (int i=0; i < missionInfo.size(); i++) {
				Map<String, Object> map = missionInfo.get(i);
				ExcelData _edata = new ExcelData();
				int sum = 0;
				for (int j=0; j < dateList.size(); j++) {
					_edata.addValue(map.get("title").toString(), map.get("title").toString());
					sum += Math.round(Double.parseDouble(map.get(dateList.get(j)).toString()));
				}
				_edata.addValue("sum"+i, sum);
				for (int j=0; j < dateList.size(); j++) {
					if ( i == 0 ) {
						missionHeaderRow1.add( new HeaderInfo(dateList.get(j).toString(), 1, 1) );
					}
					_edata.addValue(dateList.get(j), Math.round(Double.parseDouble( map.get(dateList.get(j)).toString())));
				}
				excelDataList1.add(_edata);
			}

			ExcelMetadata metadata1 = ExcelMetadata.builder()
	            .addHeaderRow( Arrays.asList(new HeaderInfo( "미션 통계", 1, 1) ) )
				.addHeaderRow( Arrays.asList(new HeaderInfo( "기간별 조회수 통계", 1, 1	)
						,  new HeaderInfo( "기간 : "+dateList.get(0)+" ~ "+dateList.get(dateList.size()-1), 1, 5 ) ) )
	            .addHeaderRow( missionHeaderRow1 )
	            .addColumnMapping( missionColumnMapping1 )
	            .startRow(0)
	            .startCol(0)
	            .build();

			ExcelMetadata metadata2 = ExcelMetadata.builder()
				.addHeaderRow( Arrays.asList(new HeaderInfo( "기간별 조회수 통계", 1, 1) ) )
	            .startRow(missionInfo.size()+7).startCol(0).build();

			String base64Image1 = null;
			ExcelImgData img1 = new ExcelImgData();
	        if (requestBody != null && requestBody.containsKey("chartImage1")) {
	            base64Image1 = (String) requestBody.get("chartImage1");
	            img1.setBase64Image(base64Image1);
	            img1.setSize(0, 9, missionInfo.size()+8, 26);
	        }

			//size 채우기용
			List<ExcelData> excelDataList2 = new ArrayList<ExcelData>();
			ExcelImgData img2 = new ExcelImgData();
			img2.setBase64Image(base64Image1);
			img2.setSize(0, 0, 0, 0);

			List<ExcelMetadata> _metadata = Arrays.asList(metadata1, metadata2);
			List<List<ExcelData>> _dataList = Arrays.asList(excelDataList1, excelDataList2);
			List<ExcelImgData> _base64Image = Arrays.asList(img1, img2);

			// 4. Excel 생성
			byte[] excelFile = ExcelUtil.generateExcel(_metadata, _dataList, _base64Image );

			// 5. 응답 헤더 설정
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
			headers.setContentDisposition(ContentDisposition.builder("attachment")
					.filename("미션 통계.xlsx", StandardCharsets.UTF_8).build());

			return new ResponseEntity<>(excelFile, headers, HttpStatus.OK);
		} catch (Exception e) {
			logger.error("Failed to generate excel file", e);
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping("/badge")
	public ModelAndView statistics_badge(
			@RequestParam(value="page", defaultValue="1") int page,
			@RequestParam(value="pageSize", defaultValue="10") int pageSize){
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> paramMap = new HashMap<>();
		mav.addObject("p", paramMap);
		mav.setViewName("manage/sub/statistics/badge");
		return mav;
	}

	@PostMapping("/badge")
	@ResponseBody
	public Map<String, Object>	statistics_badge_ajax(
			@RequestParam Map<String, Object> paramMap){
		Map<String, Object> resultMap = new HashMap<>();
		try{
			resultMap.put("p", paramMap);

			ArrayList<String> dateList         = service.selectListStatisticDateY(paramMap);

			paramMap.put("dates", dateList);

			resultMap.put("dates", dateList);
			resultMap.put("list", service.selectListStatisticBadge(paramMap));

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		}catch (Exception e){
			logger.error(e.getMessage());
			e.printStackTrace();
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}

	@RequestMapping("/badge/excel")
	public ResponseEntity<byte[]> statistics_badge_Download(
			@RequestParam Map<String, Object> paramMap,
			@RequestBody(required = false) Map<String, Object> requestBody) {

		try {
//			// 0. service를 통한 데이터 로드
			ArrayList<String> dateList         = service.selectListStatisticDateY(paramMap);
			paramMap.put("dates", dateList);
			ArrayList<Map<String, Object>> badgeInfo = service.selectListStatisticBadge(paramMap);

			List<HeaderInfo> badgeHeaderRow1 = new ArrayList<HeaderInfo>();
			Map<String, String> badgeColumnMapping1 = new LinkedHashMap<String, String>();
			badgeHeaderRow1.add( new HeaderInfo("배지이름", 1, 1) );
			badgeHeaderRow1.add( new HeaderInfo("합계", 1, 1) );
			badgeColumnMapping1.put("badge_name", "배지이름");
			badgeColumnMapping1.put("sum", "합계");

			List<ExcelData> excelDataList1 = new ArrayList<ExcelData>();

			// 2. 데이터 생성
			for (int i=0; i < badgeInfo.size(); i++) {
				Map<String, Object> map = badgeInfo.get(i);
				ExcelData _edata = new ExcelData();
				int sum = 0;
				for (int j=0; j < dateList.size(); j++) {
					_edata.addValue(map.get("badge_name").toString(), map.get("badge_name").toString());
					sum += Math.round(Double.parseDouble(map.get(dateList.get(j)).toString()));
				}
				_edata.addValue("sum"+i, sum);
				for (int j=0; j < dateList.size(); j++) {
					if ( i == 0 ) {
						badgeHeaderRow1.add( new HeaderInfo(dateList.get(j).toString(), 1, 1) );
					}
					_edata.addValue(dateList.get(j), Math.round(Double.parseDouble( map.get(dateList.get(j)).toString())));
				}
				excelDataList1.add(_edata);
			}

			ExcelMetadata metadata1 = ExcelMetadata.builder()
				.addHeaderRow( Arrays.asList(new HeaderInfo( "배지 통계", 1, 1) ) )
	            .addHeaderRow( Arrays.asList(new HeaderInfo( "기간별 조회수 통계", 1, 1	)
						,  new HeaderInfo( "기간 : "+dateList.get(0)+" ~ "+dateList.get(dateList.size()-1), 1, 5 ) ) )
	            .addHeaderRow( badgeHeaderRow1 )
	            .addColumnMapping( badgeColumnMapping1 )
	            .startRow(0)
	            .startCol(0)
	            .build();
			ExcelMetadata metadata2 = ExcelMetadata.builder()
				.addHeaderRow( Arrays.asList(new HeaderInfo( "기간별 조회수 통계", 1, 1) ) )
				.startRow(badgeInfo.size()+7).startCol(0).build();


			String base64Image1 = null;
			ExcelImgData img1 = new ExcelImgData();
	        if (requestBody != null && requestBody.containsKey("chartImage1")) {
	            base64Image1 = (String) requestBody.get("chartImage1");
	            img1.setBase64Image(base64Image1);
	            img1.setSize(0, 9, badgeInfo.size()+8, 26);
	        }

			//size 차지용
			List<ExcelData> excelDataList2 = new ArrayList<ExcelData>();
			ExcelImgData img2 = new ExcelImgData();
			img2.setBase64Image(base64Image1);
			img2.setSize(0, 9, 0, 0);

			List<ExcelMetadata> _metadata = Arrays.asList(metadata1, metadata2);
			List<List<ExcelData>> _dataList = Arrays.asList(excelDataList1, excelDataList2);
			List<ExcelImgData> _base64Image = Arrays.asList(img1, img2);

			// 4. Excel 생성
			byte[] excelFile = ExcelUtil.generateExcel(_metadata, _dataList, _base64Image );

			// 5. 응답 헤더 설정
			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
			headers.setContentDisposition(ContentDisposition.builder("attachment")
					.filename("배지 통계.xlsx", StandardCharsets.UTF_8).build());

			return new ResponseEntity<>(excelFile, headers, HttpStatus.OK);
		} catch (Exception e) {
			logger.error("Failed to generate excel file", e);
			return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
}
