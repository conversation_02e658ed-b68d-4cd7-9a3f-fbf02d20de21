package kr.co.wayplus.travel.mapper.front;

import kr.co.wayplus.travel.model.*;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

@Mapper
@Repository
public interface MenuMapper {
	
	int selectListMenuCount(HashMap<String, Object> paramMap);
	ArrayList<MenuUser> selectListMenu(HashMap<String, Object> paramMap);
	MenuUser selectOneMenu(HashMap<String, Object> paramMap);
    ArrayList<MenuUser> selectListCommunityMenu(HashMap<String, Object> paramMap);
}
