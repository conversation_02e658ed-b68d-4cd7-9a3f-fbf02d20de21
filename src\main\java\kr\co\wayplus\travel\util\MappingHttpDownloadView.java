package kr.co.wayplus.travel.util;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.servlet.view.AbstractView;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Map;

public class MappingHttpDownloadView extends AbstractView {

    @Override
    public String getContentType() {
        return "applicaiton/download;charset=utf-8"; // view가 응답할 content 타입
    }

    @Override
    protected void renderMergedOutputModel(Map<String, Object> model, HttpServletRequest request, HttpServletResponse response) throws Exception {
        String newFileName = (String) model.get("storeFileName");
        String originalFileName = (String) model.get("downloadFileName");
        String uploadUrl = (String) model.get("uploadUrl");

        File downFile = new File(uploadUrl, newFileName); // 다운로드할 파일정보를 가져온다.
        logger.debug(downFile.getAbsolutePath());

        // 응답 헤더 설정
        response.setContentType(getContentType()); // 응답 content type 설정
        response.setContentLength((int) downFile.length());

        String userAgent = request.getHeader("User-Agent");
        String filename = null;

        logger.debug(request.getHeader("User-Agent"));
        boolean ie = (userAgent.indexOf("MSIE") > -1 || userAgent.indexOf("Windows NT") > -1);
        if (ie) {
            filename = URLEncoder.encode(originalFileName, "utf-8").replace("+", "%20");
        } else {
            filename = new String(originalFileName.getBytes("utf-8"), "iso-8859-1").replace("+", "%20");
        }
        logger.debug(filename);

        // 첨부파일명 설정 : 실제 파일명으로 설정해준다.
        response.setHeader("Content-Disposition", "attachment;" + " filename=\"" + filename + "\";");
        response.setHeader("Content-Transfer-Encoding", "binary");

        OutputStream os = response.getOutputStream();
        FileInputStream fis = new FileInputStream(downFile);

        try {
            FileCopyUtils.copy(fis, os);
        } catch(Exception e){
            e.printStackTrace();
        } finally {
            if (fis != null)
                try {
                    fis.close();
                } catch (IOException ex) {
                    ex.printStackTrace();
                }
        }
        os.flush();

        if(model.get("deleteFlag") != null){
            if(model.get("deleteFlag").equals("Y")){
                downFile.delete();
            }
        }
    }
}
