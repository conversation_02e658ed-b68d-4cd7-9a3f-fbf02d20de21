package kr.co.wayplus.travel.service.api;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;

import kr.co.wayplus.travel.mapper.api.NCloudApiMapper;
import kr.co.wayplus.travel.model.AlertMessageLog;
import kr.co.wayplus.travel.model.LogMessageSend;
import kr.co.wayplus.travel.model.NCloudSmsMessage;
import kr.co.wayplus.travel.model.NCloudSmsMessageContainer;
import kr.co.wayplus.travel.model.NCloudSmsMessageResult;
import kr.co.wayplus.travel.util.LoggerUtil;
import kr.co.wayplus.travel.util.NCloudApiUtil;

@Service
public class NCloudApiService {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final NCloudApiMapper nCloudApiMapper;
    private final NCloudApiUtil nCloudApiUtil;
    private final LoggerUtil loggerUtil;

    @Value("${spring.profiles.active}")
    private String ACTIVE_PROFILE;

    @Value("${key.api.ncloud.access}")
    private String NCLOUD_ACCESS_KEY;
    @Value("${key.api.ncloud.secret}")
    private String NCLOUD_SECRET_KEY;
    @Value("${key.api.ncloud.BizMessage}")
    private String NCLOUD_BIZ_MESSAGE_KEY;
    @Value("${key.api.ncloud.SMS}")
    private String NCLOUD_SMS_MESSAGE_KEY;

    public NCloudApiService(NCloudApiMapper nCloudApiMapper, NCloudApiUtil nCloudApiUtil, LoggerUtil loggerUtil) {
        this.nCloudApiMapper = nCloudApiMapper;
        this.nCloudApiUtil = nCloudApiUtil;
        this.loggerUtil = loggerUtil;
    }

	public int selectCountLogMessageSend(HashMap<String, Object> paramMap) {
		return nCloudApiMapper.selectCountLogMessageSend(paramMap);
	}

	public ArrayList<AlertMessageLog> selectListLogMessageSend(HashMap<String, Object> paramMap) {
		return nCloudApiMapper.selectListLogMessageSend(paramMap);
	}

	public AlertMessageLog selectOneLogMessageSend(HashMap<String, Object> paramMap) {
		return nCloudApiMapper.selectOneLogMessageSend(paramMap);
	}

    public HashMap<String, Object> sendSingleSmsMessage(NCloudSmsMessage smsMessage, String requestUserId) {
        HashMap<String, Object> resultMap = new HashMap<>();
        Gson gson = new Gson();

        NCloudSmsMessageContainer smsMessageContainer = new NCloudSmsMessageContainer();
        try{
            List<NCloudSmsMessage> messageList = new ArrayList<>();
            messageList.add(smsMessage);

            String apiUrl = "https://sens.apigw.ntruss.com/sms/v2";
            nCloudApiUtil.setApiBaseUrlAndFormat(apiUrl, "JSON");
            if ( smsMessage.getMessageType() != null ) {
                smsMessageContainer.setType(smsMessage.getMessageType());
            } else {
                smsMessageContainer.setType("SMS");
            }
            smsMessageContainer.setContent("COMM");

            if( ACTIVE_PROFILE.equals("dev") ) {
            	// smsMessageContainer.setFrom("01039456944");
            	smsMessageContainer.setFrom("01039456944");
            } else {
            	smsMessageContainer.setFrom("01039456944");
            }

            smsMessageContainer.setMessages(messageList);

            LogMessageSend log = setLogMessageSend(smsMessage, smsMessageContainer, requestUserId, smsMessage.getSendReservationId(), smsMessage.getSendMessageType());
            nCloudApiMapper.insertSmsSendRequestLog(log);

            NCloudSmsMessageResult result = nCloudApiUtil.sendSmsMessage(smsMessageContainer);
            logger.debug(String.format("전달결과: [ID]: %s, 결과[%s] %s", result.getRequestId(), result.getStatusCode(), result.getStatusName()));

            log.setResultRequestId(result.getRequestId());
            log.setResultStatusCode(result.getStatusCode());
            nCloudApiMapper.updateSmsSendRequestLogResult(log);

            resultMap.put("response", gson.toJson(result));
            resultMap.put("responseCode", "200");
        }catch (Exception e){
            logger.error(String.format("SMS Message Send Error to %s", smsMessage.getTo()));
            logger.error(e.getMessage());
            resultMap.put("response", "메시지 발송 중 오류가 발생했습니다.");
            resultMap.put("responseCode", "404");
        }

        return resultMap;
    }

    public HashMap<String, Object> sendMultipleSmsMessage(List<NCloudSmsMessage> smsMessages, String requestUserId) {
        HashMap<String, Object> resultMap = new HashMap<>();
    
        NCloudSmsMessageContainer smsMessageContainer = new NCloudSmsMessageContainer();
        try {
            String apiUrl = "https://sens.apigw.ntruss.com/sms/v2";
            nCloudApiUtil.setApiBaseUrlAndFormat(apiUrl, "JSON");
            
            // 메시지 타입 설정 (첫 번째 메시지의 타입을 기준으로 설정)
            if (!smsMessages.isEmpty() && smsMessages.get(0).getMessageType() != null) {
                smsMessageContainer.setType(smsMessages.get(0).getMessageType());
            } else {
                smsMessageContainer.setType("SMS");
            }
            
            smsMessageContainer.setContentType("COMM");
            if (ACTIVE_PROFILE.equals("dev")) {
                smsMessageContainer.setFrom("01039456944");
            } else {
                smsMessageContainer.setFrom("01039456944");
            }
            smsMessageContainer.setMessages(smsMessages);
            smsMessageContainer.setContent("강화유니버스 예약관련 안내 메시지");
            
            // 로그 기록
            List<LogMessageSend> logs = new ArrayList<>();
            for (NCloudSmsMessage smsMessage : smsMessages) {
                LogMessageSend log = setLogMessageSend(smsMessage, smsMessageContainer, 
                    requestUserId, smsMessage.getSendReservationId(), 
                    smsMessage.getSendMessageType());
                nCloudApiMapper.insertSmsSendRequestLog(log);
                logs.add(log);
            }

            NCloudSmsMessageResult result = nCloudApiUtil.sendSmsMessage(smsMessageContainer);
            logger.debug(String.format("전달결과: [ID]: %s, 결과[%s] %s", 
                result.getRequestId(), result.getStatusCode(), result.getStatusName()));

            for (LogMessageSend log : logs) {
                log.setResultRequestId(result.getRequestId());
                log.setResultStatusCode(result.getStatusCode());
                nCloudApiMapper.updateSmsSendRequestLogResult(log);
            }
            
            resultMap.put("response", "success");
        } catch (Exception e) {
            logger.error("SMS Message Send Error to multiple recipients");
            logger.error(e.getMessage());
            resultMap.put("response", "메시지 발송 중 오류가 발생했습니다.");
        }
        
        return resultMap;
    }
/*
    public HashMap<String, Object> sendAuthenticationMessage(String serviceType, String sendType, String mobile, String requestUserId, int validTime) {

        HashMap<String, Object> resultMap = new HashMap<>();
        String authMessageTemplateCode = "AUTH0001";
        MessageAuthentication authentication = new MessageAuthentication();
        authentication.setServiceName(serviceType);
        authentication.setMessageType("ncloud.BizMessage");
        authentication.setToName(sendType);
        authentication.setAuthNumber(generateRandomNumberString(6));
        authentication.setSubject("인증번호발신");
        authentication.setSendRequestUserId(requestUserId);
        authentication.setContent(("[WAYPLUS]\n" +
                "인증번호: #{번호}\n" +
                "인증번호를 입력해주세요.").replaceAll("#\\{번호\\}", authentication.getAuthNumber()));
        authentication.setAuthValidTime(String.valueOf(validTime));
        authentication.setContentType(authMessageTemplateCode);
        authentication.setToAddress(mobile);
        authentication.setAuthStatus("active");

        if(sendType.contains("Resend")){
            nCloudApiMapper.updateAuthenticationMessageExpired(authentication);
        }
        nCloudApiMapper.insertAuthenticationMessage(authentication);

        try {
            String apiUrl = "https://sens.apigw.ntruss.com/alimtalk/v2";
            nCloudApiUtil.setApiBaseUrlAndFormat(apiUrl, "JSON");
            NCloudBizMessageContainer messageContainer = new NCloudBizMessageContainer();
            NCloudBizMessage ncloudBizMessage = new NCloudBizMessage();
            ncloudBizMessage.setTo(mobile);
            ncloudBizMessage.setContent(authentication.getContent());
            ncloudBizMessage.setUseSmsFailover(true);
            messageContainer.setTemplateCode(authMessageTemplateCode);
            messageContainer.setMessages(Collections.singletonList(ncloudBizMessage));
            NCloudBizMessageResult result = nCloudApiUtil.sendBizMessage(messageContainer);
            logger.debug(String.format("전달결과: [ID]: %s, 결과[%s] %s", result.getRequestId(), result.getStatusCode(), result.getMessages()));
            authentication.setSendResult(result.getStatusCode());
            authentication.setSendResultMessage(loggerUtil.getObjectListToString(result.getMessages()));
            authentication.setSendResultTime(result.getRequestTime());
            nCloudApiMapper.updateAuthenticationMessageSendResult(authentication);

            resultMap.put("response", result);
            resultMap.put("responseCode", result.getStatusCode());
        } catch (JsonProcessingException | URISyntaxException | RuntimeException e) {
            logger.error("Message Send Error : " + authentication.getToAddress());
            logger.error(e.getMessage());
            resultMap.put("response", "오류가 발생했습니다.");
            resultMap.put("responseCode", "404");
        }

        return resultMap;
    }

    public CommonApiResultSet checkAuthenticationMessageNumber(String serviceName, String authNum, String to, String authKey) {
        CommonApiResultSet result = new CommonApiResultSet();
        try{
            HashMap<String, Object> param = new HashMap<>();
            param.put("serviceName", serviceName);
            param.put("authNumber", authNum);
            param.put("toAddress", to);
            MessageAuthentication authentication = nCloudApiMapper.selectAuthenticationMessageNumber(param);
            if(authentication != null){
                param.put("id", authentication.getId());
                param.put("authResponseId", authKey);
                param.put("authResponseType", "success");
                nCloudApiMapper.updateAuthenticationMessageAuthResult(param);

                result.setResultCode("SUCCESS");
                result.setResultMessage("인증번호가 일치합니다.");
            }else{
                result.setResultCode("FAIL");
                result.setResultMessage("인증번호가 일치하지 않습니다. 인증번호 또는 만료시간을 확인해주세요.");
            }
        }catch (Exception e){
            result.setResultCode("ERROR");
            result.setResultMessage("인증번호 검증 오류");
        }

        return result;
    }

    private String generateRandomNumberString(int digit) {
        String result = "000000";
        int random = (int) (Math.random() * Math.pow(10, digit));
        return StringUtils.leftPad(String.valueOf(random), digit);
    }
*/
    private static LogMessageSend setLogMessageSend(NCloudSmsMessage smsMessage, NCloudSmsMessageContainer smsMessageContainer, String requestUserId, Long sendReservationId, String sendMessageType) {
        LogMessageSend log = new LogMessageSend();
        log.setFrom(smsMessageContainer.getFrom());
        log.setTo(smsMessage.getTo());
        log.setMessageType(smsMessageContainer.getType());  
        log.setSubject(smsMessage.getSubject());
        log.setContent(smsMessage.getContent());
        log.setRequestUserid(requestUserId);
        log.setSendReservationId(smsMessage.getSendReservationId());
        log.setSendMessageType(smsMessage.getSendMessageType());
        return log;
    }



}
