package kr.co.wayplus.travel.base.web;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.OAuthUser;
import kr.co.wayplus.travel.model.SortData;

public class BaseController {
	private final Logger logger = LoggerFactory.getLogger(getClass());

	protected List<SortData> getListOrder(HttpServletRequest request){
		//Long draw = Long.parseLong(request.getParameter("draw"));
		List<SortData> sortOrder = new ArrayList();

		for (int i = 0; i < 20; i++) {
			String column = request.getParameter("columns[" + request.getParameter("order["+i+"][column]") + "][name]");
			String sort = request.getParameter("order["+i+"][dir]");

			if(column == null || sort == null) {
				break;
			}

			sortOrder.add(new SortData(column, sort));
		}
		return sortOrder;
	}

	protected LoginUser getBaseLoginUser() {
		String userEmail = null;

		Object object = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		LoginUser loginedUser = null;
		if( object instanceof LoginUser ) {
			loginedUser = (LoginUser) object;
		}

		return loginedUser;
	}

	protected String getBaseUserEmail() {
		String userEmail = null;

		Object object = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

		if( object instanceof LoginUser ) {
			LoginUser user = (LoginUser) object;
            userEmail = user.getUserEmail();
		} else if( object instanceof OAuthUser ) {
			OAuthUser user = (OAuthUser) object;
			userEmail = user.getUserEmail();
		}

		return userEmail;
	}


	/**
     * 로그인 상태 값을 확인
     * @param session 사용자 세션
     * @return 로그인 상태
     */
	protected boolean checkLogin(HttpSession session) {
        if(!SecurityContextHolder.getContext().getAuthentication().getPrincipal().equals("anonymousUser")){
            return true;
        }else{
            if(session.getAttribute("login") != null){
                session.removeAttribute("login");
            }
            return false;
        }
    }

	protected void appendBindingErrorLog(BindingResult bindingResult) {
        List<ObjectError> errors = bindingResult.getAllErrors();
        for (ObjectError error : errors) {
            logger.error("Parameter Binding Error : " + error.getDefaultMessage());
        }
    }

	protected void setCookies(HttpServletResponse response, String key, String data, int maxAge) {
		Cookie cookie = new Cookie(key, data);
		cookie.setMaxAge(maxAge);
		cookie.setPath("/");
		response.addCookie(cookie);
	}

	protected String getCookies(HttpServletRequest request, String cookieName) {
		Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookieName.equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }
        return null;
	}



}
