package kr.co.wayplus.travel.model;

import java.util.ArrayList;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@Data
public class Contents extends CommonDataSet implements Comparable<Contents> {
	private int rownum;

	private Integer contentId;
	private Integer upperContentId;
	private Integer menuId;
	private String contentName;
	private String exposeType;
	private String contentType;
	private String contentSubType;	//메뉴 내용
	private String contentUrl;	//메뉴 내용
	private Integer contentSort;
	private String contentDesc;
	private String hideYn;
	private String useYn;

	private Boolean isContentUpperId;

	private ArrayList<Contents> listChildContentL; //하위 메뉴 목록(재귀)

	public Contents addContentId(Integer contentId) {
		this.contentId = contentId;
		return this;
	}
	public Contents addContentSort(Integer contentSort) {
		this.contentSort = contentSort;
		return this;
	}
	public Contents addUpperContentId(Integer upperMenuId) {
		this.upperContentId = upperMenuId;
		return this;
	}
	public Contents addIsUpperContentId(Boolean isContentUpperId) {
		this.isContentUpperId = isContentUpperId;
		return this;
	}
	@Override
	public int compareTo(Contents o) {
		return this.contentSort.compareTo(o.contentSort);
	}
}
