package kr.co.wayplus.travel.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Reservation extends CommonDataSet {
	private Long id; // 고유번호
	private String userEmail; // 사용자ID(이메일ID)
	private String userName; // 고객 이름(비회원 직접입력, 회원 user 테이블)
	private String userMobile; // 고객 연락처(비회원 직접입력, 회원 user 테이블)
	private String productSerial; // 상품번호(없을수 있음)
	private Long productTourId; // 상품고유번호
	private Integer priceOptionId;
	private String comment; // 코멘트
	private String applyCode; // 접수상태코드(0:접수, 1:접수확인,2:답변완료)
	private String reservationCode; // 예약상태코드
	private String reservationType; // 예약상태명
	private String cancelYn; // 취소구분
	private String cancelCode; // 취소코드
	private String groupJson; // 단체
	private String berthType; // 숙소_종류
	private String berthJson; // 숙소
	private String travelSchedule;
	private String travelScheduleJson; // 여행_스케줄(json)
	private String airTypeRequest; // 항공 종류 요청(0 : 왕복, 1 : 출발만, 2 : 복귀만, 3 : 요청안함)
	private String airScheduleJson; // 항공_스케줄(json)
	private String vehicleType; // 차량_종류
	private Integer vehicleCount; // 차량_대수
	private String vehicleJson; // 차량
	private Long usePoint; // 사용포인트
	private Integer userPointUsedLogId; // 사용포인트
	private Long totalAmount; // 총가격

	private String orderDate; // 주문일자
	private String optionTime; // 상품판매금액

	// 추가되는 부분
	private String productThumbnail; //
	private String priceOptionJson;
	private String optionName;
	private String membersJson;
	private String customerType;

	private String orderType;
	private String payMoid;
	private String payMethod;

	private String tid;

	private Integer categoryId;
	private Integer pickCapacity; // 예약한 재고수량
	private String categoryTitle;
	private Integer fileId;
	private String meetingTime; // 미팅시간
	private String meetingPlace; // 미팅장소
	private String guideName; // 인솔자 이름
	private String guideTel; // 인솔자 연락처

	private String flagDepartureYn; // 마감_출발
	private String flagPriceYn; // 마감_가격
	private String flagHotelYn; // 마감_호텔
	private String flagAirlineYn; // 마감_항공
	private String flagSchedule; // 마감_일정
	private String flagLeaderYn; // 마감_인솔자
	private String flagGuideYn; // 마감_가이드
	private String flagCloseYn; // 마감_종료
	private String flagSettlement1Yn; // 마감_종료
	private String flagSettlement2Yn; // 마감_종료

	// virtual column
	private String fid; // formated id
	private String productTitle;
	private String productType;
	private String productStayType;
	private int productCount;
	private String travelScheduleDt;
	private String reservationCodeName;
	private String cancelCodeName;
	private String userJoinType;
	private String groupId; // 단체 ID
	private String groupName; // 단체 이름
	private Integer[] optionId, productPrice;
	private Integer maxCapacity // 전체재고
			, productPriceParam, maxQuantity, totalOrderCount, orderCount // 전체주문
			, specialQuantity, specialCapacity // 특가재고
			, specialOrderCount // 특가주문
			, orderPrice, specialOrderPrice, specialOptionId, specialPrice, consecuriveDiscountAmount // 연박할인
			, extraPersonDefualtCharge // 추가인원 1박당
			, extraPersonConsecuriveCharge // 추가인원 연박
			, specialConsecuriveDiscountAmount, specialExtraPersonDefualtCharge, specialExtraPersonConsecuriveCharge,
			originPickPeople, changePickPeople;
	private String rsvPossible // 전체 예약가능 여부
			, specialRsvPossible // 특가 예약가능 여부
			, date, travelDate, specialOptionOneCode, specialOptionName, optionOneCode, startDate, endDate,
			policyInventory, isHaveSpecialPrice;

	// 빈스용
	private Long generalPrice;
	private String subscribeYn;

	private String menuType;
	private String menuSubType;

	private long amtT;
	private long amtG;
	private long amtA;
	private long amtD;
	private long amtB;

	private UserCustomerOrder orderInfo;
	private ArrayList<UserCustomerOrderList> orderList;
	private ProductInfo productInfo;

	// 청풍용
	private Integer pickPeopleCount, year, month;
	private int menuId;
	List<HashMap<String, Object>> pickPriceOptionList;
	private String pickPriceOptionInfoJson;
	private String isGroup;
	private int isRestDate; // 0: 일반일, 1: 휴가일

	public Reservation() {
		reservationCode = "";
		userName = "";
		productTitle = "";
	}

	public Reservation addOrderType(String orderType) {
		this.orderType = orderType;
		return this;
	}

	public Reservation addCreateId(String userEmail) {
		// this.userEmail = userEmail;
		setCreateId(userEmail);
		return this;
	}

	public Reservation addUserEmail(String userEmail) {
		this.userEmail = userEmail;
		return this;
	}

	public Reservation addPayMoid(String moid) {
		this.payMoid = moid;
		return this;
	}

	public Reservation addId(Long id) {
		this.id = id;
		return this;
	}

	public Reservation addFileId(Integer fileId) {
		this.fileId = fileId;
		return this;
	}

	public Reservation addOrderDate(String orderDate) {
		this.orderDate = orderDate;
		return this;
	}

	public Reservation addOptionTime(String optionTime) {
		this.optionTime = optionTime;
		return this;
	}

	public Reservation addProductSerial(String productSerial) {
		this.productSerial = productSerial;
		return this;
	}

	public Reservation addProductTourId(Long productTourId) {
		this.productTourId = productTourId;
		return this;
	}

	public Reservation addPriceOptionId(Integer priceOptionId) {
		this.priceOptionId = priceOptionId;
		return this;
	}

	public Reservation addProductCount(int productCount) {
		this.productCount = productCount;
		return this;
	}

	public Reservation addTotalAmount(long totalAmount) {
		this.totalAmount = totalAmount;
		return this;
	}

	public Reservation addReservationCode(String code) {
		this.reservationCode = code;
		return this;
	}

	public Reservation addPickCapacity(Integer pickCapacity) {
		this.pickCapacity = pickCapacity;
		return this;
	}
}
