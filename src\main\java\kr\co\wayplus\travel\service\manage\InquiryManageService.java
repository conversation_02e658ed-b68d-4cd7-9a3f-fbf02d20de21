package kr.co.wayplus.travel.service.manage;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import kr.co.wayplus.travel.model.BoardContents;
import kr.co.wayplus.travel.model.InquiryAttachFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import kr.co.wayplus.travel.mapper.manage.AlertManageMapper;
import kr.co.wayplus.travel.mapper.manage.InquiryManageMapper;
import kr.co.wayplus.travel.model.InquiryCategory;
import kr.co.wayplus.travel.model.InquiryContent;
import kr.co.wayplus.travel.model.NCloudSmsMessage;
import kr.co.wayplus.travel.model.SmsPolicy;
import kr.co.wayplus.travel.service.api.NCloudApiService;

@Service
public class InquiryManageService {
    private final Logger logger = LoggerFactory.getLogger(getClass());

	private final UserManageService userManageService;
	private final NCloudApiService nCloudApiService;
	private final AlertManageMapper alertManageMapper;
    private final InquiryManageMapper mapper;

    public InquiryManageService(InquiryManageMapper mapper, UserManageService userManageService, NCloudApiService nCloudApiService, AlertManageMapper alertManageMapper) {
        this.nCloudApiService = nCloudApiService;
        this.alertManageMapper = alertManageMapper;
        this.userManageService = userManageService;
        this.mapper = mapper;
    }

    public ArrayList<HashMap<String, Object>> selectListCountInquiryContentByCalendar(HashMap<String, Object> param) {
    	return mapper.selectListCountInquiryContentByCalendar(param);
    }
    public ArrayList<HashMap<String, Object>> selectListInquiryContentByCheckList(HashMap<String, Object> param) {
		return mapper.selectListInquiryContentByCheckList(param);
    }
//	<!--################################### InquiryContent ###################################-->
	public int selectCountInquiryContent(HashMap<String, Object> paramMap) {
		return mapper.selectCountInquiryContent(paramMap);
	}
	public int selectCountInquiryContent(InquiryContent ic) {
		return mapper.selectCountInquiryContent(ic);
	}
	public ArrayList<InquiryContent> selectListInquiryContent(HashMap<String, Object> paramMap) {
		return mapper.selectListInquiryContent(paramMap);
	}
//	public ArrayList<InquiryContent> selectListInquiryContent(InquiryContent ic) {
//		return mapperWayplusManage.selectListInquiryContent(bc);
//	}
	public InquiryContent selectOneInquiryContent(HashMap<String, Object> paramMap) {
		return mapper.selectOneInquiryContent(paramMap);
	}
	public ArrayList<HashMap<String, Object>> selectListInquiryCountStatusType(HashMap<String, Object> paramMap) {
		return mapper.selectListInquiryCountStatusType(paramMap);
	}
	public void saveInquiryContent(InquiryContent ic) throws SQLException {
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("id", ic.getId());

		if( this.selectCountInquiryContent(paramMap) == 0) {
			mapper.insertInquiryContent(ic);
		} else {
			mapper.updateInquiryContent(ic);
		}
	}
	public void insertInquiryContent(InquiryContent ic) throws SQLException {
		mapper.insertInquiryContent(ic);
	}
	public void updateInquiryContent(InquiryContent ic) throws SQLException {
		mapper.updateInquiryContent(ic);
		sendAdminReplySms(ic);
	}

	public void sendAdminReplySms(InquiryContent ic) {
		HashMap<String, Object> smsPolicyParams = new HashMap<>();
		smsPolicyParams.put("policySendScheduleType", "sendAdminReply");
		smsPolicyParams.put("useYn", "Y");
		smsPolicyParams.put("deleteYn", "N");

		SmsPolicy smsPolicy = alertManageMapper.selectOneSmsPolicy(smsPolicyParams);

		if ( smsPolicy != null && smsPolicy.getUserSendYn().equals("Y") && ic.getApplyCode().equals("2") ) {
			//답변완료 문자보냄.
			ArrayList<NCloudSmsMessage> messages = new ArrayList<>();
			NCloudSmsMessage msg1 = new NCloudSmsMessage();
			HashMap<String, Object> userParam = new HashMap<>();
			userParam.put("userEmail", ic.getUserEmail());
			msg1.setTo(userManageService.getUserDetail(userParam).getUserMobile().replaceAll("-", ""));
			msg1.setContent(String.format(
							"안녕하세요! 강화유니버스입니다.\r\n" + //
							"남겨주신 문의에 답변이 등록되었습니다. 확인 부탁드립니다. 감사합니다:)"
						));
			msg1.setMessageType("LMS");
			msg1.setSendMessageType("sendAdminReply");
			messages.add(msg1);

			if (messages.size() > 0) {
				nCloudApiService.sendMultipleSmsMessage(messages, "admin");
			}
		}
		
	}

	public void restoreInquiryContent(InquiryContent ic) throws SQLException {
		mapper.restoreInquiryContent(ic);
	}
	public void deleteInquiryContent(InquiryContent ic) throws SQLException {
		mapper.deleteInquiryContent(ic);
	}
//	<!--################################### InquiryCategory ###################################-->
	public int selectCountInquiryCategory(HashMap<String, Object> paramMap) {
		return mapper.selectCountInquiryCategory(paramMap);
	}
	public InquiryCategory selectOneInquiryCategory(HashMap<String, Object> paramMap) {
		return mapper.selectOneInquiryCategory(paramMap);
	}
	public ArrayList<InquiryCategory> selectListInquiryCategory(HashMap<String, Object> paramMap) {
		return mapper.selectListInquiryCategory(paramMap);
	}
	public void insertInquiryCategory(InquiryCategory ic) throws SQLException {
		mapper.insertInquiryCategory(ic);
	}
	public void updateInquiryCategory(InquiryCategory ic) throws SQLException {
		mapper.updateInquiryCategory(ic);
	}
	public void restoreInquiryCategory(InquiryCategory ic) throws SQLException {
		mapper.restoreInquiryCategory(ic);
	}
	public void deleteInquiryCategory(InquiryCategory ic) throws SQLException {
		mapper.deleteInquiryCategory(ic);
	}
	public List<InquiryAttachFile> selectOneInquiryContentFiles(HashMap<String, Object> paramMap) throws SQLException {
		return mapper.selectOneInquiryContentFiles(paramMap);
	}
}
