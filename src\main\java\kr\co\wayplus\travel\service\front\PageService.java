package kr.co.wayplus.travel.service.front;

import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import org.apache.ibatis.binding.BindingException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.LoadState;

import kr.co.wayplus.travel.mapper.front.PageMapper;
import kr.co.wayplus.travel.mapper.manage.AlertManageMapper;
import kr.co.wayplus.travel.model.BannerCategory;
import kr.co.wayplus.travel.model.CoachInfo;
import kr.co.wayplus.travel.model.CodeItem;
import kr.co.wayplus.travel.model.InquiryCategory;
import kr.co.wayplus.travel.model.InquiryContent;
import kr.co.wayplus.travel.model.MainBannerImage;
import kr.co.wayplus.travel.model.MainNoticePopup;
import kr.co.wayplus.travel.model.MenuUser;
import kr.co.wayplus.travel.model.NCloudSmsMessage;
import kr.co.wayplus.travel.model.PlaceSpot;
import kr.co.wayplus.travel.model.Policy;
import kr.co.wayplus.travel.model.ProductInfo;
import kr.co.wayplus.travel.model.ProductPriceOption;
import kr.co.wayplus.travel.model.Reservation;
import kr.co.wayplus.travel.model.SettingAllianceContents;
import kr.co.wayplus.travel.model.SettingAwardsContents;
import kr.co.wayplus.travel.model.SettingCompanyInfo;
import kr.co.wayplus.travel.model.SettingNavbar;
import kr.co.wayplus.travel.model.SmsPolicy;
import kr.co.wayplus.travel.model.SmsRecivedUser;
import kr.co.wayplus.travel.model.UserCustomerCart;
import kr.co.wayplus.travel.model.UserCustomerOrder;
import kr.co.wayplus.travel.model.UserCustomerOrderHistory;
import kr.co.wayplus.travel.model.UserCustomerOrderList;
import kr.co.wayplus.travel.model.UserCustomerPayment;
import kr.co.wayplus.travel.util.ReservationUtil;
import kr.co.wayplus.travel.service.api.NCloudApiService;
import kr.co.wayplus.travel.service.user.UserService;
import kr.co.wayplus.travel.mapper.admin.AdminMapper;

@Service
public class PageService {

	private final NCloudApiService nCloudApiService;
	private final ProductService productService;
	private final UserService userService;
    private final PageMapper mapper;
	private final AlertManageMapper alertManageMapper;
	private final AdminMapper adminMapper;
	private final ReservationUtil reservationUtil;

    @Autowired
    public PageService(PageMapper mapper, ProductService productService
		, UserService userService, NCloudApiService nCloudApiService
		, AlertManageMapper alertManageMapper, ReservationUtil reservationUtil, AdminMapper adminMapper) {
        this.mapper = mapper;
        this.productService = productService;
		this.alertManageMapper = alertManageMapper;
		this.reservationUtil = reservationUtil;
		this.nCloudApiService = nCloudApiService;
		this.userService = userService;
		this.adminMapper = adminMapper;
    }

    public SettingCompanyInfo getUserPageFooterInfo() {
        return mapper.selectUserPageFooterInfo();
    }

    public ArrayList<MainNoticePopup> getUserPageNoticePopupList(HashMap<String, Object> param) {
        return mapper.selectMainNoticePopupLayerList(param);
    }

    public ArrayList<MainNoticePopup>  getUserPageNoticeBarList(HashMap<String, Object> param) {
        return mapper.selectMainNoticePopupBarList(param);
    }

    public SettingNavbar getNavbar() {
        return mapper.selectNavbar();
    }

    public ArrayList<MenuUser> getUserMenuList() {
        return mapper.selectUserMenuList();
    }
//	<!--################################### MenuUser ###################################-->
	public ArrayList<MenuUser> selectListMenuUser(HashMap<String, Object> param) {
		return mapper.selectListMenuUser(param);
	}
	public MenuUser selectOneMenuUser(HashMap<String, Object> param) {
		return mapper.selectOneMenuUser(param);
	}

	public MenuUser getMenuUserFullMenuUrl(String uri) {
    	HashMap<String, Object> param = new HashMap<String, Object>();
    	param.put("fullMenuUrl", uri);
    	return mapper.selectOneMenuUser(param);
    }

    public MenuUser getMenuUserFullMenuUri(String uri) {
    	HashMap<String, Object> param = new HashMap<String, Object>();
    	param.put("fullMenuUri", uri);
    	return mapper.selectOneMenuUser(param);
    }

    public ArrayList<MenuUser> getUserMenuTreeList() {
    	//ArrayList<MenuUser> list = mapper.selectUserMenuList();
    	ArrayList<MenuUser> listTopMenu = new ArrayList<MenuUser>(); /*최상위 메뉴 뽑기*/
		HashMap<String,MenuUser> _mapByMenuId = new HashMap<String, MenuUser>(); /*색인용 Map */
//		HashMap<String,MenuUser> _mapByFullMenuUrl = new HashMap<String, MenuUser>(); /*색인용 Map */
		HashMap<String,ArrayList<MenuUser>> _mapUpper = new HashMap<String, ArrayList<MenuUser>>(); /*상위 메뉴 색인용 Map*/
		ArrayList<MenuUser> list = mapper.selectUserMenuList();

		for (MenuUser _menu : list) {
			_mapByMenuId.put(_menu.getMenuId().toString(), _menu);
//			_mapByFullMenuUrl.put( "/"+_menu.getMenuType().toString() + _menu.getFullMenuUrl().toString(), _menu);

			if(_menu.getUpperMenuId() != null) {
				ArrayList<MenuUser> subList = null;
				if( _mapUpper.containsKey(_menu.getUpperMenuId().toString()) ) {
					subList = _mapUpper.get( _menu.getUpperMenuId().toString() );
				} else {
					subList = new ArrayList<MenuUser>();
				}
				_mapUpper.put( _menu.getUpperMenuId().toString(), subList );
				subList.add( _menu );
			}
		}

		for (MenuUser _tmenu : list) {
			Long key = _tmenu.getMenuId();

			if(key != null)
    			if( _mapUpper.containsKey( key.toString() ) ) {
    				ArrayList<MenuUser> menuList = _mapUpper.get( key.toString());
    				Collections.sort( menuList );
    				_tmenu.setListChildMenuL( menuList );
    			}
		}

		for (MenuUser _menu : list) {
			if(_menu.getUpperMenuId() == null) {
				listTopMenu.add(_menu);
			}
		}
		Collections.sort( listTopMenu );

//		GrobalVariable.setGbMenuUserMapByMenuId(_mapByMenuId);
//		GrobalVariable.setGbMenuUserMapByFullMenuUrl(_mapByFullMenuUrl);
//		GrobalVariable.setGbMenuUserTreeTop(listTopMenu);

		//retMap.put("data",listTopMenu);
		//retMap.put("list",list);

        return listTopMenu;
    }

//	<!--################################### Policy ###################################-->
	public Policy selectOnePolicy(HashMap<String, Object> paramMap) {
		return mapper.selectOnePolicy(paramMap);
	}
	public Policy selectOnePolicyByPolicyType( String policyType ) {
		HashMap<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("isLastPolicy", true);
		paramMap.put("policyType", policyType);
		return this.selectOnePolicy(paramMap);
	}

//	<!--################################### Inquiry ###################################-->
	public int selectCountInquiryContent(HashMap<String, Object> paramMap) {
		return mapper.selectCountInquiryContent(paramMap);
	}
	public ArrayList<InquiryContent> selectListInquiryContent(HashMap<String, Object> paramMap) {
		return mapper.selectListInquiryContent(paramMap);
	}
	public InquiryContent selectOneInquiryContent(HashMap<String, Object> paramMap) {
		return mapper.selectOneInquiryContent(paramMap);
	}
	public void saveInquiryContent(InquiryContent ic) throws SQLException {
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("id", ic.getId());

		if( this.selectCountInquiryContent(paramMap) == 0) {
			mapper.insertInquiryContent(ic);
		} else {
			mapper.updateInquiryContent(ic);
		}
	}
	public void insertInquiryContent(InquiryContent ic) throws SQLException {
		mapper.insertInquiryContent(ic);

	}
	public void sendCustomerQnaSms() {
		
		//문의확정 문자보냄.
		HashMap<String, Object> smsPolicyParams = new HashMap<>();
		smsPolicyParams.put("policySendScheduleType", "getCustomerQna");
		smsPolicyParams.put("useYn", "Y");
		smsPolicyParams.put("deleteYn", "N");

		SmsPolicy smsPolicy = alertManageMapper.selectOneSmsPolicy(smsPolicyParams);
		if ( smsPolicy != null ) {
			ArrayList<NCloudSmsMessage> messages = new ArrayList<>();
			if ( "Y".equals(smsPolicy.getUseYn())
				&& "Y".equals(smsPolicy.getAdminSendYn()) ) {
					NCloudSmsMessage msg2 = new NCloudSmsMessage();

					HashMap<String, Object> recivedParam = new HashMap<>();
					recivedParam.put("useYn", "Y");
					recivedParam.put("qnaYn", "Y");
					ArrayList<SmsRecivedUser> recivedUsers = adminMapper.selectListSmsRecivedUser(recivedParam);

					for (SmsRecivedUser recivedUser : recivedUsers) {
						HashMap<String, Object> userParam = new HashMap<>();
						userParam.put("role", "ADMIN");
						userParam.put("userEmail", recivedUser.getUserEmail());

						msg2.setTo(userService.selectUserListByRole(userParam).get(0).getUserMobile().replaceAll("-", ""));
						msg2.setContent("홈페이지에 문의가 등록되었습니다. ");
						msg2.setMessageType("LMS");
						msg2.setSendMessageType("getCustomerQna");
						messages.add(msg2);
					}
			}

			if (messages.size() > 0) {
				nCloudApiService.sendMultipleSmsMessage(messages, "admin");
			}
		}
	}
	public void updateInquiryContent(InquiryContent ic) throws SQLException {
		mapper.updateInquiryContent(ic);
	}
	public void deleteInquiryContent(InquiryContent ic) throws SQLException {
		mapper.deleteInquiryContent(ic);
	}
//	<!--################################### InquiryCategory ###################################-->
	public int selectCountInquiryCategory(HashMap<String, Object> paramMap) {
		return mapper.selectCountInquiryCategory(paramMap);
	}
	public InquiryCategory selectOneInquiryCategory(HashMap<String, Object> paramMap) {
		return mapper.selectOneInquiryCategory(paramMap);
	}
	public ArrayList<InquiryCategory> selectListInquiryCategory(HashMap<String, Object> paramMap) {
		return mapper.selectListInquiryCategory(paramMap);
	}
//	<!--################################### CodeItem ###################################-->
	public int selectCountCodeItem(HashMap<String, Object> paramMap) {
		return mapper.selectCountCodeItem(paramMap);
	}
	public int selectCountCodeItem(CodeItem ci) {
		return mapper.selectCountCodeItem(ci);
	}
	public ArrayList<CodeItem> selectListCodeItem(HashMap<String, Object> paramMap) {
		return mapper.selectListCodeItem(paramMap);
	}
	public ArrayList<CodeItem> selectListCodeItem(CodeItem ci) {
		return mapper.selectListCodeItem(ci);
	}
	public CodeItem selectOneCodeItem(HashMap<String, Object> paramMap) {
		return mapper.selectOneCodeItem(paramMap);
	}

	public ArrayList<MainBannerImage> selectListMainBannerImage(HashMap<String, Object> param) {
		return mapper.selectListMainBannerImage(param);
	}

	public ArrayList<MainBannerImage> getListBannerImage(Long menuId) {
		HashMap<String, Object> param = new HashMap<String, Object> ();
		param.put("useYn", "Y");
		param.put("menuId", menuId);

		if( menuId != null ) {
			param.put("bannerType", "menuBanner");
		} else {
			param.put("bannerType", "MAIN");
		}

		return this.selectListMainBannerImage(param);
	}
	public ArrayList<MainBannerImage> getListBannerImage(Long menuId, String bannerType) {
		HashMap<String, Object> param = new HashMap<String, Object> ();
		param.put("useYn", "Y");
		param.put("menuId", menuId);
		param.put("bannerType", bannerType);

		return this.selectListMainBannerImage(param);
	}




//	<!--################################### UserCustomerCart ###################################-->
	public int selectCountUserCustomerCart(HashMap<String, Object> paramMap)  {
		return mapper.selectCountUserCustomerCart(paramMap);
	}
	public int selectCountUserCustomerCartProductCount(HashMap<String, Object> paramMap)  {
		return mapper.selectCountUserCustomerCartProductCount(paramMap);
	}
	public ArrayList<UserCustomerCart> selectListUserCustomerCart(HashMap<String, Object> paramMap) {
		return mapper.selectListUserCustomerCart(paramMap);
	}
	public UserCustomerCart selectOneUserCustomerCart(HashMap<String, Object> paramMap) {
		return mapper.selectOneUserCustomerCart(paramMap);
	}
	public void insertUserCustomerCart(UserCustomerCart bs) throws SQLException {
		mapper.insertUserCustomerCart(bs);
	}
	public void updateUserCustomerCart(UserCustomerCart bs) throws SQLException {
		mapper.updateUserCustomerCart(bs);
	}
	@Transactional
	public void deleteUserCustomerCart(UserCustomerCart bs) throws SQLException {
		mapper.deleteUserCustomerCart(bs);
	}

//	<!--################################### Reservation ###################################-->
	public int selectCountReservation(HashMap<String, Object> paramMap) {
		return mapper.selectCountReservation(paramMap);
	}
	public int selectCountReservation(Reservation ic) {
		return mapper.selectCountReservation(ic);
	}
	public ArrayList<Reservation> selectListReservation(HashMap<String, Object> paramMap) {
		return mapper.selectListReservation(paramMap);
	}
//	public ArrayList<Reservation> selectListReservation(Reservation ic) {
//		return mapperWayplusManage.selectListReservation(bc);
//	}
	public Reservation selectOneReservation(HashMap<String, Object> paramMap) {
		return mapper.selectOneReservation(paramMap);
	}
	public ArrayList<HashMap<String, Object>> selectListInquiryCountStatusType(HashMap<String, Object> paramMap) {
		return mapper.selectListInquiryCountStatusType(paramMap);
	}
	public void saveReservation(Reservation ic) throws SQLException {
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("id", ic.getId());

		if( this.selectCountReservation(paramMap) == 0) {
			mapper.insertReservation(ic);
		} else {
			mapper.updateReservation(ic);
		}
	}
	@Transactional(rollbackFor = {BindingException.class,RuntimeException.class,Exception.class})
	public void insertReservation(Reservation ic) throws SQLException {
		mapper.insertReservation(ic);
	}
	@Transactional
	public void updateReservation(Reservation ic) throws SQLException {
		mapper.updateReservation(ic);
	}
	@Transactional
	public void restoreReservation(Reservation ic) throws SQLException {
		mapper.restoreReservation(ic);
	}
	@Transactional
	public void deleteReservation(Reservation ic) throws SQLException {
		mapper.deleteReservation(ic);
	}

//	<!--################################### PlaceSpot ###################################-->
    public int selectCountPlaceSpot(HashMap<String, Object> paramMap) {
    	return mapper.selectCountPlaceSpot(paramMap);
    }
    public ArrayList<PlaceSpot> selectListPlaceSpot(HashMap<String, Object> paramMap) {
    	return mapper.selectListPlaceSpot(paramMap);
    }
    public ArrayList<PlaceSpot> selectListPlaceSpotWithDistanceSearch(Double ptA, Double ptB, Integer distance ) {
    	HashMap<String, Object> paramMap = new HashMap<String, Object>();
    	paramMap.put("pointA", ptA);
    	paramMap.put("pointB", ptB);
    	paramMap.put("distance", distance);

    	return mapper.selectListPlaceSpot(paramMap);
    }
	public PlaceSpot selectOnePlaceSpot(HashMap<String, Object> paramMap) {
		return mapper.selectOnePlaceSpot(paramMap);
	}

	public ArrayList<SettingAllianceContents> selectListAllianceContents(HashMap<String, Object> param) {
		return mapper.selectListAllianceContents(param);
	}
	//	<!--################################### UserCustomerPayment( ###################################-->
	@Transactional(rollbackFor = {BindingException.class,Exception.class})
	public void insertUserCustomerPayment(UserCustomerPayment data) throws SQLException {
		mapper.insertUserCustomerPayment(data);
	}
//	<!--################################### UserCustomerOrder( ###################################-->
	public int selectCountUserCustomerOrder(HashMap<String, Object> paramMap) {
		return mapper.selectCountUserCustomerOrder(paramMap);
	}

	public ArrayList<UserCustomerOrder> selectListUserCustomerOrder(HashMap param) {
		return mapper.selectListUserCustomerOrder(param);
	}
	public UserCustomerOrder selectOneUserCustomerOrder(HashMap param) {
		return mapper.selectOneUserCustomerOrder(param);
	}
	@Transactional(rollbackFor = {BindingException.class, Exception.class})
	public void insertUserCustomerOrder(UserCustomerOrder ord)  throws SQLException {
		mapper.insertUserCustomerOrder(ord);
	}
	public void updateUserCustomerOrder(UserCustomerOrder ord) throws SQLException {
		mapper.updateUserCustomerOrder(ord);
	}
//	<!--################################### UserCustomerOrderList( ###################################-->
	public int selectCountUserCustomerOrderList(HashMap<String, Object> paramMap) {
		return mapper.selectCountUserCustomerOrderList(paramMap);
	}
	public int selectSummryUserCustomerOrderList(HashMap<String, Object> paramMap) {
		return mapper.selectSummryUserCustomerOrderList(paramMap);
	}

	public ArrayList<UserCustomerOrderList> selectListUserCustomerOrderList(HashMap param) {
		return mapper.selectListUserCustomerOrderList(param);
	}
	public UserCustomerOrderList selectOneUserCustomerOrderList(HashMap param) {
		return mapper.selectOneUserCustomerOrderList(param);
	}
	@Transactional(rollbackFor = {BindingException.class,Exception.class})
	public void insertUserCustomerOrderList(UserCustomerOrderList ord)  throws SQLException {
		mapper.insertUserCustomerOrderList(ord);
	}
	@Transactional
	public void updateUserCustomerOrderList(UserCustomerOrderList ord)  throws SQLException {
		mapper.updateUserCustomerOrderList(ord);
	}
	public void updateListUserCustomerOrderList(ArrayList<UserCustomerOrderList> _ucol) throws SQLException {
		for (UserCustomerOrderList item : _ucol) {
			mapper.updateUserCustomerOrderList(item);
		}
	}
//	<!--################################### UserCustomerOrderHistory( ###################################-->
	public int selectCountUserCustomerOrderHistory(HashMap<String, Object> paramMap) {
		return mapper.selectCountUserCustomerOrderHistory(paramMap);
	}
	public ArrayList<UserCustomerOrderHistory> selectListUserCustomerOrderHistory(HashMap param) {
		return mapper.selectListUserCustomerOrderHistory(param);
	}
	public UserCustomerOrderHistory selectOneUserCustomerOrderHistory(HashMap param) {
		return mapper.selectOneUserCustomerOrderHistory(param);
	}
	@Transactional
	public void insertUserCustomerOrderHistory(UserCustomerOrderHistory ord)  throws SQLException {
		mapper.insertUserCustomerOrderHistory(ord);
	}
//	<!--################################### others ###################################-->
	public ArrayList<CoachInfo> getCoachList() {
		return mapper.selectCoachList();
	}

	public ArrayList<MenuUser> selectListSubMenuUser(HashMap<String, Object> param) {
		return mapper.selectListSubMenuUser(param);
	}

	public ArrayList<PlaceSpot> selectListPlace(HashMap<String, Object> param) {
		return mapper.selectListPlace(param);
	}

	public int selectListCountPlace(HashMap<String, Object> param) {
		return mapper.selectListCountPlace(param);
	}
//	<!--################################### AwardsContents ###################################-->
	public int selectCountAwardsContents(HashMap<String, Object> paramMap) {
		return mapper.selectCountAwardsContents(paramMap);
	}

	public ArrayList<SettingAwardsContents> selectListAwardsContents(HashMap<String, Object> paramMap) {
		return mapper.selectListAwardsContents(paramMap);
	}

    public List<MenuUser> selectListMenuUserUpperMenuId(HashMap<String, Object> param) {
        return mapper.selectListMenuUserUpperMenuId(param);
    }

	public List<ProductInfo> selectListProductByUpperMenuId(HashMap<String, Object> param) {
		return mapper.selectListProductByUpperMenuId(param);
	}



	//청풍용. PriceOptionJson 내 구매 가능 수량 비교
	//요구json형식 예시 -> {"id":"priceOptionJson","data":{"option-0":{"0":"169","1":"성인","2":2000,"3":"1", "4":"e403kdw"}}}
	public HashMap<String, Object> calcStayPriceOption(Reservation rData) throws Exception {
		HashMap<String, Object> returnMap = new HashMap<>();
		ArrayList<ArrayList<String>> listImpossibleOptionInfo = new ArrayList<>();
		int pendingStock = 0;
		if( rData.getProductStayType() != null && rData.getProductStayType().equals("room") ) {
			pendingStock = 1;
		} else {
			pendingStock = rData.getPickPeopleCount();
		}
	
		ArrayList<ArrayList<String>> listImpossibleSpecialPriceInfo = new ArrayList<>();
		HashMap<String, Object> rsvMap = new HashMap<>();
		rsvMap.put("startDate", rData.getStartDate());
		rsvMap.put("endDate", rData.getEndDate());
		rsvMap.put("productSerial", rData.getProductSerial());
		ArrayList<Reservation> reservationList = reservationUtil.selectListCalcReservationForUser(rsvMap);
		if ( rData.getPolicyInventory().equals("1") ) {
			for (Reservation item : reservationList) {
				if ( item.getIsRestDate() == 1 ) {
					returnMap.put("passFlag", false);
					break;
				}

				boolean isSpecialNextStepPass = true;
				ArrayList<String> impossibleOptionInfo = new ArrayList<>();
				//선택한 날짜범위안에 특가일때
				if ( item.getSpecialQuantity() != null &&  item.getSpecialQuantity() > 0) {
					if ( !"remain".equals(item.getSpecialRsvPossible()) ) {
						isSpecialNextStepPass = false;
						returnMap.put("passFlag", true);
						impossibleOptionInfo.add(item.getDate());
						impossibleOptionInfo.add(item.getOptionOneCode());
						listImpossibleSpecialPriceInfo.add(impossibleOptionInfo);
					}
					if ( "remain".equals(item.getSpecialRsvPossible()) && pendingStock > item.getSpecialCapacity() ) {
						isSpecialNextStepPass = false;
						returnMap.put("passFlag", true);
						impossibleOptionInfo.add(item.getDate());
						impossibleOptionInfo.add(item.getOptionOneCode());
						listImpossibleSpecialPriceInfo.add(impossibleOptionInfo);
					}
					//예약재고 불가능 판단
					if ( isSpecialNextStepPass && item.getMaxCapacity()-item.getTotalOrderCount() < pendingStock  ) {
						impossibleOptionInfo.add(item.getOptionName());
						impossibleOptionInfo.add(item.getDate());
						impossibleOptionInfo.add(String.valueOf(item.getMaxCapacity() - item.getTotalOrderCount() < 0 ? 0 : item.getMaxCapacity() - item.getTotalOrderCount()));
						listImpossibleOptionInfo.add(impossibleOptionInfo);
						
						returnMap.put("passFlag", false);
						returnMap.put("optionOneCode", item.getOptionOneCode());
						break;
					}

					//한번에 예약 가능여부 체크
					if ( isSpecialNextStepPass && "remain".equals(item.getSpecialRsvPossible()) && pendingStock > item.getSpecialQuantity()
						&& item.getSpecialQuantity() != 0 ) {
						impossibleOptionInfo.add(item.getOptionName());
						impossibleOptionInfo.add(item.getDate());
						impossibleOptionInfo.add(String.valueOf(item.getSpecialQuantity()));
						listImpossibleOptionInfo.add(impossibleOptionInfo);
		
						returnMap.put("maxQuantityOut", true);
						break;
					}

					
				}
				//선택한 날짜범위안에 정가일때
				if ( "remain".equals(item.getRsvPossible()) ){
					//예약재고 불가능 판단
					if ( item.getMaxCapacity() - item.getTotalOrderCount() < pendingStock  ) {
						impossibleOptionInfo.add(item.getOptionName());
						impossibleOptionInfo.add(item.getDate());
						impossibleOptionInfo.add(String.valueOf(item.getMaxCapacity() - item.getTotalOrderCount() < 0 ? 0 : item.getMaxCapacity() - item.getTotalOrderCount()));
						listImpossibleOptionInfo.add(impossibleOptionInfo);
						returnMap.put("passFlag", false);
						returnMap.put("optionOneCode", item.getOptionOneCode());
						break;
					}

					//한번에 예약 가능여부 체크
					if ( pendingStock > item.getMaxQuantity() && item.getMaxQuantity() != 0 ) {
						impossibleOptionInfo.add(item.getOptionName());
						impossibleOptionInfo.add(item.getDate());
						impossibleOptionInfo.add(String.valueOf(item.getMaxQuantity()));
						listImpossibleOptionInfo.add(impossibleOptionInfo);

						returnMap.put("maxQuantityOut", true);
						break;
					}
				}

				if ( !"remain".equals(item.getRsvPossible()) && !"remain".equals(item.getSpecialRsvPossible()) ) {
					impossibleOptionInfo.add(item.getOptionName());
					impossibleOptionInfo.add(item.getDate());
					impossibleOptionInfo.add(String.valueOf(item.getMaxCapacity() - item.getTotalOrderCount() < 0 ? 0 : item.getMaxCapacity() - item.getTotalOrderCount()));
					listImpossibleOptionInfo.add(impossibleOptionInfo);
					returnMap.put("passFlag", false);
					returnMap.put("optionOneCode", item.getOptionOneCode());
					break;
				} 

				returnMap.put("passFlag", true);
			}
		} else {
			for (Reservation item : reservationList) {
				if ( item.getIsRestDate() == 1 ) {
					returnMap.put("passFlag", false);
					break;
				}
				returnMap.put("passFlag", true);
			}
		}
		

		returnMap.put("listImpossibleOptionInfo", listImpossibleOptionInfo);
		returnMap.put("listImpossibleSpecialPriceInfo", listImpossibleSpecialPriceInfo);

		return returnMap;
	}

	//청풍용 html 가져오기
	public String scrapeSPAContent(String url) {
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(
                new BrowserType.LaunchOptions()
                    .setHeadless(true)
            );

            BrowserContext context = browser.newContext();
            Page page = context.newPage();

            // 페이지 로드
            page.navigate(url);

            // 네트워크 요청이 완료될 때까지 대기
            page.waitForLoadState(LoadState.NETWORKIDLE);

            // 특정 선택자가 로드될 때까지 대기 (필요한 경우 수정)
            page.waitForSelector("#root");

            // HTML 콘텐츠 추출
            String content = page.content();

            // 리소스 정리
            context.close();
            browser.close();

            return content;

        } catch (Exception e) {
            throw new RuntimeException("Failed to scrape content: " + e.getMessage(), e);
        }
    }

    public ArrayList<MainBannerImage> selectListMainBannerImageAndCategory(HashMap<String, Object> param) {
        return mapper.selectListMainBannerImageAndCategory(param);
    }
    public ArrayList<BannerCategory> selectListBannerCategory(HashMap<String, Object> param) {
    	return mapper.selectListBannerCategory(param);
    }

}
