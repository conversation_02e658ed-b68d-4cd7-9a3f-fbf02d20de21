@charset "UTF-8";
/*!
 * Wayplus Waytrip Game
 */
button {
    background: #FFFFFF;
    border: none;
    margin: 0;
    padding: 0;
    cursor: pointer;
}
ul {
    padding: 0;
}
.score-name-box {
    display: flex;
    width: 100%;
    justify-content: space-around;
    margin-bottom: 20px;
}
.balance-game-score-name-box {
    display: flex;
    width: 100%;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 14px;
}
.balance-game-score-name-box p {
    display: grid;
    text-align: center;
    font: 700 16px Pretendard;
    color: #443B36
}
.balance-game-score-name-desc {
    font: 500 12px Pretendard;
    color: #443b36;
}
.balance-game-result-data-search-select {
    max-width:120px;
    width: 100%;
    margin-right: 10px;
    padding-left: 17px;
    font: 400 14px Pretendard;
    color:#443B36;
    border: 1px solid #D9D9D9;
    border-radius: 5px;
    background: url('/images/waytrip/icon/thin_bottom_arrow.svg') no-repeat 88% 51%;
}
.score-main-box {
    display: flex;
    justify-content: space-around;
    height: 100px;
    border-top: 2px solid #443B36;
    border-bottom: 2px solid #443B36
}
.score-main-box li{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    border-right: 1px solid #D9D9D9;
}
.score-box {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    border-right: 1px solid #D9D9D9;
}
.score-box:last-child {
    border: none;
}
.score {
    text-align: center;
    font: 400 14px Pretendard;
    color: #443B36
}

.balance-game-score-main-box {
    border-top: 2px solid #443B36;
    border-bottom: 2px solid #443B36
}
.balance-game-score-box{
    display: flex;
    height: 65px
}
.balance-game-score-box li{
    list-style: none;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    border-right: 1px solid #D9D9D9;
    border-bottom: 1px solid #D9D9D9;
}
.balance-game-score-box li:last-child{
    border-right: none;
}
.balance-game-win-rate-percent-box {
    display:flex;
    justify-content: center;
    width: 100%;
    height: 20px;
}

.game-add-top-main-box {
    width: calc(100% - 220px);
    margin: 0 50px 30px;
    padding: 34px 60px;
    background: #FFFFFF;
    box-shadow: 4px 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    color: #443B36;
}
.game-add-top-inner-box {
    display:flex;
    justify-content:space-between;
    margin-bottom: 30px
}
.game-add-top-title {
    font: normal 500 18px 'Pretendard';
    line-height: 18px;
}
.game-add-btn {
    margin-right: 20px;
    padding:9px 65px 9px 65px;
    color: #FFFFFF;
    font: 700 16px Pretendard;
    background: #443B36;
    border-radius: 5px;
    border: 1px solid #443B36;
    cursor: pointer
}
.game-add-back-btn {
    margin-right: 10px;
    padding:9px 65px 9px 65px;
    color: #FFFFFF;
    font: 700 16px Pretendard;
    background: #443B36;
    border-radius: 5px;
    border: 1px solid #443B36;
    cursor: pointer
}
.game-add-back-bottom-btn {
    padding:9px 65px 9px 65px;
    color: #FFFFFF;
    font: 700 16px Pretendard;
    background: #443B36;
    border-radius: 5px;
    border: 1px solid #443B36;
    cursor: pointer
}
.game-delete-btn {
    padding:9px 65px 9px 65px;
    color: #FFFFFF;
    font: 700 16px Pretendard;
    background: #FF674F;
    border-radius: 5px;
    border: 1px solid #FF674F;
    cursor: pointer
}
.game-add-hr {
    margin-bottom:30px;
    border: 1px solid #443B36
}
.game-result-hr {
    margin-bottom:30px;
    border: 1px solid #D9D9D9
}
.game-add-form-label {
    font: 400 12px Pretendard;
    vertical-align:middle;
    color: #443B36;
}
.game-add-form-title-box {
    display: flex;
    justify-content: center;
    align-items:center;
    margin-bottom:30px;
}
.game-add-form-title {
    min-width:57px;
    margin-right:20px;
    text-align:end;
    font: normal 700 14px 'Pretendard';
}
.game-item-add-main-box {
    width: calc(100% - 220px);
    margin: 0 50px;
    padding: 34px 60px;
    background: #FFFFFF;
    box-shadow: 4px 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 10px;color: #443B36;
}
.game-item-add-title {
    margin-right:10px;
    font: normal 500 18px 'Pretendard';
    line-height: 18px;
}
.game-item-add-desc {
    font:500 14px Pretendard;
    color:#555555
}
.add-place-btn {
    width:100%;
    margin-bottom: 30px;
    padding: 32px 0 32px;
    font: normal 700 16px Pretendard;
    color: #555555;
    border: 1px solid #D9D9D9;
    border-radius: 5px;
    background-color: #FFFFFF;
    cursor: pointer;
}
.game-item-add-btn-box {
    display: flex;
    justify-content: center;
    margin-bottom:30px;
}
.game-item-add-btn-title {
    margin-right: 20px;
    padding:9px 65px 9px 65px;
    color: #FFFFFF;
    font: 700 16px Pretendard;
    background: #443B36;
    border-radius: 5px;
    border: 1px solid #443B36;
    cursor: pointer
}
.ox-quiz-item-inner-box {
    position:relative;
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}
.ox-quiz-item-order-number {
    min-width: 59px;
    margin-right: 30px;
    font:500 16px Pretendard;
    color: #333333
}
.ox-quiz-item-question-box {
    width: 100%;
    height: 90px;
    border: 1px solid #D9D9D9;
    border-radius: 5px;
}
.ox-quiz-item-question-label {
    width:100%;
    padding-left: 20px;
}
.ox-quiz-item-question-label-title {
    padding-left: 20px;
    font: 500 16px Pretendard;
    color: #443b36;
}
.ox-quiz-item-question-label-input {
    width: calc(100% - 610px);
    margin-left: 20px;
    padding-left: 20px;
    border: none;
}
.ox-quiz-item-img-box {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    right: 250px;
}
.ox-quiz-item-img-title {
    margin-right: 10px;
    font: 500 16px Pretendard;
    color: #333333;
    line-height: 17px
}
.ox-quiz-item-img-inner-box {
    position: relative;
    display: flex;
    align-items: center;
    min-width: 305px;
    height: 59px;
}
.ox-quiz-item-img-inner-label-box {
    position: absolute;
    width: 100%;
    height: 59px;
    border: 1px solid #D9D9D9;
    border-radius: 5px;
    background-color: #F8F8F8
}
.ox-quiz-item-img-inner-label {
    width: 100%;
    cursor: pointer;
}
.ox-quiz-item-img-inner-label-div {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 59px;
    font: 400 15px Pretendard;
    color: #FFFFFF;
    line-height: 17px;
    border-radius: 5px;
    background-color: #333333;
}
.ox-quiz-item-img-choose-text {
    width: 100%;
    margin: 0 5px 0 80px;
    font: 500 16px Pretendard;
    color:#333333;
    background-color: #F8F8F8;
    z-index: 1
}
.ox-quiz-item-answer-main-box {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    right: 60px;
}
.ox-quiz-item-answer-inner-box {
    position: relative;
    display: flex;
    align-items: center;
    min-width: 170px;
    height: 60px;
}
.ox-quiz-item-answer-title-box {
	display: flex;
	align-items: center;
    position: absolute;
    width: 100%;
    height: 59px;
    border: 1px solid #D9D9D9;
    border-radius: 5px;
    background-color: #F8F8F8
}
.ox-quiz-item-answer-text {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 59px;
    font: 400 15px Pretendard;
    color: #FFFFFF;
    line-height: 17px;
    border-radius: 5px;
    background-color: #333333;
}
.ox-quiz-item-select-pick-box {
    position: absolute;
    width: 90px;
    height: 57px;
    left: 75px;
    font: 400 16px Pretendard;
    color:#443b36;
    background: #F8F8F8;
    border: none;
}
.ox-quiz-item-delete-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30px;
    height: 30px;
    margin-left: 20px;
    border: none;
    background: #FFFFFF;
    cursor: pointer;
}
.game-result-top-search-box {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 30px;
}
.using-user-p {
    font: 500 16px Pretendard;
    color: #443B36
}
.using-user-span {
    font: 700 16px Pretendard;
    color:#443B36;
}
.result-game-search-form {
    display: flex;
    max-width: 539px;
    width: 100%;
}
.result-game-search-btn {
    width: 50px;
    height: 30px;
    margin-right: 25px;
    font: 500 14px Pretendard;
    color:#FFFFFF;
    background: #443B36;
    border-radius: 0 5px 5px 0;
}
.result-game-excel-btn {
    max-width:150px;
    width:100%;
    height: 30px;
    background: #443B36;
    font: 500 14px Pretendard;
    color: #FFFFFF;
    border-radius: 5px;
}
.page-win-rate-p {
    margin-bottom: 5px;
    font: 500 14px Pretendard;
    color:#443B36;
}
.win-percent-li-box {
    display: flex;
    align-items: center;
    justify-content: center;
}
.win-percent-box {
    width: 100%;
    padding: 0 20px;
}
.win-percent-text {
    margin-bottom: 5px;
    font: 500 14px Pretendard;
    color:#443B36;
}


.survery-answer-area{
	width: calc(100% - 8rem);
}

.survery-answer-item {
	display: flex;
	margin-bottom: 20px;
}
.survery-answer-item:last-child{
	margin-bottom: 5px;
}
.survery-answer-item-order-number{
	min-width: 60px;
}

.survey-item-question-box {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	position: relative;
    width: 100%;
    height: auto;
    min-height: 110px;
    border: 1px solid #D9D9D9;
    border-radius: 5px;
}
.survey-item-inner-box {
    position:relative;
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.survey-item-question-label {
    width:100%;
    padding-left: 20px;
}
.survey-item-question-label-title {
    padding-left: 20px;
    font: 500 16px Pretendard;
    color: #443b36;
}
.survey-item-question-label-input {
    width: calc(100% - 40px);
    margin-left: 20px;
    padding-left: 20px;
    border: none;
}
.survey-item-order-number {
    min-width: 59px;
    margin-right: 30px;
    font:500 16px Pretendard;
    color: #333333
}
.survey-item-img-box {
    /*position: absolute;*/
    display: flex;
    /*justify-content: center;*/
    /*align-items: center;*/
    /*right: 250px;*/
}
.survey-item-img-title {
    margin-right: 10px;
    font: 500 16px Pretendard;
    color: #333333;
    line-height: 17px
}
.survey-item-img-inner-box {
    position: relative;
    display: flex;
    align-items: center;
    min-width: 305px;
    height: 59px;
}
.survey-item-img-inner-label-box {
    position: absolute;
    width: 100%;
    height: 59px;
    border: 1px solid #D9D9D9;
    border-radius: 5px;
    background-color: #F8F8F8
}
.survey-item-img-inner-label {
    width: 100%;
    cursor: pointer;
}
.survey-item-img-inner-label-div {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 35px;
    font: 400 15px Pretendard;
    color: #FFFFFF;
    line-height: 17px;
    border-radius: 5px;
    background-color: var(--way-color-black);
}

.survey-item-img-inner-label-div.change{
	background-color: var(--way-color-red);
}

.survey-item-img-choose-text {
    width: 100%;
    margin: 0 5px 0 80px;
    font: 500 16px Pretendard;
    color:#333333;
    background-color: #F8F8F8;
    z-index: 1
}
.survey-item-answer-main-box {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    right: 60px;
}
.survey-item-answer-inner-box {
    position: relative;
    display: flex;
    align-items: center;
    min-width: 170px;
    height: 60px;
}
.survey-item-answer-title-box {
	display: flex;
	align-items: center;
    position: absolute;
    width: 100%;
    height: 59px;
    border: 1px solid #D9D9D9;
    border-radius: 5px;
    background-color: #F8F8F8
}
.survey-item-answer-text {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 59px;
    font: 400 15px Pretendard;
    color: #FFFFFF;
    line-height: 17px;
    border-radius: 5px;
    background-color: #333333;
}
.survey-item-select-pick-box {
    position: absolute;
    width: 90px;
    height: 57px;
    left: 75px;
    font: 400 16px Pretendard;
    color:#443b36;
    background: #F8F8F8;
    border: none;
}
.survey-item-delete-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30px;
    height: 30px;
    margin-left: 20px;
    border: none;
    background: #FFFFFF;
    cursor: pointer;
}

.survey-answer-item-img-box{
	display: flex;
	margin-left:20px;
}

.survey-answer-line{
	display: flex;
	margin-left:20px;
}



