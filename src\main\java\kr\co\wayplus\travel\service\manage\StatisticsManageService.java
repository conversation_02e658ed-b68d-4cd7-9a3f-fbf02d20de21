package kr.co.wayplus.travel.service.manage;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import kr.co.wayplus.travel.mapper.manage.ManageMapper;
import kr.co.wayplus.travel.mapper.manage.StatisticsMapper;
import kr.co.wayplus.travel.model.ManageMenu;
import kr.co.wayplus.travel.model.MenuConnectBoard;
import kr.co.wayplus.travel.model.MenuConnectPlace;
import kr.co.wayplus.travel.model.ProductInfo;

@Service
public class StatisticsManageService {

    private final StatisticsMapper mapper;
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    public StatisticsManageService(StatisticsMapper mapper) {
        this.mapper = mapper;
    }

	/*################################################statistics################################################*/
	public ArrayList<Map<String, Object>> selectListStatisticConnectDate(Map<String, Object> paramMap) {
		return mapper.selectListStatisticConnectDate(paramMap);
	}
	public Map<String, Object> selectListStatisticConnectInfo(Map<String, Object> paramMap) {
		return mapper.selectListStatisticConnectInfo(paramMap);
	}
	public ArrayList<Map<String, Object>> selectListStatisticProductDate(Map<String, Object> paramMap) {
		return mapper.selectListStatisticProductDate(paramMap);
	}
	public Map<String, Object> selectListStatisticProductInfo(Map<String, Object> paramMap) {
		return mapper.selectListStatisticProductInfo(paramMap);
	}

	public ArrayList<String> selectListStatisticProductItemY(Map<String, Object> paramMap) {
		return mapper.selectListStatisticProductItemY(paramMap);
	}
	public ArrayList<Map<String, Object>>  selectListStatisticProgramDate(Map<String, Object> paramMap) {
		return mapper.selectListStatisticProgramDate(paramMap);
	}

	public ArrayList<String> selectListStatisticJamsiislandItemY(Map<String, Object> paramMap) {
		return mapper.selectListStatisticJamsiislandItemY(paramMap);
	}
	public ArrayList<Map<String, Object>>  selectListStatisticJamsiislandDate(Map<String, Object> paramMap) {
		return mapper.selectListStatisticJamsiislandDate(paramMap);
	}
	public ArrayList<Map<String, Object>> selectListStatisticJamsiislandGender(Map<String, Object> paramMap) {
		return mapper.selectListStatisticJamsiislandGender(paramMap);
	}

	public ArrayList<Map<String, Object>> selectListStatisticJamsiislandRegion(Map<String, Object> paramMap) {
		return mapper.selectListStatisticJamsiislandRegion(paramMap);
	}
	public ArrayList<Map<String, Object>> selectListStatisticJamsiislandAmount(Map<String, Object> paramMap) {
		return mapper.selectListStatisticJamsiislandAmount(paramMap);
	}
	public ArrayList<Map<String, Object>> selectListStatisticJamsiislandAge(Map<String, Object> paramMap) {
		return mapper.selectListStatisticJamsiislandAge(paramMap);
	}


	public ArrayList<String> selectListStatisticProgramItemY(Map<String, Object> paramMap) {
		return mapper.selectListStatisticProgramItemY(paramMap);
	}

	public ArrayList<String> selectListStatisticDateY(Map<String, Object> paramMap) {
		return mapper.selectListStatisticDateY(paramMap);
	}

	public ArrayList<Map<String, Object>> selectListStatisticBoard(Map<String, Object> paramMap) {
		return mapper.selectListStatisticBoard(paramMap);
	}

	public ArrayList<Map<String, Object>>  selectListStatisticMission(Map<String, Object> paramMap) {
		return mapper.selectListStatisticMission(paramMap);
	}

	public ArrayList<Map<String, Object>> selectListStatisticBadge(Map<String, Object> paramMap) {
		return mapper.selectListStatisticBadge(paramMap);
	}

	public ArrayList<Map<String, Object>> selectListIslandlife(Map<String, Object> paramMap) {
		return mapper.selectListIslandlife(paramMap);
	}

	public ArrayList<Map<String, Object>> selectListStatisticDemographyGender(Map<String, Object> paramMap) {
		return mapper.selectListStatisticDemographyGender(paramMap);
	}

	public ArrayList<Map<String, Object>> selectListStatisticDemographyAge(Map<String, Object> paramMap) {
		return mapper.selectListStatisticDemographyAge(paramMap);
	}

	public ArrayList<Map<String, Object>> selectListStatisticDemographyRegion(Map<String, Object> paramMap) {
		return mapper.selectListStatisticDemographyRegion(paramMap);
	}







}
