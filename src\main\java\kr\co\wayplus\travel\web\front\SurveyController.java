package kr.co.wayplus.travel.web.front;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.servlet.http.HttpServletRequest;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.PagingDTO;
import kr.co.wayplus.travel.model.ProductInfo;
import kr.co.wayplus.travel.model.Survey;
import kr.co.wayplus.travel.model.SurveyQuestion;
import kr.co.wayplus.travel.model.SurveyQuestionAnswer;
import kr.co.wayplus.travel.model.SurveyRecommand;
import kr.co.wayplus.travel.model.SurveyResult;
import kr.co.wayplus.travel.service.front.PageService;
import kr.co.wayplus.travel.service.front.ProductService;
import kr.co.wayplus.travel.service.front.SurveyService;

@Controller
@RequestMapping("/survey")
public class SurveyController  extends BaseController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final PageService pageService;
    private final SurveyService surveyService;
    private final ProductService productService;

    @Autowired
    public SurveyController(
    		PageService pageService,
    		SurveyService surveyService,
    		ProductService productService) {
    	this.pageService = pageService;
    	this.surveyService = surveyService;
    	this.productService = productService;
    }

	@GetMapping("/questions")
	@ResponseBody
	public HashMap<String, Object> survey_item_ajax(
			HttpServletRequest request,
			@Param(value="surverVersion") String surverVersion,
			@RequestParam(value="deleteYn",defaultValue = "N") String deleteYn){
		HashMap<String, Object> retrunMap = new HashMap<>();

		try {
			HashMap<String, Object> paramMap = new HashMap<>();

			//paramMap.put("surveyVersion", surverVersion);
			paramMap.put("deleteYn", deleteYn);

			Survey sv = surveyService.selectOneLastSurvey(paramMap);
			retrunMap.put("survey", sv);
			/*
			int totalCount = surveyService.selectCountLastSurveyQuestion( paramMap );
			PagingDTO paging = new PagingDTO(totalCount, page, 0, length);
			param.put("itemStartPosition", paging.getItemStartPosition());
			param.put("pagePerSize", paging.getPagePerSize());
			*/

			paramMap.put("upperId", sv.getId());

			ArrayList<SurveyQuestion> questions = surveyService.selectListLastSurveyQuestion(paramMap);
			ArrayList<SurveyQuestionAnswer> baseListAnswer = null;

			for (SurveyQuestion item : questions) {
				paramMap.put("surveyId", item.getUpperId());
				if( baseListAnswer == null ) {
					paramMap.put("questionId", 0);
					baseListAnswer = surveyService.selectListSurveyQuestionAnswer(paramMap);
				}

				paramMap.put("questionId", item.getId());
				if( item.getAnswerType().equals("custom") ) {
					item.setListSurveyQuestionAnswers( surveyService.selectListSurveyQuestionAnswer(paramMap) );
				} else {
					item.setListSurveyQuestionAnswers( baseListAnswer );
				}
			}

			retrunMap.put("questions", questions);

			retrunMap.put("result", "success");
			retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
			e.printStackTrace();
		}

		return retrunMap;
	}

	@PostMapping("/result/list")
	@ResponseBody
	public HashMap<String, Object> survey_result_list_ajax(
			HttpServletRequest request,
			@RequestParam(value="userEmail", required = true) String userEmail,
			@RequestParam(value="start", defaultValue="0") int start,
			@RequestParam(value="length", defaultValue="6") int length,
			@RequestParam(value="page", defaultValue="1") int page){
		HashMap<String, Object> retrunMap = new HashMap<>();

		try {
			HashMap<String, Object> param = new HashMap<>();
//			param.put("searchType", "title");
//			param.put("searchKey", searchKey);

			param.put("userEmail", userEmail);
			param.put("sort", "createDate");
			param.put("sortOrder", "desc");

			int totalCount = surveyService.selectCountSurveyResult( param );

			PagingDTO paging = new PagingDTO(totalCount, page, 0, length);
			param.put("itemStartPosition", paging.getItemStartPosition());
			param.put("pagePerSize", paging.getPagePerSize());
			ArrayList<SurveyResult> list = surveyService.selectListSurveyResult( param );

			retrunMap.put("list", list);
			retrunMap.put("paging", paging);

			retrunMap.put("result", "success");
			retrunMap.put("message", "처리가 완료 되었습니다.");

		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
			e.printStackTrace();
		}

		return retrunMap;
	}

	@PostMapping("/result/save")
	@ResponseBody
	public HashMap<String, Object> survey_result_save_ajax(
			HttpServletRequest request,
			@RequestBody SurveyResult sr,
			BindingResult bindingResult) {
		HashMap<String, Object> retrunMap = new HashMap<>();

		try {
			HashMap<String, Object> paramMap = new HashMap<>();
			/*
			 * paramMap.put("surveyVersion", surverVersion); retrunMap.put("data",
			 * pageService.selectListLastSurveyQuestion(paramMap));
			 */

			ArrayList<SurveyQuestionAnswer> answers = new ArrayList<SurveyQuestionAnswer>();
			HashMap<Integer, Integer> countMap = new HashMap<>();
			ArrayList<Integer> menuIds = new ArrayList<Integer>();

			String recommandMenuIds = "";

			String jsonString = sr.getResultRecord();
			JSONParser parser = new JSONParser();

			// JSONObject로 파싱
			// "result"라는 JSONArray 추출
			try {
				// 문자열을 JSONObject로 파싱
				JSONObject jsonObject = (JSONObject) parser.parse(jsonString);

				// "result"라는 JSONArray 추출
				JSONArray resultArray = (JSONArray) jsonObject.get("result");

				// 각 질문과 답 출력
				for (Object obj : resultArray) {
					JSONObject item = (JSONObject) obj;
					String question = (String) item.get("question");
					String answer = (String) item.get("answer");

					paramMap.put("id", Integer.valueOf(answer));

					answers.add( surveyService.selectOneSurveyQuestionAnswer(paramMap) );

					//listAnswer.add(Integer.valueOf(answer));

					// System.out.println("Question: " + question);
					// System.out.println("Answer: " + answer);
				}

				for (SurveyQuestionAnswer item : answers) {
					Integer cnt = 1;

					if( countMap.containsKey( item.getMenuId() )) {
						cnt = countMap.get( item.getMenuId() )+1;
					} else {
						menuIds.add( item.getMenuId() );
					}

					countMap.put( item.getMenuId(), cnt );
				}

				var max = 0;
				for( Integer menuId : menuIds ) {
					int cnt = countMap.get( menuId );
					boolean isRecommand = false;

					if( cnt > max  ) {
						max = cnt;
						isRecommand = true;
					} else if( cnt == max ) {
						isRecommand = true;
					}

					if( isRecommand ) {

						if( recommandMenuIds.length() > 0  ) {
							recommandMenuIds += "," + menuId;
						} else {
							recommandMenuIds += "" + menuId;
						}
					}
				}

				System.out.println("recommandMenuId: " + recommandMenuIds );
				sr.setCreateId( getBaseUserEmail() );
				sr.setRecommandMenuIds(recommandMenuIds);
				surveyService.insertSurveyResult(sr);
			} catch (ParseException e) {
				e.printStackTrace();
			}

			retrunMap.put("data", sr);
			retrunMap.put("result", "success");
			retrunMap.put("message", "처리가 완료 되었습니다.");

		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
			e.printStackTrace();
		}

		return retrunMap;
	}

	@PostMapping("/result/view")
	@ResponseBody
	public HashMap<String, Object> survey_result_view_ajax(
			//@ModelAttribute SurveyRecommand sr,
			@ModelAttribute SurveyResult sr,
			HttpServletRequest request,
			BindingResult bindingResult
//			,@RequestParam(value="start", defaultValue="0") int start
//			,@RequestParam(value="length", defaultValue="4") int length
//			,@RequestParam(value="page", defaultValue="1") int page
			){
		HashMap<String, Object> retrunMap = new HashMap<>();

		try {
			HashMap<String, Object> paramMap = new HashMap<>();

			int id = sr.getId();

			paramMap.put("id", id);

			sr = surveyService.selectOneSurveyResult( paramMap );

			List<String> menuIds = new ArrayList<String>();
			paramMap.clear();
			if( sr.getRecommandMenuIds().indexOf(",") >= 0 ) {

				menuIds = Arrays.asList( sr.getRecommandMenuIds().split(",") );

				paramMap.put( "menuId", 0 );
			} else {
				paramMap.put( "menuId", sr.getRecommandMenuIds() );
			}

			SurveyRecommand data = surveyService.selectOneSurveyRecommand(paramMap);
			paramMap.clear();
			paramMap.put("recommandId", data.getId() );
			paramMap.put("deleteYn", "N");

			data.setFile( surveyService.selectOneSurveyImage(paramMap) );

			retrunMap.put("data", data);
			retrunMap.put("result", "success");
			retrunMap.put("message", "처리완료되었습니다.");

		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
			e.printStackTrace();
		}

		return retrunMap;
	}

	@PostMapping("/result/lastOne")
	@ResponseBody
	public HashMap<String, Object> survey_result_lastOne_ajax(
			@ModelAttribute SurveyRecommand sr,
			@RequestParam(value="userEmail", required = true) String userEmail,
			HttpServletRequest request,
			BindingResult bindingResult){
		HashMap<String, Object> retrunMap = new HashMap<>();

		try {
			HashMap<String, Object> paramMap = new HashMap<>();

			paramMap.put("createId", userEmail);
			paramMap.put("isLastData", true);
			SurveyRecommand data = surveyService.selectOneSurveyRecommand(paramMap);
			if( data != null ) {
				paramMap.put("recommandId", data.getId() );
				paramMap.put("deleteYn", "N");

				data.setFile( surveyService.selectOneSurveyImage(paramMap) );
			}

			retrunMap.put("data", data);
			retrunMap.put("result", "success");
			retrunMap.put("message", "처리가 완료 되었습니다.");

		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
			e.printStackTrace();
		}

		return retrunMap;
	}


}
