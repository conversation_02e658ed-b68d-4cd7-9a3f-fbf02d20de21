.user_contents {
	margin-top: 60px;
}

.title_ex_box {
	margin-bottom: 50px;
	text-align: center;
}

.title_ex {
	margin-top: 16px;
	color: #222;
}

.title_box {
	margin-bottom: 40px;
	text-align: center;
}

.user_list_box {
	padding: 40px 0;
	border-top: 1px solid #666;
	border-bottom: 1px solid #666;
}

.login_contents_box {
	width: fit-content;
	margin: 0 auto;
}

.login_contents_box {
	display: flex;
}

.id_contents_input {
	margin-bottom: 20px;
}

.d-flex {
	display: flex;
}

.d-flex.fd-c {
	flex-direction: column;
}

.login_contents_input span {
	display: inline-block;
	width: 65px;
	margin-right: 20px;
}

.re-password .login_contents_input span {
	margin-right: unset;
}

.login_contents_input input {
	width: 400px;
	height: 40px;
	border: 1px solid #CCC;
	color: #666;
	font-family: Pretendard;
	font-size: 16px;
	font-weight: 400;
	padding-left: 10px;
	box-sizing: border-box;
}

.login_contents_input .info-text {
	width: 400px;
}


.login_contents_btn {
	display: flex;
	width: 100px;
	height: auto;
	border-radius: 5px;
	background: #0062D4;
	margin-left: 20px;
	color: #FFF;
	justify-content: center;
	align-items: center;
	font-size: 18px;
	font-weight: 600;
}

.login_contents_btn:hover {
	cursor: pointer;
	opacity: 0.9;
}

.user_list {
	font-size: 0;
	line-height: 10px;
	width: fit-content;
	margin: 0 auto;
}

.user_list li {
	display: inline-block;
	font-size: 14px;
	padding: 0 10px;
	border-right: 1px solid #222;
	margin: 20px 0 0 0;
	color: #222;
}

.user_list li:last-child {
	border: none;
}

.user_list li:hover {
	color: #0062D4;
	font-weight: 500;
	cursor: pointer;
}

.user_btn_box {
	margin-top: 40px;
}

#agree_chk {
	display: none;
}

.agree {
	margin-top: 20px;
	margin-bottom: 50px;
	overflow: hidden;
}

.agree_chk {
	float: left;
	display: block;
	width: 18px;
	height: 18px;
	border: 1px solid #666;
	margin-right: 13px;
	position: relative;
	cursor: pointer;
}

.agree_chk_txt {
	float: left;
	color: #666;
	font-size: 16px;
	font-weight: 400;
}

#agree_chk:checked+label::after {
	content: '✔';
	font-size: 14px;
	width: 18px;
	height: 18px;
	text-align: center;
	position: absolute;
	left: 0;
	top: 0;
}

.underline {
	text-decoration: underline;
}

.find-result {
	display: none;
}

.simple_login {
	text-align: center;
	margin-top: 80px;
	color: #444;
	font-size: 18px;
	font-weight: 600;
}

.sns_box {
	display: flex;
	justify-content: center;
	margin-top: 15px;
}

.naver {
	width: 60px;
	height: 61px;
	background: url(/images/icon/sns/naver.svg);
	cursor: pointer;
	margin-left: 20px;
}

.kakao {
	width: 60px;
	height: 61px;
	background: url(/images/icon/sns/kakao.svg);
	cursor: pointer;
}

.join-agree {text-align:left;color:#443B36;font-weight:400;}
.join-agree .agree-title {display: flex;justify-content: space-between;font-size: 18px; font-weight:600;line-height:30px; border-bottom:1px solid #444444;}
.join-agree .agree-item {height:20px;overflow:hidden;margin-top:20px;}
.join-agree .agree-item.all {font-weight:500;margin-top:5px;}
.join-agree .agree-item .label {line-height:20px;vertical-align: top;font-size:15px;}
.join-agree .agree-item .label.headline {font-weight:600;font-size:16px;}
.join-agree .agree-item .popup {color:#555555;text-decoration:underline;}
.join-agree .agree-item .popup:hover {cursor:pointer;opacity:0.8;}
.join-agree .agree-description {border: 1px solid #CCCCCC;margin: 20px auto 30px;padding: 20px 15px;height: 180px;overflow-y: auto;line-height: 1.5;}
.join-agree .agree-description h2 {margin-top:20px;}

.join-agree input[type=checkbox], .join-agree input[type=checkbox]:checked {appearance:none;-webkit-appearance:none;-moz-appearance:none;}
.join-agree input[type=checkbox] {height:16px;width:16px;margin:2px 5px 3px 0;border:1px solid #D9D9D9;border-radius:2px;}
.join-agree input[type=checkbox]:checked {background-color:#222222;border-color:#222222;}
.join-agree input[type=checkbox]:checked::after{content: '✔';font-size: 15px;text-align: center;position: relative;top: -3px;color: #FFFFFF;width: 16px;height: 16px;display: inline-block;}
.join-agree .checkbox-right {float: right;}

.join-agree .terms-area {position:fixed;top:100vh;left:50%;height:calc(100vh - 170px);width:100%;max-width:840px;background-color: #FFFFFF;border-top-right-radius:20px;border-top-left-radius:20px;transform: translateX(-50%);padding:20px;border:1px solid #DDDDDD;}
.join-agree .terms-area .popup-control {height:0;float:right;text-align:right;background-color:#965841;position: absolute;right: 10px;top:-10px;}
.join-agree .terms-area .popup-close-button {width:40px;height:40px;border:none;background:url("/images/icon/close.svg") center / 30px no-repeat;margin:20px 0 0;}
.join-agree .terms-area .popup-close-button:hover {cursor:pointer;opacity:0.8;}
.join-agree .terms-item {display:none;height:100%;overflow-x: clip;overflow-y: auto;}
.join-agree .terms-item.on {display:block;}
.join-agree .terms-data {height:100%;width:100%;overflow:hidden;}
.join-agree .terms-data .login-title {text-align:center;padding:20px 0;border-bottom:2px solid #443b36;font-size:1.6em;}
.join-agree .terms-data .login-title .title-text:before {width:20px;height:20px;left:12px;top:-8px;z-index:-1;}
.join-agree .terms-data .terms-data-textarea {height:calc(100% - 90px);margin-top:18px;overflow-y: auto;}
.join-agree .terms-data .terms-data-textarea::-webkit-scrollbar {width:5px;background-color:rgba(0,0,0,0.1);border-radius:5px;}
.join-agree .terms-data .terms-data-textarea::-webkit-scrollbar-thumb {background-color:rgba(68, 59, 54, 0.5);border-radius:5px;}
.join-agree .terms-data .terms-data-textarea h3 {margin:0;padding:20px 0 10px;}
.join-agree .terms-data .terms-data-textarea h4 {margin:0;padding:15px 0 5px 10px;}
.join-agree .terms-data .terms-data-textarea ul {list-style:none;padding:0 0 0 10px;margin:10px auto;}
.join-agree .terms-data .terms-data-textarea ul li {margin-top:5px;}
.join-agree .terms-data .terms-data-textarea p {margin:0;padding:5px 0 5px 10px;text-indent: 5px;word-break: break-word;}
.join-agree .terms-data .terms-data-textarea hr {border-style: dashed;border-color:#D9D9D9;margin:20px 0 30px;}


@media screen and (max-width:1024px) {
	.wrap {
		margin-top: 60px;
	}
}

@media screen and (max-width:768px) {
	.login_contents_input span {
		display: none;
	}
}

@media screen and (max-width:600px) {
	.wrap {
		margin-top: 0px;
	}

	.login_contents_box {
		width: 100%;
	}

	.login_contents_input_box {
		width: 100%;
	}

	.login_contents_box {
		flex-wrap: wrap;
	}

	.login_contents_btn {
		width: 100%;
		height: 50px;
		margin: 20px 0 0 0;
	}

	.login_contents_input input {
		width: 100%;
	}
}