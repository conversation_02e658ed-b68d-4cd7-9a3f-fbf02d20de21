package kr.co.wayplus.travel.service.manage;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import kr.co.wayplus.travel.mapper.manage.AlertManageMapper;
import kr.co.wayplus.travel.model.AlertMessageLog;
import kr.co.wayplus.travel.model.SmsPolicy;

@Service
public class AlertManageService {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final AlertManageMapper mapper;

    public AlertManageService(AlertManageMapper mapper) {
        this.mapper = mapper;
    }

//	<!--################################### AlertMessageLog ###################################-->
    public int selectCountAlertMessageLog(HashMap<String, Object> paramMap) {
    	return mapper.selectCountAlertMessageLog(paramMap);
    }
    public ArrayList<AlertMessageLog> selectListAlertMessageLog(HashMap<String, Object> paramMap) {
    	return mapper.selectListAlertMessageLog(paramMap);
    }
	public AlertMessageLog selectOneAlertMessageLog(HashMap<String, Object> paramMap) {
		return mapper.selectOneAlertMessageLog(paramMap);
	}
	public void insertAlertMessageLog(AlertMessageLog param) throws SQLException {
		mapper.insertAlertMessageLog(param);
	}
	public void updateAlertMessageLog(AlertMessageLog param) throws SQLException {
		mapper.updateAlertMessageLog(param);
	}
	public void deleteAlertMessageLog(AlertMessageLog param) throws SQLException {
		mapper.deleteAlertMessageLog(param);
	}


	//	<!--################################### sms_policy ###################################-->
	public void insertSmsPolicy(SmsPolicy smsPolicy) {
		mapper.insertSmsPolicy(smsPolicy);
	}

	public SmsPolicy selectOneSmsPolicy(HashMap<String, Object> paramMap) {
		return mapper.selectOneSmsPolicy(paramMap);
	}

	public int selectCountListSmsPolicy(HashMap<String, Object> paramMap) {
		return mapper.selectCountListSmsPolicy(paramMap);
	}

	public ArrayList<SmsPolicy> selectListSmsPolicy(HashMap<String, Object> paramMap) {
		return mapper.selectListSmsPolicy(paramMap);
	}

	public void updateSmsPolicy(SmsPolicy smsPolicy) {
		mapper.updateSmsPolicy(smsPolicy);
	}

	public void deleteSmsPolicy(SmsPolicy smsPolicy) {
		mapper.deleteSmsPolicy(smsPolicy);
	}

	public void updateSmsPolicyYn(SmsPolicy smsPolicy) {
		mapper.updateSmsPolicyYn(smsPolicy);
	}

}
