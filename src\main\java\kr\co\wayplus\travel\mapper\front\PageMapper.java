package kr.co.wayplus.travel.mapper.front;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import kr.co.wayplus.travel.model.BannerCategory;
import kr.co.wayplus.travel.model.CoachInfo;
import kr.co.wayplus.travel.model.CodeItem;
import kr.co.wayplus.travel.model.InquiryCategory;
import kr.co.wayplus.travel.model.InquiryContent;
import kr.co.wayplus.travel.model.MainBannerImage;
import kr.co.wayplus.travel.model.MainNoticePopup;
import kr.co.wayplus.travel.model.MenuUser;
import kr.co.wayplus.travel.model.PlaceSpot;
import kr.co.wayplus.travel.model.Policy;
import kr.co.wayplus.travel.model.ProductInfo;
import kr.co.wayplus.travel.model.Reservation;
import kr.co.wayplus.travel.model.SettingAllianceContents;
import kr.co.wayplus.travel.model.SettingAwardsContents;
import kr.co.wayplus.travel.model.SettingCompanyInfo;
import kr.co.wayplus.travel.model.SettingNavbar;
import kr.co.wayplus.travel.model.UserCustomerCart;
import kr.co.wayplus.travel.model.UserCustomerOrder;
import kr.co.wayplus.travel.model.UserCustomerOrderHistory;
import kr.co.wayplus.travel.model.UserCustomerOrderList;
import kr.co.wayplus.travel.model.UserCustomerPayment;

@Mapper
@Repository
public interface PageMapper {

    SettingCompanyInfo selectUserPageFooterInfo();

    ArrayList<MainNoticePopup> selectMainNoticePopupLayerList(HashMap<String, Object> ids);

    ArrayList<MainNoticePopup> selectMainNoticePopupBarList(HashMap<String, Object> ids);

    SettingNavbar selectNavbar();

    ArrayList<MenuUser> selectUserMenuList();
	//	<!--################################### Policy ###################################-->
	Policy selectOnePolicy(HashMap<String, Object> paramMap);


//	<!--################################### Inquiry ###################################-->
	int selectCountInquiryContent(HashMap<String, Object> paramMap);
	ArrayList<InquiryContent> selectListInquiryContent(HashMap<String, Object> paramMap);
	InquiryContent selectOneInquiryContent(HashMap<String, Object> paramMap);
	void insertInquiryContent(InquiryContent ic) throws SQLException;
	void updateInquiryContent(InquiryContent ic) throws SQLException;
	void deleteInquiryContent(InquiryContent ic) throws SQLException;
//	<!--################################### InquiryCategory ###################################-->
	int selectCountInquiryCategory(HashMap<String, Object> paramMap);
	ArrayList<InquiryCategory> selectListInquiryCategory(HashMap<String, Object> paramMap);
	InquiryCategory selectOneInquiryCategory(HashMap<String, Object> paramMap);
//	<!--################################### Reservation ###################################-->
	int selectCountReservation(HashMap<String, Object> paramMap);
	int selectCountReservation(Reservation ic);
	ArrayList<Reservation> selectListReservation(HashMap<String, Object> paramMap);
	ArrayList<Reservation> selectListReservation(Reservation ic);
	Reservation selectOneReservation(HashMap<String, Object> paramMap);
	ArrayList<HashMap<String, Object>> selectListInquiryCountStatusType(HashMap<String, Object> paramMap);
	void insertReservation(Reservation ic)throws SQLException;
	void updateReservation(Reservation ic) throws SQLException;
	void restoreReservation(Reservation ic) throws SQLException;
	void deleteReservation(Reservation ic) throws SQLException;
//	<!--################################### CodeItem ###################################-->
	int selectCountCodeItem(HashMap<String, Object> paramMap);
	int selectCountCodeItem(CodeItem ci);
	ArrayList<CodeItem> selectListCodeItem(HashMap<String, Object> paramMap);
	ArrayList<CodeItem> selectListCodeItem(CodeItem ci);
	CodeItem selectOneCodeItem(HashMap<String, Object> paramMap);

	ArrayList<MenuUser> selectListMenuUser(HashMap<String, Object> paramMap);

	ArrayList<MainBannerImage> selectListMainBannerImage(HashMap<String, Object> param);

	MenuUser selectOneMenuUser(HashMap<String, Object> param);
	//	<!--################################### PlaceSpot ###################################-->
	int selectCountPlaceSpot(HashMap<String, Object> paramMap);
	ArrayList<PlaceSpot> selectListPlaceSpot(HashMap<String, Object> paramMap);
	PlaceSpot selectOnePlaceSpot(HashMap<String, Object> paramMap);

	ArrayList<SettingAllianceContents> selectListAllianceContents(HashMap<String, Object> param);

//	<!--################################### UserCustomerCart ###################################-->
	int selectCountUserCustomerCart(HashMap<String, Object> paramMap);
	int selectCountUserCustomerCartProductCount(HashMap<String, Object> paramMap);
	ArrayList<UserCustomerCart> selectListUserCustomerCart(HashMap<String, Object> paramMap);
	UserCustomerCart selectOneUserCustomerCart(HashMap<String, Object> paramMap);
	void insertUserCustomerCart(UserCustomerCart ucc) throws SQLException;
	void updateUserCustomerCart(UserCustomerCart ucc) throws SQLException;
	void restoreUserCustomerCart(UserCustomerCart ucc) throws SQLException;
	void deleteUserCustomerCart(UserCustomerCart ucc) throws SQLException;

//	<!--################################### UserCustomerPayment ###################################-->
	void insertUserCustomerPayment(UserCustomerPayment data)throws SQLException;

//	<!--################################### UserCustomerOrder ###################################-->
	int selectCountUserCustomerOrder(HashMap<String, Object> paramMap);
	ArrayList<UserCustomerOrder> selectListUserCustomerOrder(HashMap param);
	UserCustomerOrder selectOneUserCustomerOrder(HashMap param);
	void insertUserCustomerOrder(UserCustomerOrder ord) throws SQLException;
	void updateUserCustomerOrder(UserCustomerOrder ord) throws SQLException;

//	<!--################################### UserCustomerOrderList ###################################-->
	int selectCountUserCustomerOrderList(HashMap<String, Object> paramMap);
	int selectSummryUserCustomerOrderList(HashMap<String, Object> paramMap);
	ArrayList<UserCustomerOrderList> selectListUserCustomerOrderList(HashMap param);
	UserCustomerOrderList selectOneUserCustomerOrderList(HashMap param);
	void insertUserCustomerOrderList(UserCustomerOrderList ord) throws SQLException;
	void updateUserCustomerOrderList(UserCustomerOrderList ord) throws SQLException;

//	<!--################################### UserCustomerOrderHistory ###################################-->
	int selectCountUserCustomerOrderHistory(HashMap<String, Object> paramMap);
	ArrayList<UserCustomerOrderHistory> selectListUserCustomerOrderHistory(HashMap param);
	UserCustomerOrderHistory selectOneUserCustomerOrderHistory(HashMap param);
	void insertUserCustomerOrderHistory(UserCustomerOrderHistory ord) throws SQLException;

//	<!--################################### others ###################################-->
	ArrayList<CoachInfo> selectCoachList();
	ArrayList<MenuUser> selectListSubMenuUser(HashMap<String, Object> param);
	ArrayList<PlaceSpot> selectListPlace(HashMap<String, Object> param);
	int selectListCountPlace(HashMap<String, Object> param);
//	<!--################################### AwardsContents ###################################-->
	int selectCountAwardsContents(HashMap<String, Object> paramMap);
	ArrayList<SettingAwardsContents> selectListAwardsContents(HashMap<String, Object> paramMap);

    List<MenuUser> selectListMenuUserUpperMenuId(HashMap<String, Object> param);

    List<ProductInfo> selectListProductByUpperMenuId(HashMap<String, Object> param);

    ArrayList<MainBannerImage> selectListMainBannerImageAndCategory(HashMap<String, Object> param);

	ArrayList<BannerCategory> selectListBannerCategory(HashMap<String, Object> param);

}
