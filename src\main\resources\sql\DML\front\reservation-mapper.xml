<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.front.ReservationMapper">
<!--
	 * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
-->
<!--##########################reservation##########################-->
	<select id="selectCountReservation" parameterType="HashMap" resultType="Integer">
		SELECT count(id)
		FROM (
		SELECT
		id,
		@travel := json_value(travel_schedule_json,'$.data.travelSchedule') travel_schedule_dt,
		@travelF := substring_index(@travel, ' ~ ',1) as datef,
		@travelT :=substring_index(@travel, ' ~ ',-1) as datet
		FROM reservation a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateType)">
				<choose>
					<when test='dateType eq "travelFrom"'>@travelF between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d') </when>
					<when test='dateType eq "travelTo"'>@travelT between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d') </when>
					<when test='dateType eq "receipt"'>create_date between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d')</when>
				</choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and concat(user_email,user_name,user_mobile) like concat('%',#{searchKey},'%')</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and a.user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)" >	and user_name=#{userName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPhone)" >	and user_mobile=#{userMobile}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	and berth_type=#{berthType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_serial=#{productSerial}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	and apply_code=#{applyCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationCode)" >	and reservation_code=#{reservationCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	and cancel_yn=#{cancelYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelCode)" >	and cancel_code=#{cancelCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	and berth_json=#{berthJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	and travel_schedule_json=#{travelScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	and air_type_request=#{airTypeRequest}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	and air_schedule_json=#{airScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	and vehicle_type=#{vehicleType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	and vehicle_count=#{vehicleCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	and vehicle_json=#{vehicleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(usePoint)" >	and use_point=#{usePoint}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(totalAmount)" >	and total_amount=#{totalAmount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>) a
	</select>

	<select id="selectListReservation" parameterType="HashMap" resultType="Reservation">
		SELECT *
		FROM(
		SELECT @rownum:=@rownum+1 AS rownum,
		a.id, a.user_email,
		IFNULL(a.user_name, u.user_name) user_name,
		IFNULL(IFNULL(a.user_mobile, u.user_mobile),'~연락처없음~') user_mobile,
		@travel := json_value(travel_schedule_json,'$.data.travelSchedule') travel_schedule_dt,
		@travelF := substring_index(@travel, ' ~ ',1) as datef,
		@travelT :=substring_index(@travel, ' ~ ',-1) as datet,
		berth_type, a.product_serial,a.product_tour_id,ifNUll(p.product_title, '~상품미선택~') product_title, apply_code,
		reservation_code, b.name reservation_code_name, cancel_yn, cancel_code, berth_json,
		travel_schedule_json, air_type_request, air_schedule_json,
		vehicle_type, vehicle_count, vehicle_json, use_point, total_amount,
		a.create_id, DATE_FORMAT(a.create_date, '%Y. %m. %d') AS create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.delete_id, a.delete_date
		FROM reservation a
		left join product_tour p on a.product_serial = p.product_serial and a.product_tour_id = p.product_tour_id
		left join code_item b on a.reservation_code = b.code and b.upper_code = 'reservationType'
		left join `user` u on a.user_email = u.user_email
		join (SELECT @rownum:= 0) rnum
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateType)">
				<choose>
					<when test="dateType eq 'travelFrom'">@travelF between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d') </when>
					<when test="dateType eq 'travelTo'">@travelT between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d') </when>
					<when test="dateType eq 'receipt'">create_date between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d')</when>
				</choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and concat(user_email,user_name,user_mobile) like concat('%',#{searchKey},'%')</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and a.user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)" >	and user_name=#{userName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPhone)" >	and user_mobile=#{userMobile}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	and berth_type=#{berthType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_serial=#{productSerial}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	and apply_code=#{applyCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationCode)" >	and reservation_code=#{reservationCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	and cancel_yn=#{cancelYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelCode)" >	and cancel_code=#{cancelCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	and berth_json=#{berthJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	and travel_schedule_json=#{travelScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	and air_type_request=#{airTypeRequest}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	and air_schedule_json=#{airScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	and vehicle_type=#{vehicleType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	and vehicle_count=#{vehicleCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	and vehicle_json=#{vehicleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(totalAmount)" >	and total_amount=#{totalAmount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>

		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
			ORDER BY
			<choose>
				<when test="sort=='travelScheduleDt'" >	travel_schedule_dt	</when>
				<when test="sort=='productTitle'" >	p.product_title	</when>
				<when test="sort=='id'" >	id	</when>
				<when test="sort=='userEmail'" >	user_email	</when>
				<when test="sort=='userName'" >	user_name	</when>
				<when test="sort=='userMobile'" >	user_mobile	</when>
				<when test="sort=='berthType'" >	berth_type	</when>
				<when test="sort=='productSerial'" >	product_serial	</when>
				<when test="sort=='applyCode'" >	apply_code	</when>
				<when test="sort=='reservationCode'" >	reservation_code	</when>
				<when test="sort=='cancelYn'" >	cancel_yn	</when>
				<when test="sort=='cancelCode'" >	cancel_code	</when>
				<when test="sort=='berthJson'" >	berth_json	</when>
				<when test="sort=='travelScheduleJson'" >	travel_schedule_json	</when>
				<when test="sort=='airTypeRequest'" >	air_type_request	</when>
				<when test="sort=='airScheduleJson'" >	air_schedule_json	</when>
				<when test="sort=='vehicleType'" >	vehicle_type	</when>
				<when test="sort=='vehicleCount'" >	vehicle_count	</when>
				<when test="sort=='vehicleJson'" >	vehicle_json	</when>
				<when test="sort=='totalAmount'" >	total_amount	</when>
				<when test="sort=='createId'" >	create_id	</when>
				<when test="sort=='createDate'" >	create_date	</when>
				<when test="sort=='lastUpdateId'" >	last_update_id	</when>
				<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
				<when test="sort=='deleteYn'" >	delete_yn	</when>
				<when test="sort=='deleteId'" >	delete_id	</when>
				<when test="sort=='deleteDate'" >	delete_date	</when>
				<otherwise>rownum</otherwise>
			</choose>
			<choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
			ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
			<choose>
				<when test="item.sort=='travelScheduleDt'" >	travel_schedule_dt	</when>
				<when test="item.sort=='productTitle'" >	p.product_title	</when>
				<when test="item.sort=='id'" >	id	</when>
				<when test="item.sort=='userEmail'" >	user_email	</when>
				<when test="item.sort=='userName'" >	user_name	</when>
				<when test="item.sort=='userMobile'" >	user_mobile	</when>
				<when test="item.sort=='berthType'" >	berth_type	</when>
				<when test="item.sort=='productSerial'" >	product_serial	</when>
				<when test="item.sort=='applyCode'" >	apply_code	</when>
				<when test="item.sort=='reservationCode'" >	reservation_code	</when>
				<when test="item.sort=='cancelYn'" >	cancel_yn	</when>
				<when test="item.sort=='cancelCode'" >	cancel_code	</when>
				<when test="item.sort=='berthJson'" >	berth_json	</when>
				<when test="item.sort=='travelScheduleJson'" >	travel_schedule_json	</when>
				<when test="item.sort=='airTypeRequest'" >	air_type_request	</when>
				<when test="item.sort=='airScheduleJson'" >	air_schedule_json	</when>
				<when test="item.sort=='vehicleType'" >	vehicle_type	</when>
				<when test="item.sort=='vehicleCount'" >	vehicle_count	</when>
				<when test="item.sort=='vehicleJson'" >	vehicle_json	</when>
				<when test="item.sort=='totalAmount'" >	total_amount	</when>
				<when test="item.sort=='createId'" >	create_id	</when>
				<when test="item.sort=='createDate'" >	create_date	</when>
				<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
				<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
				<when test="item.sort=='deleteYn'" >	delete_yn	</when>
				<when test="item.sort=='deleteId'" >	delete_id	</when>
				<when test="item.sort=='deleteDate'" >	delete_date	</when>

			</choose>
			<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
		</if>
		) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
			LIMIT #{itemStartPosition}, #{pagePerSize}
		</if>
	</select>

	<select id="selectListCalcReservationForUser" parameterType="HashMap" resultType="Reservation">
		WITH RECURSIVE
			DateRange AS (
				SELECT DATE_FORMAT(#{startDate}, '%Y-%m-%d') AS date
				UNION ALL
				SELECT DATE_ADD(date, INTERVAL 1 DAY)
				FROM DateRange
				WHERE DATE_ADD(date, INTERVAL 1 DAY) &lt;= DATE_FORMAT(#{endDate}, '%Y-%m-%d') ),
			productPeriod as (
				select pr.product_serial, date rest_date, pr.id
					from DateRange dr, 
						product_tour_rest_period pr
					where dr.date between pr.product_rest_start_date and pr.product_rest_end_date
					and pr.product_serial = #{productSerial} 
					and pr.delete_yn = 'N'),
			daycheck as(
				select
					pt.product_serial,
					case when count(*) > 0 then 'day' else 'fix' end isDay
				from product_tour_price_option po
				JOIN product_tour pt on po.product_tour_id = pt.product_tour_id
				join product_tour_price_set pd on po.price_option_id = pd.price_option_id
				WHERE pt.product_serial = #{productSerial} ) ,
			mix as (
				select
					pt.product_serial
					, pt.policy_inventory
					, pt.product_use_yn
					, pt.delete_yn product_delete_yn
					, pt.regacy_yn product_regacy_yn
					, mix.price_id
					, mix.gubn
					, mix.isDay
					, po.*
					, mix.product_price
					, mix.price_set_date
					, mix.start_date
					, mix.end_date
					, mix.consecurive_discount_amount
					, mix.extra_person_defualt_charge
					, mix.extra_person_consecurive_charge
				from product_tour_price_option po
				JOIN product_tour pt on po.product_tour_id = pt.product_tour_id
				join (
					select
						gubn
						, price_option_id
						, price_id
						, price_set_date
						, start_date
						, end_date
						, product_price
						, d.isDay
						, consecurive_discount_amount
						, extra_person_defualt_charge
						, extra_person_consecurive_charge
					from(
						select
							'day' gubn
							, price_option_id
							, price_id
							, price_set_date
							, null start_date
							, null end_date
							, (CASE WHEN price_sale != 0 THEN price_sale ELSE price_normal END ) AS product_price
							, null consecurive_discount_amount
							, null extra_person_defualt_charge
							, null extra_person_consecurive_charge
						from product_tour_price_set
					union all
						select
							'fix' gubn
							, price_option_id
							, price_id
							, null price_set_date
							, start_date
							, end_date
							, (CASE WHEN price_sale != 0 THEN price_sale ELSE price_normal END ) AS product_price
							, consecurive_discount_amount
							, extra_person_defualt_charge
							, extra_person_consecurive_charge
						from product_tour_price_fix_set) as b
					join daycheck d on b.gubn = d.isDay ) mix ON po.price_option_id = mix.price_option_id
				WHERE pt.product_serial = #{productSerial}),
			RankedPrices AS (
				SELECT
					dr.date
					, po.product_serial
					, po.product_tour_id
					, po.price_option_id
					, po.price_id
					, po.policy_inventory
					, po.gubn
					, (CASE WHEN po.start_date IS NOT NULL THEN 'S' ELSE 'N' END) gubn2
					, ROW_NUMBER() OVER ( PARTITION BY dr.date, CASE WHEN po.start_date IS NOT NULL THEN 1 ELSE 0 END order by po.price_option_id /*ORDER BY CASE WHEN po.start_date IS NOT NULL THEN 1 ELSE 0 END*/ ) as rn
					, po.max_quantity
					, po.max_capacity
					, po.product_price
					, po.price_set_date
					, po.start_date
					, po.end_date
					, po.option_one_code
					, po.option_name
					, po.product_delete_yn
					, po.product_regacy_yn
					, po.use_yn
					, po.delete_yn
					, consecurive_discount_amount
					, extra_person_defualt_charge
					, extra_person_consecurive_charge
				FROM DateRange dr
				JOIN mix po ON
					( (po.gubn = 'fix'
						AND ((po.start_date IS NOT NULL
							AND dr.date BETWEEN po.start_date AND po.end_date)
						OR po.start_date IS NULL))
					OR (po.gubn = 'day'
						AND po.price_set_date = dr.date
						AND po.start_date is null
						AND po.end_date is null ) ) ) ,
			resvDates AS (
				SELECT
					id,
					product_serial,
					seq,
					DATE_ADD(start_date, INTERVAL seq -1 DAY) as date,
					start_date,
					end_date,
					checkout_date,
					option_id,
					order_count,
					order_one_code,
					pickPeople
				FROM(
					SELECT id, product_serial, ROW_NUMBER() OVER (PARTITION BY id ORDER BY t.option_key) as seq,
						JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.travelSchedule')) as travel_schedule,
						STR_TO_DATE( JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.travelSchedule')) , '%Y-%m-%d') as start_date,
						DATE_ADD(STR_TO_DATE(SUBSTRING_INDEX( JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.travelSchedule')) , ' ~ ', -1), '%Y-%m-%d'), INTERVAL -1 DAY) as end_date,
						STR_TO_DATE(SUBSTRING_INDEX( JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.travelSchedule')) , ' ~ ', -1), '%Y-%m-%d') as checkout_date,
						JSON_UNQUOTE(JSON_EXTRACT(travel_schedule_json, '$.data.pickPeople')) as pickPeople,
						t.option_key,
						JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.0'))) as option_id,
						JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.1'))) as price,
						JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.2'))) as value2,
						JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.3'))) as order_count,
						JSON_UNQUOTE(JSON_EXTRACT(price_option_json, CONCAT('$.data.', t.option_key, '.4'))) as order_one_code
					FROM
						reservation
					CROSS JOIN JSON_TABLE(
							JSON_KEYS(JSON_EXTRACT(price_option_json, '$.data')),
							'$[*]' COLUMNS(option_key VARCHAR(50) PATH '$') ) AS t
					where
						cancel_yn = 'N'
						and delete_yn = 'N'
						and is_group = 'N'
						and t.option_key like 'option-%'
						and product_serial = #{productSerial} ) as base ) ,
			aggResvTable as (
				select
					date
					, option_id
					, order_one_code
					, sum(order_count) order_count
					, sum(pickPeople) pick_people
				from resvDates
				group by date, option_id, order_one_code),
			aggData as (
				select st.*,
			       rd.order_one_code, rd.option_id, rd.order_count, rd.pick_people
			  from RankedPrices st
			  left join aggResvTable rd on st.date = rd.date and st.option_one_code = rd.order_one_code	and st.price_option_id = rd.option_id	),
			lastRnData as (
				select date,gubn2,option_one_code, max(rn) last_rn
				  from RankedPrices
				 group by date,gubn2,option_one_code),
			preLastData as (
				SELECT
					ag.date
					, ag.gubn2
					, max(ag.rn) rn
					, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.price_option_id END) as option_id
					, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.max_quantity END) as max_quantity
					, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.max_capacity END) as max_capacity
					, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.option_one_code END) as option_one_code
					, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.product_price END) as product_price
					, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.start_date END) as start_date
					, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.end_date END) as end_date
					, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.option_name END) as option_name
					, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.consecurive_discount_amount END) as consecurive_discount_amount
					, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.extra_person_defualt_charge END) as extra_person_defualt_charge
					, MAX(CASE WHEN ag.rn = ld.last_rn THEN ag.extra_person_consecurive_charge END) as extra_person_consecurive_charge
					, ifnull(sum(order_count),0) order_count
					, max(case when ag.rn = ld.last_rn then ag.max_capacity else null end) - ifnull(sum(order_count),0) remain_count
					, ifnull(sum(pick_people),0) pick_people
				FROM aggData ag
				left join lastRnData ld on ag.date = ld.date and ag.rn = ld.last_rn and ag.option_one_code = ld.option_one_code and ag.rn = ld.last_rn
				GROUP BY ag.date, ag.gubn2),
			lastData as (
				select *
					   , (order_count + order_count2) oc
				  from (
					select date
						, MAX(CASE WHEN gubn2 = 'N' THEN option_id END) as option_id
						, MAX(CASE WHEN gubn2 = 'N' THEN max_quantity END) as max_quantity
						, MAX(CASE WHEN gubn2 = 'N' THEN max_capacity END) as max_capacity
						, MAX(CASE WHEN gubn2 = 'N' THEN option_one_code END) as option_one_code
						, MAX(CASE WHEN gubn2 = 'N' THEN product_price END) as product_price
						, MAX(CASE WHEN gubn2 = 'N' THEN option_name END) as option_name
						, MAX(CASE WHEN gubn2 = 'N' THEN consecurive_discount_amount END) as consecurive_discount_amount
						, MAX(CASE WHEN gubn2 = 'N' THEN extra_person_defualt_charge END) as extra_person_defualt_charge
						, MAX(CASE WHEN gubn2 = 'N' THEN extra_person_consecurive_charge END) as extra_person_consecurive_charge
						, MAX(CASE WHEN gubn2 = 'N' THEN order_count END) as order_count
						, MAX(CASE WHEN gubn2 = 'N' THEN remain_count END) as remain_count
						, MAX(CASE WHEN gubn2 = 'N' THEN pick_people END) as pick_people
						, MAX(CASE WHEN gubn2 = 'S' THEN option_id END) as option_id2
						, MAX(CASE WHEN gubn2 = 'S' THEN max_quantity END) as max_quantity2
						, MAX(CASE WHEN gubn2 = 'S' THEN max_capacity END) as max_capacity2
						, MAX(CASE WHEN gubn2 = 'S' THEN option_one_code END) as option_one_code2
						, MAX(CASE WHEN gubn2 = 'S' THEN product_price END) as product_price2
						, MAX(CASE WHEN gubn2 = 'S' THEN start_date END) as start_date
						, MAX(CASE WHEN gubn2 = 'S' THEN end_date END) as end_date
						, MAX(CASE WHEN gubn2 = 'S' THEN option_name END) as option_name2
						, MAX(CASE WHEN gubn2 = 'S' THEN consecurive_discount_amount END) as consecurive_discount_amount2
						, MAX(CASE WHEN gubn2 = 'S' THEN extra_person_defualt_charge END) as extra_person_defualt_charge2
						, MAX(CASE WHEN gubn2 = 'S' THEN extra_person_consecurive_charge END) as extra_person_consecurive_charge2
						, MAX(CASE WHEN gubn2 = 'S' THEN order_count END) as order_count2
						, MAX(CASE WHEN gubn2 = 'S' THEN remain_count END) as remain_count2
						, MAX(CASE WHEN gubn2 = 'S' THEN pick_people END) as pick_people2
					from preLastData a
					GROUP BY date
					ORDER BY date
					) a)
			select
				date,
				date as travel_date ,
				IFNULL(max_quantity,0) as max_quantity,
				IFNULL(max_capacity,0) as max_capacity ,
				IFNULL(max_quantity2,0) as special_quantity ,
				IFNULL(max_capacity2,0) as special_capacity ,
				IFNULL(order_count,0) as order_count ,
				IFNULL(order_count2,0) as special_order_count ,
				IFNULL(oc,0) as total_order_count ,
				case
					when max_capacity is null then 'can\' t sell'
					when order_count > max_capacity then ' over '
					when order_count = max_capacity then 'sold out' else 'remain' end 'rsv_possible' ,
				case when max_capacity2 is null then ' can\'t sell'
					when order_count2 > max_capacity2 then 'over'
					when order_count2 = max_capacity2 then 'sold out'
					else 'remain'
				end 'special_rsv_possible' ,
				product_price as product_price_param ,
				product_price2 as special_price ,
				option_id as price_option_id,
				option_id2 as special_option_id ,
				option_name ,
				option_name2 as special_option_name ,
				option_one_code ,
				option_one_code2 as special_option_one_code ,
				consecurive_discount_amount ,
				extra_person_defualt_charge ,
				extra_person_consecurive_charge ,
				consecurive_discount_amount2 as special_consecurive_discount_amount ,
				extra_person_defualt_charge2 as special_extra_person_defualt_charge ,
				extra_person_consecurive_charge2 as special_extra_person_consecurive_charge,
				case when pr.rest_date is null then 0 else 1 end is_rest_date
			from lastData a
			left join productPeriod pr on a.date = pr.rest_date
	</select>

	<select id="selectOneReservation" parameterType="HashMap" resultType="Reservation">
		SELECT @rownum:=@rownum+1 AS rownum,
		a.id, a.user_email,
		IFNULL(a.user_name, u.user_name) user_name,
		IFNULL(a.user_mobile, u.user_mobile) user_mobile,
		@travel := json_value(travel_schedule_json,'$.data.travelSchedule') travel_schedule_dt,
		@travelF := substring_index(@travel, ' ~ ',1) as datef,
		@travelT :=substring_index(@travel, ' ~ ',-1) as datet,
		berth_type, a.product_serial,a.product_tour_id,ifNUll(p.product_title, '~상품미선택~') product_title, apply_code,
		reservation_code, b.name reservation_code_name, cancel_yn, cancel_code, berth_json,
		travel_schedule_json, air_type_request, air_schedule_json,
		vehicle_type, vehicle_count, vehicle_json, use_point, total_amount,
		a.create_id, DATE_FORMAT(a.create_date, '%Y. %M. %D') AS create_date, a.create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.delete_id, a.delete_date
		FROM reservation a
		left join product_tour p on a.product_serial = p.product_serial and a.product_tour_id = p.product_tour_id
		left join code_item b on a.reservation_code = b.code and b.upper_code = 'reservationType'
		left join `user` u on a.user_email = u.user_email
		join (SELECT @rownum:= 0) rnum
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)" >	and user_name=#{userName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPhone)" >	and user_mobile=#{userMobile}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	and berth_type=#{berthType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_serial=#{productSerial}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	and apply_code=#{applyCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationCode)" >	and reservation_code=#{reservationCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	and cancel_yn=#{cancelYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelCode)" >	and cancel_code=#{cancelCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	and berth_json=#{berthJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	and travel_schedule_json=#{travelScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	and air_type_request=#{airTypeRequest}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	and air_schedule_json=#{airScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	and vehicle_type=#{vehicleType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	and vehicle_count=#{vehicleCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	and vehicle_json=#{vehicleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(usePoint)" >	and use_point=#{usePoint}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(totalAmount)" >	and total_amount=#{totalAmount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		</where>
	</select>

	<insert id="insertReservation" parameterType="Reservation" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO reservation
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)" >	user_name=#{userName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userMobile)" >	user_mobile=#{userMobile},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	berth_type=#{berthType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	product_serial=#{productSerial},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	product_tour_id=#{productTourId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	apply_code=#{applyCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationCode)" >	reservation_code=#{reservationCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	cancel_yn=#{cancelYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelCode)" >	cancel_code=#{cancelCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	berth_json=#{berthJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	travel_schedule_json=#{travelScheduleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	air_type_request=#{airTypeRequest},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	air_schedule_json=#{airScheduleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	vehicle_type=#{vehicleType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	vehicle_count=#{vehicleCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	vehicle_json=#{vehicleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(usePoint)" >	and use_point=#{usePoint}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(totalAmount)" >	total_amount=#{totalAmount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			create_date = now()
		</set>
	</insert>

	<update id="updateReservation" parameterType="Reservation">
		UPDATE reservation
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)" >	user_name=#{userName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userMobile)" >	user_mobile=#{userMobile},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	berth_type=#{berthType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	product_serial=#{productSerial},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	product_tour_id=#{productTourId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	apply_code=#{applyCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationCode)" >	reservation_code=#{reservationCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	cancel_yn=#{cancelYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelCode)" >	cancel_code=#{cancelCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	berth_json=#{berthJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	travel_schedule_json=#{travelScheduleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	air_type_request=#{airTypeRequest},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	air_schedule_json=#{airScheduleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	vehicle_type=#{vehicleType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	vehicle_count=#{vehicleCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	vehicle_json=#{vehicleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(totalAmount)" >	total_amount=#{totalAmount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	create_date=#{createDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
		</where>
	</update>

</mapper>