package kr.co.wayplus.travel.model;

import java.util.ArrayList;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@Data
public class MailLog extends CommonDataSet {
	private int rownum;
	private Integer id;
	private String subject;
	private String fromPresnal;
	private String fromEmail;
	private String fromDttm;
	private String receivedDttm;
	private String flag;
	private String tradeInfomation;

	private String attachHtml;
	private String tradeInfoJson;

	private String date;
	private String type;
	private String amount;
	private String name;
}
