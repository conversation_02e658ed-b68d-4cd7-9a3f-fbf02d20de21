package kr.co.wayplus.travel.config.handler;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import kr.co.wayplus.travel.model.BannerCategory;
import kr.co.wayplus.travel.model.InquiryCategory;
import kr.co.wayplus.travel.model.MainNoticePopup;
import kr.co.wayplus.travel.model.MenuUser;
import kr.co.wayplus.travel.model.SettingCompanyInfo;
import kr.co.wayplus.travel.service.front.PageService;
import kr.co.wayplus.travel.service.front.ProductService;
import kr.co.wayplus.travel.model.ProductCategory;
/**
 * 사용자 정보 기록을 위해 쿠키 기반으로 접속자 고유 아이디를 확인한다.
 */
@Component
public class UserPageCommonInterceptor implements HandlerInterceptor {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final PageService pageService;
	private final ProductService productService;

    @Value("${cookie-set.domain}")
    private String cookieDomain;
    @Value("${cookie-set.prefix}")
    private String cookiePrefix;

    @Autowired
    public UserPageCommonInterceptor(PageService pageService, ProductService productService) {
        this.pageService = pageService;
		this.productService = productService;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        logger.debug("================== Start User Page Common Interceptor ==================");
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                    @Nullable ModelAndView modelAndView) throws Exception {
        try {
            /*
             Get 데이터 요청 및 ContentType 이 application이 아닐 경우에만 메뉴, 헤더/팝업 정보 조회
             */
        	logger.info("/******* "+ request.getRequestURI() +" *********/");
        	logger.info("/******* "+ request.getMethod() +" *********/");
        	logger.info("/******* "+ request.getContentType() +" *********/");
        	logger.info("/******* "+ modelAndView +" *********/");

        	if( modelAndView != null ) {
	        	if((request.getMethod().equals("GET") || request.getMethod().equals("POST"))
	               || (request.getContentType() != null && request.getContentType().startsWith("application"))) {
	               //&& (request.getContentType() == null || !request.getContentType().startsWith("application"))) {
		            Cookie[] cookies = request.getCookies();
		            HashMap<String, Object> param = new HashMap<>();
		            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
		            String strMenuUserFullUri = request.getRequestURI();

		            param.put("now", sdf.format(new Date()));
		            List<String> ids = new ArrayList<>();
		            if(cookies != null)
		                for (Cookie cookie : cookies) {
		                    if (cookie.getName().startsWith(cookiePrefix + "popup.")) {
		                        ids.add(cookie.getName().replace(cookiePrefix + "popup.", ""));
		                    }
		                }
		            param.put("list", ids);
		            modelAndView.addObject("navbar", pageService.getNavbar());

		            modelAndView.addObject("menuList", pageService.getUserMenuList());
		            modelAndView.addObject("menuTreeList", pageService.getUserMenuTreeList());

		            MenuUser nowMenu = pageService.getMenuUserFullMenuUrl( strMenuUserFullUri );
		            modelAndView.addObject("nowMenu", nowMenu );
					//청풍용
					if ( nowMenu != null ) {
						HashMap<String, Object> paramCategory = new HashMap<String, Object>();
						paramCategory.put("productMenuId", nowMenu.getMenuId());
						ArrayList<ProductCategory> menuCategoryList = productService.selectListProductCategory(paramCategory);
						modelAndView.addObject("menuCategoryList", menuCategoryList);
					}
					modelAndView.addObject("servletPath", request.getServletPath());

		            ArrayList<MainNoticePopup> popupList = pageService.getUserPageNoticePopupList(param);
		            modelAndView.addObject("noticePopupList", popupList);

		            ArrayList<MainNoticePopup> barList = pageService.getUserPageNoticeBarList(param);
//		            ArrayList<MainNoticePopup> barPcList = new ArrayList<MainNoticePopup>();
//		            ArrayList<MainNoticePopup> barMobileList = new ArrayList<MainNoticePopup>();
		            Integer cntP=0, cntM=0;

		            for (MainNoticePopup pop : barList) {
						if( pop.getVisibleYnPc().equals("Y") ) {
//							barPcList.add(pop);
							cntP++;
						}
						if( pop.getVisibleYnMobile().equals("Y") ) {
//							barMobileList.add(pop);
							cntM++;
						}
					}
		            modelAndView.addObject("noticeBarList",        barList);
//		            modelAndView.addObject("noticeBarListPc",      barPcList);
		            modelAndView.addObject("noticeBarCountPc",     cntP);
//		            modelAndView.addObject("noticeBarListMobile",  barMobileList);
		            modelAndView.addObject("noticeBarCountMobile", cntM);

		            Long loadMenuId = null;

		            if( nowMenu!= null) {
		            	if( nowMenu.getRootMenuId() != null) {
		            		loadMenuId = nowMenu.getRootMenuId();
		            	} else if( nowMenu.getUpperMenuId() != null) {
		            		loadMenuId = nowMenu.getUpperMenuId();
		            		//modelAndView.addObject("bannerList", pageService.getListBannerImage( nowMenu.getUpperMenuId() , "sub"));
		            	} else {
		            		loadMenuId = nowMenu.getMenuId();
		            	}

		            }

	            	if ( loadMenuId!= null ) {
		            	HashMap<String, Object> paramMap = new HashMap<>();

		            	paramMap.put("menuId", loadMenuId);
		    			paramMap.put("useYn", "Y");
		    			paramMap.put("deleteYn", "N");
		    			ArrayList<BannerCategory> categories = pageService.selectListBannerCategory(paramMap);

		    			for(BannerCategory  bc : categories ) {
							HashMap<String, Object> bannerParam = new HashMap<String, Object>();
							//bannerParam.put("bannerType", "MAIN");

							bannerParam.put("useYn", "Y" );
							bannerParam.put("visibleYnPc", "Y" );
							bannerParam.put("deleteYn", "N");
							bannerParam.put("bannerType", bc.getBannerType() );
							bannerParam.put("menuId", loadMenuId );

							if( bc.getBannerType().equals("sub") || bc.getBannerType().equals("vision") ) {
								bannerParam.put("itemStartPosition", 0 );
								bannerParam.put("pagePerSize", 1 );
							} if( bc.getBannerType().equals("work") ) {
								bannerParam.put("itemStartPosition", 0 );
								bannerParam.put("pagePerSize", 4 );
							} else {
								bannerParam.remove("itemStartPosition");
								bannerParam.remove("pagePerSize" );
							}


							System.out.println( "rollingTopBannerList_"+bc.getBannerType() );

							modelAndView.addObject("rollingTopBannerList_"+bc.getBannerType(), pageService.selectListMainBannerImage(bannerParam));
		    			}
	            	}


					HashMap<String, Object> bannerParam = new HashMap<>();
					if( nowMenu!= null && nowMenu.getUpperMenuId() != null) {
						bannerParam.put("menuId", nowMenu.getUpperMenuId());
					} else if ( nowMenu!= null ) {
						bannerParam.put("menuId", nowMenu.getMenuId());
					} else {
						bannerParam.put("menuId", 0);
					}
					/*
					bannerParam.put("useYn", "Y");
					bannerParam.put("bannerType", "subBanner");
					modelAndView.addObject("subItemBannerList", pageService.selectListMainBannerImageAndCategory(bannerParam));
					*/

		            SettingCompanyInfo company = pageService.getUserPageFooterInfo();
		            //modelAndView.addObject("footer", company);
		            modelAndView.addObject("company", company);

					// 문의하기 - 문의종류
					HashMap<String, Object> paramMap = new HashMap<>();
					paramMap.put("deleteYn", "N");
					ArrayList<InquiryCategory> categories = pageService.selectListInquiryCategory(paramMap);
					modelAndView.addObject("categories", categories);
		        }
        	}
        } catch (Exception e) {
            logger.error(e.getMessage());
            e.printStackTrace();
        }
        logger.debug("================== End User Page Common Interceptor ==================");
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                 @Nullable Exception ex) throws Exception {
    }


}
