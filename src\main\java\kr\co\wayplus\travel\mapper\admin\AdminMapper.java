package kr.co.wayplus.travel.mapper.admin;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.ManageMenuAuth;
import kr.co.wayplus.travel.model.ManageMenuConnectAuth;
import kr.co.wayplus.travel.model.SmsRecivedUser;

@Mapper
@Repository
public interface AdminMapper {

    int selectAdministratorUserListCount();

    ArrayList<LoginUser> selectAdministratorUserList(HashMap<String, Object> param);

    LoginUser selectAdministratorInfo(String userEmail);

    void insertAdministrator(LoginUser user);

    void insertWithdrawalAdministrator(LoginUser user);

    void deleteAdministrator(LoginUser user);

    void updateAdministrator(LoginUser user);

//	<!--################################### manageMenuAuth ###################################-->
	int selectCountManageMenuAuth(HashMap<String, Object> paramMap);

	ArrayList<ManageMenuAuth> selectListManageMenuAuth(HashMap<String, Object> paramMap);

	ManageMenuAuth selectOneManageMenuAuth(HashMap<String, Object> paramMap);

	void insertManageMenuAuth(ManageMenuAuth bc) throws SQLException;

	void updateManageMenuAuth(ManageMenuAuth bc) throws SQLException;

	void deleteManageMenuAuth(ManageMenuAuth bc) throws SQLException;

	void restoreManageMenuAuth(ManageMenuAuth bc) throws SQLException;

	int selectCountSmsRecivedUser(HashMap<String, Object> paramMap);

	ArrayList<SmsRecivedUser> selectListSmsRecivedUser(HashMap<String, Object> paramMap);
	SmsRecivedUser selectOneSmsRecivedUser(HashMap<String, Object> paramMap);
	void insertSmsRecivedUser(SmsRecivedUser bc) throws SQLException;
	void updateSmsRecivedUser(SmsRecivedUser bc) throws SQLException;
	void deleteSmsRecivedUser(SmsRecivedUser bc) throws SQLException;
	void restoreSmsRecivedUser(SmsRecivedUser bc) throws SQLException;

//	<!--################################### manageMenuAuth ###################################-->
	int selectCountManageMenuConnectAuth(HashMap<String, Object> paramMap);

	ArrayList<ManageMenuConnectAuth> selectListManageMenuConnectAuth(HashMap<String, Object> paramMap);

	ManageMenuConnectAuth selectOneManageMenuConnectAuth(HashMap<String, Object> paramMap);

	void insertManageMenuConnectAuth(ManageMenuConnectAuth bc) throws SQLException;

	void updateManageMenuConnectAuth(ManageMenuConnectAuth bc) throws SQLException;

	void deleteManageMenuConnectAuth(ManageMenuConnectAuth bc) throws SQLException;

}
