package kr.co.wayplus.travel.web.user;

import java.sql.SQLException;
import java.time.LocalDate;
import java.time.Month;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import kr.co.wayplus.travel.model.*;
import kr.co.wayplus.travel.service.manage.BadgeManageService;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import kr.co.wayplus.travel.service.common.MessageSenderService;
import kr.co.wayplus.travel.service.front.BoardService;
import kr.co.wayplus.travel.service.front.CodeService;
import kr.co.wayplus.travel.service.front.PageService;
import kr.co.wayplus.travel.service.front.ReservationService;
import kr.co.wayplus.travel.service.manage.InquiryManageService;
import kr.co.wayplus.travel.service.manage.ManageService;
import kr.co.wayplus.travel.service.manage.ProductManageService;
import kr.co.wayplus.travel.service.manage.ReservationManageService;
import kr.co.wayplus.travel.service.manage.UserManageService;
import kr.co.wayplus.travel.service.user.UserPointService;
import kr.co.wayplus.travel.service.user.UserService;
import kr.co.wayplus.travel.util.CookieUtil;
import kr.co.wayplus.travel.util.CryptoUtil;
import kr.co.wayplus.travel.util.CustomBcryptPasswordEncoder;
import java.io.File;

@Controller
@RequestMapping("/user")
public class UserController {

    private final UserService userService;
    private final UserPointService userPointService;
    private final BadgeManageService badgeManageService;
    private final InquiryManageService inquiryManageService;
    private final ReservationService reservationService;
    private final BoardService boardService;
    private final PageService pageService;
    private final CookieUtil cookieUtil;
    private final MessageSenderService messageSenderService;
    private final CodeService codeService;
    private final ReservationManageService reservationManageService;
    private final UserManageService userManageService;
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Value("${key.crypto.encrypt}")
    private String encrypt;
    @Value("${key.crypto.iv}")
    private String iv;

    @Value("${cookie-set.domain}")
    private String cookieDomain;
    @Value("${cookie-set.prefix}")
    private String cookiePrefix;
    @Value("${cookie-set.tracking-age}")
    private int trackerMaxAge;
    @Value("${upload.file.path}")
    String externalImageUploadPath;
    @Value("${upload.file.max-size}")
    int maxFileSize;

    final String addPath = "images/user/";
    private String frontUrl = "/front";

    @Autowired
    public UserController(
            UserService userService,
            UserPointService userPointService,
            BadgeManageService badgeManageService, InquiryManageService inquiryManageService,
            ReservationService reservationService,
            BoardService boardService,
            PageService pageService,
            CookieUtil cookieUtil,
            MessageSenderService messageSenderService,
            CodeService codeService,
            ReservationManageService reservationManageService,
			UserManageService userManageService) {
        this.userService = userService;
        this.userPointService = userPointService;
        this.badgeManageService = badgeManageService;
        this.inquiryManageService = inquiryManageService;
        this.reservationService = reservationService;
        this.boardService = boardService;
        this.pageService = pageService;
        this.cookieUtil = cookieUtil;
        this.messageSenderService = messageSenderService;
        this.codeService = codeService;
        this.reservationManageService = reservationManageService;
		this.userManageService = userManageService;
    }

    /**
     * 로그인 페이지 호출
     * @param error 로그인 실패시 전달 받은 오류 메시지
     * @param code 로그인 실패시 전달 받은 오류 코드
     * @return Login Page
     */
    @GetMapping("/login")
    public ModelAndView login(HttpServletRequest request,
          HttpSession session,
          @RequestParam(value="error", defaultValue="n") String error,
          @RequestParam(value="code", defaultValue="n") String code,
          @Param(value="redirect") String redirect){
        logger.debug("Get Login Page.");

        ModelAndView mav = new ModelAndView();

        if (checkLogin(session)) {
            return new ModelAndView(new RedirectView("/"));
        }

        // 로그인 전문 전송시 사용할 AES 암호화 키 생성
        setSessionEncryptKey(session);

        // 아이디 저장 사용 시 쿠키에 저장된 아이디 읽어옴
        Cookie savedLoginIdCookie = cookieUtil.getCookieByName(request, "login.id");
        if(savedLoginIdCookie != null){
            mav.addObject("loginId", savedLoginIdCookie.getValue());
        }

        if(redirect != null){
            mav.addObject("redirect", redirect);
        }

        // 로그인 에러 메시지 처리
        if(error.equals("y")){
            switch (code) {
                case "NON" -> mav.addObject("message", "존재하지 않는 사용자입니다.");
                case "PAS" -> mav.addObject("message", "비밀번호가 일치하지 않습니다.");
                case "LOC" -> mav.addObject("message", "잠긴 계정입니다.");
                case "DIS" -> mav.addObject("message", "비활성화 된 계정입니다.");
                case "EXD" -> mav.addObject("message", "만료된 계정입니다.");
                case "EXP" -> mav.addObject("message", "비밀번호가 만료된 계정입니다.");
                default -> mav.addObject("message", "로그인에 실패했습니다.");
            }
        }


        mav.setViewName("user/login");
        return mav;
    }

    /**
     * 사용자 로그 아웃 입력 처리
     * @return Redirect Index Page
     */
    @RequestMapping("/logout")
    public ModelAndView logout(HttpSession session){
        logger.debug("User Action Logout");
        try {
            SecurityContextHolder.clearContext();
            session.invalidate();

            if (SecurityContextHolder.getContext().getAuthentication() != null
                    && !SecurityContextHolder.getContext().getAuthentication().getPrincipal().equals("anonymousUser")) {
                //로그인 정보가 있을 경우 기록 작성
                LoginUserSession loginUserSession = new LoginUserSession();
                LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
                loginUserSession.setLoginSession(session.getId());
                loginUserSession.setUserEmail(user.getUserEmail());
                loginUserSession.setLogoutType("ACTION");
                userService.updateUserSessionLogout(loginUserSession);
            }
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
        }

        return new ModelAndView(new RedirectView("/user/login"));
    }

    /**
     * 사용자 세션 만료 처리
     * @return Redirect Index Page
     */
    @RequestMapping("/session-expired")
    public ModelAndView sessionExpired(HttpSession session){
        logger.debug("User Session Expired.");
        session.invalidate();
        return new ModelAndView(new RedirectView("/"));
    }

    /**
     * 회원 가입 페이지 호출
     * @return User Join Page
     */
    @GetMapping("/join")
    public ModelAndView join(HttpSession session){
        logger.debug("User Join Page");
        if (checkLogin(session)) {
            return new ModelAndView(new RedirectView("/"));
        }

        ModelAndView mav = new ModelAndView();

        // 로그인 전문 전송시 사용할 AES 암호화 키 생성
        setSessionEncryptKey(session);

        mav.setViewName("user/join");

        HashMap<String, Object> param = new HashMap<>();
        param.put("bannerType", "MAIN");
        param.put("useYn", "Y");
        mav.addObject("bannerList", pageService.selectListMainBannerImage(param));

        HashMap<String, Object> paramPolicy = new HashMap<>();

        paramPolicy.put("isLastPolicy", true);
        paramPolicy.put("policyType", "1");
		Policy policy1 = pageService.selectOnePolicy( paramPolicy );
		paramPolicy.put("policyType", "2");
		Policy policy2 = pageService.selectOnePolicy( paramPolicy );
        paramPolicy.put("policyType", "3");
		Policy policy3 = pageService.selectOnePolicy( paramPolicy );
        paramPolicy.put("policyType", "4");
		Policy policy4 = pageService.selectOnePolicy( paramPolicy );
		paramPolicy.put("policyType", "5");
		Policy policy5 = pageService.selectOnePolicy( paramPolicy );
		paramPolicy.put("policyType", "9");
		Policy policy9 = pageService.selectOnePolicy( paramPolicy );
		paramPolicy.put("policyType", "10");
		Policy policy10 = pageService.selectOnePolicy( paramPolicy );

		mav.addObject("policyOperating", policy1);
		mav.addObject("policyMarketing", policy2);
        mav.addObject("policyUsage", policy3);
		mav.addObject("policyPrivacy", policy4);
		mav.addObject("policyPolicy", policy5);
		mav.addObject("policyRegulation", policy9);
		mav.addObject("policyStipulation", policy10);
        return mav;
    }

    /**
     * 회원 가입 완료페이지
     * @param session 사용자 세션e
     */
    @GetMapping("/join-complete")
    public ModelAndView joinComplte(HttpSession session){
        logger.debug("User Find ID Page");
        if (checkLogin(session)) {
            return new ModelAndView(new RedirectView("/"));
        }
        ModelAndView mav = new ModelAndView();
        session.removeAttribute("SMS_SUCCESS");

        mav.setViewName("user/join-complete");
        return mav;
    }

    /**
     * 회원 아이디 찾기 페이지 호출
     * @param session 사용자 세션
     * @return User Find ID Page
     */
    @GetMapping("/find-id")
    public ModelAndView findId(HttpSession session){
        logger.debug("User Find ID Page");
        if (checkLogin(session)) {
            return new ModelAndView(new RedirectView("/"));
        }
        CryptoUtil cryptoUtil = new CryptoUtil();
        session.setAttribute("encrypt", cryptoUtil.generateRandomEncryptKey(""));
        session.setAttribute("iv", cryptoUtil.generateRandomIv(""));

        ModelAndView mav = new ModelAndView();
        mav.setViewName("user/find-id");
        return mav;
    }

    /**
     * 회원 비밀번호 찾기 페이지 호출
     * @param session 사용자 세션
     * @return User Find Password Page
     */
    @GetMapping("/find-password")
    public ModelAndView findIdResult(HttpSession session){
        logger.debug("User Find Password Page");
        if (checkLogin(session)) {
            return new ModelAndView(new RedirectView("/"));
        }
        ModelAndView mav = new ModelAndView();

        // 로그인 전문 전송시 사용할 AES 암호화 키 생성
        setSessionEncryptKey(session);

        mav.setViewName("user/find-password");
        return mav;
    }

    /**
     * 아이디 중복 체크
     * @param id 사용자 아이디
     * @return 해당 아이디 사용자 검색 결과 수
     */
    @ResponseBody
    @GetMapping("/check/{id}")
    public Map<String, Object> checkId(@PathVariable String id){
        logger.debug("User Exist Check Start");
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            HashMap<String, Object> param = new HashMap<>();
            param.put("id", id);
            int count = userService.findUserCountById(param);

            if(count > 0) resultMap.put("duplicate", true);
            else resultMap.put("duplicate", false);
            resultMap.put("result", "success");

        }catch (Exception e){
            logger.debug("User Exist Check Error");
            logger.error(e.getMessage());
            resultMap.put("result", "error");
            resultMap.put("message", "오류가 발생했습니다.");
        }

        logger.debug("User Exist Check End");
        return resultMap;
    }

    /**
     * 회원 가입 처리
     * @param encrypted 사용자 암호 평문 암호화 여부
     * @param user 등록할 사용자 정보
     * @return 결과 JSON {result: "처리 결과", message: "처리 내용"}
     */
    @ResponseBody
    @PostMapping("/register")
    public Map<String, Object> register(HttpSession session,
                                        HttpServletResponse response,
                                        @RequestParam(value="encrypted", defaultValue="true") String encrypted,
                                        @ModelAttribute LoginUser user, BindingResult bindingResult){
        logger.debug("User Register Start");
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            //필수 정보 확인
            if(user == null
                    || user.getUserEmail() == null
                    || user.getUserPassword() == null
                    || user.getUserName() == null
            ) {
                throw new Exception("사용자 정보를 확인 해 주세요.");
            }

            if(session.getAttribute("encrypt") != null) encrypt = (String) session.getAttribute("encrypt");
            if(session.getAttribute("iv") != null) iv = (String) session.getAttribute("iv");
            user.setEncrypt(encrypt);
            user.setIv(iv);

            //새로 생성된 ID 트래킹 값 갱신 처리(중복 방지)
            user.setUserTokenId(String.valueOf(UUID.randomUUID()));
            response.addCookie(cookieUtil.createCookie("tracker.id", user.getUserTokenId(), trackerMaxAge));

            userService.addUser(user, Boolean.parseBoolean(encrypted));
            userPointService.createJoinPoint(user);

            resultMap.put("result", "success");
            resultMap.put("message", "등록 완료");
        }catch (Exception e){
            logger.debug("User Register Error");
            logger.error(e.getMessage());
            resultMap.put("result", "error");
            resultMap.put("message", "오류가 발생했습니다.");
        }
        logger.debug("User Register End");
        return resultMap;
    }

    /**
     * 회원 탈퇴 처리(DB 분리 보관)
     * @param encrypted 사용자 암호 평문 암호화 처리 여부
     * @param user 탈퇴 사용자 정보
     * @return 결과 JSON {result: "처리 결과", message: "처리 내용"}
     */
    @ResponseBody
    @PostMapping("/withdrawal")
    public Map<String, Object> withdrawal(HttpSession session,
                                        @RequestParam(value="encrypted", defaultValue="true") String encrypted,
                                        @ModelAttribute LoginUser user, BindingResult bindingResult){
        logger.debug("User Withdrawal Start");
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            //필수 정보 확인
            if(user == null
                    || user.getUserEmail() == null
                    || user.getUserPassword() == null
            ) {
                throw new Exception("사용자 정보를 확인 해 주세요.");
            }

            if(session.getAttribute("encrypt") != null) encrypt = (String) session.getAttribute("encrypt");
            if(session.getAttribute("iv") != null) iv = (String) session.getAttribute("iv");
            user.setEncrypt(encrypt);
            user.setIv(iv);

            userService.withdrawalUser(user, Boolean.parseBoolean(encrypted));

            resultMap.put("result", "success");
            resultMap.put("message", "탈퇴 완료");
        }catch (Exception e){
            logger.debug("User Withdrawal Error");
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }
        logger.debug("User Withdrawal End");
        return resultMap;
    }

    @ResponseBody
    @PostMapping("/find-id")
    public Map<String, Object> findId(@RequestParam(value="user_name", defaultValue="") String name,
                                      @RequestParam(value="user_mobile", defaultValue="") String mobile){
        logger.debug("User Find Start");

        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            ArrayList<LoginUser> users = userService.findUserListByUserName(name, mobile);
            resultMap.put("list", users);
            resultMap.put("result", "success");
        }catch (Exception e){
            logger.debug("User Find Error");
            logger.error(e.getMessage());
            resultMap.put("result", "error");
            resultMap.put("message", "오류가 발생했습니다.");
        }

        logger.debug("User Find End");
        return resultMap;
    }

    @ResponseBody
    @PostMapping("/find-password")
    public Map<String, Object> findPassword(HttpSession session,
                                            @RequestParam(value="user_email", defaultValue="") String email,
                                            @RequestParam(value="user_name", defaultValue="") String name,
                                            @RequestParam(value="user_mobile", defaultValue="") String mobile){
        logger.debug("User Find Password Start");

        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            HashMap<String, Object> param = new HashMap<>();
            param.put("userEmail", email);
            param.put("userName", name);
            LoginUser user = userService.findRePasswordUserByUserInfo(param);

            if( user == null ) {
            	resultMap.put("result", "none-user");
                resultMap.put("message", "해당되는 계정정보가 없습니다.");
                return resultMap;
            }

            if(session.getAttribute("encrypt") != null) encrypt = (String) session.getAttribute("encrypt");
            if(session.getAttribute("iv") != null) iv = (String) session.getAttribute("iv");
            user.setEncrypt(encrypt);
            user.setIv(iv);
            if ( user != null ) {
                messageSenderService.sendMailFromSet("inquiry", 1, "pwd", user);
            	resultMap.put("user", user);
            }
            resultMap.put("result", "success");
        }catch (Exception e){
            logger.debug("User Find Password Error");
            logger.error(e.getMessage());
            resultMap.put("result", "error");
            resultMap.put("message", "오류가 발생했습니다.");
        }

        logger.debug("User Find Password End");
        return resultMap;
    }

    @ResponseBody
    @PutMapping("/find-password-change")
    public Map<String, Object> findPassword(HttpSession session,
            @RequestParam(value="encrypted", defaultValue="true") String encrypted,
            @ModelAttribute LoginUser user, BindingResult bindingResult){
        logger.debug("User Password Update Start");

        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            if(session.getAttribute("encrypt") != null) encrypt = (String) session.getAttribute("encrypt");
            if(session.getAttribute("iv") != null) iv = (String) session.getAttribute("iv");
            user.setEncrypt(encrypt);
            user.setIv(iv);
            userService.updateUserPasswordByLost(user, Boolean.parseBoolean(encrypted));
            resultMap.put("result", "success");
        }catch (Exception e){
            logger.debug("User Password Update Error");
            logger.error(e.getMessage());
            resultMap.put("result", "error");
            resultMap.put("message", "오류가 발생했습니다.");
        }

        logger.debug("User Password Update End");
        return resultMap;
    }

    /**
     * 로그인 사용자 비밀번호 인증
     * @param session
     * @param encrypted
     * @param password
     * @return
     */
    @ResponseBody
    @PostMapping("/password-check")
    public Map<String, Object> passwordCheck(HttpSession session,
                                             @RequestParam(value="encrypted", defaultValue="true") String encrypted,
                                             @RequestParam(value="password", defaultValue = "") String password){
        logger.debug("User Password Check Start");

        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            if(session.getAttribute("encrypt") != null) encrypt = (String) session.getAttribute("encrypt");
            if(session.getAttribute("iv") != null) iv = (String) session.getAttribute("iv");
            user.setUserPassword(password);
            user.setEncrypt(encrypt);
            user.setIv(iv);

            LoginUser checkUser = userService.findUserByIdPassword(user, Boolean.parseBoolean(encrypted));
            if(checkUser == null){
                resultMap.put("result", "fail");
                resultMap.put("message", "비밀번호가 일치하지 않습니다.");
            }else{
                resultMap.put("result", "success");
                resultMap.put("tokenId", checkUser.getUserTokenId());
            }
        }catch (Exception e){
            logger.debug("User Password Check Error");
            logger.error(e.getMessage());
            resultMap.put("result", "error");
            resultMap.put("message", "오류가 발생했습니다.");
        }

        logger.debug("User Exist Check End");
        return resultMap;
    }

    /**
     * 사용자 비밀번호 일치 여부
     * @param session 사용자 세션
     * @param userPass 사용자가 입력한 값
     */
//    @PostMapping("/password-check")
//    @ResponseBody
//    public HashMap<String, Object> userPasswordCheck(HttpSession session, @RequestParam(value = "userPass") String userPass){
//        HashMap<String, Object> resultMap = new HashMap<>();
//
//        if(session.getAttribute("encrypt") == null
//                || session.getAttribute("iv") == null){
//            resultMap.put("message", "Login session information is invalid.");
//            resultMap.put("result", "error");
//        }else {
//            String encrypt = (String) session.getAttribute("encrypt");
//            String iv = (String) session.getAttribute("iv");
//            try {
//                LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
//                loginUser.setUserPassword(CryptoUtil.aesDecode(userPass, encrypt, iv));
//
//                HashMap<String, Object> param = new HashMap<>();
//                param.put("id", loginUser.getUserEmail());
//                LoginUser storedUser = userService.findUserById(param);
//                CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();
//                if(passwordEncoder.directMatches(loginUser.getUserPassword(), storedUser.getPassword())){
//                    resultMap.put("result", "success");
//                }else{
//                    resultMap.put("result", "notMatch");
//                    resultMap.put("message", "Passwords do not match.");
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//                resultMap.put("result", "error");
//                resultMap.put("message", e.getMessage());
//            }
//        }
//
//        return resultMap;
//    }

    @ResponseBody
    @PutMapping("/update")
    public Map<String, Object> updateUser(HttpSession session,
            @RequestParam(value="encrypted", defaultValue="true") String encrypted,
            @ModelAttribute LoginUser user, BindingResult bindingResult,
            MultipartHttpServletRequest request){
        logger.debug("User Update Start");

        HashMap<String, Object> resultMap = new HashMap<>();
        String userId;

        try{
            LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            if(user == null
                    || user.getUserEmail() == null
                    || user.getUserPassword() == null
                    || user.getUserName() == null
            ) {
                throw new Exception("사용자 정보를 확인 해 주세요.");
            }
            
            if(session.getAttribute("encrypt") != null) encrypt = (String) session.getAttribute("encrypt");
            if(session.getAttribute("iv") != null) iv = (String) session.getAttribute("iv");

            //프로필 사진등록
            List<MultipartFile> multipartFiles;
            if (request.getFile("image") != null) {
                multipartFiles = request.getFiles("image");
                File file = new File(externalImageUploadPath + addPath);
                if (!file.exists()) file.mkdirs();
                for (MultipartFile multipartFile : multipartFiles) {
                    if(multipartFile.getSize() == 0) continue;
                    String uploadName = UUID.randomUUID().toString();
                    multipartFile.transferTo(new File(externalImageUploadPath + addPath + uploadName));
                    logger.debug("User Question File Uploaded : " + multipartFile.getOriginalFilename());
                    UserAttachFile attachImage = new UserAttachFile();
                    attachImage.setServiceType("profile");
                    attachImage.setUserEmail(user.getUserEmail());
                    attachImage.setUploadPath(externalImageUploadPath + addPath);
                    attachImage.setUploadFilename(addPath + uploadName);
                    attachImage.setOriginFilename(multipartFile.getOriginalFilename());
                    attachImage.setFileSize((int) multipartFile.getSize());
                    attachImage.setFileMimetype(multipartFile.getContentType());
                    if (multipartFile.getOriginalFilename().contains(".")) {
                        attachImage.setFileExtension(multipartFile.getOriginalFilename().substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
                    }
                    if (user != null) {
                        attachImage.setUploadId(String.valueOf(user.getUserEmail()));
                    }
                    userService.writeUserAttachFile(attachImage);
                    user.setUserProfileImage(addPath + uploadName);
                }
            }

            user.setEncrypt(encrypt);
            user.setIv(iv);
            user.setUserTokenId(loginUser.getUserTokenId());
            userService.updateUserInfo(user, Boolean.parseBoolean(encrypted));
            user.setUserPassword(null);
            session.setAttribute("login", user);

            //프로파일 노출 설정 뱃지 추가
            if(user.getUserNickName() != null
                    && user.getUserShowYn() != null && user.getUserShowYn().equals("Y")){
                HashMap<String, Object> param = new HashMap<>();
                param.put("automationType", "profile");
                ArrayList<BadgeContents> profileBadgeList = badgeManageService.selectBadgeListByAutomationType(param);
                if(profileBadgeList != null){
                    for(BadgeContents badge : profileBadgeList){
                        badgeManageService.badgeAcquire(user.getUserEmail(),user.getUserEmail(), badge);
                    }
                }
            }

            resultMap.put("result", "success");
            resultMap.put("message", "저장됐습니다.");
        }catch (Exception e){
            e.printStackTrace();
            logger.debug("User Update Error");
            logger.error(e.getMessage());
            resultMap.put("result", "error");
            resultMap.put("message", "오류가 발생했습니다.");
        }

        logger.debug("User Update End");
        return resultMap;
    }

    /**
     * 사용자의 포인트 목록을 조회한다.
     * @param userEmail
     * @param userToken
     * @return
     */
    @ResponseBody
    @GetMapping("/point/list")
    public Map<String, Object> pointList(@RequestParam(value="userEmail", defaultValue="") String userEmail,
                                         @RequestParam(value="userToken", defaultValue="") String userToken,
                                         @RequestParam(value="page",defaultValue="1") int page,
                                         @RequestParam(value="pageSize",defaultValue="10") int pageSize){
        logger.debug("User Point List Get Start");

        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            HashMap<String, Object> param = new HashMap<>();
            param.put("userEmail", userEmail);
            param.put("userToken", userToken);
            LoginUser user = userService.findUserByIdToken(param);
            if(user != null) {
                int totalCount = userPointService.getUserPointRecordListCount(param);
                PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
                param.put("itemStartPosition", paging.getItemStartPosition());
                param.put("pagePerSize", paging.getPagePerSize());
                resultMap.put("paging", paging);
                resultMap.put("point", userPointService.getUserPointRecordSummary(param));
                resultMap.put("pointList", userPointService.getUserPointRecordList(param));
                resultMap.put("result", "success");
                resultMap.put("message", "조회 완료.");
            }else {
                resultMap.put("result", "fail");
                resultMap.put("message", "사용자 정보를 찾을 수 없습니다.");
            }
        }catch (Exception e){
            logger.debug("User Point List Get Error");
            logger.error(e.getMessage());
            resultMap.put("result", "error");
            resultMap.put("message", "오류가 발생했습니다.");
        }

        logger.debug("User Point List Get");
        return resultMap;
    }

    /**
     * 사용자 포인트를 사용한다.
     * @param userEmail
     * @param userToken
     * @param usedType
     * @param usedPoint
     * @return
     */
    @ResponseBody
    @PutMapping("/point/use")
    public Map<String, Object> pointUse(HttpSession session,
                                        @RequestParam(value="userEmail", defaultValue="") String userEmail,
                                        @RequestParam(value="userToken", defaultValue="") String userToken,
                                        @RequestParam(value="usedType", defaultValue="") String usedType,
                                        @RequestParam(value="usedPoint", defaultValue="0") int usedPoint){
        logger.debug("User Point Use Start");

        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            HashMap<String, Object> param = new HashMap<>();
            param.put("userEmail", userEmail);
            param.put("userToken", userToken);
            LoginUser userCheck = userService.findUserByIdToken(param);
            if(userCheck != null) {
                LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
                userPointService.useUserPoint(userEmail, usedType, usedPoint, user.getUserEmail());
                resultMap.put("result", "success");
                resultMap.put("message", "저장됐습니다.");
            }else {
                resultMap.put("result", "fail");
                resultMap.put("message", "사용자 정보를 찾을 수 없습니다.");
            }
        }catch (Exception e){
            logger.debug("User Point Use Error");
            logger.error(e.getMessage());
            resultMap.put("result", "error");
            resultMap.put("message", "오류가 발생했습니다.");
        }

        logger.debug("User Point Use End");
        return resultMap;
    }

    /**
     * 사용자 포인트 습득 페이지 호출
     * @param session 사용자 세션
     * @return User Point Earn Page
     */
    @GetMapping("/point-earn")
    public ModelAndView pointEarn(HttpSession session){
        logger.debug("User Point Earn Page");
        if (!checkLogin(session)) {
            return new ModelAndView(new RedirectView("/"));
        }
        ModelAndView mav = new ModelAndView();
        HashMap<String, Object> param = new HashMap<>();


        String userId;
        LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        param.put("userEmail", user.getUserEmail());
        param.put("accruedType", "Day Login");

        LocalDate currentDate = LocalDate.now();
        // 현재 월을
        Month currentMonth = currentDate.getMonth();
        param.put("currentMonth", currentMonth.getValue());

        List<UserPointRecord> userDayPoint =  userPointService.getUserPointRecord(param);

        mav.addObject("userDayPoint", userDayPoint);

        mav.setViewName(frontUrl+"/point-earn");
        return mav;
    }

    /**
     * 사용자 예약 현황 페이지 호출 (사용자 마이페이지 첫화면)
     * @param session 사용자 세션
     * @return User Reservation List Page
     */
    @GetMapping("/my-page")
    public ModelAndView reservationList(HttpSession session,
                                        @RequestParam(value="page",defaultValue="1") int page,
                                        @RequestParam(value="pageSize",defaultValue="10") int pageSize){
        logger.debug("User Reservation List Page");
        if (!checkLogin(session)) {
            return new ModelAndView(new RedirectView("/"));
        }
        ModelAndView mav = new ModelAndView();
        HashMap<String, Object> param = new HashMap<>();

        LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        param.put("customerName", user.getUserName());
        param.put("customerPhone", user.getUserMobile());
        param.put("userEmail", user.getUserEmail());

        //유저 예약리스트
        int totalCount = reservationService.selectCountReservation(param);
        PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
        param.put("itemStartPosition", paging.getItemStartPosition());
        param.put("pagePerSize", paging.getPagePerSize());
        ArrayList<Reservation> reservationList = reservationService.selectListReservation(param);
        //유저 포인트
        param.put("userEmail", user.getUserEmail());
        UserPointRecord userPoint = userPointService.getUserPointRecordSummary(param);
        //현재날짜로부터 한달안에 삭제될 포인트
        UserPointRecord userExpiringPoint = userPointService.getUserExpiringPointRecordSummary(param);

        mav.addObject("paging", paging);
        mav.addObject("reservationList", reservationList);
        mav.addObject("userPoint", userPoint);
        mav.addObject("userExpiringPoint", userExpiringPoint);

        mav.setViewName(frontUrl+"/user/mypage/home");
        return mav;
    }

    /**
     * 사용자 문의 현황 페이지 호출
     * @param session 사용자 세션
     * @return User Inquiry List Page
     */
    @GetMapping("/inquiry")
    public ModelAndView inquiryList(HttpSession session,
                                        @RequestParam(value="page",defaultValue="1") int page,
                                        @RequestParam(value="pageSize",defaultValue="10") int pageSize){
        logger.debug("User Inquiry List Page");
        if (!checkLogin(session)) {
            return new ModelAndView(new RedirectView("/"));
        }
        ModelAndView mav = new ModelAndView();
        HashMap<String, Object> param = new HashMap<>();

        LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        param.put("customerName", user.getUserName());
        param.put("createId", user.getUserEmail());

        //유저 예약리스트
        int totalCount = pageService.selectCountInquiryContent(param);
        PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
        param.put("itemStartPosition", paging.getItemStartPosition());
        param.put("pagePerSize", paging.getPagePerSize());
        ArrayList<InquiryContent> inquiryList = pageService.selectListInquiryContent(param);
        //유저 포인트
        param.put("userEmail", user.getUserEmail());
        UserPointRecord userPoint = userPointService.getUserPointRecordSummary(param);
        //현재날짜로부터 한달안에 삭제될 포인트
        UserPointRecord userExpiringPoint = userPointService.getUserExpiringPointRecordSummary(param);

        mav.addObject("paging", paging);
        mav.addObject("inquiryList", inquiryList);
        mav.addObject("userPoint", userPoint);
        mav.addObject("userExpiringPoint", userExpiringPoint);

        mav.setViewName(frontUrl+"/user/mypage/inquiry-list");
        return mav;
    }

    /**
     * 사용자 문의 현황 상세 페이지 호출
     * @param session 사용자 세션
     * @return User Inquiry Detail Page
     */
    @GetMapping("/inquiry/view")
    public ModelAndView inquiryView(
            HttpSession session,
            @RequestParam(value="id") Integer id ) throws SQLException {
        logger.debug("User Inquiry Detail Page");
        ModelAndView mav = new ModelAndView();
        if (!checkLogin(session)) {
            return new ModelAndView(new RedirectView("/"));
        }

        HashMap<String, Object> paramMap = new HashMap<>();
        LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        paramMap.put("userEmail", user.getUserEmail());

        paramMap.put("id", id);
        InquiryContent data = pageService.selectOneInquiryContent(paramMap);
        List<InquiryAttachFile> attachList = inquiryManageService.selectOneInquiryContentFiles(paramMap);

        //유저 포인트
        paramMap.put("userEmail", user.getUserEmail());
        UserPointRecord userPoint = userPointService.getUserPointRecordSummary(paramMap);
        //현재날짜로부터 한달안에 삭제될 포인트
        UserPointRecord userExpiringPoint = userPointService.getUserExpiringPointRecordSummary(paramMap);

        mav.addObject("content", data);
        mav.addObject("attachList", attachList);
        mav.addObject("userPoint", userPoint);
        mav.addObject("userExpiringPoint", userExpiringPoint);

        mav.setViewName(frontUrl+"/user/mypage/inquiry-detail");
        return mav;
    }

    /**
     * 사용자 리뷰 페이지 호출
     * @param session 사용자 세션
     * @return User Review List Page
     */
    @GetMapping("/review-list")
    public ModelAndView reviewList(
            HttpSession session,
            @RequestParam(value="page", defaultValue="1") int page,
            @RequestParam(value="pageSize", defaultValue="10") int pageSize){
        logger.debug("User Inquiry Detail Page");

        ModelAndView mav = new ModelAndView();
        if (!checkLogin(session)) {
            return new ModelAndView(new RedirectView("/"));
        }

        try {
            HashMap<String, Object> paramMap = new HashMap<>();
            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            paramMap.put("userEmail", user.getUserEmail());

            paramMap.put("typeCode", "album");
            BoardSetting boardSetting = boardService.selectOneBoardSetting(paramMap);
            paramMap.put("categoryId", boardSetting.getId());
            paramMap.put("createId", user.getUserEmail());
            //유저 포인트
            paramMap.put("userEmail", user.getUserEmail());
            UserPointRecord userPoint = userPointService.getUserPointRecordSummary(paramMap);
            //현재날짜로부터 한달안에 삭제될 포인트
            UserPointRecord userExpiringPoint = userPointService.getUserExpiringPointRecordSummary(paramMap);

            int totalCount = boardService.selectCountBoardContents(paramMap);

            PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
            paramMap.put("itemStartPosition", paging.getItemStartPosition());
            paramMap.put("pagePerSize", paging.getPagePerSize());

            mav.addObject("userPoint", userPoint);
            mav.addObject("userExpiringPoint", userExpiringPoint);
            mav.addObject("p", paramMap);
            mav.addObject("reviewList", boardService.selectListBoardContents(paramMap));
            mav.addObject("paging", paging);

        } catch (Exception e) {
            mav.addObject("reviewList", null);
            logger.error(e.getMessage());
        }

        mav.setViewName(frontUrl+"/user/mypage/review-list");
        return mav;
    }

    /**
     * 사용자 포인트 페이지 호출
     * @param session 사용자 세션
     * @return User Point List Page
     */
    @GetMapping("/point-list")
    public ModelAndView pointList(
            HttpSession session,
            @RequestParam(value="page", defaultValue="1") int page,
            @RequestParam(value="pageSize", defaultValue="10") int pageSize){
        logger.debug("User Point List Page");

        ModelAndView mav = new ModelAndView();
        if (!checkLogin(session)) {
            return new ModelAndView(new RedirectView("/"));
        }

        try {
            HashMap<String, Object> paramMap = new HashMap<>();
            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            paramMap.put("userEmail", user.getUserEmail());

            UserPointRecord userPoint = userPointService.getUserPointRecordSummary(paramMap);
            //현재날짜로부터 한달안에 삭제될 포인트
            UserPointRecord userExpiringPoint = userPointService.getUserExpiringPointRecordSummary(paramMap);

            int totalCount = userPointService.getUserPointRecordListCount(paramMap);

            PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
            paramMap.put("itemStartPosition", paging.getItemStartPosition());
            paramMap.put("pagePerSize", paging.getPagePerSize());

            mav.addObject("userPoint", userPoint);
            mav.addObject("userExpiringPoint", userExpiringPoint);
            mav.addObject("p", paramMap);
            mav.addObject("pointList", userPointService.getUserPointRecordList(paramMap));
            mav.addObject("paging", paging);

        } catch (Exception e) {
            mav.addObject("reviewList", null);
            logger.error(e.getMessage());
        }

        mav.setViewName(frontUrl+"/user/mypage/point-list");
        return mav;
    }

    /**
     * 사용자 구독 이력 페이지 호출
     * @param session 사용자 세션
     * @return User Subscribe List Page
     */
    @GetMapping("/subscribe-list")
    public ModelAndView subscribeList(
            HttpSession session,
            @RequestParam(value="page", defaultValue="1") int page,
            @RequestParam(value="pageSize", defaultValue="10") int pageSize){
        logger.debug("User Subscribe List Page");

        ModelAndView mav = new ModelAndView();
        if (!checkLogin(session)) {
            return new ModelAndView(new RedirectView("/"));
        }

        try {
            HashMap<String, Object> paramMap = new HashMap<>();
            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

            //유저 포인트
            paramMap.put("userEmail", user.getUserEmail());
            UserPointRecord userPoint = userPointService.getUserPointRecordSummary(paramMap);
            //현재날짜로부터 한달안에 삭제될 포인트
            UserPointRecord userExpiringPoint = userPointService.getUserExpiringPointRecordSummary(paramMap);

            int totalCount = userService.selectUserSubscribeServiceInfoListCount(paramMap);
            PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
            paramMap.put("itemStartPosition", paging.getItemStartPosition());
            paramMap.put("pagePerSize", paging.getPagePerSize());

            mav.addObject("userPoint", userPoint);
            mav.addObject("userExpiringPoint", userExpiringPoint);
            mav.addObject("p", paramMap);
            mav.addObject("subscribeServiceList", userService.selectUserSubscribeServiceInfoList(paramMap));
            mav.addObject("paging", paging);

        } catch (Exception e) {
            mav.addObject("reviewList", null);
            logger.error(e.getMessage());
        }

        mav.setViewName(frontUrl+"/user/mypage/subscribe-list");
        return mav;
    }

    /**
     * 사용자 정보 수정 페이지 호출
     * @param session 사용자 세션
     * @return User Modify Info Page
     */
    @GetMapping("/modify-info")
    public ModelAndView modifyInfo(
            HttpSession session){
        logger.debug("User Modify Info Page");

        ModelAndView mav = new ModelAndView();
        if (!checkLogin(session)) {
            return new ModelAndView(new RedirectView("/"));
        }

        try {
            HashMap<String, Object> paramMap = new HashMap<>();

            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            paramMap.put("userEmail", user.getUserEmail());

            UserPointRecord userPoint = userPointService.getUserPointRecordSummary(paramMap);
            //현재날짜로부터 한달안에 삭제될 포인트
            UserPointRecord userExpiringPoint = userPointService.getUserExpiringPointRecordSummary(paramMap);

            mav.addObject("userPoint", userPoint);
            mav.addObject("userExpiringPoint", userExpiringPoint);
        } catch (Exception e) {
            mav.addObject("reviewList", null);
            logger.error(e.getMessage());
        }

        mav.setViewName(frontUrl+"/user/mypage/modify-info");
        return mav;
    }

    /**
     * 패스워드 일치 체크
     * @param loginUser 사용자 패스워드가 포함된 form
     * @return 패스워드 일치여부
     */
    @ResponseBody
    @PostMapping("/check/password")
    public Map<String, Object> checkPasswrd(LoginUser loginUser){
        logger.debug("User Check Passwrd");
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
//            int count = userService.findUserCountById(id);

//            if(count > 0) resultMap.put("duplicate", true);
//            else resultMap.put("duplicate", false);
            resultMap.put("result", "success");

        }catch (Exception e){
            logger.debug("User Exist Check Error");
            logger.error(e.getMessage());
            resultMap.put("result", "error");
            resultMap.put("message", "오류가 발생했습니다.");
        }

        logger.debug("User Exist Check End");
        return resultMap;
    }

    /**
     * 좋아요 하기 기능
     * @param UserFavorite 좋아요할 값의 정보
     * @return 좋아요 성공여부
     */
    @ResponseBody
    @PostMapping("/save-favorite")
    public Map<String, Object> saveFavorite(UserFavorite userFavorite){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

            if(_user instanceof LoginUser user) {
                userFavorite.setUserEmail(user.getUserEmail());
                userFavorite.setUserRole(user.getUserRole());
                userFavorite.setUserName(user.getUserName());
                String result = userService.addUserFavoriteSv(userFavorite);

                if (result.equals("already")) {
                    resultMap.put("result", "already");
                    resultMap.put("message", "이미 좋아요 목록에 있습니다.");
                }
                else if (result.equals("success")){
                    resultMap.put("result", "success");
                    resultMap.put("message", "좋아요 목록에 추가되었습니다.");
                    resultMap.put("favoriteId", userFavorite.getId());
                }

            } else {
                resultMap.put("result", "fail");
                resultMap.put("message", "먼저 로그인해주세요");
            }
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", "오류가 발생했습니다.");
        }

        return resultMap;
    }

    /**
     * 찜삭제 기능
     * @param UserFavorite 찜하기할 값의 정보
     * @return 찜삭제 성공여부
     */
    @ResponseBody
    @PostMapping("/remove-favorite")
    public Map<String, Object> removeFavorite(UserFavorite userFavorite){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

            if(_user instanceof LoginUser user) {
                userFavorite.setUserEmail(user.getUserEmail());
                userService.removeUserFavorite(userFavorite);

                resultMap.put("result", "removed");
                resultMap.put("message", "찜목록에서 제외되었습니다.");
            } else {
                resultMap.put("result", "fail");
                resultMap.put("message", "먼저 로그인해주세요");
            }
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", "오류가 발생했습니다.");
        }

        return resultMap;
    }

    /**
     * 찜목록
     * @return 찜목록
     */
    @ResponseBody
    @PostMapping("/favorite-list")
    public Map<String, Object> userFavoriteList(
            @RequestParam(value="start", defaultValue="0") int start,
            @RequestParam(value="length", defaultValue="10") int length,
            @RequestParam(value="page", defaultValue="1") int page,
            @Param(value="selectType") String selectType,
            @Param(value="spotId") String spotId,
            @Param(value="productSerial") String productSerial,
            @Param(value="titleLike") String titleLike,
            @Param(value="contentLike") String contentLike,
            //플래닝용
            @Param(value="placeType") String placeType,
            @Param(value="areaType") String areaType){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

            if(_user instanceof LoginUser user) {
                HashMap<String, Object> paramMap = new HashMap<>();
                paramMap.put("userEmail", ( user.getUserEmail() ));
                paramMap.put("selectType", selectType);
                paramMap.put("tsId", spotId);
                paramMap.put("productSerial", productSerial);
                paramMap.put("titleLike", titleLike);
                paramMap.put("contentLike", contentLike);
                paramMap.put("placeType", placeType); //플래닝용
                paramMap.put("areaType", areaType); //플래닝용

                int totalCount = userService.getCountUserFavorite(paramMap);
                PagingDTO paging = new PagingDTO(totalCount, page, start, length);
                if(length >= 0) {
                    paramMap.put("itemStartPosition", paging.getItemStartPosition());
                    paramMap.put("pagePerSize", paging.getPagePerSize());
                }
                ArrayList<UserFavorite> lists = userService.getListUserFavorite(paramMap);

                resultMap.put("recordsTotal", totalCount);
                resultMap.put("list", lists);
                resultMap.put("paging", paging);
                //플래닝용
                ArrayList<CodeItem> codeItemList = userPointService.getCodeItemList(placeType);
                resultMap.put("codeItemList",codeItemList);

                resultMap.put("result", "success");
            } else {
                resultMap.put("result", "fail");
                resultMap.put("message", "먼저 로그인해주세요");
            }
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", "오류가 발생했습니다.");
        }

        return resultMap;
    }

    /**
     * 프로필 설정 페이지 호출
     * @return 프로필 설정 페이지
     */
    @GetMapping("/noti-set-profile")
    public ModelAndView notiSetProfile(){
        return new ModelAndView("/user/noti-set-profile");
    }

    /**
     * 사용자 ID 풀네임 보여줄시 사용
     * @param session 사용자 세션
     * @param userPass 사용자가 입력한 값
     */
    @PostMapping("/find-id-password-check")
    @ResponseBody
    public HashMap<String, Object> userPasswordCheck(HttpSession session
            , @RequestParam(value = "userPass") String userPass
            , @RequestParam(value = "userName") String userEmail
            , @RequestParam(value = "userMobile") String userMobile){
        HashMap<String, Object> resultMap = new HashMap<>();

        if(session.getAttribute("encrypt") == null
                || session.getAttribute("iv") == null){
            resultMap.put("message", "Session information is invalid.");
            resultMap.put("result", "error");
        }else {
            String encrypt = (String) session.getAttribute("encrypt");
            String iv = (String) session.getAttribute("iv");
            try {
                HashMap<String, Object> paramMap = new HashMap<>();
                paramMap.put("userName", userEmail);
                paramMap.put("userMobile", userMobile);

                LoginUser storedUser = userService.checkUserPassword(paramMap);

                CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();
                if(passwordEncoder.directMatches(CryptoUtil.aesDecode(userPass, encrypt, iv), storedUser.getPassword())){
                    resultMap.put("result", "success");
                }else{
                    resultMap.put("result", "notMatch");
                    resultMap.put("message", "Passwords do not match.");
                }
            } catch (Exception e) {
                e.printStackTrace();
                resultMap.put("result", "error");
                resultMap.put("message", e.getMessage());
            }
        }

        return resultMap;
    }

    /**
     * 사용자 소개 페이지 링크 수정
     * @param session 사용자 세션
     * @param userIntroPageLink 사용자가 입력한 값
     */
    @PostMapping("/modify-intro-page-link")
    @ResponseBody
    public HashMap<String, Object> modifyIntroPageLink(HttpSession session
            , @RequestParam(value = "userIntroPageLink") String userIntroPageLink
            , @RequestParam(value = "userEmail") String userEmail){
        HashMap<String, Object> resultMap = new HashMap<>();
        Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if(_user instanceof LoginUser user) {
            if (user.getUserEmail().equals(userEmail)) {
                HashMap<String, Object> paramMap = new HashMap<>();
                paramMap.put("userIntroPageLink", userIntroPageLink);
                paramMap.put("userEmail", userEmail);
                userService.updateUserIntroPageLink(paramMap);
                resultMap.put("result", "success");
                resultMap.put("message", "소개 페이지 링크가 수정되었습니다.");
            } else {
                resultMap.put("result", "fail");
                resultMap.put("message", "먼저 로그인해주세요");
            }
        } else {
            resultMap.put("result", "fail");
            resultMap.put("message", "먼저 로그인해주세요");
        }


        return resultMap;
    }

    /**
     * 사용자 섬살이 유형 수정
     * @param session 사용자 세션
     * @param islandLifeId 섬살이 id
     */
    @PostMapping("/modify-island-life")
    @ResponseBody
    public HashMap<String, Object> modifyUserIslandLife(HttpSession session
            , @RequestParam(value = "islandLifeId") String islandLifeId
            , @RequestParam(value = "userEmail") String userEmail){
        HashMap<String, Object> resultMap = new HashMap<>();
        Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if(_user instanceof LoginUser user) {
            if (user.getUserEmail().equals(userEmail)) {
                HashMap<String, Object> paramMap = new HashMap<>();
                paramMap.put("islandLifeId", islandLifeId);
                paramMap.put("userEmail", userEmail);
                userService.updateUserIslandLife(paramMap);

                resultMap.put("result", "success");
                resultMap.put("message", "섬살이 유형이 선택되었습니다.");
            } else {
                resultMap.put("result", "fail");
                resultMap.put("message", "먼저 로그인해주세요");
            }
        } else {
            resultMap.put("result", "fail");
            resultMap.put("message", "먼저 로그인해주세요");
        }


        return resultMap;
    }

    /**
     * 사용자 예약 결제취소신청  
     * @param session 사용자 세션
     * @param reservationId 예약 id
     * * @param userEmail 사용자 이메일
     * @throws SQLException 
     */
    @PostMapping("/reservation-cancel-request")
    @ResponseBody
    public HashMap<String, Object> reservationCancelRequest(HttpSession session
            , @RequestParam(value = "reservationId") Long reservationId
            , @RequestParam(value = "userEmail") String userEmail) throws SQLException{
        HashMap<String, Object> resultMap = new HashMap<>();
        Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        try {
            if(_user instanceof LoginUser user) {
                if (user.getUserEmail().equals(userEmail)) {
                    HashMap<String, Object> codeMap = new HashMap<>();
                    codeMap.put("name", "취소요청");
                    codeMap.put("useYn", "Y");
                    codeMap.put("deleteYn", "N");
                    String reservationCode = codeService.selectOneCodeItem(codeMap).getCode();
    
                    Reservation paramReservation = new Reservation();
                    paramReservation.setReservationCode(reservationCode);
                    paramReservation.setId(reservationId);
                    paramReservation.setLastUpdateId(userEmail);
                    
                    reservationService.updateReservation(paramReservation);
    
                    resultMap.put("result", "success");
                    resultMap.put("message", "결제 취소신청 완료되었습니다.");
                } else {
                    resultMap.put("result", "fail");
                    resultMap.put("message", "먼저 로그인해주세요");
                }
            } else {
                resultMap.put("result", "fail");
                resultMap.put("message", "먼저 로그인해주세요");
            }
        } catch (Exception e) {
            resultMap.put("result", "fail");
            resultMap.put("message", "신청 중 오류가 발생하였습니다.");
            logger.error(e.getMessage());
        }

        return resultMap;
    }
    
    @PostMapping("/reservation/cancel/save")
    @ResponseBody
    public HashMap<String, Object> usercoustomer_cancel_ajax(
    		HttpSession session,
    		//Multipart
    		@ModelAttribute Reservation r,
    		@ModelAttribute UserCustomerOrder uco,
    		@Param(value="cancelAmount") Long cancelAmount,
    		HttpServletRequest request,
    		BindingResult bindingResult
    	){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser user) {
        		HashMap<String, Object> param = new HashMap<>();
        		param.put("id", r.getId() );
        		Reservation r2 = reservationManageService.selectOneReservation(param);

        		String orderId = r2.getPayMoid();
        		String txTid = r2.getTid();
        		Long amount = r2.getTotalAmount();

        		UserCustomerPayment payDataG = new UserCustomerPayment()
					.addUserEmail( r2.getUserEmail() )
					.addReservationId( r2.getId() )
					.addPayMoid( orderId )
					.addPayAmount( amount * -1 )
					.addPayDivision("G")
					.addPayComment("최초 결제금액 등록")
					.addCreateId( user.getUserEmail() )
				;

        		UserCustomerOrderHistory _ucoh = new UserCustomerOrderHistory()
        				.addUserEmail( r2.getUserEmail())
        				.addPayMoid(orderId)
        				.addPayStatusType( "PAY" )
        				.addAmt( amount )
        				.addPayTid(txTid)
        				//.addStatusResultCode(status)
        				//.addStatusResultMsg(method)
    				;

				userManageService.updateUserCustomerOrder(uco);
        		userManageService.insertUserCustomerOrderHistory(_ucoh);

        		if( r2.getUsePoint() > 0) {
        			UserCustomerPayment payDataDbyUsePoint = new UserCustomerPayment()
						.addUserEmail(r2.getUserEmail())
						.addReservationId( r2.getId() )
						.addPayMoid( orderId )
						.addPayAmount( r2.getUsePoint() * -1 )
						.addPayDivision("D")
						.addPayComment("포인트 사용금액")
						.addCreateId( user.getUserEmail() )
					;
        			userManageService.insertUserCustomerPayment(payDataDbyUsePoint); //토스페이 결제금액.

	                userPointService.refundUserUsedPoint(r2.getUserPointUsedLogId(), "예약취소 포인트 환불", user.getUserEmail());
        		}
        		UserCustomerPayment payDataD = new UserCustomerPayment()
					.addUserEmail( r2.getUserEmail() )
					.addReservationId( r2.getId() )
					.addPayMoid( orderId )
					.addPayAmount( cancelAmount * -1 )
					.addPayDivision("D")
					.addPayComment("결제금액")
					.addCreateId( user.getUserEmail() )
				;

        		userManageService.insertUserCustomerPayment(payDataD); //토스페이 결제금액.
        		userManageService.insertUserCustomerPayment(payDataG); //받을금액

        		reservationManageService.updateReservation(r);

	    		retrunMap.put("result", "success");
	    		retrunMap.put("message", "처리가 완료 되었습니다.");
        	} else {
        		retrunMap.put("result", "fail");
	    		retrunMap.put("message", "로그인 문제가 발생되었습니다.");
        	}
		} catch (Exception e) {
			retrunMap.put("result", "error");

			if(e.getMessage() != null) {
				retrunMap.put("message", e.getMessage());
			} else {
				retrunMap.put("message", "처리중 문제가 발생했습니다.");
			}
		}
        return retrunMap;
    }

    /**
     * 세션에 encrypt 와 iv 가 없을 경우 설정
     * @param session 사용자 세션
     */
    private void setSessionEncryptKey(HttpSession session){
        if(session.getAttribute("encrypt") == null || session.getAttribute("iv") == null){
            CryptoUtil cryptoUtil = new CryptoUtil();
            session.setAttribute("encrypt", cryptoUtil.generateRandomEncryptKey(""));
            session.setAttribute("iv", cryptoUtil.generateRandomIv(""));
        }
    }

    /**
     * 로그인 상태 값을 확인
     * @param session 사용자 세션
     * @return 로그인 상태
     */
    private boolean checkLogin(HttpSession session) {
        if(!SecurityContextHolder.getContext().getAuthentication().getPrincipal().equals("anonymousUser")){
            return true;
        }else{
            if(session.getAttribute("login") != null){
                session.removeAttribute("login");
            }
            return false;
        }
    }

}
