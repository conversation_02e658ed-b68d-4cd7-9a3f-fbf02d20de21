package kr.co.wayplus.travel.web.manage;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartRequest;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.PagingDTO;
import kr.co.wayplus.travel.model.SortData;
import kr.co.wayplus.travel.model.UserAttachFile;
import kr.co.wayplus.travel.model.UserCustomerCounsel;
import kr.co.wayplus.travel.model.UserCustomerInfo;
import kr.co.wayplus.travel.model.UserCustomerPayment;
import kr.co.wayplus.travel.model.UserGroup;
import kr.co.wayplus.travel.model.UserGroupConnect;
import kr.co.wayplus.travel.service.manage.UserManageService;
import kr.co.wayplus.travel.util.CryptoUtil;
import kr.co.wayplus.travel.util.FileInfoUtil;
import kr.co.wayplus.travel.util.LoggerUtil;

@Controller
@RequestMapping("/manage/user")
public class UserManageController extends BaseController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final UserManageService userManageService;

    @Value("${key.crypto.encrypt}")
    private String encrypt;
    @Value("${key.crypto.iv}")
    private String iv;
    @Value("${upload.file.path}")
	private String imageUploadPath;
	@Value("${upload.file.max-size}")
	int maxFileSize;
	final String addPath = "passport/";

    public UserManageController(UserManageService userManageService) {
        this.userManageService = userManageService;
    }

    @GetMapping("/list")
    public ModelAndView userList(@RequestParam(value="page",defaultValue="1") int page,
                                 @RequestParam(value="pageSize",defaultValue="20") int pageSize,
                                 @RequestParam(value="type",defaultValue="ALL") String type,
                                 @RequestParam(value="searchKey",defaultValue="") String searchKey){
        ModelAndView mav = new ModelAndView();
        mav.setViewName("manage/sub/user/list");

        HashMap<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("searchKey", searchKey);

        int totalCount = userManageService.getUserListCount(param);
        PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
        param.put("itemStartPosition", paging.getItemStartPosition());
        param.put("pagePerSize", paging.getPagePerSize());
        mav.addObject("p", param);
        mav.addObject("userList", userManageService.getUserList(param));
        mav.addObject("paging", paging);

        mav.addObject("accountCount", userManageService.getUserAccountStatusCount(null));

        return mav;
    }

    @PostMapping("/ajax-list")
    @ResponseBody
    public Map<String, Object> ajaxUserList(
            @RequestParam(value = "draw", defaultValue = "1") int draw,
            @RequestParam(value = "start", defaultValue = "0") int start,
            @RequestParam(value = "length", defaultValue = "10") int length,
            @RequestParam(value = "type", defaultValue = "ALL") String type,
            @RequestParam(value = "searchKey", defaultValue = "") String searchKey) {
        
        HashMap<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("searchKey", searchKey);
        param.put("itemStartPosition", start);
        param.put("pagePerSize", length);

        // 전체 데이터 수 조회
        int totalCount = userManageService.getUserListCount(param);
        
        // 필터링된 데이터 수 (검색 조건 적용된 수)
        int filteredCount = totalCount;  // 필요한 경우 별도 카운트 쿼리 추가
        
        // 데이터 조회
        ArrayList<LoginUser> userList = userManageService.getUserList(param);

        // DataTable 응답 형식에 맞게 데이터 구성
        Map<String, Object> response = new HashMap<>();
        response.put("draw", draw);
        response.put("recordsTotal", totalCount);
        response.put("recordsFiltered", filteredCount);
        response.put("data", userList);

        return response;
    }

    @GetMapping("/view")
    public ModelAndView userForm(@RequestParam(value="userEmail", defaultValue="") String userEmail){
        ModelAndView mav = new ModelAndView();
        mav.setViewName("manage/sub/user/view");
        mav.addObject("user", userManageService.getUserDetail(userEmail));
        mav.addObject("customer", userManageService.getUserCustomerInfo(userEmail));
        return mav;
    }

//<!--################################### counsel ###################################-->
    @GetMapping("/counsel/list")
    public ModelAndView userCounselList(@RequestParam(value="page",defaultValue="1") int page,
                                 @RequestParam(value="pageSize",defaultValue="20") int pageSize,
                                 @RequestParam(value="type",defaultValue="ALL") String type,
                                 @RequestParam(value="searchKey",defaultValue="") String searchKey){
        ModelAndView mav = new ModelAndView();
        mav.setViewName("manage/sub/user/counsel/list");
/*
        HashMap<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("searchKey", searchKey);

        int totalCount = userManageService.getUserListCount(param);
        PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
        param.put("itemStartPosition", paging.getItemStartPosition());
        param.put("pagePerSize", paging.getPagePerSize());
        mav.addObject("p", param);
        mav.addObject("userList", userManageService.getUserList(param));
        mav.addObject("paging", paging);
*/
        return mav;
    }

    @GetMapping("/counsel/view")
    public ModelAndView userCounselView(
    		@RequestParam(value="id", defaultValue="") String id,
    		@RequestParam HashMap<String, Object> param){
        ModelAndView mav = new ModelAndView();
        mav.setViewName("manage/sub/user/counsel/view");
        UserCustomerCounsel data = userManageService.getUserCustomerCounsel(param);
        String userEmail = data.getUserEmail();

        mav.addObject("counsel", data);
        mav.addObject("id", id);

        LoginUser user = userManageService.getUserDetail(userEmail);
        if(user != null) {
        	mav.addObject("user", user);
        } else {
        	mav.addObject("user", new LoginUser());
        }

        return mav;
    }

    @GetMapping("/counsel/form")
    public ModelAndView userCounselForm(
    		@Param(value="mode") String mode,
    		@ModelAttribute UserCustomerCounsel ucc,
    		HashMap<String, Object> param){
        ModelAndView mav = new ModelAndView();

        if(mode.equals("I") ) {
        	mav.addObject("counsel", new UserCustomerCounsel());
        } else {
        	param.put("id", ucc.getId());
        	mav.addObject("counsel", userManageService.getUserCustomerCounsel( param ) );
        }
        mav.addObject("mode", mode);
        mav.setViewName("manage/sub/user/counsel/form");

        return mav;
    }

    @GetMapping("/mypage")
    public ModelAndView useMypage(HttpSession session){
        ModelAndView mav = new ModelAndView();
        mav.setViewName("manage/sub/user/mypage");

        LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        String userEmail = user.getUserEmail();

        HashMap<String, Object> param = new HashMap<String, Object>();
    	param.put("userEmail",userEmail);

        mav.addObject("user", userManageService.getUserDetail(param) );

		if(session.getAttribute("encrypt") == null || session.getAttribute("iv") == null){
			CryptoUtil cryptoUtil = new CryptoUtil();
			session.setAttribute("encrypt", cryptoUtil.generateRandomEncryptKey(""));
			session.setAttribute("iv", cryptoUtil.generateRandomIv(""));
		}

		mav.addObject("mode", "U");

        return mav;
    }

//<!--################################### payment ###################################-->
      @GetMapping("/payment/list")
      public ModelAndView userPaymentList(@RequestParam(value="page",defaultValue="1") int page,
                                   @RequestParam(value="pageSize",defaultValue="20") int pageSize,
                                   @RequestParam(value="type",defaultValue="ALL") String type,
                                   @RequestParam(value="searchKey",defaultValue="") String searchKey){
          ModelAndView mav = new ModelAndView();
          mav.setViewName("manage/sub/user/payment/list");
/*
          HashMap<String, Object> param = new HashMap<>();
          param.put("type", type);
          param.put("searchKey", searchKey);

          int totalCount = userManageService.getUserListCount(param);
          PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
          param.put("itemStartPosition", paging.getItemStartPosition());
          param.put("pagePerSize", paging.getPagePerSize());
          mav.addObject("p", param);
          mav.addObject("userList", userManageService.getUserList(param));
          mav.addObject("paging", paging);
*/
          return mav;
      }

      @GetMapping("/payment/view")
      public ModelAndView userpaymentView(
      		@RequestParam(value="id", defaultValue="") String id,
      		@RequestParam HashMap<String, Object> param){
          ModelAndView mav = new ModelAndView();
          mav.setViewName("manage/sub/user/payment/view");

          UserCustomerPayment uPay = userManageService.selectOneUserCustomerPayment(param);

          String userEmail = uPay.getUserEmail();

          mav.addObject("id", id);

          LoginUser user = userManageService.getUserDetail(userEmail);

          mav.addObject("user", user);
          //UserCustomerCounsel data = userManageService.getUserCustomerCounsel(param);
          //mav.addObject("counsel", data);

          return mav;
      }

      @GetMapping("/payment/form")
      public ModelAndView userpaymentForm(){
          ModelAndView mav = new ModelAndView();
          mav.setViewName("manage/sub/user/payment/form");

          return mav;
      }

//<!--################################### ajax ###################################-->
    @PostMapping("/list")
    @ResponseBody
    public HashMap<String, Object> user_list_ajax(HttpServletRequest request,
    		@RequestParam HashMap<String, Object> paramMap,
    		@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length,
    		@Param(value="searchText") String searchText,
    		@RequestParam(value="isMember", defaultValue="false") Boolean isMember,
    		@RequestParam(value="isMission", defaultValue="false") Boolean isMission
    		){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
    		//List<SortData> listSort = getListOrder(request);
    		//paramMap.put("listSort", listSort);



    		if(length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
    		}
    		if(isMission) {
	    		paramMap.put("sort", "missionCount");
	    		paramMap.put("sortOrder", "desc");
    		}

    		if( isMember ) {
    			ArrayList<String> userRoles = new ArrayList<String>();
    			userRoles.add("ADMIN");
    			userRoles.add("MANAGE");

    			paramMap.put("userRoles", userRoles);
    		}
    		paramMap.put("isMission", isMission);
    		paramMap.put("searchText", searchText);

    		int totalCount = userManageService.getUserListCount(paramMap);

    		retrunMap.put("recordsTotal", totalCount);
    		retrunMap.put("recordsFiltered", totalCount);
    		retrunMap.put("data", userManageService.getUserList(paramMap));

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

        return retrunMap;
    }

    @PutMapping("/account")
    @ResponseBody
    public HashMap<String, Object> userAccountStatus(@RequestParam(value="userEmail",defaultValue="") String userEmail,
                                                     @RequestParam(value="userTokenId",defaultValue="") String userTokenId,
                                                     @RequestParam(value="status",defaultValue="") String accountStatus){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            if(userEmail.isEmpty() || userTokenId.isEmpty() || accountStatus.isEmpty()){
                throw new Exception("파라미터가 없습니다.");
            }

            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            HashMap<String, Object> param = new HashMap<>();
            param.put("userEmail", userEmail);
            param.put("userTokenId", userTokenId);
            param.put("accountStatus", accountStatus);
            userManageService.changeUserAccountStatus(param);

            resultMap.put("result", "success");
            resultMap.put("message", "처리됐습니다.");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }

        return resultMap;
    }

    @PostMapping("/accountSimpleInfo")
    @ResponseBody
    public HashMap<String, Object> userAccountSimpleInfo(
    		@RequestParam(value="userEmail",defaultValue="") String userEmail,
            @RequestParam(value="userTokenId",defaultValue="") String userTokenId,
            @RequestParam(value="userName",defaultValue="") String userName,
            @RequestParam(value="userMobile",defaultValue="") String userMobile,
            @RequestParam(value="userGrade",defaultValue="") String userGrade,
            @RequestParam(value="userMemo",defaultValue="") String userMemo
             ){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            if(userEmail.isEmpty() || userTokenId.isEmpty() || userName.isEmpty() || userMobile.isEmpty()){
                throw new Exception("파라미터가 없습니다.");
            }

            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            HashMap<String, Object> param = new HashMap<>();
            param.put("userEmail",   userEmail);
            param.put("userTokenId", userTokenId);
            param.put("userName",    userName);
            param.put("userMobile",  userMobile);
            param.put("userGrade",  userGrade);
            param.put("userMemo",    userMemo);
            userManageService.changeUserAccountSimpleInfo(param);

            resultMap.put("result", "success");
            resultMap.put("message", "처리됐습니다.");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }

        return resultMap;
    }

    @PostMapping("/create")
    @ResponseBody
    public HashMap<String, Object> userCreate(HttpSession session,
                                              @RequestParam(value="encrypted", defaultValue="true") String encrypted,
                                              @ModelAttribute LoginUser user, BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            if(user == null
                    || user.getUserEmail() == null
                    || user.getUserPassword() == null
                    || user.getUserName() == null
            ) {
                throw new Exception("필수정보를 확인 해 주세요.");
            }

            if(session.getAttribute("encrypt") != null) encrypt = (String) session.getAttribute("encrypt");
            if(session.getAttribute("iv") != null) iv = (String) session.getAttribute("iv");
            user.setEncrypt(encrypt);
            user.setIv(iv);
            user.setUserTokenId(String.valueOf(UUID.randomUUID()));
            userManageService.createUser(user, Boolean.parseBoolean(encrypted));

            resultMap.put("result", "success");
            resultMap.put("message", "처리됐습니다.");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }
        return resultMap;
    }

    @PutMapping("/mypage/update")
	@ResponseBody
	public HashMap<String,Object> administratorUpdate(
			HttpSession session,
			@RequestParam(value="encrypted", defaultValue="true") String encrypted,
			@ModelAttribute LoginUser user,
			BindingResult bindingResult){
		HashMap<String,Object> resultMap = new HashMap<String, Object>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			if(user == null
				|| user.getUserEmail() == null
				|| user.getUserPassword() == null
				|| user.getUserName() == null) {
				throw new Exception("필수 입력 정보를 확인 해 주세요.");
			} else if( loginUser.getUserPassword() == null ) {
				throw new Exception("로그인 입력 정보를 확인 해 주세요.");
			} else if( !user.getUserEmail().equals( loginUser.getUserEmail() ) ) {
				throw new Exception("로그인정보와 수정 대상 정보가 다릅니다.");
			}

			if(session.getAttribute("encrypt") != null) encrypt = (String) session.getAttribute("encrypt");
			if(session.getAttribute("iv") != null) iv = (String) session.getAttribute("iv");
			user.setEncrypt(encrypt);
			user.setIv(iv);

			userManageService.updateMyUserInfo(user, Boolean.parseBoolean(encrypted));

			resultMap.put("result","success");
			resultMap.put("message","처리 완료.");
		} catch (Exception e) {
			resultMap.put("result","error");

			if(e.getMessage() != null ) {
				resultMap.put("message","처리중 오류가 발생했습니다.");
			} else {
				resultMap.put("message",e.getMessage());
			}

			//logger.debug(e.getCause().getMessage());
		}

		return resultMap;
	}

    @PutMapping("/info")
    @ResponseBody
    public HashMap<String, Object> userInfo(@ModelAttribute LoginUser user, @ModelAttribute UserCustomerInfo customerInfo, BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            LoggerUtil.writeBindingResultErrorLog(bindingResult, logger);

            LoginUser manager = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            userManageService.updateUserInfoByManager(user);
            customerInfo.setCreateType("manage/user/view");
            customerInfo.setCreateId(manager.getUserEmail());
            userManageService.writeUserCustomerInfo(customerInfo);

            resultMap.put("result", "success");
            resultMap.put("message", "처리됐습니다.");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }

        return resultMap;
    }


    @GetMapping("/count")
    @ResponseBody
    public HashMap<String, Object> userCountAjax(
    		@ModelAttribute LoginUser user,
    		BindingResult bindingResult,
    		@Param(value="isOnlyNewJoin") Boolean isOnlyNewJoin){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
        	HashMap<String, Object> param = new HashMap<>();
        	param.put("isOnlyNewJoin", isOnlyNewJoin);

            int totalCount = userManageService.getUserListCount(param);

            resultMap.put("count", totalCount);
            resultMap.put("result", "success");
            resultMap.put("message", "조회완료");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }

        return resultMap;
    }

    @GetMapping("/counsel-list")
    @ResponseBody
    public HashMap<String, Object> userCounselList(
    		HttpServletRequest request,
    		BindingResult bindingResult,
    		@ModelAttribute LoginUser user,
    		@RequestParam(value="page", defaultValue="1") int page,
    		@RequestParam(value="pageSize", defaultValue="10") int pageSize){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            if(user.getUserEmail() == null || user.getUserEmail().isEmpty()) throw new Exception("파라미터가 없습니다.");

            HashMap<String, Object> param = new HashMap<>();

            List<SortData> listSort = getListOrder(request);
    		param.put("listSort", listSort);

    		if(pageSize >= 0) {
				param.put("itemStartPosition", page);
				param.put("pagePerSize", pageSize);
    		}

    		param.put("userEmail", user.getUserEmail());

            int totalCount = userManageService.getUserCustomerCounselListCount(param);
            PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);

            resultMap.put("paging", paging);
            resultMap.put("list", userManageService.getUserCustomerCounselList(param));

            resultMap.put("result", "success");
            resultMap.put("message", "조회완료");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }

        return resultMap;
    }

    @PostMapping("/counsel-list")
    @ResponseBody
    public HashMap<String, Object> userCounselList_ajax(
    		HttpServletRequest request,
    		@ModelAttribute LoginUser user,
            @RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length,
            @RequestParam HashMap<String, Object> param){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            //if(user.getUserEmail() == null || user.getUserEmail().isEmpty()) throw new Exception("파라미터가 없습니다.");
            if(length >= 0) {
				param.put("itemStartPosition", start);
				param.put("pagePerSize", length);
    		}
        	List<SortData> listSort = getListOrder(request);
     		param.put("listSort", listSort);

            int totalCount = userManageService.getUserCustomerCounselListCount(param);

            param.put("itemStartPosition", start);
            param.put("pagePerSize", length);

            resultMap.put("recordsTotal", totalCount);
            resultMap.put("recordsFiltered", totalCount);
            resultMap.put("data", userManageService.getUserCustomerCounselList(param));

            resultMap.put("result", "success");
            resultMap.put("message", "조회완료");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }

        return resultMap;
    }

    @PostMapping("/counsel")
    @ResponseBody
    public HashMap<String, Object> userCounselWrite(
    		HttpSession session,
    		@ModelAttribute UserCustomerCounsel counsel,
            BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            if(counsel.getUserEmail().isEmpty() || counsel.getRequestText().isEmpty()) throw new Exception("파라미터가 없습니다.");
            LoginUser manager = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            counsel.setCreateId(manager.getUserEmail());

            //userManageService.getUserDetail(  )
            String userEmail = counsel.getUserEmail();
            boolean isGuestReg = false;

            LoginUser regUser = userManageService.getUserDetail(userEmail);

            if( userEmail.trim() == "" || regUser == null ) {
            	isGuestReg = true;
            }

            /*신규 비회원계정 등록처리.*/
            if(isGuestReg) {
            	if(userEmail == null)
            		userEmail = String.valueOf(UUID.randomUUID());

				if(session.getAttribute("encrypt") != null)
					encrypt = (String) session.getAttribute("encrypt");
				if(session.getAttribute("iv") != null)
					iv = (String) session.getAttribute("iv");

				LoginUser userGuest = new LoginUser()
					.addGuestUser( userEmail )
					.addUserName( counsel.getUserName() )
					.addUserMobile( counsel.getUserMobile() )
					.addUserJoinType( "reservation" )
					.addEncrypt( encrypt )
					.addIv( iv )
					.addUserPassworad("Guest");
				;

				userManageService.createUser(userGuest, false);

				counsel.setUserEmail( userEmail );
            }

            userManageService.writeUserCustomerCounsel(counsel);

            resultMap.put("result", "success");
            resultMap.put("message", "저장완료");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }

        return resultMap;
    }

    @PostMapping("/counsel-delete")
    @ResponseBody
    public HashMap<String, Object> userCounselDelete(@ModelAttribute UserCustomerCounsel counsel,
                                                    BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            if(counsel.getId() == null) throw new Exception("파라미터가 없습니다.");
            Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser user = (LoginUser)_user;
        		HashMap<String, Object> paramMap = new HashMap<>();

        		if(counsel.getCreateId() == null) counsel.setCreateId(counsel.getUserEmail());
	            userManageService.deleteUserCustomerCounsel(counsel);

	            resultMap.put("result", "success");
	            resultMap.put("message", "삭제완료");
	        } else {
	    		resultMap.put("result", "fail");
	    		resultMap.put("message", "로그인 문제가 발생되었습니다.");
	    	}
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }

        return resultMap;
    }

    @PostMapping("/counsel-restore")
    @ResponseBody
    public HashMap<String, Object> userCounselRestore(@ModelAttribute UserCustomerCounsel counsel,
                                                    BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            if(counsel.getId() == null) throw new Exception("파라미터가 없습니다.");

            Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser user = (LoginUser)_user;
        		HashMap<String, Object> paramMap = new HashMap<>();

        		if(counsel.getCreateId() == null) counsel.setCreateId(counsel.getUserEmail());
	            userManageService.restoreUserCustomerCounsel(counsel);

	            resultMap.put("result", "success");
	            resultMap.put("message", "복구완료");
        	} else {
        		resultMap.put("result", "fail");
        		resultMap.put("message", "로그인 문제가 발생되었습니다.");
        	}
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }

        return resultMap;
    }

    @PutMapping("/user-show-yn/update")
	@ResponseBody
	public HashMap<String,Object> userShowYnUpdate(
			HttpSession session,
			@RequestBody LoginUser user,
			BindingResult bindingResult){
		HashMap<String,Object> resultMap = new HashMap<String, Object>();

		try {
			userManageService.updateUserShowYn(user);

			resultMap.put("result","success");
			resultMap.put("message","처리 완료.");
		} catch (Exception e) {
			resultMap.put("result","error");

			if(e.getMessage() != null ) {
				resultMap.put("message","처리중 오류가 발생했습니다.");
			} else {
				resultMap.put("message",e.getMessage());
			}

			//logger.debug(e.getCause().getMessage());
		}

		return resultMap;
	}
    //<!--################################### UserCustomerPayment ###################################-->
    @PostMapping("/payment-list-v")
    @ResponseBody
    public HashMap<String, Object> userCustomerPayment_List_ajax(
    		HttpServletRequest request,
    		@ModelAttribute LoginUser user,
    		@RequestParam HashMap<String, Object> param,
    		@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length,
    		@Param(value="reservationId") Integer reservationId,
    		@Param(value="dateType") String dateType,
    		@Param(value="dateFrom") String dateFrom,
    		@Param(value="dateTo") String dateTo,
    		@Param(value="payDivision") String payDivision,
    		@RequestParam(value="sortOrder", defaultValue="desc") String sortOrder,
    		@RequestParam(value="sort", defaultValue="payDate") String sort,
            BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
/*
            if(reservationId == null || user.getUserEmail() == null || user.getUserEmail().isEmpty())
            	throw new Exception("파라미터가 없습니다.");
*/
        	List<SortData> listSort = getListOrder(request);
        	param.put("listSort", listSort);

        	if(length >= 0) {
        		param.put("itemStartPosition", start);
        		param.put("pagePerSize", length);
    		}

        	param.put("dateType", dateType);
        	param.put("dateFrom", dateFrom);
        	param.put("dateTo",   dateTo + " 23:59:59");
        	param.put("payDivision",   payDivision);
        	
        	param.put("sortOrder", sortOrder);
        	param.put("sort", sort);

            int totalCount = userManageService.selectCountUserCustomerPaymentVirtual(param);
            //PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);

            //param.put("itemStartPosition", paging.getItemStartPosition());
            //param.put("pagePerSize", paging.getPagePerSize());
            if(reservationId != null) param.put("reservationId", reservationId);

            resultMap.put("recordsTotal", totalCount);
            resultMap.put("recordsFiltered", totalCount);
            resultMap.put("data", userManageService.selectListUserCustomerPaymentVirtual(param));
            resultMap.put("dataTotal", userManageService.selectListUserCustomerPaymentVirtualTotal(param));

            resultMap.put("result", "success");
            resultMap.put("message", "조회완료");
        }catch (Exception e){
            logger.error(e.getMessage());
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }

        return resultMap;
    }

    @PostMapping("/payment-save")
    @ResponseBody
    public HashMap<String, Object> userCustomerPayment_save_ajax(
    				HttpSession session,
					@RequestParam(value="encrypted", defaultValue="true") String encrypted,
					@ModelAttribute LoginUser user,
					@ModelAttribute UserCustomerPayment pay,
					BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            if(pay == null
                || pay.getUserEmail() == null
                || pay.getPayDivision() == null
                || pay.getPayComment() == null
                || pay.getPayAmount() == null
            ) {
                throw new Exception("필수정보를 확인 해 주세요.");
            }

            LoginUser manager = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

            pay.setCreateId( manager.getUserEmail());

            userManageService.insertUserCustomerPayment(pay);

            resultMap.put("result", "success");
            resultMap.put("message", "처리됐습니다.");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }
        return resultMap;
    }

    @PostMapping("/passport-image-upload")
	@ResponseBody
	public HashMap<String, Object> ckImageUpload(
			MultipartRequest request,
			@ModelAttribute LoginUser userreal,
			@RequestParam(value="service_type", defaultValue="product") String serviceType){
		HashMap<String, Object> resultMap = new HashMap<>();
		LoginUser manage = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		try{
			HashMap<String, Object> paramMap = new HashMap<>();
			paramMap.put("userEmail", userreal.getUserEmail());
			paramMap.put("deleteYn", "N");

			Integer cnt = userManageService.selectCountUserAttachFile(paramMap);
			if(cnt > 0) {
				ArrayList<UserAttachFile> list = userManageService.selectListUserAttachFile(paramMap);

				for (UserAttachFile _old : list) {
					if(_old != null) {
    					FileInfoUtil.deleteImageFile_real(_old); /*실제 이미지 파일 제거*/
    					userManageService.deleteUserAttachFile(_old);
    				}
				}
			}

			MultipartFile multipartFile = null;
			if(request.getFile("passport") != null){
				multipartFile = request.getFile("passport");
				if(multipartFile.getSize() > 0){
					if(multipartFile.getSize() > maxFileSize){
						throw new Exception(String.valueOf(maxFileSize/1024/1024) + "MB 이하의 파일만 첨부 가능합니다.");
					}

					String uploadName = UUID.randomUUID().toString();

					File file = new File(imageUploadPath+addPath);
					if(!file.exists()) file.mkdirs();

					multipartFile.transferTo(new File(imageUploadPath+addPath+uploadName));
					logger.debug("File Uploaded : " + multipartFile.getOriginalFilename());

					UserAttachFile userPassportImage = new UserAttachFile();
					//productDetailScheduleImage.setDetailId(0);
					userPassportImage.setUploadPath(imageUploadPath+addPath);
					userPassportImage.setUploadFilename(uploadName);
					userPassportImage.setOriginFilename(multipartFile.getOriginalFilename());
					userPassportImage.setFileSize((int) multipartFile.getSize());
					userPassportImage.setFileMimetype(multipartFile.getContentType());
					if (multipartFile.getOriginalFilename().contains(".")) {
						userPassportImage.setFileExtension(multipartFile.getOriginalFilename().substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
					}
					userPassportImage.setUserEmail(userreal.getUserEmail());
					userPassportImage.setUploadId(manage.getUserEmail());

					String imgUrl = "/upload/"+addPath+ uploadName;
					userreal.setUserPassportImgPath( imgUrl );

					userManageService.insertUserAttachFile(userPassportImage);
					userManageService.updateUserInfoByPassportImgPath(userreal);

					resultMap.put("result", "success");
					resultMap.put("url", imgUrl);
					resultMap.put("file_id", userPassportImage.getFileId());
					resultMap.put("message", "저장되었습니다.");
				}
			}else{
				throw new Exception("첨부된 파일이 없습니다.");
			}
		}catch (Exception e){
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}
    //<!--################################### group ###################################-->
    @GetMapping("/group/list")
    public ModelAndView userGroupList(@RequestParam(value="page",defaultValue="1") int page,
                                 @RequestParam(value="pageSize",defaultValue="20") int pageSize,
                                 @RequestParam(value="type",defaultValue="ALL") String type,
                                 @RequestParam(value="searchKey",defaultValue="") String searchKey){
        ModelAndView mav = new ModelAndView();
        mav.setViewName("manage/sub/user/group/list");
/*
        HashMap<String, Object> param = new HashMap<>();
        param.put("type", type);
        param.put("searchKey", searchKey);

        int totalCount = userManageService.getUserListCount(param);
        PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
        param.put("itemStartPosition", paging.getItemStartPosition());
        param.put("pagePerSize", paging.getPagePerSize());
        mav.addObject("p", param);
        mav.addObject("userList", userManageService.getUserList(param));
        mav.addObject("paging", paging);
*/
        return mav;
    }
    @GetMapping("/group/form")
    public ModelAndView userGroupForm(
    		@RequestParam(value="mode", defaultValue="I") String mode,
    		@RequestParam(value="id", defaultValue="0") String id,
    		@RequestParam HashMap<String, Object> param
    		){
        ModelAndView mav = new ModelAndView();

        if(mode.equals("U")) {
        	HashMap<String, Object> paramMap = new HashMap<>();
        	paramMap.put("id", id);
        	mav.addObject("group",  userManageService.selectOneUserGroup(param));
        	mav.addObject("p", paramMap);
        } else {
        	mav.addObject("group",  new UserGroup());
        }
        mav.addObject("mode", mode);
        mav.setViewName("manage/sub/user/group/form");

        return mav;
    }
    @GetMapping("/group/view")
    public ModelAndView userGroupView(
    		@RequestParam(value="id", defaultValue="") String id,
    		@RequestParam HashMap<String, Object> param){
        ModelAndView mav = new ModelAndView();
        mav.setViewName("manage/sub/user/group/view");
        UserGroup data = userManageService.selectOneUserGroup(param);

        mav.addObject("id", id);
        mav.addObject("group", data);

        return mav;
    }

    //<!--################################### ajax ###################################-->
    @PostMapping("/group-list")
    @ResponseBody
    public HashMap<String, Object> user_group_list_ajax(HttpServletRequest request,
    		@RequestParam HashMap<String, Object> paramMap,
    		@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length,
    		@Param(value="searchText") String searchText,
    		@Param(value="userEmail") String userEmail
    		){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
//        	List<SortData> listSort = getListOrder(request);
//        	paramMap.put("listSort", listSort);

    		if(length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
    		}

    		if(searchText != null)
    			paramMap.put("searchText", searchText);

    		if(userEmail != null)
    			paramMap.put("userEmail", userEmail);

    		int totalCount = userManageService.selectCountUserGroup(paramMap);

    		retrunMap.put("recordsTotal", totalCount);
    		retrunMap.put("recordsFiltered", totalCount);
    		retrunMap.put("data", userManageService.selectListUserGroup(paramMap));

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

        return retrunMap;
    }

    @PostMapping("/group-save")
    @ResponseBody
    public HashMap<String, Object> user_group_save_ajax(
    				HttpSession session,
					@RequestParam(value="encrypted", defaultValue="true") String encrypted,
					@ModelAttribute LoginUser user,
					@ModelAttribute UserGroup group){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
        	/*
            if(pay == null
                || pay.getUserEmail() == null
                || pay.getPayDivision() == null
                || pay.getPayComment() == null
                || pay.getPayAmount() == null
            ) {
                throw new Exception("필수정보를 확인 해 주세요.");
            }

            LoginUser manager = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

            pay.setCreateId( manager.getUserEmail());

            userManageService.insertUserCustomerPayment(pay);
            */

        	if(group.getId() == null) {
        		userManageService.insertUserGroup(group);
        	} else {
        		userManageService.updateUserGroup(group);
        	}

        	resultMap.put("data", group);
            resultMap.put("result", "success");
            resultMap.put("message", "처리됐습니다.");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }
        return resultMap;
    }

    @PostMapping("/group-delete")
    @ResponseBody
    public HashMap<String, Object> userGroupDelete(@ModelAttribute UserGroup group,
                                                    BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            if(group.getId() == null) throw new Exception("파라미터가 없습니다.");
            Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser user = (LoginUser)_user;
        		HashMap<String, Object> paramMap = new HashMap<>();

        		if(group.getCreateId() == null) group.setCreateId(user.getUserEmail());
	            userManageService.deleteUserGroup(group);

	            resultMap.put("result", "success");
	            resultMap.put("message", "삭제완료");
	        } else {
	    		resultMap.put("result", "fail");
	    		resultMap.put("message", "로그인 문제가 발생되었습니다.");
	    	}
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }

        return resultMap;
    }

    @PostMapping("/group-restore")
    @ResponseBody
    public HashMap<String, Object> userGroupRestore(@ModelAttribute UserGroup group,
                                                    BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            if(group.getId() == null) throw new Exception("파라미터가 없습니다.");

            Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        	if(_user instanceof LoginUser) {
        		LoginUser user = (LoginUser)_user;
        		HashMap<String, Object> paramMap = new HashMap<>();

        		if(group.getCreateId() == null) group.setCreateId(user.getUserEmail());
	            userManageService.restoreUserGroup(group);

	            resultMap.put("result", "success");
	            resultMap.put("message", "복구완료");
        	} else {
        		resultMap.put("result", "fail");
        		resultMap.put("message", "로그인 문제가 발생되었습니다.");
        	}
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }

        return resultMap;
    }
    //<!--################################### ajax ###################################-->
    @PostMapping("/group-customer-list")
    @ResponseBody
    public HashMap<String, Object> user_group_customer_list_ajax(HttpServletRequest request,
    		@RequestParam HashMap<String, Object> paramMap,
    		@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length,
    		@Param(value="searchText") String searchText
    		){
    	HashMap<String, Object> retrunMap = new HashMap<>();

    	try {
//        	List<SortData> listSort = getListOrder(request);
//        	paramMap.put("listSort", listSort);

    		if(length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
    		}

    		paramMap.put("searchText", searchText);

    		int totalCount = userManageService.selectCountUserGroupCustomer(paramMap);

    		retrunMap.put("recordsTotal", totalCount);
    		retrunMap.put("recordsFiltered", totalCount);
    		retrunMap.put("data", userManageService.selectListUserGroupCustomer(paramMap));

    		retrunMap.put("result", "success");
    		retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

        return retrunMap;
    }

    @PostMapping("/group-customer-save")
    @ResponseBody
    public HashMap<String, Object> user_group_customer_save_ajax(
    				HttpSession session,
					@RequestParam(value="encrypted", defaultValue="true") String encrypted,
					@ModelAttribute LoginUser user,
					@ModelAttribute UserGroupConnect ugc){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
        	/*
            if(pay == null
                || pay.getUserEmail() == null
                || pay.getPayDivision() == null
                || pay.getPayComment() == null
                || pay.getPayAmount() == null
            ) {
                throw new Exception("필수정보를 확인 해 주세요.");
            }

            LoginUser manager = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

            pay.setCreateId( manager.getUserEmail());

            userManageService.insertUserCustomerPayment(pay);
            */

        	userManageService.insertUserGroupConnect(ugc);
        	/*
        	if(ugc.getGroupId() == null) {
        	} else {
        		userManageService.updateUserGroupConnect(ugc);
        	}
        	*/

            resultMap.put("result", "success");
            resultMap.put("message", "처리됐습니다.");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }
        return resultMap;
    }
    @PostMapping("/group-customer-delete")
    @ResponseBody
    public HashMap<String, Object> user_group_customer_delete_ajax(
    		HttpSession session,
    		@RequestParam(value="encrypted", defaultValue="true") String encrypted,
    		@ModelAttribute LoginUser user,
    		@ModelAttribute UserGroupConnect ugc){
    	HashMap<String, Object> resultMap = new HashMap<>();
    	try{
    		 if(ugc == null
                || ugc.getUserEmail() == null
                || ugc.getGroupId() == null
            ) {
                throw new Exception("필수정보를 확인 해 주세요.");
            }

    		userManageService.deleteUserGroupConnect(ugc);

    		resultMap.put("result", "success");
    		resultMap.put("message", "처리됐습니다.");
    	}catch (Exception e){
    		logger.error(e.getMessage());
    		e.printStackTrace();
    		resultMap.put("result", "error");
    		resultMap.put("message", e.getMessage());
    	}
    	return resultMap;
    }

    @PostMapping("/group-customer-restore")
    @ResponseBody
    public HashMap<String, Object> user_group_customer_restore_ajax(
    		HttpSession session,
    		@RequestParam(value="encrypted", defaultValue="true") String encrypted,
    		@ModelAttribute LoginUser user,
    		@ModelAttribute UserGroupConnect ugc){
    	HashMap<String, Object> resultMap = new HashMap<>();
    	try{
    		 if(ugc == null
                || ugc.getUserEmail() == null
                || ugc.getGroupId() == null
            ) {
                throw new Exception("필수정보를 확인 해 주세요.");
            }

    		userManageService.deleteUserGroupConnect(ugc);

    		resultMap.put("result", "success");
    		resultMap.put("message", "처리됐습니다.");
    	}catch (Exception e){
    		logger.error(e.getMessage());
    		e.printStackTrace();
    		resultMap.put("result", "error");
    		resultMap.put("message", e.getMessage());
    	}
    	return resultMap;
    }
}
