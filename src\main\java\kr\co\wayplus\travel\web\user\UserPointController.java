package kr.co.wayplus.travel.web.user;

import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.UserPointExchange;
import kr.co.wayplus.travel.model.UserPointSet;
import kr.co.wayplus.travel.service.user.UserPointService;

import java.util.Calendar;
import java.util.HashMap;

import kr.co.wayplus.travel.util.ShortUrlUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/user/point")
public class UserPointController {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final UserPointService userPointService;

    public UserPointController(UserPointService userPointService) {
        this.userPointService = userPointService;
    }

    @PostMapping("/exchange/insert")
    @ResponseBody
    public HashMap<String, Object> pointExchangeInsert(@ModelAttribute UserPointExchange userPointExchange, BindingResult bindingResult){
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            Calendar cal = Calendar.getInstance();
            userPointExchange.setCreateId(user.getUserEmail());
            userPointExchange.setUserEmail(user.getUserEmail());
            userPointExchange.setExchangeCode(ShortUrlUtil.convertNumberToShortUrl(10,53, cal.getTimeInMillis()));
            userPointService.userPointExchangeService(userPointExchange);
            resultMap.put("result", "success");
            resultMap.put("message", "저장됐습니다.");
        }catch (Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
        }
        return resultMap;
    }
}
