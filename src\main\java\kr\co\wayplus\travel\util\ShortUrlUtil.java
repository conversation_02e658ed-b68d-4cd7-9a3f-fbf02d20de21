package kr.co.wayplus.travel.util;

import org.springframework.stereotype.Component;

import java.util.ArrayList;

@Component
public class ShortUrlUtil {
    private static String[] urlkey = { "B", "a", "A", "b", "C", "c", "D", "d", "E", "e",
                                       "U", "u", "V", "v", "W", "w", "X", "x", "Y", "y",
                                       "P", "p", "Q", "q", "R", "r", "S", "s", "T", "t",
                                       "K", "k", "L", "l", "M", "m", "N", "n", "O", "o",
                                       "F", "f", "G", "g", "H", "h", "I", "i", "J", "j",
                                       "Z", "z", "0", "1", "2", "3", "4", "5", "6", "7",
                                       "8", "9" };

    /**
     * <pre>
     * urlkey 배열을 활용하여 key 진법으로 plainInt 값을 변환하여
     * maxdigit 자리수의 문자열을 만든다
     * </pre>
     *
     * <AUTHOR>
     * @date 2017. 7. 7.
     * @param maxdigit	최대 자리수
     * @param key		진법 (62 이하)
     * @param plainInt	변환할 숫자열
     * @return
     */
    public static String convertNumberToShortUrl(int maxdigit, int key, long plainInt){
        ArrayList<String> textarr = new ArrayList<>();

        long tempkey = plainInt;
        int digitcount = 1;
        while(tempkey/key >= 0 && digitcount <= maxdigit){
            textarr.add(urlkey[(int) (tempkey%key)]);
            tempkey = (long) Math.floor(tempkey/key);
            digitcount++;
        }

        String plainText = "";
        for (int i = textarr.size()-1; i >= 0; i-- ) {
            plainText += textarr.get(i);
        }
        return plainText;
    }

    /**
     * <pre>
     * urlkey 배열을 활용하여 key 진법으로 plainInt 값을 다시 숫자값으로 변환시킨다
     * </pre>
     *
     * <AUTHOR>
     * @date 2017. 7. 7.
     * @param key		진법 (62 이하)
     * @param plainText 변환할 문자열
     * @return
     */
    public static String convertShotUrlToNumber(int key, String plainText){
        char[] temp = plainText.toCharArray();
        long result = (long) 0;
        int digitcount = 0;
        for(int i = temp.length - 1; i >= 0; i--){
            for (int j = 0; j < urlkey.length; j++){
                if(urlkey[j].equals(String.valueOf(temp[i]))){
                    result += j * Math.pow(key, digitcount);
                    digitcount++;
                    break;
                }
            }
        }
        return String.valueOf(result);
    }
}
