/*결제내역_리스트*/
.my_product_img_title {
    display: flex;
}
.my_product_img_box {
    position: relative;
    overflow: hidden;
    width: 200px; height: 200px;
    margin-right: 30px;
}

.my_product_list {
    padding: 20px 0;
    border-bottom: 1px solid #d9d9d9;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.my_product_title_box {
    color: #222;
}

.my_product_day {
    color: #666;
    font-size: 14px;
}

.my_product_title {
    font-size: 18px;
    font-weight: 600;
    margin: 10px 0 20px 0;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
.my_product_date {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.my_product_price {
    font-weight: 500;
    margin-bottom: 20px;
}

.my_product_option {
    display: flex;
}

.my_product_option_list p{
    margin-bottom: 10px;
}

.my_list_btn {
    width: 140px;
    font-size: 16px;
}

/*결제내역 상세*/
.member_pay_box .board_table {
    border-top: 1px solid #222;
    border-bottom: 1px solid #222;
}

.member_pay_box .table_board_none {
    border: none;
}

.member_pay_box .table_list {
    width: 90px;
    padding: 20px 10px;
    text-align: left;
}

.table_list_top {
    vertical-align: top;
}

.member_pay_box .table_title_name {
    line-height: unset;
    padding: 20px 20px 20px 100px;
}

.member_pay_box .table_title_name p {
    text-align: left;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break:break-all;
    overflow: hidden;
    table-layout: fixed;
    line-height: 24px;
}

.member_pay_box .table_no p {
    line-height: 24px;
}

.table_price {
    width: 90px;
    padding: 0 10px;
}

.member_pay_box .table_price p {
    text-align: right;
    line-height: 24px;
}

.table_txt_red {
    color: #E74133;
    font-weight: 600;
}

.table_txt_bold {
    font-weight: 600;
}

/*반응형쿼리*/
@media screen and (max-width:768px) {
    .my_product_list {
        display: block;
    }

    .my_product_img_title {
        margin-bottom: 20px;
    }

    .my_product_option {
        display: unset;
    }

    .member_pay_box .table_title_name {
        padding: 20px 20px 20px 20px;
    }

}

@media screen and (max-width:425px) {
    .my_product_img_title {
        flex-wrap: wrap;
    }

    .my_product_img_box {
        width: 100%; height: 300px;
        margin: 0 0 20px 0;
    }

    .member_pay_box .table_list {
        display: none;
    }

    .member_pay_box .table_title_name {
        padding: 20px 20px 20px 10px;
    }

}