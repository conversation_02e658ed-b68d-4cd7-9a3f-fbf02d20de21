package kr.co.wayplus.travel.model;

import com.fasterxml.jackson.annotation.JsonAutoDetect;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
@Data

public class ContentsItem extends CommonDataSet  {
	private Integer contentItemId;	//콘텐츠 항목ID
	private Integer contentId;	//콘텐츠ID
	private String contentLangType;	//메뉴 내용
	private String contentItemDetail;	//메뉴 내용
	private Integer contentItemSort;	//콘텐츠 항목 순서
	private String useYn, oldUseYn;	//사용여부

	public ContentsItem addContentId( Integer contentId ) {
		this.contentId = contentId;
		return this;
	}

	public ContentsItem addContentLangType( String contentLangType ) {
		this.contentLangType = contentLangType;
		return this;
	}

	public ContentsItem addUseYn( String useYn ) {
		this.useYn = useYn;
		return this;
	}
	public ContentsItem addOldUseYn( String oldUseYn ) {
		this.oldUseYn = oldUseYn;
		return this;
	}

}
