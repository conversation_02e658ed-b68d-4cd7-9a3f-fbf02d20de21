package kr.co.wayplus.travel.service.manage;

import java.sql.SQLException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import kr.co.wayplus.travel.mapper.manage.ProductManageMapper;
import kr.co.wayplus.travel.mapper.manage.ReservationManageMapper;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.ProductInfo;
import kr.co.wayplus.travel.model.Reservation;
import kr.co.wayplus.travel.model.ReservationQna;
import kr.co.wayplus.travel.model.ReservationUserConnect;
import kr.co.wayplus.travel.util.ReservationUtil;

@Service
public class ReservationManageService {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final ReservationManageMapper mapper;
	private final ReservationUtil reservationUtil;
	private final ProductManageMapper mapperProduct;
    public ReservationManageService(ReservationManageMapper mapper, ReservationUtil reservationUtil, ProductManageMapper mapperProduct) {
        this.mapper = mapper;
		this.reservationUtil = reservationUtil;
		this.mapperProduct = mapperProduct;
    }

//	<!--################################### Reservation ###################################-->
	public int selectCountReservation(HashMap<String, Object> paramMap) {
		return mapper.selectCountReservation(paramMap);
	}
	public int selectCountReservation(Reservation ic) {
		return mapper.selectCountReservation(ic);
	}
	public ArrayList<Reservation> selectListReservation(HashMap<String, Object> paramMap) {
		return mapper.selectListReservation(paramMap);
	}
//	public ArrayList<Reservation> selectListReservation(Reservation ic) {
//		return mapperWayplusManage.selectListReservation(bc);
//	}
	public Reservation selectOneReservation(HashMap<String, Object> paramMap) {
		return mapper.selectOneReservation(paramMap);
	}
	public ArrayList<HashMap<String, Object>> selectListInquiryCountStatusType(HashMap<String, Object> paramMap) {
		return mapper.selectListInquiryCountStatusType(paramMap);
	}
	public String selectOneIsHaveSpecialPrice(HashMap<String, Object> paramMap) {
		return mapper.selectOneIsHaveSpecialPrice(paramMap);
	}
	public void saveReservation(Reservation ic) throws SQLException {
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("id", ic.getId());

		if( this.selectCountReservation(paramMap) == 0) {
			mapper.insertReservation(ic);
		} else {
			mapper.updateReservation(ic);
		}
	}
	public void insertReservation(Reservation ic) throws SQLException {
		mapper.insertReservation(ic);
	}
	@Transactional
	public void updateReservation(Reservation ic) throws SQLException {
		mapper.updateReservation(ic);
	}
	public void restoreReservation(Reservation ic) throws SQLException {
		mapper.restoreReservation(ic);
	}
	public void deleteReservation(Reservation ic) throws SQLException {
		mapper.deleteReservation(ic);
	}

	public ArrayList<HashMap<String, Object>> selectListCountReservationContentByCalendar(HashMap<String, Object> paramMap) {
		return mapper.selectListCountReservationContentByCalendar(paramMap);
	}

	public ArrayList<HashMap<String, Object>> selectListReservationContentByCheckList(HashMap<String, Object> paramMap) {
		return mapper.selectListReservationContentByCheckList(paramMap);
	}

	public HashMap<String, Object> selectListReservationCountStatusType(HashMap<String, Object> paramMap) {
		return mapper.selectListReservationCountStatusType(paramMap);
	}

//	<!--################################### ReservationQna ###################################-->
	public int selectCountReservationQna(HashMap<String, Object> paramMap) {
		return mapper.selectCountReservationQna(paramMap);
	}
	public ArrayList<ReservationQna> selectListReservationQna(HashMap<String, Object> paramMap) {
		return mapper.selectListReservationQna(paramMap);
	}
	public void saveReservationQna(ReservationQna ic) throws SQLException {
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("id", ic.getId());

		if( this.selectCountReservationQna(paramMap) == 0) {
			mapper.insertReservationQna(ic);
		} else {
			mapper.updateReservationQna(ic);
		}
	}
	public void insertReservationQna(ReservationQna ic) throws SQLException {
		mapper.insertReservationQna(ic);
	}
	public void updateReservationQna(ReservationQna ic) throws SQLException {
		mapper.updateReservationQna(ic);
	}
	public void restoreReservationQna(ReservationQna ic) throws SQLException {
		mapper.restoreReservationQna(ic);
	}
	public void deleteReservationQna(ReservationQna ic) throws SQLException {
		mapper.deleteReservationQna(ic);
	}
//	<!--################################### ReservationUserConnect ###################################-->
	public int selectCountReservationUserConnect(HashMap<String, Object> paramMap) {
		return mapper.selectCountReservationUserConnect(paramMap);
	}
	public ArrayList<LoginUser> selectListReservationUserConnect(HashMap<String, Object> paramMap) {
		return mapper.selectListReservationUserConnect(paramMap);
	}
	public void insertReservationUserConnect(ReservationUserConnect ic) throws SQLException {
		mapper.insertReservationUserConnect(ic);
	}
	public void deleteReservationUserConnect(ReservationUserConnect ic) throws SQLException {
		mapper.deleteReservationUserConnect(ic);
	}

	//예약가능 여부
	public HashMap<String, Object> checkPossibleReservation(Reservation rc) throws SQLException {
		HashMap<String, Object> returnMap = new HashMap<String, Object>();
		returnMap.put("result", "success");

		String[] pickDates = calculatePickDate(rc.getTravelSchedule());
		LocalDate pickStartDate = LocalDate.parse(pickDates[0]);
		LocalDate pickEndDate = pickDates[1].isEmpty() ? pickStartDate : LocalDate.parse(pickDates[1]);

		HashMap<String, Object> rsvParam = new HashMap<>();
		rsvParam.put("startDate", pickStartDate);
		rsvParam.put("endDate", pickEndDate);
		rsvParam.put("productSerial", rc.getProductSerial());

		HashMap<String, Object> productParam = new HashMap<>();
		productParam.put("productSerial", rc.getProductSerial());
		ProductInfo innerProduct = mapperProduct.selectProductInfo(productParam);
		if ( innerProduct.getPolicyInventory().equals("1") ) {
			ArrayList<Reservation> calcReservationList = reservationUtil.selectListCalcReservation(rsvParam);

			for (Reservation reservation : calcReservationList) { 

				if (reservation.getIsRestDate() == 1) {
					returnMap.put("result", "restDate");
					returnMap.put("failDate", reservation.getTravelDate());
					return returnMap;
				}
				
				JSONObject rcJson = new JSONObject(rc.getPriceOptionJson());
				JSONObject rcData = rcJson.getJSONObject("data");
				Iterator<String> rsvKeys = rcData.keys();
				while (rsvKeys.hasNext()) {
					String rsvKey = rsvKeys.next();
					if (rsvKey.startsWith("option-")) {
						JSONObject reservationOption = rcData.getJSONObject(rsvKey);
						String rsvOptionOneCode = reservationOption.getString("4");
						if( rsvOptionOneCode.equals(reservation.getSpecialOptionOneCode()) ) {
							int reservationOptionCount = Integer.parseInt(reservationOption.getString("3"));
							//전체 capacity 판단
							int totalCount = reservation.getTotalOrderCount();
							totalCount = Math.max(0, totalCount - rc.getOriginPickPeople());
							int maxCapacity = reservation.getMaxCapacity();
							if ( reservationOptionCount > maxCapacity - totalCount ) {
								returnMap.put("result", "duplicateFail");
								returnMap.put("failOptionName", reservationOption.getString("1"));
								returnMap.put("failDate", reservation.getTravelDate());
								returnMap.put("dupCapacity", maxCapacity - totalCount < 0 ? 0 : maxCapacity - totalCount);

								return returnMap;
							}
							//통과 후 특가 capacity 판단
							int specialOrderCount = reservation.getSpecialOrderCount();
							specialOrderCount = Math.max(0, specialOrderCount - rc.getOriginPickPeople());
							int specialCapacity = reservation.getSpecialCapacity();
							if ( reservationOptionCount > specialCapacity - specialOrderCount )
							{
								returnMap.put("result", "duplicateFail");
								returnMap.put("failOptionName", reservationOption.getString("1"));
								returnMap.put("failDate", reservation.getTravelDate());
								returnMap.put("dupCapacity", specialCapacity - specialOrderCount < 0 ? 0 : specialCapacity - specialOrderCount);

								return returnMap;
							}
						}
						if( rsvOptionOneCode.equals(reservation.getOptionOneCode()) ) {
							int reservationOptionCount = Integer.parseInt(reservationOption.getString("3"));
							int totalCount = reservation.getTotalOrderCount();
							totalCount =  Math.max(0, totalCount - rc.getOriginPickPeople());
							int maxCapacity = reservation.getMaxCapacity();
							//전체 capacity 판단
							if ( reservationOptionCount > maxCapacity - totalCount ) {
								returnMap.put("result", "duplicateFail");
								returnMap.put("failOptionName", reservationOption.getString("1"));
								returnMap.put("failDate", reservation.getTravelDate());
								returnMap.put("dupCapacity", maxCapacity - totalCount < 0 ? 0 : maxCapacity - totalCount);

								return returnMap;
							}
							//통과 후 정가 capacity 판단
							int orderCount = reservation.getOrderCount(); // 5명 예약
							orderCount = Math.max(0, orderCount - rc.getOriginPickPeople()); // 5명 - 6명
							int orderCapacity = reservation.getMaxCapacity();
							if ( reservationOptionCount > orderCapacity - orderCount )
							{
								returnMap.put("result", "duplicateFail");
								returnMap.put("failOptionName", reservationOption.getString("1"));
								returnMap.put("failDate", reservation.getTravelDate());
								returnMap.put("dupCapacity", orderCapacity - orderCount < 0 ? 0 : orderCapacity - orderCount);

								return returnMap;
							}
						} 
					}
				}
			}
		}
		
		return returnMap;
	}
	//선택 날짜계산
	private String[] calculatePickDate(String pickDate) throws SQLException {
		System.out.println("pickDate " + pickDate);
		String[] pickDates = new String[2];
		if (pickDate != null && !pickDate.isEmpty()) {
			String[] dates = pickDate.split("~");
			pickDates[0] = dates[0].trim();
			pickDates[1] = dates.length > 1 ? dates[1].trim() : "";
		}
		return pickDates;
	}

	//json 중복제거
	private JSONObject removeDuplicateJson(String priceOptionJson) {
		JSONObject root = new JSONObject(priceOptionJson);
        JSONObject data = root.getJSONObject("data");
        
        // 결과를 저장할 Map (옵션값 -> 해당 옵션의 개수)
        Map<String, List<String>> optionGroups = new HashMap<>();
        
        // option- 로 시작하는 키들을 찾아서 처리
        Iterator<String> keys = data.keys();
        while (keys.hasNext()) {
            String key = keys.next();
            if (key.trim().startsWith("option-")) {
                JSONObject optionNode = data.getJSONObject(key);
                
                // 옵션의 값들을 키로 사용하기 위해 정렬된 형태로 저장
                String optionKey = optionNode.getString("0") + "|" +
                                 optionNode.getString("1") + "|" +
                                 optionNode.getString("2") + "|" +
                                 optionNode.getString("3") + "|" +
                                 optionNode.getString("4");
                
                optionGroups.computeIfAbsent(optionKey, k -> new ArrayList<>())
                           .add(key);
            }
        }
        
        // 결과 JSON 생성
        JSONObject resultData = new JSONObject();
        int newOptionIndex = 0;
        
        // 중복 제거된 옵션들을 새로운 JSON에 추가
        for (Map.Entry<String, List<String>> entry : optionGroups.entrySet()) {
            String originalOptionKey = entry.getValue().get(0);
            JSONObject originalOption = data.getJSONObject(originalOptionKey);
            resultData.put("option-" + newOptionIndex, originalOption);
            newOptionIndex++;
        }
        
        // 나머지 필드들을 복사
        keys = data.keys();
        while (keys.hasNext()) {
            String key = keys.next();
            if (!key.trim().startsWith("option-")) {
                resultData.put(key, data.get(key));
            }
        }
        
        // 최종 JSONObject 생성
        JSONObject result = new JSONObject();
        result.put("id", root.getString("id"));
        result.put("data", resultData);
        
        return result;
	}

	//예약된 수 계산
	private int calculateReservedStock(JSONObject reservationJson, String optionOneCode) {
		int reservedStock = 0;
		JSONObject reservationData = reservationJson.getJSONObject("data");
		Iterator<String> rsvKeys = reservationData.keys();
		System.out.println("중복제거된 reservationData는 " + reservationData);
		while (rsvKeys.hasNext()) {
			String rsvKey = rsvKeys.next();
			if (rsvKey.startsWith("option-")) {
				JSONObject reservationOption = reservationData.getJSONObject(rsvKey);
				String rsvOptionOneCode = reservationOption.getString("4");
				if( rsvOptionOneCode.equals(optionOneCode) ) {
					String reservationOptionCount = reservationOption.getString("3");
					//이미 예약된 인원수 계산
					reservedStock += Integer.parseInt(reservationOptionCount.isEmpty() ? "0" : reservationOptionCount);
				}
			}
		}

		return reservedStock;
	}
}
