package kr.co.wayplus.travel.mapper.manage;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import kr.co.wayplus.travel.model.AlertMessageLog;
import kr.co.wayplus.travel.model.SmsPolicy;

@Mapper
@Repository
public interface AlertManageMapper {
	/**
	 * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
	 */

	//	<!--################################### AlertMessageLog ###################################-->
	int selectCountAlertMessageLog(HashMap<String, Object> paramMap);
	ArrayList<AlertMessageLog> selectListAlertMessageLog(HashMap<String, Object> paramMap);
	AlertMessageLog selectOneAlertMessageLog(HashMap<String, Object> paramMap);
	void insertAlertMessageLog(AlertMessageLog param) throws SQLException;
	void updateAlertMessageLog(AlertMessageLog param) throws SQLException;
	void deleteAlertMessageLog(AlertMessageLog param) throws SQLException;

	//	<!--################################### sms_policy ###################################-->
	void insertSmsPolicy(SmsPolicy smsPolicy);
	SmsPolicy selectOneSmsPolicy(HashMap<String, Object> paramMap);
	int selectCountListSmsPolicy(HashMap<String, Object> paramMap);
	ArrayList<SmsPolicy> selectListSmsPolicy(HashMap<String, Object> paramMap);
	void updateSmsPolicy(SmsPolicy smsPolicy);
	void deleteSmsPolicy(SmsPolicy smsPolicy);
	void updateSmsPolicyYn(SmsPolicy smsPolicy);
}
