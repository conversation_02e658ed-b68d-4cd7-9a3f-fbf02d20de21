<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.common.MailMapper">
<!--
	 * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
-->
<!--################################### boardContents ###################################-->
	<select id="selectCountMailLog" parameterType="HashMap" resultType="Integer">
        SELECT count(id) FROM mail_log a
          left join JSON_TABLE( trade_info_json, '$[*]' COLUMNS( date VARCHAR(100) PATH '$.date', type VARCHAR(50) PATH '$.type', amount VARCHAR(50) PATH '$.amount', name VARCHAR(100) PATH '$.partner' ) ) AS jt ON TRUE
		<where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and a.id = #{id}</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(from_presnal)">and from_presnal = #{fromPresnal}</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fromEmail)">and from_email = #{fromEmail}</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fromDttm)">and from_dttm = #{fromDttm}</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(receivedDttm)">and received_dttm = #{receivedDttm}</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flag)">and flag = #{flag}</if>

         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateType)">and ifnull(jt.type,'') = #{dateType}</if>

         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateFrom) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateTo)">
				and jt.date between DATE_FORMAT(#{dateFrom}, '%Y/%m/%d') and DATE_FORMAT(#{dateTo}, '%Y/%m/%d')
			</if>
         </where>
    </select>

	<select id="selectListMailLog" parameterType="HashMap" resultType="MailLog">
		SELECT *
		  FROM(
	        SELECT @rownum:=@rownum+1 AS rownum,
	               id, subject, from_presnal, from_email, from_dttm, received_dttm, flag, trade_infomation, last_update_date
	               , ifnull(jt.date,'') date, ifnull(jt.type,'') type,ifnull(jt.amount,'') amount, ifnull(jt.name,'') name
	          FROM mail_log a
	          left join JSON_TABLE( trade_info_json, '$[*]' COLUMNS( date VARCHAR(100) PATH '$.date', type VARCHAR(50) PATH '$.type', amount VARCHAR(50) PATH '$.amount', name VARCHAR(100) PATH '$.partner' ) ) AS jt ON TRUE
	          join (SELECT @rownum:= 0) rnum
	         <where>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and a.id = #{id}</if>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(from_presnal)">and from_presnal = #{fromPresnal}</if>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fromEmail)">and from_email = #{fromEmail}</if>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fromDttm)">and from_dttm = #{fromDttm}</if>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(receivedDttm)">and received_dttm = #{receivedDttm}</if>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flag)">and flag = #{flag}</if>

	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateType)">and ifnull(jt.type,'') = #{dateType}</if>

	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateFrom) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateTo)">
					and jt.date between DATE_FORMAT(#{dateFrom}, '%Y/%m/%d') and DATE_FORMAT(#{dateTo}, '%Y/%m/%d')
				</if>
	         </where>
	         <if test="sort eq null or sortOrder eq null">
	         	ORDER BY id desc
	         </if>
			) a
		ORDER BY date DESC
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneMailLog" parameterType="HashMap" resultType="MailLog">
        SELECT id, subject, from_presnal, from_email, from_dttm, received_dttm, flag, trade_infomation, last_update_date
               , ifnull(jt.date,'') date, ifnull(jt.type,'') type,ifnull(jt.amount,'') amount, ifnull(jt.name,'') name
          FROM mail_log a
           left join JSON_TABLE( trade_info_json, '$[*]' COLUMNS( date VARCHAR(100) PATH '$.date', type VARCHAR(50) PATH '$.type', amount VARCHAR(50) PATH '$.amount', name VARCHAR(100) PATH '$.partner' ) ) AS jt ON TRUE
		   join (SELECT @rownum:= 0) rnum
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and a.id = #{id}</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(from_presnal)">and from_presnal = #{fromPresnal}</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fromEmail)">and from_email = #{fromEmail}</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fromDttm)">and from_dttm = #{fromDttm}</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(receivedDttm)">and received_dttm = #{receivedDttm}</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flag)">and flag = #{flag}</if>
         </where>
	</select>

	<insert id="insertMailLog" parameterType="MailLog" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO mail_log
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subject)" >	subject=#{subject},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fromPresnal)" >	from_presnal=#{fromPresnal},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fromEmail)" >	from_email=#{fromEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fromDttm)" >	from_dttm=#{fromDttm},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(receivedDttm)" >	received_dttm=#{receivedDttm},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flag)" >	flag=#{flag},	</if>
			last_update_date = now()
		</set>
    </insert>

	<update id="updateMailLog" parameterType="MailLog" useGeneratedKeys="true" keyProperty="id">
        update mail_log
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subject)" >	subject=#{subject},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fromPresnal)" >	from_presnal=#{fromPresnal},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fromEmail)" >	from_email=#{fromEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fromDttm)" >	from_dttm=#{fromDttm},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(receivedDttm)" >	received_dttm=#{receivedDttm},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(flag)" >	flag=#{flag},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(attachHtml)" >	attach_html=#{attachHtml},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tradeInfoJson)" >	trade_info_json=#{tradeInfoJson},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
		</where>
    </update>

<!--
	<insert id="insertBoardContents" parameterType="BoardContents" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO board_contents
               (<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	board_id,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)" >	category_id,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	title,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	content,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	apply_code,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(seriesId)" >	series_id,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tags)" >	tags,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(thumbnailUrl)" >	thumbnail_url,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(favoriteCount)" >	favorite_count,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(scrapCount)" >	scrap_count,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentCount)" >	comment_count,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(viewCount)" >	view_count,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(attachmentCount)" >	attachment_count,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fixYn)" >	fix_yn,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	secret_yn,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestName)" >	guest_name,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestPass)" >	guest_pass,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	start_date,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	expire_date,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userDay)" >	user_day,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(peopleNum)" >	people_num,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(destination)" >	destination,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(phoneNum)" >	phone_num,	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id,	</if>
                create_date)
        VALUES (<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	#{boardId},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)" >	#{categoryId},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	#{title},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	#{content},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	#{applyCode},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(seriesId)" >	#{seriesId},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tags)" >	#{tags},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(thumbnailUrl)" >	#{thumbnailUrl},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(favoriteCount)" >	#{favoriteCount},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(scrapCount)" >	#{scrapCount},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentCount)" >	#{commentCount},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(viewCount)" >	#{viewCount},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(attachmentCount)" >	#{attachmentCount},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	#{useYn},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fixYn)" >	#{fixYn},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	#{secretYn},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestName)" >	#{guestName},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestPass)" >	#{guestPass},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	#{startDate},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	#{expireDate},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userDay)" >	#{userDay},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(peopleNum)" >	#{peopleNum},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(destination)" >	#{destination},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(phoneNum)" >	#{phoneNum},	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	#{createId},	</if>
                now()
                )
    </insert>

    <update id="updateBoardContents" parameterType="BoardContents">
        UPDATE board_contents
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(boardId)" >	board_id=#{boardId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)" >	category_id=#{categoryId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	title=#{title},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	content=#{content},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	apply_code=#{applyCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(seriesId)" >	series_id=#{seriesId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tags)" >	tags=#{tags},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(thumbnailUrl)" >	thumbnail_url=#{thumbnailUrl},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(favoriteCount)" >	favorite_count=#{favoriteCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(scrapCount)" >	scrap_count=#{scrapCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentCount)" >	comment_count=#{commentCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(viewCount)" >	view_count=#{viewCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(attachmentCount)" >	attachment_count=#{attachmentCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fixYn)" >	fix_yn=#{fixYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	secret_yn=#{secretYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestName)" >	guest_name=#{guestName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(guestPass)" >	guest_pass=#{guestPass},	</if>
			<choose>
				<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)">start_date=#{startDate},</when>
				<otherwise>start_date=null,</otherwise>
			</choose>
			<choose>
				<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)">expire_date=#{expireDate},</when>
				<otherwise>expire_date=null,</otherwise>
			</choose>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	delete_yn=#{deleteYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	delete_id=#{deleteId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	delete_date=#{deleteDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
		</where>
    </update>

    <update id="restoreBoardContents" parameterType="BoardContents">
        UPDATE board_contents
           SET delete_yn='N'
        WHERE id = #{id}
    </update>

    <update id="deleteBoardContents" parameterType="BoardContents">
        UPDATE board_contents
           SET delete_yn='Y'
        WHERE id = #{id}
    </update>

    <update id="updateBoardContentDelete" parameterType="BoardContents">
        UPDATE board_contents
           <set>
               view_count = view_count + 1
           </set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
		</where>
    </update>

    <update id="updateBoardContent_addViewCount" parameterType="HashMap">
        UPDATE board_contents
           SET comment_count = 1
        WHERE id = #{content_id} AND delete_yn='N'
    </update>

    <update id="updateBoardContent_CommentCount" parameterType="HashMap">
        UPDATE board_contents
        SET comment_count = 1
        WHERE id = #{content_id} AND delete_yn='N'
    </update>
-->

<!--################################### BoardAttachFile ###################################-->
<!--
	<select id="selectCountMailAttachs" parameterType="HashMap" resultType="Integer">
		SELECT count(id)
		  FROM mail_attachs
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType) and
					  @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey) ">
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType=='userEmail'" >and title LIKE CONCAT('%', #{searchKey}, '%')</if>
			</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mailId)" >	and mail_id=#{mailId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>
			and delete_yn = 'N'
		</where>
	</select>

	<select id="selectListMailAttachs" parameterType="HashMap" resultType="MailAttachs">
		select *
		  from (
			SELECT @rownum:=@rownum+1 AS rownum,
			       file_id, mail_id, service_type, upload_path, upload_filename, file_extension, file_size, file_mimetype, origin_filename, upload_id, upload_date
			  FROM mail_attachs
			  join (SELECT @rownum:= 0) rnum
			<where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType) and
						  @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey) ">
					<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType=='userEmail'" >and title LIKE CONCAT('%', #{searchKey}, '%')</if>
				</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mailId)" >	and mail_id=#{mailId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>

			</where> ) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOneMailAttachs" parameterType="HashMap" resultType="MailAttachs">
		SELECT file_id, mail_id, service_type, upload_path, upload_filename, file_extension, file_size, file_mimetype, origin_filename, upload_id, upload_date
		  FROM mail_attachs
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mailId)" >	and mail_id=#{mailId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>
		</where>
	</select>

    <insert id="insertMailAttachs" parameterType="MailAttachs" useGeneratedKeys="true" keyProperty="fileId">
        INSERT INTO mail_attachs
               (
	                mail_id, service_type,
	                upload_path, upload_filename,
	                file_extension, file_size, file_mimetype,
	                origin_filename,
	                <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	upload_id,	</if>
	                 upload_date
                )
        VALUES (
                   #{mailId}, #{serviceType},
                   #{uploadPath}, #{uploadFilename},
                   #{fileExtension}, #{fileSize}, #{fileMimetype},
                   #{originFilename},
                   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	#{uploadId},	</if>
                    now()
               )
    </insert>

    <delete id="deleteMailAttachs" parameterType="MailAttachs">
		delete from mail_attachs
		 WHERE file_id = #{fileId}
	</delete>
-->
	   <!--################################### mailRecivedUser ###################################-->
    <select id="selectCountMailRecivedUser" parameterType="HashMap" resultType="Integer">
		SELECT count(create_date) FROM mail_recived_user
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
		</where>
	</select>

    <select id="selectListMailRecivedUser" parameterType="HashMap" resultType="MailRecivedUser">
		SELECT @rownum:=@rownum+1 AS rownum,
			   user_email, use_yn, create_id, create_date
		  FROM mail_recived_user
		  join (SELECT @rownum:= 0) rnum
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
		</where>
	</select>

	<select id="selectOneMailRecivedUser" parameterType="HashMap" resultType="MailRecivedUser">
		SELECT menu_id, place_code, create_date
		  FROM mail_recived_user
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
		 </where>
	</select>

	<insert id="insertMailRecivedUser" parameterType="MailRecivedUser" useGeneratedKeys="true" keyProperty="menuId">
		INSERT INTO mail_recived_user
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	use_yn=#{useYn},	</if>
			create_date = now()
		</set>
	</insert>

	<delete id="deleteMailRecivedUser" parameterType="HashMap">
        DELETE FROM mail_recived_user
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
		</where>
    </delete>
</mapper>