package kr.co.wayplus.travel.web.pay;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.ModelAndView;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.UserCustomerOrder;
import kr.co.wayplus.travel.model.UserCustomerOrderHistory;
import kr.co.wayplus.travel.model.UserCustomerOrderList;
import kr.co.wayplus.travel.model.UserCustomerPayment;
import kr.co.wayplus.travel.service.front.PageService;
import kr.co.wayplus.travel.service.front.ProductService;
import kr.co.wayplus.travel.service.pay.NicepayService;

@Controller
@RequestMapping("/nicepay")
public class NicepayController  extends BaseController {

	@Value("${spring.profiles.active}")
    private String ACTIVE_PROFILE;
	private String nicePayUrl = "";

    @Value("${cookie-set.domain}")
    private String cookieDomain;
    @Value("${cookie-set.prefix}")
    private String cookieName;

    /*기본 파라미터 셋팅*/
    @Value("${pg.nicepay.merchantID}")
	private String merchantID;
	@Value("${pg.nicepay.merchantKey}")
	private String merchantKey;

	/*기본 파라미터 셋팅(forstartup)*/
	@Value("${pg.nicepay.isDevReal}")
	private Boolean isDevReal;
	@Value("${pg.nicepay.type}")
	private String payType;
	@Value("${pg.nicepay.method}")
	private String payMethod;
	@Value("${pg.nicepay.clientID}")
	private String clientID;
	@Value("${pg.nicepay.secretKey}")
	private String secretKey;

	@Value("${pg.nicepay.GoodsCl}")
	private String GoodsCl; 		//상품구분
	@Value("${pg.nicepay.TransType}")
	private String TransType;		//일반(0)/에스크로(1)
	@Value("${pg.nicepay.CharSet}")
	private String CharSet;			//응답 파라미터 인코딩
	@Value("${pg.nicepay.ReqResrved}")
	private String ReqResrved;		//상점 예약필드

	private final Logger logger = LoggerFactory.getLogger(getClass());

	private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    private final NicepayService svcNicepay;
    private final PageService pageService;
    private final ProductService productService;

    @Autowired
    public NicepayController(
    		NicepayService nicePay,
    		PageService pageService,
    		ProductService productService) {
    	this.svcNicepay    = nicePay;
        this.pageService    = pageService;
        this.productService = productService;
    }

    @RequestMapping(value="/cancel")
    public String cancelDemo(){
        return "/cancel";
    }

    @RequestMapping("/serverAuth")
    public ModelAndView requestPayment(
            @RequestParam String tid,
            @RequestParam Long amount,
            Model model) throws Exception {

    	ModelAndView mav = new ModelAndView();

    	String orderId = "", resultCode, txTid;
    	JsonNode responseNode;
    	UserCustomerOrder _uco = null;
    	ArrayList<UserCustomerOrderList> _ucol = null;
    	UserCustomerOrderHistory _ucoh = new UserCustomerOrderHistory();

    	try {
    		initUrlSetting();

	        HttpHeaders headers = new HttpHeaders();
	        headers.set("Authorization", "Basic " + Base64.getEncoder().encodeToString((clientID + ":" + secretKey).getBytes()));
	        headers.setContentType(MediaType.APPLICATION_JSON);

	        Map<String, Object> AuthenticationMap = new HashMap<>();
	        AuthenticationMap.put("amount", String.valueOf(amount));

	        HttpEntity<String> request = new HttpEntity<>(objectMapper.writeValueAsString(AuthenticationMap), headers);

//	        ResponseEntity<JsonNode> responseEntity = restTemplate.postForEntity(
//	            "https://sandbox-api.nicepay.co.kr/v1/payments/" + tid, request, JsonNode.class);
	        ResponseEntity<JsonNode> responseEntity = restTemplate.postForEntity(
	        	nicePayUrl+ "/payments/" + tid, request, JsonNode.class);

	        responseNode = responseEntity.getBody();
	        resultCode = responseNode.get("resultCode").asText();
	        model.addAttribute("resultMsg", responseNode.get("resultMsg").asText());

	        System.out.println(responseNode.toPrettyString());

	        /*nicepay 결제 이후 로직처리*/

	        HashMap<String, Object> paramMap = new HashMap<>();
	        String
	        	ResultCode 	= responseNode.get("resultCode").asText(),
	        	ResultMsg 	= responseNode.get("resultMsg").asText(),
	        	method 		= responseNode.get("payMethod").asText(),
	        	receiptUrl 	= responseNode.get("receiptUrl").asText()
        	;
	        orderId 	= responseNode.get("orderId").asText();
	        txTid 		= responseNode.get("tid").asText();

	        paramMap.put("payMoid", orderId );

	        _uco  = pageService.selectOneUserCustomerOrder( paramMap );
	        _ucol = pageService.selectListUserCustomerOrderList( paramMap );

			_uco
				.addPayMoid(orderId)
				.addOrderStatus(ResultCode)
				.addOrderStatusName(ResultMsg)
				.addMid(clientID)
				.addTid(txTid)
				.addMacketId(clientID)
				.addMethod(method)
				.addPayType(payType)
				.addPayMethod(payMethod)
			;

			_ucoh
				.addUserEmail(_uco.getUserEmail())
				.addPayMoid(orderId)
				.addPayTid(txTid)
				.addPayStatusType("PAY")
				.addStatusResultCode(ResultCode)
				.addStatusResultMsg(ResultMsg)
				.addAmt(_uco.getProductAmt().longValue())
			;

	        if (resultCode.equalsIgnoreCase("0000")) {
	            // 결제 성공 비즈니스 로직 구현
	        	String code = "0000", msg = "결제완료";
				_uco.addStatusResultCode(code).addStatusResultMsg(msg);

				for (UserCustomerOrderList item : _ucol) {
					item.addProductStatus(code);
				}

				UserCustomerOrder _tmp = pageService.selectOneUserCustomerOrder(paramMap);

				UserCustomerPayment payData = new UserCustomerPayment()
					.addReservationId(_tmp.getReservationId())
					.addUserEmail(_tmp.getUserEmail())
					.addPayMoid( orderId )
					.addPayAmount( _tmp.getProductAmt().longValue() )
					.addPayDivision("D")
					.addPayComment("pg 결제 대금")
					.addCreateId( _tmp.getUserEmail() )
				;
				pageService.insertUserCustomerPayment(payData);

				pageService.updateUserCustomerOrder(_uco);
				pageService.insertUserCustomerOrderHistory(_ucoh);
				pageService.updateListUserCustomerOrderList(_ucol);

	        } else {
	            // 결제 실패 비즈니스 로직 구현
	        	pageService.insertUserCustomerOrderHistory(_ucoh);
	        }
	        mav.addObject("RID", _uco.getReservationId() );
	        mav.setViewName("/front/order/process-complete");
    	} catch (Exception e) {
    		logger.info( e.getMessage() );

    		if( orderId != "") {
    			/**
    			*************************************************************************************
    			* <망취소 요청>
    			* 승인 통신중에 Exception 발생시 망취소 처리를 권고합니다.
    			*************************************************************************************
    			*/

//    			HashMap<String, Object> paramMap = new HashMap<>();
//    			 paramMap.put("payMoid", orderId );
//    		     _uco  = pageService.selectOneUserCustomerOrder( paramMap );

	    		responseNode = svcNicepay.netCancel( nicePayUrl, clientID, secretKey, orderId );

	    		resultCode = responseNode.get("resultCode").asText();

	    		String
		        	ResultCode 	= responseNode.get("resultCode").asText(),
		        	ResultMsg 	= responseNode.get("resultMsg").asText();
	    		txTid 		= responseNode.get("tid").asText();

	    		_ucoh
					.addUserEmail(_uco.getUserEmail())
					.addPayMoid(orderId)
					.addPayTid(txTid)
					.addPayStatusType("netcancel")
					.addStatusResultCode(ResultCode)
					.addStatusResultMsg(ResultMsg)
					.addAmt(_uco.getProductAmt().longValue())
				;

	    		pageService.insertUserCustomerOrderHistory(_ucoh);
    		}
			mav.setViewName("/front/order/error");
		}

        return mav;

        //return "/response";
    }

    @RequestMapping("/cancelAuth")
    @ResponseBody
    public ModelAndView requestCancel(
            @RequestParam String tid,
            @RequestParam String amount,
            Model model,

            @RequestParam(value="userEmail", defaultValue = "", required = false)  String userEmail,
            UserCustomerOrder uco,
    		UserCustomerOrderHistory ucoh,
    		UserCustomerOrderList ucol
    		) throws Exception {

    	ModelAndView mav = new ModelAndView();
    	mav.setViewName("/front/order/cancel");
    	HashMap<String, Object> resultMap = new HashMap<>();
    	try {
    		initUrlSetting();

    		HashMap<String, Object> paramMap = new HashMap<>();

    		if(uco.getTid() == null) {
        		throw new Exception("tid is null.");
        	}

        	if(userEmail == null) {
        		throw new Exception("user email is null.");
        	}

    		/**
        	 * 취소 가능 검사
        	 */

        	paramMap.clear();
            paramMap.put("payMoid", uco.getPayMoid());

            uco  = pageService.selectOneUserCustomerOrder( paramMap );
            ArrayList<UserCustomerOrderList> getOrderDetailList = pageService.selectListUserCustomerOrderList(paramMap);

        	int statusCnt = 0, canceled=0;
            for (UserCustomerOrderList prdt : getOrderDetailList) {
            	if(prdt.getProductStatus().equals("9999")) {
            		canceled++;
            	} else if(prdt.getProductStatus().equals("0000")) {
            		statusCnt++;
            	} else if(prdt.getProductStatus().startsWith("9")) {
            		statusCnt++;
            	}
    		}

            boolean isBreak = false;

            if( getOrderDetailList.size() != statusCnt ||  getOrderDetailList.size() == canceled ) {
            	resultMap.put("result", 	"fail");
            	resultMap.put("message", 	"취소불가능");

            	mav.addObject("data", resultMap);

            	return mav;
            }

	        HttpHeaders headers = new HttpHeaders();
	        headers.set("Authorization", "Basic " + Base64.getEncoder().encodeToString((clientID + ":" + secretKey).getBytes()));
	        headers.setContentType(MediaType.APPLICATION_JSON);

	        Map<String, Object> AuthenticationMap = new HashMap<>();
	        AuthenticationMap.put("amount", amount);
	        AuthenticationMap.put("reason", "test");
	        AuthenticationMap.put("orderId", UUID.randomUUID().toString());

	        HttpEntity<String> request = new HttpEntity<>(objectMapper.writeValueAsString(AuthenticationMap), headers);

	        ResponseEntity<JsonNode> responseEntity =
	        		restTemplate.postForEntity(nicePayUrl+"/payments/"+ tid +"/cancel", request, JsonNode.class);

	        JsonNode responseNode = responseEntity.getBody();
	        String resultCode = responseNode.get("resultCode").asText();
	        String resultMsg = responseNode.get("resultMsg").asText();
	        model.addAttribute("resultMsg", responseNode.get("resultMsg").asText());

	        System.out.println(responseNode.toPrettyString());

	        if ( resultCode.equalsIgnoreCase("0000") ) {
	        	String cancelMsg 			= "고객요청 '주문취소'";	// 취소사유

	            // 취소 성공 비즈니스 로직 구현

	        	String CancelAmt 	= (String)responseNode.get("amount").asText();	// 취소금액
	        	String CancelDate 	= (String)responseNode.get("ediDate").asText();	// 취소일
//	        	String CancelTime 	= (String)responseNode.get("CancelTime").asText();	// 취소시간
//	        	String TID 			= (String)responseNode.get("tid").asText();		// 거래아이디 TID
	        	String orderId 		= (String)responseNode.get("orderId").asText();		// 거래아이디 TID
				//Signature       	= (String)resultData.get("Signature");
				//cancelSignature = sha256Enc.encrypt(TID + mid + CancelAmt + merchantKey);

	    		uco
		    		.addUserEmail(userEmail)
		    		.addStatusResultCode("9999")
		    		.addStatusResultMsg(cancelMsg)
		    		.addOrderStatus(resultCode)
		    		.addOrderStatusName(resultMsg)
		    		.addCancelAmt(Long.parseLong(CancelAmt))
	    		;
	    		ucoh
		    		.addUserEmail(userEmail)
		    		.addPayMoid(uco.getPayMoid())
		    		.addPayTid(tid)
		    		.addAmt(uco.getCancelAmt())
		    		.addPayStatusType("CANCEL")
	    		;

	    		List<String> listCode = new ArrayList<String>();
	    		listCode.add("0000");
	    		listCode.add("9990");

	        	ucol
		        	.addUserEmail(userEmail)
		        	.addPayMoid(uco.getPayMoid())
		        	.addProductStatus("9999")
		        	//.addListNowProductStatus(listCode)
	        	;

	        	UserCustomerOrder _tmp = pageService.selectOneUserCustomerOrder(paramMap);
	        	if(uco.getStatusResultCode().equals("9999")) {

					UserCustomerPayment payData = new UserCustomerPayment()
    					.addReservationId(_tmp.getReservationId())
    					.addUserEmail(_tmp.getUserEmail())
    					.addPayMoid( orderId )
    					.addPayAmount( ( _tmp.getProductAmt().longValue() * -1) )
    					.addPayDivision("D")
    					.addPayComment("pg 결제 대금")
    					.addCreateId( _tmp.getUserEmail() )
    				;
					pageService.insertUserCustomerPayment(payData);
				}
	        	pageService.updateUserCustomerOrderList( ucol );
	        	pageService.insertUserCustomerOrderHistory(ucoh);
	        	pageService.updateUserCustomerOrder(uco);

	//        	resultMap.put("oid", formatter.format(new java.util.Date()));
	    		resultMap.put("result", "success");
	    		resultMap.put("message", "정상 처리되었습니다.");

	        } else if( resultCode.equalsIgnoreCase("2015") ) {
	        	uco
		    		.addUserEmail(userEmail)
		    		.addStatusResultCode("9999")
		    		.addStatusResultMsg("기취소성공")
		    		.addOrderStatus(resultCode)
		    		.addOrderStatusName(resultMsg)
		    		.addCancelAmt(Long.parseLong("0"))
	    		;
	    		ucoh
		    		.addUserEmail(userEmail)
		    		.addPayMoid(uco.getPayMoid())
		    		.addPayTid(tid)
		    		.addAmt(uco.getCancelAmt())
		    		.addPayStatusType("CANCEL")
	    		;

	    		ucol
		        	.addUserEmail(userEmail)
		        	.addPayMoid(uco.getPayMoid())
		        	.addProductStatus("9999")
		        	//.addListNowProductStatus(listCode)
	        	;

	    		pageService.updateUserCustomerOrderList( ucol );
	        	pageService.insertUserCustomerOrderHistory(ucoh);
	        	pageService.updateUserCustomerOrder(uco);

	        	resultMap.put("result", "success");
	    		resultMap.put("message", "정상 처리되었습니다.");
	        } else {
	            // 취소 실패 비즈니스 로직 구현

	        	ucoh
		    		.addUserEmail(userEmail)
		    		.addPayMoid(uco.getPayMoid())
		    		.addPayTid(tid)
		    		.addAmt(uco.getCancelAmt())
		    		.addPayStatusType("CANCEL")
		    		.addStatusResultCode(resultCode)
		    		.addStatusResultMsg(resultMsg);

				pageService.insertUserCustomerOrderHistory(ucoh); //실패 이력 남기기~~

				resultMap.put("result",  "fail");
				resultMap.put("message", "오류발생했습니다.");
	        }
    	} catch (Exception e) {
    		logger.debug(e.getCause().getMessage());
		}

//        return "/response";
    	mav.addObject("data", resultMap);

        return mav;
    }

    @RequestMapping("/hook")
    public ResponseEntity<String> hook(@RequestBody HashMap<String, Object> hookMap) throws Exception {
        String resultCode = hookMap.get("resultCode").toString();

        System.out.println(hookMap);

        if(resultCode.equalsIgnoreCase("0000")){
            return ResponseEntity.status(HttpStatus.OK).body("ok");
        }

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }


    private void initUrlSetting() {
    	if(ACTIVE_PROFILE != null) {
	        if( ACTIVE_PROFILE.equals("server") || isDevReal ) {
	        	nicePayUrl = "https://api.nicepay.co.kr/v1";
	        } else if( ACTIVE_PROFILE.equals("dev") ) {
	        	nicePayUrl = "https://sandbox-api.nicepay.co.kr/v1";
	        }
        }
    }

}
