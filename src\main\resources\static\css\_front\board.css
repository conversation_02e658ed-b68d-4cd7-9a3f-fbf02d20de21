/*리스트_공통*/
.list_all_number {
    color: #666;
}

.list_number {
    color: #222;
}

.list_box {
    margin: 12px 0 60px 0;
    padding: 40px 0;
    border-top: 1px solid #666;
    border-bottom: 1px solid #666;
}

.list_narrow_box {
    padding: 0;
}

.list_contents_box {
    display: grid;
    grid-template-columns: repeat(4, calc(25% - 18px));
    grid-column-gap: 24px;
    grid-row-gap: 40px;
}

/*리스트_검색영역*/
.select_search_box.board {
    display: flex;
    justify-content: space-between;
    margin-bottom: 50px;
}

.event_select_box {
    width: 200px;
    height: 40px;
    border-radius: 5px;
    background: #F5F5F5;
}

.event_select {
    border-radius: 5px;
    border: 1px solid #D9D9D9;
    width: 100%; height: 100%;
    padding: 0 20px; box-sizing: border-box;
    color: #222;
    font-family: Pretendard;
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    appearance:none;
    background:url('/images/icon/i_drop.svg') no-repeat right 20px center;
}

.select_search {
	position: relative;
    width: calc(100% - 200px - 20px);
    height: 40px;
    border-radius: 5px;
    border: 1px solid #D9D9D9;
    background: #FFF;
    padding: 8px 20px;
    box-sizing: border-box;
}

/*갤러리형식_리스트*/
.list_contents:hover .list_contents_img img {
    transform: scale(1.1);
    cursor: pointer;
}

.list_contents_img {
    width: 100%;
    height: 282px;
    overflow: hidden;
}

.list_contents_img img {
    width: 100%;
    transition: all 0.3s;
}

/*갤러리형식_뷰페이지*/
.gallery_view_content_title_box {
    margin-top: 50px;
}

.gallery_view_day {
    margin-top: 20px;
}

.gallery_view_name {
    margin-left: 20px;
}

.gallery_view_box {
    margin: 20px 0 40px 0;
    padding: 40px 0;
    border-top: 1px solid #666;
    border-bottom: 1px solid #666;
}

.gallery_view_file {
    padding-bottom: 40px;
    margin-bottom: 40px;
    border-bottom: 1px solid #D9D9D9;
    color: #222;
    width: 100%;
}

.gallery_view_img {
    width: 50%;
    height: auto;
    margin: 0 auto;
    margin-bottom: 40px;
}

.gallery_view_img img {
    width: 100%;
}

/*공통_입력폼*/
.common_input_list {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.common_textarea_list {
    display: flex;
    justify-content: space-between;
}

.common_list_mg_n {
    margin: 0;
}

.common_input_title {
    color: #222;
    font-size: 18px;
    font-weight: 600;
    white-space: nowrap;
    width: 120px;
    margin-right: 20px;
}

.common_input_list_ex_box {
    width: calc(100% - 120px - 20px);
}

.common_input {
    width: calc(100% - 120px - 20px);
    height: 40px;
    border: 1px solid #D9D9D9;
    box-sizing: border-box;
    padding: 0 20px;
    color: #666;
    font-family: Pretendard;
    font-size: 16px;
}

.common_input_list_ex_box .common_input {
    width: 100%;
}

.common_input_ex {
    color: #666;
    font-size: 14px;
    margin-top: 10px;
}

.common_textarea {
    width: calc(100% - 120px - 20px);
    height: 400px;
    border: 1px solid #D9D9D9;
    box-sizing: border-box;
    padding: 12px 20px;
    color: #666;
    font-family: Pretendard;
    font-size: 16px;
    resize: none;
}

/*공지사항_리스트*/
.board_table {
    border-collapse: collapse;
    width: 100%;
    margin-top: 10px;
    margin-bottom: 90px;
    table-layout: fixed;
}

.board_table_mg_b {
    margin-bottom: 20px;
}

.board_table thead tr{
    border-top: 1px solid #666;
    border-bottom: 1px solid #666;
    height: 56px;
    text-align: center;
    color: #222;
    font-weight: 600;
}

.table_no {
    width: 30px;
    padding: 20px 10px;
    vertical-align: top;

}

.board_table tbody tr{
    border-bottom: 1px solid #D9D9D9;
    height: 56px;
    text-align: center;
    color: #222;
}

.table_title_name {
    text-align: left;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break:break-all;
    overflow: hidden;
    table-layout: fixed;
    line-height: 55px;
    padding: 0 30px;
    color: #222;
    /*cursor: pointer;*/
}

.pay .table_title_name{
	text-align: right;
}

.table_title_name a {
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break:break-all;
    overflow: hidden;
}

.table_title_name .qna_item{
	height: 65px;
	line-height: 65px;
}

.table_day {
    width: 90px;
    padding: 0 10px;
}

/*자주하는질문*/
.table_title_question {
    text-align: left;
    color: #222;
    cursor: pointer;
}

.table_title_question p {
    padding: 20px 0px;
}


.table_type {
    width: 90px;
    padding: 20px 60px;
    vertical-align: top;
}

.qna_answer {
    width: 100%;
    padding: 24px 15px;
    border-radius: 5px;
    box-sizing: border-box;
    background: #EEE;
    color: #222;
    line-height: 24px;
    margin-bottom: 20px;
    white-space: wrap;
    display: none;
}

.qna_answer.active {
	animation: fade-in 1s;
	animation-fill-mode: forwards;
	display: block;
}

@keyframes fade-in {
  from {opacity: 0;}
  to {opacity: 1;}
}

@keyframes fade-out {
  from {opacity: 1;}
  to {opacity: 0;}
}


/*글쓰기 버튼*/
.writing_btn_box {
    overflow: hidden;
    margin-bottom: 10px;
}

.writing_btn {
    float: right;
}

/*글쓰기_뷰페이지*/
.writing_view_contents {
    margin-top: 88px;
}
.qna_write_table {
    border-collapse: collapse;
    width: 100%;
    border-top: 1px solid #888888;
    margin-bottom: 40px;
}

.qna_write_table td {
    padding: 40px 0;
    border-bottom: 1px solid rgba(136, 136, 136, 0.53);
}

.table_mg_b {
    margin-bottom: 70px;
}

.graph_title {
    color: #222;
    font-family: Pretendard;
    font-size: 18px;
    font-weight: 500;
    margin-right: 10px;
}

.member_view_contents .graph_title_pd_l {
    padding-left: 60px;
}

.graph_title_estimate {
    width: 140px;
}

/*문의유형*/
.group_select_typeA {
    width: 430px;
    height: 40px;
    background-color: #FFFFFF;
}

.group_select_typeA select {
    width: 100%;
    height: 100%;
    border: 1px solid #CCC;
    color: #666666;
    font-family: Pretendard;
    font-size: 16px;
    padding-left: 10px;
    padding-right: 25px;
    appearance:none;
    background:url('/images/icon/i_drop.svg') no-repeat right 18px center;
}

/*제목*/
.group_title {
    width: 100%;
    height: 40px;
    border: 1px solid #CCC;
    color: #666;
    font-family: Pretendard;
    font-size: 16px;
    font-weight: 400;
    padding-left: 10px;
    box-sizing: border-box;
}

.group_tel{
	display: flex;
	width: 66%;
}
.inline-letter{
	height: 40px;
	line-height: 40px;
	vertical-align: middle;
}

.group_title.tel{
	width: 32%
}


.estimate_view_contents .group_title_estimate {
    color: #222;
}

.table_list_border_n td {
    border: none;
    padding-bottom: 20px;
}

.table_list_last td {
    padding-top: 0;
}

.table_list_last .graph_title {
    vertical-align: top;
    line-height: 40px;
    word-break: keep-all;
}

/*문의내용*/
.inquiry_input_box {
    padding: 10px 0 10px 10px;
}

.inquiry_input {
    width: 100%;
    height: 248px;
    box-sizing: border-box;
    padding: 10px;
    border: 1px solid #CCC;
    background: #FFF;
    color: #666;
    font-family: Pretendard;
    font-size: 16px;
    font-weight: 400;
}

/*공개여부*/
.open_box {
    margin-left: 10px;
}

.open_box input {
    margin-right: 7px;
}

.open_chk {
    float: left;
    color: #888;
    font-family: Pretendard;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 30px;
}

.private_chk {
    float: left;
    margin-left: 70px;
    color: #888;
    font-family: Pretendard;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 30px;
}

/*첨부파일*/
.attach_file_title {
    vertical-align: top;
    line-height: 40px;
}

.images_attach {
    width: 180px;
    height: 40px;
    border: 1px solid #222;
    background: #222;
    color: #FFF;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    line-height: 40px;
}

.images_attach_file {
    font-size: 0;
    line-height: 11px;
    text-align: left;
}
.images_attach_file li {
    display: inline-block;
    font-size: 16px;
    padding: 0 12px 0 0;
    border-right: 1px solid #888;
    color: #222;
    margin-top: 20px;
}

.images_attach_file li:last-child {
    border: none;
    padding: 0 0 0 12px;
}

.images_attach_file .delete {
    color: #E74133;
}

/*비회원 문의 정보*/
.info_check_box {
    padding-bottom: 40px;
    border-bottom: 1px solid #666;
    margin-bottom: 40px;
}

.info_check_list {
    display: flex;
    width: fit-content;
    margin: 0 auto;
}

.info_check_list_input_mg_l {
    margin-left: 50px;
}

.info_check_list_input span {
    margin-right: 20px;
}

.info_check_list_input input {
    width: 400px;
    height: 40px;
    border: 1px solid #CCC;
    color: #666;
    font-family: Pretendard;
    font-size: 16px;
    font-weight: 400;
    padding-left: 10px;
    box-sizing: border-box;
}

/*문의하기_답변*/
.answer_contents {
    width: 100%;
    padding: 60px 0;
    background: #F5F5F5;
    margin-bottom: 40px;
}

/*회원정보수정*/
.password_ex {
    color: #666;
}
.address_title {
    width: 260px;
    float: left;
}
.zip_code {
    float: left;
    width: 180px;
    height: 40px;
    border: 1px solid #222;
    background: #222;
    box-sizing: border-box;
    margin-left: 20px;
    color: #FFF;
    font-weight: 500;
    text-align: center;
    line-height: 40px;
}

.duplication_btn {
    float: left;
    width: 180px;
    height: 40px;
    border: 1px solid #222;
    background: #222;
    box-sizing: border-box;
    margin-left: 20px;
    color: #FFF;
    font-weight: 500;
    text-align: center;
    line-height: 40px;
}


.graph_none_pc {
    display: none;
}

/*나의 문의내역*/
.answer_ok_red {
    color: #E74133;
}
.answer_receipt {
    color: #41a733;
}
.answer_progress {
    color: #4133E7;
}


/*조회기간*/
.viewing_period_box {
    margin: 80px 0 50px 0;
    display: flex;
    align-items: center;
}

.viewing_period_title {
    color: #222;
    font-size: 15px;
    font-weight: 600;
    margin-right: 20px;
}

.viewing_period_date {
    width: 300px;
    height: 40px;
    border-radius: 5px;
    border: 1px solid #D9D9D9;
    background: #F5F5F5;
    color: #222;
    text-align: center;
    font-family: Pretendard;
    font-size: 15px;
}

.viewing_month_box {
    margin-left: 18px;
}

.viewing_month_box li {
    float: left;
    width: 100px;
    height: 40px;
    border-radius: 5px;
    border: 1px solid #222;
    background: #222;
    color: #FFF;
    text-align: center;
    font-size: 15px;
    font-weight: 500;
    line-height: 40px;
    margin-right: 10px;
}

.viewing_month_box li:last-child {
    margin: 0;
}

.table_title_first {
    padding-left: 65px;
}

.board-button-area{text-align:center;}
.file-label {font-size:15px;font-weight:600;margin-right:10px;display:inline-block;vertical-align:top;line-height:24px;}
.file-label:after {content:"|";margin: 0 10px;}
.attach-list {display:inline-block;width: calc(100% - 140px); margin:0;}
.attach-list .attach-item {line-height:1.5;}
.attach-list .attach-item:hover {cursor:pointer;text-decoration:underline;}
.attach-list .filesize {margin-left:20px;color:#888888;}
.attach-list .filesize:before {content: "(";}
.attach-list .filesize:after {content: ")";}
.hidden-input {display:none;}

@media screen and (max-width:1024px) {
    .list_contents_box {
        display: grid;
        grid-template-columns: repeat(3, calc(33.3% - 16px));
        grid-column-gap: 24px;
        grid-row-gap: 40px;
    }
}

@media screen and (max-width:768px) {
    .list_contents_box {
        display: grid;
        grid-template-columns: repeat(2, calc(50% - 12px));
        grid-column-gap: 24px;
        grid-row-gap: 40px;
    }

    /*공통 입력폼*/
    .common_input_list {
        flex-wrap: wrap;
    }

    .common_input_title {
        width: 100%;
        margin-bottom: 10px;
    }

    .common_input {
        width: 100%;
    }

    .common_input_list_ex_box {
        width: 100%;
    }

    .common_textarea_list {
        flex-wrap: wrap;
    }

    .common_textarea {
        width: 100%;
    }

    /*글쓰기*/
    .group_select_typeA  {
        width: 100%;
    }

    /*문의 시 정보 입력*/
    .info_check_list_input input {
        width: 100%;
    }

    .info_check_list_input_mg_l {
        margin-left: 20px;
    }
}

@media screen and (max-width:600px) {
    .select_search_box {
        flex-wrap: wrap;
    }

    .select_search {
        width: 100%;
        margin-top: 10px;
    }

    .board_table {
        margin-top: 20px;
        margin-bottom: 40px;
    }

    .table_title_name {
        padding: 0;
        padding-right: 10px;
    }

    .table_day {
        display: none;
    }

    .table_type {
        width: 90px;
        padding: 20px 10px;
    }

    /*회원정보수정*/
    .graph_none {
        display: none;
    }

    .graph_none_pc {
        display:contents;
    }
}

@media screen and (max-width:425px) {
    .list_contents_box {
        display: block;
    }

    .list_contents {
        margin-bottom: 20px;
    }

    /*갤러리형식_뷰페이지*/
    .gallery_view_img {
        width: 100%;
    }

    /*공지사항*/
    .table_type {
        display: none;
    }

    /**/
    .info_check_list {
        flex-wrap: wrap;
    }

    .info_check_list_input {
        width: 100%;
    }

    .info_check_list_input_mg_l {
        margin-left: 0;
        margin-top: 20px;
    }
}

@media screen and (max-width:562px) {
    .address_title {
        float: unset;
        width: 100%;
        margin-bottom: 20px;
    }
    .zip_code {
        float: unset;
        margin-left: 0;
    }

}

@media screen and (max-width:375px) {
    /*글쓰기*/
    .graph_title {
        font-size: 16px;
    }

    /*회원정보수정*/
    .member_view_contents .group_title {
        width: auto;
    }
}