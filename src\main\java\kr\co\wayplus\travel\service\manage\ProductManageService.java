package kr.co.wayplus.travel.service.manage;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.security.SecureRandom;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import kr.co.wayplus.travel.mapper.manage.ManageMapper;
import kr.co.wayplus.travel.mapper.manage.ProductManageMapper;
import kr.co.wayplus.travel.model.CodeInfo;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.ManageMenu;
import kr.co.wayplus.travel.model.MenuUser;
import kr.co.wayplus.travel.model.ProductAttachFile;
import kr.co.wayplus.travel.model.ProductCategory;
import kr.co.wayplus.travel.model.ProductDetailSchedule;
import kr.co.wayplus.travel.model.ProductDetailScheduleImage;
import kr.co.wayplus.travel.model.ProductInfo;
import kr.co.wayplus.travel.model.ProductInventory;
import kr.co.wayplus.travel.model.ProductPostJson;
import kr.co.wayplus.travel.model.ProductPriceOption;
import kr.co.wayplus.travel.model.ProductPriceOption.DayList;
import kr.co.wayplus.travel.model.ProductPriceOption.FixPriceList;
import kr.co.wayplus.travel.model.ProductRestPeriod;
import kr.co.wayplus.travel.model.ProductTemplate;
import kr.co.wayplus.travel.model.ProductTemplateFile;
import kr.co.wayplus.travel.model.ProductTourImages;
import kr.co.wayplus.travel.model.ProductTourLink;
import kr.co.wayplus.travel.model.RentCarOption;
import kr.co.wayplus.travel.model.Reservation;
import kr.co.wayplus.travel.util.ReservationUtil;

@Service
public class ProductManageService {

	@Value("${upload.file.path}")
	private String imageUploadPath;
	private final ProductManageMapper mapperProduct;
	private final ManageMapper mapperManage;
	private final ReservationUtil reservationUtil;
	private final Logger logger = LoggerFactory.getLogger(getClass());

	@Autowired
	public ProductManageService(ProductManageMapper mapperProduct, ManageMapper mapperManage,
			ReservationUtil reservationUtil) {
		this.mapperProduct = mapperProduct;
		this.mapperManage = mapperManage;
		this.reservationUtil = reservationUtil;
	}

	/*
	 * ################################################product######################
	 * ##########################
	 */
	public int selectCountProduct(HashMap<String, Object> paramMap) {
		return mapperProduct.selectCountProduct(paramMap);
	}

	public List<ProductInfo> selectListProduct(HashMap<String, Object> paramMap) {
		return mapperProduct.selectListProduct(paramMap);
	}

	public ArrayList<ProductCategory> getProductCategories() {
		return mapperProduct.selectProductCategoryList();
	}

	// 상품등록시 템플릿 에디터에 있는 html 부분을 이미지로 바꾸고 db에 저장하는 역할
	public void htmlImageConverter(ProductTemplate productTemplate, String userEmail) {
		if (productTemplate.getImageByte() != null) {

			byte[] decodedImage = Base64.getDecoder().decode(productTemplate.getImageByte().replace(' ', '+'));

			try {
				// 저장 디렉토리 생성 확인
				File file = new File(imageUploadPath);
				if (!file.exists())
					file.mkdirs();

				// 이미지를 저장할 경로와 파일 이름
				String uploadName = UUID.randomUUID().toString();
				String filePath = imageUploadPath + uploadName;

				// 이미지 저장
				FileOutputStream fos = new FileOutputStream(filePath);
				fos.write(decodedImage);
				fos.close();

				File savedFile = new File(filePath);

				ProductAttachFile productAttachFile = new ProductAttachFile();
				productAttachFile.setContentId(productTemplate.getTemplateId());
				productAttachFile.setContentType("template");
				productAttachFile.setUploadPath(imageUploadPath);
				productAttachFile.setUploadFilename(uploadName);
				// TODO : (상품등록기능이 완료될시)상품이름 <- 설정하기
				productAttachFile.setOriginFilename("(상품등록기능이 완료될시)상품이름" + "html-image");
				productAttachFile.setFileSize((int) savedFile.length());
				productAttachFile.setFileMimetype("");
				productAttachFile.setFileExtension("");
				productAttachFile.setUploadId(userEmail);

				mapperProduct.insertProductAttachFile(productAttachFile);

			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	// 템플릿 등록(공통)
	public void insertProductTemplate(ProductTemplate productTemplate) {
		mapperProduct.insertProductTemplate(productTemplate);

		/*
		 * // product-description정보 업데이트
		 * ProductInfo product = new ProductInfo();
		 * product.setProductTourId(productTemplate.getProductTourId());
		 * product.setProductDescription(productTemplate.getTemplateHtml());
		 * product.setCreateId(productTemplate.getCreateId());
		 * mapper.updateProductDescription(product);
		 */
	}

	public void updateProductTemplate(ProductTemplate productTemplate) {
		mapperProduct.updateProductTemplate(productTemplate);
	}

	public void updateProductDescription(ProductInfo productInfo) {
		mapperProduct.updateProductDescription(productInfo);
	}

	// 템플릿 리스트
	public List<ProductTemplate> selectProductTemplateList(HashMap<String, Object> paramMap) {
		return mapperProduct.selectProductTemplateList(paramMap);
	}

	// 템플릭 삭제
	public void deleteTemplate(HashMap<String, Object> paramMap) {
		mapperProduct.deleteTemplate(paramMap);
	}

	// 상품 가격 옵션 리스트
	public List<ProductPriceOption.DayList> selectPriceOptionList(HashMap<String, Object> paramMap) {
		return mapperProduct.selectPriceOptionList(paramMap);
	}

	// 상품 일정 등록
	@Transactional
	public HashMap<String, Object> insertProductSchedule(LoginUser loginUser, ProductPriceOption productPriceOption) {
		HashMap<String, Object> resultMap = new HashMap<>();

		List<Integer> deleteBasicId = productPriceOption.getDeleteBasicPriceOptionList();
		if (deleteBasicId != null && deleteBasicId.size() > 0) {
			HashMap<String, Object> paramMap = new HashMap<>();
			paramMap.put("createId", loginUser.getUserEmail());
			paramMap.put("deleteBasicPriceOptionList", productPriceOption.getDeleteBasicPriceOptionList());
			mapperProduct.deleteCalendarBasicPriceOption(paramMap);
		}

		productPriceOption.setCreateId(loginUser.getUserEmail());
		if (!productPriceOption.getPriceSequence().equals("0")) {
			mapperProduct.deleteProductPriceSet(productPriceOption);
		}

		List<Integer> priceIdList = new ArrayList<>();
		List<Integer> priceOptionIdList = new ArrayList<>();

		if (productPriceOption.getPriceSetType() != null &&
				productPriceOption.getPriceSetType().equals("weekDaySamePrice")) {
			int priceSequence = mapperProduct.selectPriceSequenceNum(productPriceOption) + 1;
			Map<Integer, List<ProductPriceOption.DayList>> periodPriceOptions = productPriceOption.getDayList();

			String oldDay = null;
			Boolean isNewBasic = false;

			for (Map.Entry<Integer, List<ProductPriceOption.DayList>> entry : periodPriceOptions.entrySet()) {
				List<ProductPriceOption.DayList> dayList = entry.getValue();
				for (ProductPriceOption.DayList dayListItem : dayList) {

					if (oldDay == null || oldDay != dayListItem.getPriceSetDate()) {
						oldDay = dayListItem.getPriceSetDate();
						isNewBasic = true;
						priceSequence = mapperProduct.selectPriceSequenceNum(productPriceOption) + 1;
					}

					for (ProductPriceOption.BasicPriceList basicPriceList : productPriceOption.getBasicPriceList()) {
						if (periodPriceOptions != null) {
							if (isNewBasic) {
								basicPriceList.setOptionOneCode(generateRandomCode(8));
								basicPriceList.setPriceOptionId(0);
								basicPriceList.setCreateId(loginUser.getUserEmail());
								mapperProduct.insertProductPriceOption(basicPriceList);
								priceOptionIdList.add(basicPriceList.getPriceOptionId());
								isNewBasic = false;
							}

							String basicOptionName = basicPriceList.getOptionName();

							if (dayListItem.getOptionName().equals(basicOptionName) &&
									dayListItem.getPriceSetDate().equals(oldDay)) {

								dayListItem.setCreateId(loginUser.getUserEmail());
								dayListItem.setPriceOptionId(basicPriceList.getPriceOptionId());
								if (dayListItem.getPriceSequence() == 0) {
									dayListItem.setPriceSequence(priceSequence);
								}
								dayListItem.setPriceSale(0);
								mapperProduct.insertProductPriceSet(dayListItem);
								priceIdList.add(dayListItem.getPriceId());
							}
						}
					}

				}
			}
		} else {
			int priceSequence = mapperProduct.selectPriceSequenceNum(productPriceOption) + 1;
			for (ProductPriceOption.BasicPriceList basicPriceList : productPriceOption.getBasicPriceList()) {
				basicPriceList.setCreateId(loginUser.getUserEmail());

				if (basicPriceList.getOptionOneCode() == null || basicPriceList.getOptionOneCode().equals("")) {
					basicPriceList.setOptionOneCode(generateRandomCode(8));
				}
				mapperProduct.insertProductPriceOption(basicPriceList);
				priceOptionIdList.add(basicPriceList.getPriceOptionId());

				String basicOptionName = basicPriceList.getOptionName();
				Map<Integer, List<ProductPriceOption.DayList>> periodPriceOptions = productPriceOption.getDayList();
				if (periodPriceOptions != null) {
					for (Map.Entry<Integer, List<ProductPriceOption.DayList>> entry : periodPriceOptions.entrySet()) {
						List<ProductPriceOption.DayList> dayList = entry.getValue();
						for (ProductPriceOption.DayList dayListItem : dayList) {
							if (dayListItem.getOptionName().equals(basicOptionName)) {
								dayListItem.setCreateId(loginUser.getUserEmail());
								dayListItem.setPriceOptionId(basicPriceList.getPriceOptionId());
								if (dayListItem.getPriceSequence() == 0) {
									dayListItem.setPriceSequence(priceSequence);
								}

								dayListItem.setPriceSale(0);
								mapperProduct.insertProductPriceSet(dayListItem);
								priceIdList.add(dayListItem.getPriceId());
							}
						}
					}
				}
			}
		}

		resultMap.put("priceIdList", priceIdList);
		resultMap.put("basicPriceIdList", priceOptionIdList);

		return resultMap;
	}

	// 저장된 상품 가격 정보 리스트(최저값만)
	public List<ProductPriceOption> selectAddedPriceSetMinimumList(ProductPriceOption productPriceOption) {
		return mapperProduct.selectAddedPriceSetMinimumList(productPriceOption);
	}

	// 저장된 상품 기본가격 정보 리스트
	public List<ProductPriceOption> selectAddedBasicPriceList(HashMap<String, Object> paramMap) {
		return mapperProduct.selectAddedBasicPriceList(paramMap);
	}

	// 저장된 상품 가격 정보 리스트
	public List<ProductPriceOption> selectAddedPriceSetList(HashMap<String, Object> paramMap) {
		return mapperProduct.selectAddedPriceSetList(paramMap);
	}

	// 일정 수정시 상품판매 기본가격 정보 생성
	public void insertPriceBasicOption(ProductPriceOption.BasicPriceList basicPriceList) {
		mapperProduct.insertPriceBasicOption(basicPriceList);
	}

	// 일정 삭제
	@Transactional
	public void deletePriceSetService(HashMap<String, Object> paramMap) {
		mapperProduct.deleteBasicPriceOption(paramMap);
		mapperProduct.deletePriceSet(paramMap);
	}

	public int insertProductInfoWithProductSerial(ProductInfo productInfo) {
		// 임시 저장
		validationDateTimeForProduct(productInfo);
		productInfo.setProductSerial("0");
		String productCode = generateRandomCode(8);
		productInfo.setProductCode(productCode);
		mapperProduct.insertProductInfo(productInfo);
		int tourId = productInfo.getProductTourId();

		HashMap<String, Object> paramMap = new HashMap<>();
		String generateProductSerialCode = generateProductSerial(String.valueOf(tourId));
		paramMap.put("productSerial", generateProductSerialCode);
		paramMap.put("creatId", productInfo.getCreateId());
		paramMap.put("productTourId", productInfo.getProductTourId());
		mapperProduct.updateProductSerial(paramMap);

		return tourId;
	}

	public void insertProductInfo(ProductInfo productInfo) {
		validationDateTimeForProduct(productInfo);
		mapperProduct.insertProductInfo(productInfo);
	}

	public void updateProductInfo(ProductInfo productInfo) {
		validationDateTimeForProduct(productInfo);
		validationJsonForProduct(productInfo);

		mapperProduct.updateProductInfo(productInfo);
	}

	private void updateProductInfoByProductStatus(ProductInfo productInfo) {
		mapperProduct.updateProductInfoByProductStatus(productInfo);
	}

	public void deleteProductInfo(ProductInfo productInfo) {
		mapperProduct.deleteProductInfo(productInfo);
	}

	@Transactional
	public int updateBasicPriceOptionUseYnService(LoginUser loginUser, ProductPostJson productPostJson)
			throws SQLException {
		// 상품등록완료 되었을때 기본가격정보 use_y처리
		if (productPostJson.getPriceOptionList().getAddBasicOptionId() != null &&
				!productPostJson.getPriceOptionList().getAddBasicOptionId().isEmpty()) {
			HashMap<String, Object> paramMap = new HashMap<>();
			paramMap.put("createId", loginUser.getUserEmail());
			// 마지막 "," 제거용
			StringBuilder builder = new StringBuilder(productPostJson.getPriceOptionList().getAddBasicOptionId());
			if (builder.length() > 0 && builder.charAt(builder.length() - 1) == ',') {
				builder.deleteCharAt(builder.length() - 1);
			}
			String modified = builder.toString();
			String[] basicPriceId = modified.split(",");

			paramMap.put("createId", loginUser.getUserEmail());
			paramMap.put("basicPriceId", basicPriceId);
			mapperProduct.updateBasicPriceOptionUseYn(paramMap);
		}

		if (productPostJson.getPriceOptionList().getFixPriceList() != null &&
				productPostJson.getPriceOptionList().getFixPriceList().size() > 0) {
			// 상품고정가격 등록
			this.insertFixPriceOption(loginUser, productPostJson.getPriceOptionList().getFixPriceList());
		}

		if (productPostJson.getPriceOptionList().getDeleteBasicPriceOptionList() != null &&
				productPostJson.getPriceOptionList().getDeleteBasicPriceOptionList().size() > 0) {
			HashMap<String, Object> basicParamMap = new HashMap<>();
			basicParamMap.put("createId", loginUser.getUserEmail());
			basicParamMap.put("deleteBasicPriceOptionList",
					productPostJson.getPriceOptionList().getDeleteBasicPriceOptionList());
			mapperProduct.deleteCalendarBasicPriceOption(basicParamMap);
		}

		ProductInfo productInfo = productPostJson.getProductInfo();
		productInfo.setLastUpdateId(loginUser.getUserEmail());
		int productTourId = productInfo.getProductTourId();

		this.updateProductInfo(productPostJson.getProductInfo());

		if (productInfo.getProductStatus().equals("S")) {
			HashMap<String, Object> paramMap = new HashMap<>();
			paramMap.put("productTourId", productTourId);
			paramMap.put("productCategoryId", productInfo.getProductCategoryId());
			paramMap.put("createId", loginUser.getUserEmail());
			paramMap.put("type", "modify");
			productTourId = copyProductService(paramMap);

			productPostJson.setUpdOptionGroupCode(paramMap.get("optionGroupCode").toString());

			// 기존 옵션,고정가격 regacy_Y처리
			if (productPostJson.getPriceOptionList().getFixPriceList().size() > 0) {
				HashMap<String, Object> regacyParamMap = new HashMap<>();
				regacyParamMap.put("createId", loginUser.getUserEmail());

				List<String> priceOptionIds = productPostJson.getPriceOptionList().getFixPriceList().stream()
						.map(fixPrice -> String.valueOf(fixPrice.getPriceOptionId()))
						.collect(Collectors.toList());
				regacyParamMap.put("priceOptionIds", priceOptionIds);
				mapperProduct.updateProductPriceOptionRegacyYn(regacyParamMap);

				List<String> priceFixIds = productPostJson.getPriceOptionList().getFixPriceList().stream()
						.map(fixPrice -> String.valueOf(fixPrice.getPriceId()))
						.collect(Collectors.toList());
				regacyParamMap.put("priceFixIds", priceFixIds);
				mapperProduct.updateProductPriceFixRegacyYn(regacyParamMap);
			}

			mapperProduct.updateProductRegacyYn(paramMap);
		} else {
			this.updateProductInfoByProductStatus(productPostJson.getProductInfo()); // 상태저장 처리 R111
		}

		/*
		 * if ( productPostJson.getProductInfo().getRegisterType().equals("modify") ) {
		 * paramMap.put("type", "modify");
		 * //새로운 상품으로 복사
		 * productTourId = copyProductService(paramMap);
		 * //기존 상품 relacy_Y처리
		 * mapperProduct.updateProductRegacyYn(paramMap);
		 * }
		 */

		if (productPostJson.getProductInfo() != null &&
				productPostJson.getProductInfo().getProductMenuId() != 0) {
			HashMap<String, Object> updateParamMap = new HashMap<>();
			updateParamMap.put("productTourId", productTourId);
			updateParamMap.put("createId", loginUser.getUserEmail());
			updateParamMap.put("productMenuId", productPostJson.getProductInfo().getProductMenuId());
			// 골프상품 타입 업데이트
			this.updateProductMenuId(updateParamMap);
		}

		return productTourId;
	}

	@Transactional
	public int addProductDetailScheduleService(LoginUser loginUser, ProductPostJson productPostJson) {
		// 세부일정 등록
		HashMap<String, Object> paramMap = new HashMap<>();
		int productTourId = productPostJson.getDetailScheduleList().get(0).getProductTourId();

		if (productPostJson.getProductInfo() != null) {

			if (productPostJson.getProductInfo().getLastUpdateId() == null)
				productPostJson.getProductInfo().setLastUpdateId(loginUser.getUserEmail());

			this.updateProductInfo(productPostJson.getProductInfo());
		}

		paramMap.put("productTourId", productPostJson.getDetailScheduleList().get(0).getProductTourId());
		paramMap.put("createId", loginUser.getUserEmail());
		if (productPostJson.getDetailScheduleList().size() > 0) {
			mapperProduct.deleteDetailSchedule(paramMap);
			if (!productPostJson.getDetailScheduleList().get(0).getDetailType().equals("notUse")) {
				for (ProductDetailSchedule productDetailSchedule : productPostJson.getDetailScheduleList()) {
					productDetailSchedule.setCreateId(loginUser.getUserEmail());
					productDetailSchedule.parseAndSetAttachImageNums();
					mapperProduct.insertDetailSchedule(productDetailSchedule);
					mapperProduct.deleteDetailScheduleAttachFile(productDetailSchedule);
					if (productDetailSchedule.getAttachImageNums().length > 0) {
						mapperProduct.updateDetailScheduleImage(productDetailSchedule);
					}
				}
			}
		}

		if (productPostJson.getDetailScheduleList().get(0).getRegisterType() != null &&
				productPostJson.getDetailScheduleList().get(0).getRegisterType().equals("modify")) {
			paramMap.put("type", "modify");
			// 새로운 상품으로 복사
			productTourId = copyProductService(paramMap);
			// 기존 상품 relacy_Y처리
			mapperProduct.updateProductRegacyYn(paramMap);
		}

		if (productPostJson.getProductInfo() != null && productPostJson.getProductInfo().getProductMenuId() != 0) {
			HashMap<String, Object> updateParamMap = new HashMap<>();
			updateParamMap.put("productTourId", productTourId);
			updateParamMap.put("createId", loginUser.getUserEmail());
			updateParamMap.put("productMenuId", productPostJson.getProductInfo().getProductMenuId());
			// 골프상품 타입 업데이트
			this.updateProductMenuId(updateParamMap);
		}

		return productTourId;
	}

	public void insertDetailScheduleAttachFile(ProductDetailScheduleImage productDetailScheduleImage) {
		mapperProduct.insertDetailScheduleAttachFile(productDetailScheduleImage);
	}

	public void writeProductSlideImages(ProductTourImages productTourImages) {
		mapperProduct.insertProductSlideImages(productTourImages);
	}

	public void updateProductTourImages(ProductTourImages productTourImages) {
		mapperProduct.updateProductTourImages(productTourImages);
	}

	public void uploadEditorImage(ProductTemplateFile productTemplateFile) {
		mapperProduct.insertTemplateEditorFile(productTemplateFile);
	}

	public ProductTourImages getProductImagesByKey(HashMap<String, Object> paramMap) {
		// return mapper.deleteProductImagesByKey(paramMap);
		return mapperProduct.selectProductImagesByKey(paramMap);
	}

	public void deleteProductImageByKey(HashMap<String, Object> paramMap) {
		mapperProduct.deleteProductImagesByKey(paramMap);
	}

	public void deleteDetailScheduleAttachFile(ProductDetailSchedule productDetailSchedule) {
		mapperProduct.deleteDetailScheduleAttachFile(productDetailSchedule);
	}

	public void updateDetailScheduleImage(ProductDetailSchedule productDetailSchedule) {
		mapperProduct.updateDetailScheduleImage(productDetailSchedule);
	}

	public List<ProductDetailSchedule> selectProductDetailScheduleBtnList(HashMap<String, Object> paramMap) {
		return mapperProduct.selectProductDetailScheduleBtnList(paramMap);
	}

	public List<ProductDetailSchedule> selectProductDetailScheduleList(HashMap<String, Object> paramMap) {
		return mapperProduct.selectProductDetailScheduleList(paramMap);
	}

	public List<ProductDetailSchedule.ImageNumList> selectProductDetailScheduleImageList(
			ProductDetailSchedule productDetailSchedule) {
		return mapperProduct.selectProductDetailScheduleImageList(productDetailSchedule);
	}

	@Transactional
	public void insertFixPriceOption(LoginUser loginUser, List<ProductPriceOption.FixPriceList> fixPriceList)
			throws SQLException {
		for (ProductPriceOption.FixPriceList item : fixPriceList) {
			ProductPriceOption.BasicPriceList basicPriceList = new ProductPriceOption.BasicPriceList();
			if (item != null) {
				basicPriceList.setPriceOptionId(item.getPriceOptionId());
			}
			basicPriceList.setProductTourId(item.getProductTourId());
			basicPriceList.setCreateId(loginUser.getUserEmail());
			basicPriceList.setPriceSequence(0);
			basicPriceList.setOptionGroupName(item.getOptionGroupName());
			basicPriceList.setOptionName(item.getOptionName());
			basicPriceList.setMaxCapacity(item.getMaxCapacity());
			basicPriceList.setMaxQuantity(item.getMaxQuantity());
			basicPriceList.setOptionGroupCode(item.getOptionGroupCode());
			basicPriceList.setProductCode(item.getProductCode());
			basicPriceList.setOptionOneCode(item.getOptionOneCode());

			mapperProduct.insertProductPriceOption(basicPriceList);
			item.setCreateId(loginUser.getUserEmail());
			item.setPriceOptionId(basicPriceList.getPriceOptionId());
			mapperProduct.insertFixPriceOption(item);
		}

	}

	public List<ProductPriceOption> selectOneProductPriceOptionList(HashMap<String, Object> paramMap) {
		return mapperProduct.selectOneProductPriceOptionList(paramMap);
	}

	public List<ProductPriceOption> selectOneProductDayPriceOptionList(HashMap<String, Object> paramMap) {
		return mapperProduct.selectOneProductDayPriceOptionList(paramMap);
	}

	public List<ProductPriceOption.FixPriceList> selectOneProductFixPriceOption(HashMap<String, Object> paramMap) {
		return mapperProduct.selectOneProductFixPriceOption(paramMap);
	}

	public List<ProductTourImages> selectProductInfoImageList(HashMap<String, Object> paramMap) {
		return mapperProduct.selectProductInfoImageList(paramMap);
	}

	public void connectProductImage(HashMap<String, Object> paramMap) {
		mapperProduct.updateProductImageTourId(paramMap);
	}

	/*
	 * #######################################info##################################
	 * ##################
	 */

	public void insertProductOption(ProductPriceOption productPriceOption) {
		// 마지막 순서 값을 가져옴
		int lastOptionOrder = mapperProduct.selectOptionLastOrderNum();
		productPriceOption.setOptionOrder(lastOptionOrder + 1);
		mapperProduct.insertProductOption(productPriceOption);
	}

	public void updateProductOption(ProductPriceOption productPriceOption) {
		mapperProduct.updateProductOption(productPriceOption);
	}

	public void deleteProductOption(ProductPriceOption productPriceOption) {
		mapperProduct.deleteProductOption(productPriceOption);
	}

	public ArrayList<ProductPriceOption> selectProductOptionList(HashMap<String, Object> paramMap) {
		return mapperProduct.selectProductOptionList(paramMap);
	}

	public int selectProductOptionListCount() {
		return mapperProduct.selectProductOptionListCount();
	}

	public ProductPriceOption selectProductOptionItem(HashMap<String, Object> paramMap) {
		return mapperProduct.selectProductOptionItem(paramMap);
	}

	public List<ProductCategory> selectProductCategoryList() {
		return mapperProduct.selectProductCategoryList();
	}

	public HashMap<String, Object> selectListProductCategoryByMenuId() {
		HashMap<String, Object> map = new HashMap<String, Object>();

		List<ProductCategory> list = this.selectProductCategoryList();

		for (ProductCategory pc : list) {
			ArrayList<ProductCategory> _list = null;

			String key = String.valueOf(pc.getProductMenuId());

			if (map.containsKey(key)) {
				_list = (ArrayList<ProductCategory>) map.get(key);
			} else {
				_list = new ArrayList<ProductCategory>();
			}

			_list.add(pc);
			map.put(key, _list);

		}

		return map;
	}

	public void updateBasicProductOptionOrder(ProductPriceOption.BasicPriceList basicPriceList) {
		mapperProduct.updateBasicProductOptionOrder(basicPriceList);
	}

	public void updateProductOrder(ProductInfo productInfo) {
		mapperProduct.updateProductOrder(productInfo);
	}

	public String selectProductGroupCode(HashMap<String, Object> paramMap) {
		return mapperProduct.selectProductGroupCode(paramMap);
	}

	public ArrayList<ProductTemplate> getTemplateList() throws Exception {
		return mapperProduct.selectTemplateList();
	}

	public void validationDateTimeForProduct(ProductInfo product) {

		if (product.getProductShowStartDate() != null && !product.getProductShowStartDate().trim().equals("")) {
			product.setProductShowStartDate(product.getProductShowStartDate());
		} else {
			product.setProductShowStartDate(null);
		}
		if (product.getProductShowEndDate() != null && !product.getProductShowEndDate().trim().equals("")) {
			product.setProductShowEndDate(product.getProductShowEndDate());
		} else {
			product.setProductShowEndDate(null);
		}
		if (product.getProductReservStartDate() != null && !product.getProductReservStartDate().trim().equals("")) {
			product.setProductReservStartDate(product.getProductReservStartDate());
		} else {
			product.setProductReservStartDate(null);
		}
		if (product.getProductReservEndDate() != null && !product.getProductReservEndDate().trim().equals("")) {
			product.setProductReservEndDate(product.getProductReservEndDate());
		} else {
			product.setProductReservEndDate(null);
		}
	}

	public void validationJsonForProduct(ProductInfo product) {

		if (product.getProductJsonAirline() != null && !product.getProductJsonAirline().trim().equals("")) {
			product.setIsProductJsonAirline(false);
		} else {
			product.setIsProductJsonAirline(true);
		}
		if (product.getProductJsonPoint() != null && !product.getProductJsonPoint().trim().equals("")) {
			product.setIsProductJsonPoint(false);
		} else {
			product.setIsProductJsonPoint(true);
		}
	}

	public ArrayList<ProductTourImages> getProductImagesById(HashMap<String, Object> param) {
		return mapperProduct.selectProductImageLists(param);
	}

	public ProductTourImages getProductImageById(HashMap<String, Object> paramMap) {
		return mapperProduct.selectProductImageOne(paramMap);
	}

	public ProductTemplate getSampleTemplateById(int templateId) {
		return mapperProduct.selectSampleTemplateById(templateId);
	}

	public ProductInfo selectProductInfo(HashMap<String, Object> paramMap) {
		return mapperProduct.selectProductInfo(paramMap);
	}

	public List<ProductCategory> selectProductSubCategoryList(HashMap<String, Object> paramMap) {
		return mapperProduct.selectProductSubCategoryList(paramMap);
	}

	public ProductPriceOption.FixPriceList selectOneProductFixPriceList(HashMap<String, Object> paramMap) {
		return mapperProduct.selectOneProductFixPriceList(paramMap);
	}

	public ProductPriceOption.DayList selectOneProductDayPriceList(HashMap<String, Object> paramMap) {
		return mapperProduct.selectOneProductDayPriceList(paramMap);
	}

	@Transactional
	public int copyProductService(HashMap<String, Object> paramMap) {
		/*** 기본,상품정보 복사 ***/
		List<ProductInfo> item = selectListProduct(paramMap);
		ProductInfo productInfo = item.get(0);
		int oldProductTourId = productInfo.getProductTourId();
		// 상품등록시간 수정
		// Date currentDate = new Date();
		// SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd
		// HH:mm:ss.SSS");
		// productInfo.setCreateDate(dateFormat.format(currentDate));

		if (paramMap.get("type") != null) {
			if (paramMap.get("type").equals("modify")) {
				if (productInfo.getProductUseYn() == null)
					productInfo.setProductUseYn("Y");

				productInfo.setProductStatus("S");
			} else if (paramMap.get("type").equals("copy")) {
				productInfo.setProductTitle(item.get(0).getProductTitle() + "_복사본");
				productInfo.setProductUseYn("N");
			}
		}

		mapperProduct.insertCopyProductInfo(productInfo); // 상품정보 복사 함.
		int productTourId = productInfo.getProductTourId();

		if (paramMap.get("type").equals("copy")) {
			// 상품 복사시
			HashMap<String, Object> SerialParamMap = new HashMap<>();
			String generateProductSerialCode = generateProductSerial(String.valueOf(productTourId));
			SerialParamMap.put("productSerial", generateProductSerialCode);
			SerialParamMap.put("creatId", productInfo.getCreateId());
			SerialParamMap.put("productTourId", productTourId);
			mapperProduct.updateProductSerial(SerialParamMap);
		}
		// 이미지 복사
		if (productInfo.getProductImages() != null) {
			String[] images = productInfo.getProductImages().split(",");
			List<Integer> copiedImageNumList = new ArrayList<>();
			for (String imageItem : images) {
				paramMap.put("imageId", imageItem);
				ProductTourImages productTourImages = mapperProduct.selectProductImagesByKey(paramMap);
				if (productTourImages != null) {
					productTourImages.setProductTourId(productTourId);
					mapperProduct.insertCopyProductImages(productTourImages);
					copiedImageNumList.add(productTourImages.getImageId());
				}
			}
			// 복사된 상품과 복사된 이미지 번호 매칭
			if (copiedImageNumList.size() > 0) {
				StringBuilder productImages = new StringBuilder();
				for (Integer copiedImageNum : copiedImageNumList) {
					productImages.append(copiedImageNum).append(",");
				}
				paramMap.put("productImages", productImages.toString());
				paramMap.put("copiedProductTourId", productTourId);
				mapperProduct.updateCopiedProductImage(paramMap);
			}
		}

		/*** 판매가격 복사 ***/
		// 판매 기본가격복사
		List<ProductPriceOption> basicPriceList = mapperProduct.selectBasicPriceList(paramMap);
		if (basicPriceList.size() > 0) {
			paramMap.put("optionGroupCode", basicPriceList.get(0).getOptionGroupCode());
		}

		int priceSequence = 0;
		for (ProductPriceOption basicPrice : basicPriceList) {
			int lastPriceSequence = mapperProduct.selectPriceSetLastSequence();
			paramMap.put("priceOptionId", basicPrice.getPriceOptionId());
			basicPrice.setProductTourId(productTourId);
			// basicPrice.setOptionGroupCode(groupCode);
			mapperProduct.insertCopyProductBasicPrice(basicPrice);

			int copiedPriceOptionId = basicPrice.getPriceOptionId();
			// 판매 고정가격복사
			List<ProductPriceOption.FixPriceList> fixPriceList = mapperProduct.selectFixPriceOptionForCopy(paramMap);
			for (ProductPriceOption.FixPriceList fixPriceItem : fixPriceList) {
				fixPriceItem.setProductTourId(productTourId);
				fixPriceItem.setCopiedPriceOptionId(copiedPriceOptionId);
				// fixPriceItem.setOptionGroupCode(groupCode);
				mapperProduct.insertCopyProductFixPrice(fixPriceItem);
			}
			// 판매 요일별가격복사
			List<ProductPriceOption.DayList> dayPriceList = mapperProduct.selectDayPriceOptionForCopy(paramMap);
			for (ProductPriceOption.DayList dayPrice : dayPriceList) {
				dayPrice.setProductTourId(productTourId);
				dayPrice.setCopiedPriceOptionId(copiedPriceOptionId);
				// dayPrice.setOptionGroupCode(groupCode);
				// 가격 아이템이 같은 그룹인지 체크
				if (priceSequence != dayPrice.getPriceSequence()) {
					priceSequence = dayPrice.getPriceSequence();
					lastPriceSequence += 1;
					dayPrice.setPriceSequence(lastPriceSequence);
				} else {
					dayPrice.setPriceSequence(lastPriceSequence);
				}
				mapperProduct.insertCopyProductDayPrice(dayPrice);
			}
		}
		if (!paramMap.get("type").equals("copy")) {
			// 사용자 요청복사가 아닌 복사완료후 복사전 상품ID의 가격 옵션(최상위옵션,고정,일정)들 삭제처리.
			HashMap<String, Object> deletePriceParam = new HashMap<>();
			deletePriceParam.put("oldProductTourId", oldProductTourId);
			mapperProduct.deleteOldProductPriceFix(deletePriceParam);
			mapperProduct.deleteOldProductPriceSet(deletePriceParam);
			mapperProduct.deleteOldProductPriceOption(deletePriceParam);
		}

		/*** 세부일정 복사 ***/
		List<ProductDetailSchedule> productDetailScheduleList = mapperProduct.selectProductDetailScheduleList(paramMap);
		this.copyProductDetailInfo(productDetailScheduleList, paramMap, productTourId);

		/*** 객실 정보 복사 ***/
		List<ProductDetailSchedule> productStayOptionList = mapperProduct.selectProductStayOptionList(paramMap);
		this.copyProductDetailInfo(productStayOptionList, paramMap, productTourId);

		return productTourId;

	}

	public ArrayList<ProductCategory> getProductCategoriesById(int categoryId) {
		return mapperProduct.selectProductCategoriesById(categoryId);
	}

	public ArrayList<ProductCategory> selectListProductCategory(HashMap<String, Object> param) {
		return mapperProduct.selectListProductCategory(param);
	}

	public ArrayList<ProductCategory> selectListProductCategoryByMenuId(int menuId) {
		HashMap<String, Object> param = new HashMap<String, Object>();
		param.put("menuId", menuId);

		return mapperProduct.selectListProductCategory(param);
	}

	public int updateProductUseYn(HashMap<String, Object> paramMap) {
		return mapperProduct.updateProductUseYn(paramMap);
	}

	public ProductTemplateFile getTemplateEditorImageByKey(HashMap<String, Object> paramMap) {
		return mapperProduct.selectTemplateEditorImageByKey(paramMap);
	}

	public void deleteTemplateEditorImageByKey(HashMap<String, Object> paramMap) {
		mapperProduct.deleteEditorImageByKey(paramMap);
	}

	public List<CodeInfo> selectOptionCodeList(HashMap<String, Object> paramMap) {
		return mapperProduct.selectOptionCodeList(paramMap);
	}

	public int selectOptionCodeListCount(HashMap<String, Object> paramMap) {
		return mapperProduct.selectOptionCodeListCount(paramMap);
	}

	public int selectOptionLastOrderNum() {
		return mapperProduct.selectOptionLastOrderNum();
	}

	public ProductTemplateFile getProductTemplateFileImageByKey(HashMap<String, Object> paramMap) {
		return mapperProduct.selectProductTemplateFileImageByKey(paramMap);
	}

	public ProductTemplate selectProductTemplateById(HashMap<String, Object> paramMap) {
		return mapperProduct.selectProductTemplateById(paramMap);
	}

	public ArrayList<ProductCategory> getSubCategoryListByMenuId(int productMenuId) {
		return mapperProduct.selectSubCategoryListByMenuId(productMenuId);
	}

	@Transactional
	public int updateAndCopyProductInfo(ProductInfo productInfo) {
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("productTourId", productInfo.getProductTourId());
		paramMap.put("createId", productInfo.getCreateId());
		paramMap.put("type", "modify");
		// mapper.updateProductInfo(productInfo);
		// 새로운 상품으로 복사
		int productTourId = copyProductService(paramMap);
		// 기존 상품 relacy_Y처리
		mapperProduct.updateProductRegacyYn(paramMap);

		return productTourId;
	}

	public String generateRandomCode(int length) {
		String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
		SecureRandom random = new SecureRandom();
		StringBuilder result = new StringBuilder(length);

		for (int i = 0; i < length; i++) {
			int randomIndex = random.nextInt(characters.length());
			char randomChar = characters.charAt(randomIndex);
			result.append(randomChar);
		}

		return result.toString();
	}

	// public ArrayList<ProductCategory>
	// getSubCategoryListByMenuId(HashMap<String,Object> param) {
	//
	// param.get("menuSubCategory");
	//
	//
	// return mapper.selectSubCategoryListByMenuId(param);
	// }

	public List<ProductDetailSchedule> selectProductDetailList(HashMap<String, Object> paramMap) {
		return mapperProduct.selectProductDetailList(paramMap);
	}

	// public List<ProductDetailSchedule> selectProductDetailList(HashMap<String,
	// Object> paramMap) {
	// return mapper.selectProductDetailList(paramMap);
	// }

	public void copyProductDetailInfo(List<ProductDetailSchedule> listItem, HashMap<String, Object> paramMap,
			int productTourId) {
		for (ProductDetailSchedule productDetailSchedule : listItem) {
			productDetailSchedule.setProductTourId(productTourId);
			mapperProduct.insertCopyProductDetailSchedule(productDetailSchedule);
			String copiedProductDetailId = String.valueOf(productDetailSchedule.getDetailId());
			// 이미지 복사
			String[] scheduleImages = productDetailSchedule.getAttachImage().split(",");
			List<Integer> copiedScheduleImageNumList = new ArrayList<>();
			for (String imageItem : scheduleImages) {
				if (!imageItem.isEmpty()) {
					paramMap.put("detailImageId", imageItem);
					ProductDetailScheduleImage copyImage = mapperProduct
							.selectProductDetailScheduleImageForCopy(paramMap);
					copyImage.setDetailId(productDetailSchedule.getDetailId());
					mapperProduct.insertCopyProductDetailScheduleImage(copyImage);
					copiedScheduleImageNumList.add(copyImage.getDetailImageId());
				}
			}
			// 복사된 세부일정과 복사된 이미지 번호 매칭
			if (copiedScheduleImageNumList.size() > 0) {
				StringBuilder matchScheduleImages = new StringBuilder();
				for (Integer copiedScheduleImageNum : copiedScheduleImageNumList) {
					matchScheduleImages.append(",").append(copiedScheduleImageNum);
				}
				paramMap.put("attachImages", matchScheduleImages.toString());
				paramMap.put("detailId", copiedProductDetailId);
				mapperProduct.updateCopiedProductDetailScheduleImage(paramMap);
			}
		}
	}

	public void updateProductMenuId(HashMap<String, Object> paramMap) {
		mapperProduct.updateProductMenuId(paramMap);
	}

	public List<RentCarOption> selectRentCarOptionList() {
		return mapperProduct.selectRentCarOptionList();
	}

	/*
	 * public void saveProductRentCarInfoService(ProductInfo productInfo) {
	 * mapper.updateProductRentCarInfo(productInfo);
	 * }
	 */
	@Transactional
	public int updateProductRentCarInfoService(ProductInfo productInfo) {
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("productTourId", productInfo.getProductTourId());
		paramMap.put("createId", productInfo.getCreateId());
		paramMap.put("type", "modify");

		mapperProduct.updateProductRentCarInfo(productInfo);
		// mapper.updateProductInfo(productInfo);
		// 새로운 상품으로 복사
		int productTourId = copyProductService(paramMap);
		// 기존 상품 relacy_Y처리
		mapperProduct.updateProductRegacyYn(paramMap);

		return productTourId;
	}

	public String generateProductSerial(String productTourId) {
		// 현재 날짜를 가져옵니다.
		Date currentDate = new Date();

		// 년, 월, 일을 3자리 숫자 형식으로 포맷합니다.
		SimpleDateFormat yearFormat = new SimpleDateFormat("yyy");
		SimpleDateFormat monthFormat = new SimpleDateFormat("MM");
		SimpleDateFormat dayFormat = new SimpleDateFormat("dd");

		String year = yearFormat.format(currentDate);
		String month = monthFormat.format(currentDate);
		String day = dayFormat.format(currentDate);

		// 3자리 숫자를 조합하여 한 문장을 생성합니다.
		StringBuilder sb = new StringBuilder();
		sb.append(year);
		sb.append(month);
		sb.append(day);
		sb.append("-");
		sb.append(productTourId);

		return sb.toString();
	}

	public ArrayList<FixPriceList> selectListProductFixPrice(HashMap<String, Object> param) {
		return mapperProduct.selectListProductFixPrice(param);
	}

	public ArrayList<DayList> selectListProductDayPrice(HashMap<String, Object> param) {
		return mapperProduct.selectListProductDayPrice(param);
	}

	public void saveProductCommonCategory(ProductCategory pc) throws SQLException {
		if (pc == null) {
			throw new SQLException("model is null");
		} else {
			if (pc.getProductCategoryId() == null) {
				this.insertProductCommonCategory(pc);
			} else {
				this.updateProductCommonCategory(pc);
			}
		}
	}

	public void insertProductCommonCategory(ProductCategory pc) throws SQLException {
		mapperProduct.insertProductCommonCategory(pc);
	}

	public void updateProductCommonCategory(ProductCategory pc) throws SQLException {
		mapperProduct.updateProductCommonCategory(pc);
	}

	public void deleteProductCommonCategory(ProductCategory pc) throws SQLException {
		mapperProduct.deleteProductCommonCategory(pc);
	}

	public void resotreProductCommonCategory(ProductCategory pc) throws SQLException {
		mapperProduct.resotreProductCommonCategory(pc);
	}

	public ArrayList<ProductPriceOption> selectListPriceOption(HashMap<String, Object> paramMap) {
		return mapperProduct.selectListPriceOption(paramMap);
	}

	/*
	 * ################################################ProductInventory#############
	 * ###################################
	 */

	public Integer selectCountProductInventory(HashMap<String, Object> paramMap) {
		return mapperProduct.selectCountProductInventory(paramMap);
	}

	public ArrayList<ProductInventory> selectListProductInventory(HashMap<String, Object> paramMap) {
		return mapperProduct.selectListProductInventory(paramMap);
	}

	public ProductInventory selectOneProductInventory(HashMap<String, Object> paramMap) {
		return mapperProduct.selectOneProductInventory(paramMap);
	}

	public void insertProductInventory(ProductInventory data) throws SQLException {
		mapperProduct.insertProductInventory(data);
	}

	public void updateProductInventory(ProductInventory data) throws SQLException {
		mapperProduct.updateProductInventory(data);
	}

	public void deleteProductInventory(ProductInventory data) throws SQLException {
		mapperProduct.deleteProductInventory(data);
	}

	public ProductInventory selectSummaryProductInventory(HashMap<String, Object> paramMap) {
		return mapperProduct.selectSummaryProductInventory(paramMap);
	}

	/*
	 * ################################################ProductTourLink##############
	 * ##################################
	 */
	public List<ProductTourLink> selectListProductTourLink(HashMap<String, Object> param) {
		return mapperProduct.selectListProductTourLink(param);
	}

	@Transactional
	public void saveProductTourLink(LoginUser loginUser, ProductPostJson productPostJson) throws SQLException {
		// 세부일정 등록

		List<ProductTourLink> list = productPostJson.getProductTourLinkList();

		for (ProductTourLink item : list) {

			switch (item.getStatusType().toUpperCase()) {
				case "N":
					this.insertProductTourLink(item);
					break;
				case "U":
					this.updateProductTourLink(item);
					break;
				case "D":
					this.deleteProductTourLink(item);
					break;
			}

		}
	}

	public void insertProductTourLink(ProductTourLink pc) throws SQLException {
		mapperProduct.insertProductTourLink(pc);
	}

	public void updateProductTourLink(ProductTourLink pc) throws SQLException {
		mapperProduct.updateProductTourLink(pc);
	}

	public void deleteProductTourLink(ProductTourLink pc) throws SQLException {
		mapperProduct.deleteProductTourLink(pc);
	}

	public ArrayList<String> getCalendarPossibleDateList(ProductInfo product) throws SQLException {
		ArrayList<String> calendarResvPossibleList = new ArrayList<String>();
		HashMap<String, Object> rsvParam = new HashMap<>();

		rsvParam.put("startDate", product.getStartDate());
		rsvParam.put("endDate", product.getEndDate());
		rsvParam.put("productSerial", product.getProductSerial());
		ArrayList<Reservation> reservationList = reservationUtil.selectListCalcReservation(rsvParam);

		int pendingStock = product.getPickPeopleCount() == null ? 1 : product.getPickPeopleCount();
		HashMap<String, Object> productParam = new HashMap<>();
		for (Reservation item : reservationList) {
			if (item.getIsRestDate() == 1) {
				continue;
			}

			productParam.put("productSerial", product.getProductSerial());
			ProductInfo innerProduct = mapperProduct.selectProductInfo(productParam);
			if ("1".equals(innerProduct.getPolicyInventory())) {
				// 선택한 날짜범위안에 특가일때
				if (item.getSpecialQuantity() != null) {
					// 예약재고 불가능 판단
					if (item.getMaxCapacity() - item.getTotalOrderCount() < pendingStock) {
						continue;
					}
				}
				// 선택한 날짜범위안에 정가일때
				if ("remain".equals(item.getRsvPossible())) {
					// 예약재고 불가능 판단
					if (item.getMaxCapacity() - item.getTotalOrderCount() < pendingStock) {
						continue;
					}
				}

				if (!"remain".equals(item.getRsvPossible()) && !"remain".equals(item.getSpecialRsvPossible())) {
					continue;
				}
			}

			calendarResvPossibleList.add(item.getDate());
		}

		return calendarResvPossibleList;
	}

	public Map<String, Object> addedPriceListService(int productTourId) {
		Map<String, Object> resultMap = new HashMap<>();

		HashMap<String, Object> param = new HashMap<>();
		param.put("productTourId", productTourId);

		// 새로 등록한 가격옵션값 반환
		List<ProductPriceOption> priceOptionList = mapperProduct.selectPriceOptionListInPriceOptionIds(param);
		resultMap.put("priceOptionList", priceOptionList);

		return resultMap;
	}

	public void updateProductSalesDate(HashMap<String, Object> paramMap) {
		mapperProduct.updateProductSalesDate(paramMap);
	}

	/*
	 * ################################### ProductTourRestPeriod
	 * ###################################
	 */

	public List<HashMap<String, Object>> selectListProductWithRestPeriod(HashMap<String, Object> paramMap) {
		return mapperProduct.selectListProductWithRestPeriod(paramMap);
	}

	public int selectCountProductRestPeriod(HashMap<String, Object> paramMap) {
		return mapperProduct.selectCountProductRestPeriod(paramMap);
	}

	public List<ProductRestPeriod> selectListProductRestPeriod(HashMap<String, Object> paramMap) {
		return mapperProduct.selectListProductRestPeriod(paramMap);
	}

	public ProductRestPeriod selectOneProductRestPeriod(HashMap<String, Object> paramMap) {
		return mapperProduct.selectOneProductRestPeriod(paramMap);
	}

	public void saveProductRestPeriod(LoginUser loginUser, ProductRestPeriod productRestPeriod) throws SQLException {
		if (productRestPeriod.getId() == null) {
			productRestPeriod.setCreateUserId(loginUser.getUserEmail());
			this.insertProductRestPeriod(productRestPeriod);
		} else {
			productRestPeriod.setUpdateUserId(loginUser.getUserEmail());
			this.updateProductRestPeriod(productRestPeriod);
		}
	}

	public void insertProductRestPeriod(ProductRestPeriod data) throws SQLException {
		mapperProduct.insertProductRestPeriod(data);
	}

	public void updateProductRestPeriod(ProductRestPeriod data) throws SQLException {
		mapperProduct.updateProductRestPeriod(data);
	}

	public void deleteProductRestPeriod(ProductRestPeriod data) throws SQLException {
		mapperProduct.deleteProductRestPeriod(data);
	}
}
