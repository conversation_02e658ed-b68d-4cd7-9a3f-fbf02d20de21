package kr.co.wayplus.travel.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class ProductInfo {
    private int
        rownum
    	, productTourId
    	, productMenuId
    	, productStipulation
    	, productRegulation
    	, productViewCount
    	, productFavoriteCount
    	, productSortOrder
    	, productSale
    	, productNormal
    	, upperMenuId
        // 청풍용
    	, productEquipment
    	, productService;
    private Integer productCategoryId
                    , pickPeopleCount
                    , year
                    , month
                    , productThumbnailId;
    private String productPrice;
    private String productTitle;
    private String productSubtitle;
    private String productTag;
    private String productSalesMethod;
    private String productLabel;
    private String productDescription;
    private String productDescriptionType;
    private String productShowStartDate;
    private String productShowEndDate;
    private String productReservStartDate;
    private String productReservEndDate;
    private String productTourStartDate;
    private String productTourEndDate;
    private String productThumbnail;
    private String productImages;
    private String productIncludeItem;
    private String productExcludeItem;
    private String productNeedItem;
    private String productOptionItem;
    private String productNotice;
    private String productUseYn;
    private String productAmenity;
    private String policyInventory;
    private String menuName;
    private String createId;
    private String createDate;
    private String lastUpdateId;
    private String lastUpdateDate;
    private String deleteYn;
    private String deleteId;
    private String deleteDate;
    private String productStatus;
    private String[] images;
    private String isPopular;
    private String regacyYn;
    private String productSerial;
    private String url;
    private String categoryTitle;
    private String subCategoryTitle;
    private String subCategoryClass;
    private String registerType;
    private String carFuelType;
    private String carModelYear;
    private String carCapacity;
    private String carInsurance;
    private String carOption;
    private String productTourRange;
    private String menuType;
    private String menuSubType;
    private String mainExposeType;
    private String menuUrl;
    private String privateCarCapacity;
    private String subscriptionSaleYn;

    private String metaTag;
    private String productSegments;
    private String productAddress;
    private String productAddressDetail;
    private Double productLatitude;	//위도
	private Double productLongitude;	//경도
    private String productLink;
    private String productLocation;
    private String productOption1;
    private String productOption2;
    private String productOption3;
    private String productJsonAirline;
    private String productJsonPoint;
    private String optionName;
    private String maxCapacity;
    private List<String> stayCalendarResvPossibleList;
    private List<String> programCalendarResvPossibleList;

    private Boolean
    	isProductJsonAirline,
    	isProductJsonPoint;
    private Boolean
    	isProductShowStartDate,
    	isProductShowEndDate,
    	isProductReservStartDate,
    	isProductReservEndDate;

    private String productZipcode;
    private String productRoad;
    private String productDetail;
    private String productJibun;
    private String productExtra;
    private String productSigungu;
    private String bname1;
    private String bname2;

    private int orderCount;
    private String orderDate;
    private String optionPack;
    private Boolean isProductAddressInfoNull;

    /*vuitual*/
    private String productLangName;
    private String fullMenuUrl;
    private String originProductPrice;

    /* 청풍용 */
    private String productRunDay;
    private String productRunStartTime;
    private String productRunEndTime;
    private String userFavorite;
    private String userFavoriteId;
    private String moveMenuSort;
    private String productStayStandardPeopleMin;
    private String productStayStandardPeopleMax;

    private String productExtraDescription;

    private String productStayCheckIn;
    private String productStayCheckOut;
    private String productStayCleanTime;
    private String productStayShareLoungeEquipment;
    private String productStayShareBathroomEquipment;
    private String productStayRoomEquipment;
    private String productStayService;
    private List<ProductTourImages> stayImageList;
    private String favoriteCalcType;
    private String earlyBirdPrice;
    private String earlyBirdPriceName;
    private String reservRangeFlag;

    private String productStayType; //숙박유형(게하·펜션)
    private String productStayRoomPeople;
    private String productStayMaxNight; //숙박 최대박수 (1[박] 2일)
    private String productStayConsecuriveDiscountYn; //연박할인 여부
    private String productStayConsecuriveDiscountType; //연박할일 종류(정액/정율)
    private Double productStayConsecuriveDiscountAmount; //연박할인 금액/비율
    private Long productStayExtraPersonDefualtCharge; //연박할인 금액/비율
    private Long productStayExtraPersonConsecuriveCharge; //연박할인 금액/비율
    private String productStayCancelTerms; //취소 및 환불규정
    private String productStayMustKnow; //숙소 숙지사항
    private String productProgramMustKnow; //프로그램 숙지사항
    private String productCode;
    private String startDate;
    private String endDate;
    private String categoryCommonTitle;



    public ProductInfo() {
        this.productSale = 0;
        this.productNormal = 0;
    }

    private Integer priceOptionId;
    private String priceSetType;
    private String priceSetDate;
    private String upperMenuUrl;
    private String langType;
    private String productFavoriteType;

    public ProductInfo addOrderCount(int orderCount) {
    	this.orderCount = orderCount;
    	return this;
    }
    public ProductInfo addProductTourId(int productTourid) {
    	this.productTourId = productTourid;
    	return this;
    }

    public ProductInfo addOderDate(String pickupDate) {
    	this.orderDate = pickupDate;
    	return this;
    }

    public ProductInfo addOptionPack(String packOption) {
    	this.optionPack = packOption;
    	return this;
    }

}
