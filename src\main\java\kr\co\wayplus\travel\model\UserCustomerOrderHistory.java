package kr.co.wayplus.travel.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
public class UserCustomerOrderHistory {
	private Long historySeq;	//결제상태 이력, 자동증가
	private Long payStatusDttm;	//결제상태 일시, now() 기본값
	private String payStatusType;	//결제구분 (PAY : 결제, CANCEL: 취소, PARTIAL: 부분취소)
	private String payMoid;	//거래 주문번호
	private String payTid;	//거래 번호
	private String userEmail;	//사용자ID(이메일ID)

	private String method;	//결제방법
	private Long amt;	//결제금액
	private String statusResultCode;	//결제 상태 코드
	private String statusResultMsg;	//결제 상태 결과 메세지

	public UserCustomerOrderHistory addPayStatusType(String payStatusType) {
		this.payStatusType = payStatusType;
		return this;
	}

	public UserCustomerOrderHistory addPayMoid(String pay_moid) {
		this.payMoid = pay_moid;
		return this;
	}

	public UserCustomerOrderHistory addPayTid(String payTid) {
		this.payTid = payTid;
		return this;
	}

	public UserCustomerOrderHistory addUserEmail(String userEmail) {
		this.userEmail = userEmail;
		return this;
	}

	public UserCustomerOrderHistory addMethod(String method) {
		this.method = method;
		return this;
	}

	public UserCustomerOrderHistory addAmt(Long amt) {
		this.amt = amt;
		return this;
	}

	public UserCustomerOrderHistory addStatusResultCode(String statusResultCode) {
		this.statusResultCode = statusResultCode;
		return this;
	}

	public UserCustomerOrderHistory addStatusResultMsg(String statusResultMsg) {
		this.statusResultMsg = statusResultMsg;
		return this;
	}
}
