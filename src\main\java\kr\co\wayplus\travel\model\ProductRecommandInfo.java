package kr.co.wayplus.travel.model;
import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
//@Table(name = "product_recommendations")
public class ProductRecommandInfo extends CommonDataSet {
    //@Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String recommandDate;	//추천데이터 생성일
    private String productSerial;	//상품고유번호
    private Integer ageGroup;	//연령대
    private String gender;	//성별
    private String purpose;	//목적별
    private Double score;	//추천점수

}