package kr.co.wayplus.travel.mapper.user;

import kr.co.wayplus.travel.model.*;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;

@Mapper
@Repository
public interface UserMapper {

    LoginUser selectUserByUserid(HashMap<String, Object> param);

    LoginUser selectUserByUserid(String username);

    void insertUserLoginLog(HashMap<String, String> parameterMap);

    void updateUserLastLoginDate(LoginUser user);

    int selectUserCountById(HashMap<String, Object> id);

    void insertNewUser(LoginUser user);

    void insertWithdrawalUser(LoginUser storedUser);

    void deleteUserByUserid(LoginUser user);

    ArrayList<LoginUser> selectUserListByUserName(HashMap<String, Object> param);

    LoginUser selectRePasswordUserByUserInfo(HashMap<String, Object> param);

    void updateUserPassword(LoginUser user);


    String selectUserLastLoginTime(HashMap<String, String> userEmail);


    ArrayList<LoginUser> selectUserListByRole(HashMap<String, Object> param);

    int selectCountUserListByRole(HashMap<String, Object> param);

    void updateUserSessionLogout(LoginUserSession loginUserSession);

    void insertUserLoginAttemptLog(LoginAttemptLog attemptLog);

    void updateUserNewTokenId(LoginUser user);

    void updateUserWebLog(HashMap<String, String> param);

    void insertUserWebLog(WebServiceLog webLog);

    void updateUserInfo(LoginUser user);

    void updateUserFavoriteCount(HashMap<String, Object> userParam);

    LoginUser selectUserByIdToken(HashMap<String, Object> param);

    void updateUserSubscribeInfo(LoginUser user);

    LoginUser selectUserByNameMobile(HashMap<String, Object> param);

    UserCustomerPayment selectUserPayTotalSummary(HashMap<String, Object> param);

    void insertUserSubscribeServiceInfo(UserSubscribeServiceInfo userSubscribeServiceInfo);

    int selectUserSubscribeServiceInfoListCount(HashMap<String, Object> param);

    ArrayList<UserSubscribeServiceInfo> selectUserSubscribeServiceInfoList(HashMap<String, Object> param);

    UserFavorite selectOneUserFavorite(UserFavorite userFavorite);

    ArrayList<UserFavorite> selectListUserFavorite(HashMap<String, Object> paramMap);

    int selectCountUserFavorite(HashMap<String, Object> paramMap);

    void insertUserFavorite(UserFavorite userFavorite);

    void deleteUserFavorite(UserFavorite userFavorite);

    void insertUserAttachFile(UserAttachFile userAttachFile);

    ArrayList<BadgeAcquireHistory> selectListUserBadgeAcquireHistory(HashMap<String, Object> userParam);

    ArrayList<BoardContents> selectListUserCompleteMission(HashMap<String, Object> userParam);

    int selectCountUserComment(HashMap<String, Object> userParam);

    void updateUserIntroPageLink(HashMap<String, Object> paramMap);

    ArrayList<Islandlife> selectListIslandlife(HashMap<String, Object> paramMap);

    void updateUserIslandLife(HashMap<String, Object> paramMap);


}
