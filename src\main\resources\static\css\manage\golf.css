th { border-bottom: none !important; }
tr { background: #FFFFFF;}
td { height: 57px; font-weight: 400; font-size: 15px; color: #444444; }
button { background: none; border: none; }
p { margin: 0; padding: 0 }
select { -o-appearance: none; -webkit-appearance: none; -moz-appearance: none; appearance: none;}
label { margin-bottom: 0}
.product-option-change-box { display: flex; align-items: center; padding: 21px 30px 21px 0; }
.golf-schedule-select {
    position: relative; width: 440px; height: 35px;
    margin-left: 10px; padding: 8px 12px;
    font: 400 15px Pretendard; color: #444444;
    border: 1px solid #CCCCCC; background: #FFFFFF url('/images/icon/full-arrow-down.svg') no-repeat 98% 50%;
}
.golf-schedule-text-box {
    display: flex;
    align-items: center;
    padding: 30px 0;
}
.golf-option-box {
    display: none;
    padding-top: 25px;
    padding-left: 70px;
    padding-bottom: 31px;
    border-top:solid 1px #CCCCCC;
}
.golf-option-day-tab-box {
    display: flex;
    flex-wrap: wrap;
}
.golf-option-content-box {
    /*display: none;*/
    width: 100%;
    padding: 25px;
    border: 1px solid #CCCCCC;
    background: rgba(238, 238, 238, 0.93);
}
.golf-option-content-inner-box {
    display:flex; width: 100%; height: 275px; margin-bottom: 20px;
    padding: 25px 25px 25px 20px; border: 1px solid #CCCCCC; background: #FFFFFF;
}
.golf-option-order-text {
    min-width: 66px;
    font: 600 16px Pretendard;
    color: #222222;
}
.golf-option-item-box {
    width: 100%;
    margin-right: 11px;
}
.golf-option-first-line {
    display:flex; margin-bottom: 8px;
}
.golf-option-name-box {
    width: 100%; height: 70px;
    padding-top: 13px; padding-right: 20px; padding-left: 20px; border: 1px solid #CCCCCC
}
.golf-option-name-label {
    display: grid; height: 49px;
    font-weight: 600; font-size: 15px; color: #222222
}
.golf-option-name-input {
    height: 19px;
    font-weight: 400; font-size: 15px; color: #666666
}
.golf-option-second-line {
    width: 100%; height: 70px; margin-right: 2px; margin-bottom: 8px;
    padding-top: 13px; padding-right: 20px; padding-left: 20px; border: 1px solid #CCCCCC
}
.golf-option-title-label {
    display: grid; height: 49px;
    font-weight: 600; font-size: 15px; color: #222222
}
.golf-option-title-input {
    height: 19px;
    font-weight: 400; font-size: 15px; color: #666666
}
.golf-option-third-line {
    display:flex; justify-content: space-between;
}
.golf-option-item-image-list {
    position: relative;
    display: flex; max-width: 160px; width: 100%;
    overflow: hidden;
}
.golf-option-item-image-box {
    position: relative; width: 70px; height: 70px;
}
.golf-option-item-image-box img {
    width: 70px; height: 70px;
}
.golf-option-swiper-button-next::after,
.golf-option-swiper-button-prev::after {
    display: none;
}
.golf-option-swiper-button-prev {
    position: absolute;
    top: 37%;
    width: 25px; height: 25px;
    background: url('/images/icon/left-arrow-gray-6x12.svg') no-repeat;
    background-size: 25px 25px;
    text-indent: -9999px;
    z-index: 1;
}

.golf-option-swiper-button-next {
    position: absolute;
    top: 37%;
    right: 0;
    width: 25px; height: 25px;
    background: url('/images/icon/right-arrow-gray-6x12.svg') no-repeat;
    background-size: 25px 25px;
    text-indent: -9999px;
    z-index: 1;
}
.golf-option-save-btn-box {
    text-align: center;
    margin-bottom:0;
    padding-bottom:40px;
    padding-top:40px;
}

.golf-option-save-btn-box > .save-cancel {
    width: 102px; height: 35px;
    margin-right: 10px;
    color: #FFFFFF;
    border-radius: 5px;
    background-color: #383838;
    line-height: 35px;
}

.golf-option-save-btn-box > .save {
    width: 102px; height: 35px;
    margin-right: 10px;
    color: #FFFFFF;
    border-radius: 5px;
    background-color: #0062D4;
    line-height: 35px;
}
.golf-option-outer-box {
    display: none;
}