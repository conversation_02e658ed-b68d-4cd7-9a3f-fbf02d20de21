package kr.co.wayplus.travel.mapper.front;

import kr.co.wayplus.travel.model.ProductPriceOption;
import kr.co.wayplus.travel.model.Reservation;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

@Mapper
@Repository 
public interface ReservationMapper {
    /**
     * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
     */
//	<!--################################### Reservation ###################################-->
    int selectCountReservation(HashMap<String, Object> paramMap);
    int selectCountReservation(Reservation ic);
    ArrayList<Reservation> selectListReservation(HashMap<String, Object> paramMap);
    ArrayList<Reservation> selectListReservation(Reservation ic);
    ArrayList<Reservation> selectListCalcReservationForUser(HashMap<String, Object> paramMap);

    Reservation selectOneReservation(HashMap<String, Object> paramMap);
    ArrayList<HashMap<String, Object>> selectListInquiryCountStatusType(HashMap<String, Object> paramMap);
    void insertReservation(Reservation ic)throws SQLException;
    void updateReservation(Reservation ic) throws SQLException;
    void restoreReservation(Reservation ic) throws SQLException;
    void deleteReservation(Reservation ic) throws SQLException;

    ArrayList<HashMap<String, Object>> selectListCountReservationContentByCalendar(HashMap<String, Object> paramMap);
    ArrayList<HashMap<String, Object>> selectListReservationContentByCheckList(HashMap<String, Object> paramMap);
    HashMap<String, Object> selectListReservationCountStatusType(HashMap<String, Object> paramMap);

    ArrayList<ProductPriceOption> selectListPriceOptions(int tourId);

    ArrayList<Reservation> selectListResOptionsById(int resId);
}
