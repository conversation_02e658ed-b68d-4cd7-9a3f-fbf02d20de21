package kr.co.wayplus.travel.model;

import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.regex.Pattern;

import org.apache.catalina.Role;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.util.Assert;

import com.fasterxml.jackson.annotation.JsonInclude;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import kr.co.wayplus.travel.model.recommandation.Purchase;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LoginUser extends CommonDataSet implements UserDetails, OAuth2User {
    private String userEmail;
    private String userPassword;
    private String userJoinType;
    private String userLangType = "kr";
    private String userName;
    private String userMobile;
    private String userPassportNo;		//여권번호
    private String userPassportImgPath;	//여권사진경로
    private String userPassportExpirationDay;
    private String userNameEn;			//이름영문
    private String userTel;
    private String userAddrZipcode;
    private String userAddrExtra;
    private String userAddrJibun;
    private String userAddrRoad;
    private String userAddrDetail;
    private String userNationality;
    private String userBirthday;
    private String userAge;
    private String userGender;
    private String userRole;
    private Integer userAuthId;
    private String userClassName;
    private String userClassCode;
    private String userGroupCode;
    private String userTokenId;
    private int userGradeId;
    private String userGrade;
    private String userGradeStart;
    private String userGradeEnd;
    private int userMembershipId;
    private String userMembershipGrade;
    private String userMembershipStart;
    private String userMembershipStartFormat;
    private String userMembershipEnd;
    private String userMembershipEndFormat;
    private String userVerifiedEmail;
    private String userVerifiedMobile;
    private String userCi;
    private String userDi;
    private String userDiCorp;
    private String userJoinDate;
    private String userModifyDate;
    private String userMemo; /*사용자 메모*/
    private String lastLoginDate;
    private String lastPasswordDate;
    private int lastLoginFailCount;
    private String accountStatus;
    private String accountType;
    private String naverToken;
    private String naverEmail;
    private String naverJoinDate;
    private String kakaoToken;
    private String kakaoEmail;
    private String kakaoJoinDate;
    private String googleToken;
    private String googleEmail;
    private String googleJoinDate;
    private String facebookToken;
    private String facebookEmail;
    private String facebookJoinDate;
    private String secondaryEmail;
    private int privacyRetentionDays;
    private String mailingYn;
    private List<Role> roles;
    private boolean accountNonExpired = true;
    private boolean accountNonLocked = true;
    private boolean credentialsNonExpired = true;
    private boolean enabled = true;

    private String encrypt;
    private String iv;
    private String operator;
    private String userTeam;

    private Integer groupId;

    private Set<GrantedAuthority> authorities;
    private Map<String, Object> attributes;
    private String nameAttributeKey;

    //청풍용
    private String userNickName;
    private String userProfileImage;
    private String userIntro;
    private String userShowYn;
    private String userSumLifeStyle;
    private String userIntroPageLink;
    private String joinRootType;
    private String joinRoot;
    private String joinReasonType;
    private String joinReason;
    private String userFavorite;
    private Integer userGetLikeCount;
    private Integer userSendLikeCount;
    private Integer userWroteCommentCount;
    private Integer userFavoriteId;
    private String userIslandLifeId;

    private Integer missionCount;

    private String userLivePlace;
    private String userLivePlaceType;

    //private List<Purchase> purchases = new ArrayList<>();

    //UserDetail 의 Username 으로인해 자동처리 안되어 생성
    public String getUserName() {
        return userName;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }

    @Override
    public String getPassword() {
        return userPassword;
    }

    @Override
    public String getUsername() {
        return userEmail;
    }

    @Override
    public boolean isAccountNonExpired() {
        return accountNonExpired;
    }

    @Override
    public boolean isAccountNonLocked() {
        return accountNonLocked;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return credentialsNonExpired;
    }

    @Override
    public boolean isEnabled() {
        return enabled;
    }


    public LoginUser addGuestUser(String uuidData) {
    	this.userEmail = uuidData;
    	this.userTokenId = uuidData;
    	return this;
    }

    public LoginUser addUserName(String userName) {
    	this.userName = userName;
    	return this;
    }
    public LoginUser addUserJoinType(String userJoinType) {
    	this.userJoinType = userJoinType;
    	return this;
    }

	public LoginUser addEncrypt(String Encrypt) {
		this.encrypt = Encrypt;
		return this;
	}
	public LoginUser addIv(String Iv) {
		this.iv = Iv;
		return this;
	}

	public LoginUser addUserPassworad(String UserPassword) {
		this.userPassword = UserPassword;
		return this;
	}

	public LoginUser addUserMobile(String UserMobile) {
		this.userMobile = UserMobile;
		return this;
	}

	public LoginUser addUserNameEn(String userNameEn) {
		this.userNameEn = userNameEn;
		return this;
	}
	public LoginUser addUserPassportNo(String userPassportNo) {
		this.userPassportNo = userPassportNo;
		return this;
	}
	public LoginUser addUserBirthday(String userBirthday) {
		this.userBirthday = userBirthday;
		return this;
	}
	public LoginUser addUserPassportExpirationDay(String userPassportExpirationDay) {
		this.userPassportExpirationDay = userPassportExpirationDay;
		return this;
	}

    public LoginUser(){};

    public LoginUser(Collection<? extends GrantedAuthority> authorities, Map<String, Object> attributes, String nameAttributeKey) {
        Assert.notEmpty(attributes, "attributes cannot be empty");
        Assert.hasText(nameAttributeKey, "nameAttributeKey cannot be empty");
        if (!attributes.containsKey(nameAttributeKey)) {
            throw new IllegalArgumentException("Missing attribute '" + nameAttributeKey + "' in attributes");
        }
        this.authorities = (authorities != null)
                ? Collections.unmodifiableSet(new LinkedHashSet<>(this.sortAuthorities(authorities)))
                : Collections.unmodifiableSet(new LinkedHashSet<>(AuthorityUtils.NO_AUTHORITIES));
        this.attributes = Collections.unmodifiableMap(new LinkedHashMap<>(attributes));
        this.nameAttributeKey = nameAttributeKey;
    }

    @Override
    public String getName() {
        if(this.nameAttributeKey != null)
            return this.getAttribute(this.nameAttributeKey).toString();
        else
            return this.userEmail;
    }

    @Override
    public Map<String, Object> getAttributes() {
        return this.attributes;
    }

    public void setNewUserInfo(String email, String name, String registrationId) {
        this.userEmail = email;
        this.userName = name;
        this.userJoinType = registrationId;
    }

    private Set<GrantedAuthority> sortAuthorities(Collection<? extends GrantedAuthority> authorities) {
        SortedSet<GrantedAuthority> sortedAuthorities = new TreeSet<>(
                Comparator.comparing(GrantedAuthority::getAuthority));
        sortedAuthorities.addAll(authorities);
        return sortedAuthorities;
    }

    /* 연령대를 알기 위해 추가
     * 작성자 : 윤재웅*/
	public Integer getAge() {
		if( this.getUserBirthday() != null && this.getUserBirthday().trim().length() !=0 ) {
	        LocalDate birth = parseFlexibleDate( this.getUserBirthday());
	        return Period.between(birth, LocalDate.now()).getYears();
		} else {
			return null;
		}
	}
	private static LocalDate parseFlexibleDate(String dateStr) {
        // 입력값 검증
        if (dateStr == null || dateStr.trim().isEmpty()) {
            throw new IllegalArgumentException("날짜가 비어있습니다.");
        }

        // 기본적인 형식 검증 (yyyy-M-d 또는 yyyy-MM-dd)
        if (!Pattern.matches("^\\d{4}-\\d{1,2}-\\d{1,2}$", dateStr)) {
            throw new IllegalArgumentException("날짜 형식이 올바르지 않습니다. (예: 1985-05-03 또는 1985-5-3)");
        }

        // 날짜 부분 분리
        String[] parts = dateStr.split("-");
        int year = Integer.parseInt(parts[0]);
        int month = Integer.parseInt(parts[1]);
        int day = Integer.parseInt(parts[2]);

        // 날짜 유효성 검증
        if (year < 1900 || year > LocalDate.now().getYear()) {
            throw new IllegalArgumentException("유효하지 않은 연도입니다.");
        }
        if (month < 1 || month > 12) {
            throw new IllegalArgumentException("유효하지 않은 월입니다.");
        }
        if (day < 1 || day > 31) {
            throw new IllegalArgumentException("유효하지 않은 일자입니다.");
        }

        try {
            return LocalDate.of(year, month, day);
        } catch (Exception e) {
            throw new IllegalArgumentException("유효하지 않은 날짜입니다: " + e.getMessage());
        }
    }

    public Integer getAgeGroup() {
    	if( getAge() != null) {
    		return getAge() / 10 * 10;
    	} else {
    		return null;
    	}
    }

}
