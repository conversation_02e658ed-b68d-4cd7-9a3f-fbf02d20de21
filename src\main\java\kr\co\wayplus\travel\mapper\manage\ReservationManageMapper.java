package kr.co.wayplus.travel.mapper.manage;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.Reservation;
import kr.co.wayplus.travel.model.ReservationQna;
import kr.co.wayplus.travel.model.ReservationUserConnect;

@Mapper
@Repository
public interface ReservationManageMapper {
	/**
	 * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
	 */
//	<!--################################### Reservation ###################################-->
	int selectCountReservation(HashMap<String, Object> paramMap);
	int selectCountReservation(Reservation ic);
	ArrayList<Reservation> selectListReservation(HashMap<String, Object> paramMap);
	ArrayList<Reservation> selectListReservation(Reservation ic);
	Reservation selectOneReservation(HashMap<String, Object> paramMap);
	ArrayList<HashMap<String, Object>> selectListInquiryCountStatusType(HashMap<String, Object> paramMap);
	void insertReservation(Reservation ic)throws SQLException;
	void updateReservation(Reservation ic) throws SQLException;
	void restoreReservation(Reservation ic) throws SQLException;
	void deleteReservation(Reservation ic) throws SQLException;

	ArrayList<HashMap<String, Object>> selectListCountReservationContentByCalendar(HashMap<String, Object> paramMap);
	ArrayList<HashMap<String, Object>> selectListReservationContentByCheckList(HashMap<String, Object> paramMap);
	HashMap<String, Object> selectListReservationCountStatusType(HashMap<String, Object> paramMap);
	String selectOneIsHaveSpecialPrice(HashMap<String, Object> paramMap);
	ArrayList<Reservation> selectListCalcReservation(HashMap<String, Object> paramMap);
//	<!--################################### ReservationQna ###################################-->
	int selectCountReservationQna(HashMap<String, Object> paramMap);
	ArrayList<ReservationQna> selectListReservationQna(HashMap<String, Object> paramMap);
	Reservation selectOneReservationQna(HashMap<String, Object> paramMap);
	void insertReservationQna(ReservationQna ic)throws SQLException;
	void updateReservationQna(ReservationQna ic) throws SQLException;
	void restoreReservationQna(ReservationQna ic) throws SQLException;
	void deleteReservationQna(ReservationQna ic) throws SQLException;
//	<!--################################### ReservationUserConnect ###################################-->
	int selectCountReservationUserConnect(HashMap<String, Object> paramMap);
	ArrayList<LoginUser> selectListReservationUserConnect(HashMap<String, Object> paramMap);
	Reservation selectOneReservationUserConnect(HashMap<String, Object> paramMap);
	void insertReservationUserConnect(ReservationUserConnect ic)throws SQLException;
	void deleteReservationUserConnect(ReservationUserConnect ic) throws SQLException;
	

}
