package kr.co.wayplus.travel.web.front;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;

import kr.co.wayplus.travel.model.*;
import kr.co.wayplus.travel.service.manage.BadgeManageService;
import kr.co.wayplus.travel.service.user.UserPointService;
import kr.co.wayplus.travel.service.user.UserService;

import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.security.SecurityProperties.User;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.ModelAndView;

import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.mapper.admin.AdminMapper;
import kr.co.wayplus.travel.mapper.manage.AlertManageMapper;
import kr.co.wayplus.travel.service.api.NCloudApiService;
import kr.co.wayplus.travel.service.front.PageService;
import kr.co.wayplus.travel.service.front.ProductService;

@Controller
@RequestMapping("/order")
public class OrderController  extends BaseController {

    @Value("${cookie-set.domain}")
    private String cookieDomain;
    @Value("${cookie-set.prefix}")
    private String cookieName;

    /*기본 파라미터 셋팅
    @Value("${pg.nicepay.merchantID}")
	private String merchantID;
	@Value("${pg.nicepay.merchantKey}")
	private String merchantKey;
	*/

	/*기본 파라미터 셋팅(forstartup)
	@Value("${pg.nicepay.type}")
	private String payType;
	@Value("${pg.nicepay.method}")
	private String payMethod;
	@Value("${pg.nicepay.clientID}")
	private String clientID;
	@Value("${pg.nicepay.secretKey}")
	private String secretKey;

	@Value("${pg.nicepay.GoodsCl}")
	private String GoodsCl; 		//상품구분
	@Value("${pg.nicepay.TransType}")
	private String TransType;		//일반(0)/에스크로(1)
	@Value("${pg.nicepay.CharSet}")
	private String CharSet;			//응답 파라미터 인코딩
	@Value("${pg.nicepay.ReqResrved}")
	private String ReqResrved;		//상점 예약필드
	*/

	/* 기본 파라미터 셋팅 */
    @Value("${pg.tosspay.method}")
    private String payMethod;
	@Value("${pg.tosspay.client.key}")
	private String pgTosspayClientKey;
	@Value("${pg.tosspay.server.key}")
	private String pgTosspayServerKey;

	private final Logger logger = LoggerFactory.getLogger(getClass());

	private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    private final PageService pageService;
    private final ProductService productService;
	private final UserPointService userPointService;
	private final BadgeManageService badgeManageService;
	private final UserService userService;
	private final NCloudApiService nCloudApiService;
	private final AlertManageMapper alertManageMapper;
	private final AdminMapper adminMapper;

    @Autowired
	private DataSourceTransactionManager transactionManager;

    @Autowired
    public OrderController(PageService pageService, ProductService productService, UserPointService userPointService, BadgeManageService badgeManageService
		,UserService userService, NCloudApiService nCloudApiService, AlertManageMapper alertManageMapper, AdminMapper adminMapper) {
        this.pageService    = pageService;
        this.productService = productService;
		this.userPointService = userPointService;
		this.badgeManageService = badgeManageService;
		this.userService = userService;
		this.nCloudApiService = nCloudApiService;
		this.alertManageMapper = alertManageMapper;
		this.adminMapper = adminMapper;
	}

    @PostMapping("/beforeSheet")
    public ModelAndView order_befor_sheet(
    		HttpServletRequest request
    		,HttpSession session
    		,@RequestParam(value="productSerial") String productSerial
    		,@RequestParam(value="productTourId") Integer productTourId
			,@RequestParam(value="userIslandLifeId", defaultValue = "0") Integer userIslandLifeId
    		,@RequestParam(value="pickPeople") Integer pickPeople
    		,@RequestParam(value="pickDate[]") String[] pickDate
    		,@RequestParam(value="productPrice[]") Integer[] productPrice
    		,@Param(value="totalPrice") Long totalPrice

    		, @RequestParam(value="originPrice", defaultValue = "0") Long originPrice
    		, @RequestParam(value="discountAmount", defaultValue = "0") Long discountAmount
    		, @RequestParam(value="chargePeopleAmount", defaultValue = "0") Long chargePeopleAmount

    		,@Param(value="pickDays") Long pickDays
    		,@RequestParam(value="formattedDate") String formattedDate
    		,@RequestParam(value="optionId[]") Integer[] optionId
    		,@RequestParam(value="optionName[]") String[] optionName
			,@RequestParam(value="optionOneCode[]") String[] optionOneCode
			,@RequestParam(value="productType", defaultValue = "product") String productType
			,@RequestParam(value="pickCapacity", defaultValue = "0") Integer pickCapacity
			,@RequestParam(value="consecuriveDiscountAmount[]") Integer[] consecuriveDiscountAmount
    		,@RequestParam(value="extraPersonDefualtCharge[]") String[] extraPersonDefualtCharge
			,@RequestParam(value="extraPersonConsecuriveCharge[]") String[] extraPersonConsecuriveCharge 
    	){

    	ModelAndView mav = new ModelAndView();

    	HashMap<String, Object> params = new HashMap<>();

        try{
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			if(_user instanceof LoginUser user) {
				HashMap<String, Object> paramMap = new HashMap<>();
				paramMap.put("islandLifeId", userIslandLifeId);
				paramMap.put("userEmail", user.getUserEmail());
				userService.updateUserIslandLife(paramMap);
			}


        	java.text.SimpleDateFormat formatter = new java.text.SimpleDateFormat("yyyyMMddHHmmssSSS", java.util.Locale.KOREA);
        	String orderId = "A00"+formatter.format(new java.util.Date()); 			// 상품주문번호

        	params.put( "productSerial", productSerial );
        	params.put( "productTourId", productTourId );

        	params.put("productUseYn", "Y");
        	params.put("regcyYn", "N");
        	params.put("deleteYn", "N");

        	ProductInfo data = productService.selectOneProduct( params );
        	ArrayList<ProductTourImages> imageList = productService.selectListProductImages(params);

        	/*사용자포인트정보*/
        	HashMap<String, Object> userParam = new HashMap<>();
        	userParam.put("userEmail", getBaseUserEmail());
			userParam.put("excludeExpired", "true");
        	mav.addObject("userPoint", userPointService.getUserPointRecordSummary(userParam));
        	/*상점키정보*/
        	mav.addObject("pgTosspayCKey", pgTosspayClientKey);
        	mav.addObject("pgTosspaySKey", pgTosspayServerKey);

        	/*상품정보*/
        	mav.addObject("imageList", imageList);
        	mav.addObject("product", data);
			mav.addObject("productType", productType);
        	/*사용자 선택정보*/
			totalPrice = (long) 0;

			/*
			if( pickDate.length >= 2 ) {
				totalPrice *= (pickDate.length -1);
			}
			*/


			if( data.getProductStayType() != null &&
				( data.getProductStayType().equals("room") || data.getProductStayType().equals("bed") ) ) {


				totalPrice = (long) (originPrice + chargePeopleAmount - discountAmount);
			} else {
				for( Integer price : productPrice ) {
					totalPrice +=  price * pickPeople;
				}
			}
			mav.addObject("defualtAmount",  originPrice);
			mav.addObject("discountAmount", discountAmount);
			mav.addObject("chargeAmount",   chargePeopleAmount);

			mav.addObject("pickDays",       pickDays);
			mav.addObject("productStayType", data.getProductStayType());
        	mav.addObject("orderId", orderId);
        	mav.addObject("pickPeople", pickPeople);
        	mav.addObject("pickDate", pickDate);
        	mav.addObject("productPrice", productPrice);
        	mav.addObject("totalPrice", totalPrice);
        	mav.addObject("formattedDate", formattedDate);
        	mav.addObject("optionId", optionId);
        	mav.addObject("optionName", optionName);
			mav.addObject("optionOneCode", optionOneCode);
			mav.addObject("userIslandLifeId", userIslandLifeId);
			mav.addObject("pickCapacity", pickCapacity);
			mav.addObject("consecuriveDiscountAmount", consecuriveDiscountAmount);
			mav.addObject("extraPersonDefualtCharge", extraPersonDefualtCharge);
			mav.addObject("extraPersonConsecuriveCharge", extraPersonConsecuriveCharge);


        	/*결제전 약관 리스트*/
        	mav.addObject("policyPrivacy", pageService.selectOnePolicyByPolicyType( "4" ));
			mav.addObject("policyRegulation", pageService.selectOnePolicyByPolicyType( "9" ));
        	mav.addObject("policyStipulation", pageService.selectOnePolicyByPolicyType( "10" ));
			mav.addObject("policyUniverseTerms", pageService.selectOnePolicyByPolicyType( "13" ));
			if ( data.getMenuSubType().equals("package") ) {
				mav.addObject("policyNotice", pageService.selectOnePolicyByPolicyType( "11"));
			} else if ( data.getMenuSubType().equals("stay") ) {
				mav.addObject("policyNotice", pageService.selectOnePolicyByPolicyType( "12"));

			}

            mav.setViewName("/front/order/beforeSheet");
        }catch(Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
        }
        return mav;
    }


    @RequestMapping("/process")
	public ModelAndView payResult(
			HttpServletRequest request
			, HttpSession session
			, @RequestParam(value="productSerial") String productSerial
    		, @RequestParam(value="productTourId") Integer productTourId
    		, @RequestParam(value="productType", defaultValue = "product") String productType
    		, @RequestParam(value="pickPeople") Integer pickPeople
    		, @RequestParam(value="pickDate") String[] pickDate
    		, @RequestParam(value="productPrice") Integer[] productPrice
    		, @RequestParam(value="optionId") Integer[] optionId
    		, @RequestParam(value="optionName") String[] optionName
			, @RequestParam(value="optionOneCode") String[] optionOneCode
			, @RequestParam(value="userIslandLifeId", defaultValue = "0", required = false) Integer userIslandLifeId
    		, @RequestParam(value="paymentType") String paymentType
    		, @RequestParam(value="orderId") String orderId
    		, @RequestParam(value="paymentKey") String paymentKey
    		, @RequestParam(value="amount") Long amount
			, @RequestParam(value="consecuriveDiscountAmount") Integer[] consecuriveDiscountAmount
    		, @RequestParam(value="extraPersonDefualtCharge") String[] extraPersonDefualtCharge
			, @RequestParam(value="extraPersonConsecuriveCharge") String[] extraPersonConsecuriveCharge 
    		, @RequestParam(value="defualtAmount", defaultValue = "0") Long defualtAmount
    		, @RequestParam(value="discountAmount", defaultValue = "0") Long discountAmount
    		, @RequestParam(value="chargePeopleAmount", defaultValue = "0") Long chargePeopleAmount
    		, @RequestParam(value="pickCapacity", defaultValue = "0") Long pickCapacity
    		
    		, @RequestParam(value="txTid", defaultValue = "") String txTid
    		, @RequestParam(value="status", defaultValue = "") String status
    		, @RequestParam(value="method", defaultValue = "") String method
    		, @RequestParam(value="tossPaymentJson", defaultValue = "") String tossPaymentJson	//토스페이결제내역(json)

    		, @Param(value="usePoint") Long usePoint
    		, @Param(value="pickDays") Long pickDays
		) {
		ModelAndView mav = new ModelAndView();
		/*
		http://localhost:8081/order/process
		?productSerial=20241031-69&productTourId=176
		&pickDate=2024-11-14,2024-11-15,2024-11-16
		&productPrice=58000,90000
		&pickPeople=1
		&optionId=366,365
		&paymentType=NORMAL
		&orderId=A0020241108125233105
		&paymentKey=tgen_20241108125242DBMv5
		&amount=148000
		*/

//		if(platform == "P")
//			if(session.getAttribute("hash") == null) {
//				mav.setView(new RedirectView("/order/expired"));
//				return mav;
//			}

		mav.addObject( "productSerial", productSerial );
		mav.addObject( "productTourId", productTourId );
		mav.addObject( "productType", productType );
		mav.addObject( "pickPeople", pickPeople );
		mav.addObject( "pickDate", pickDate );
		mav.addObject( "productPrice", productPrice );
		mav.addObject( "optionName", optionName );
		mav.addObject( "optionId", optionId );
		mav.addObject( "optionOneCode", optionOneCode );
		mav.addObject( "pickCapacity", pickCapacity );
		mav.addObject( "paymentType", paymentType );
		mav.addObject( "userIslandLifeId", userIslandLifeId );
		mav.addObject( "usePoint", usePoint );

		mav.addObject("defualtAmount",  defualtAmount);
		mav.addObject("discountAmount", discountAmount);
		mav.addObject("chargeAmount",   chargePeopleAmount);

		mav.addObject("orderId", orderId);
    	mav.addObject("pickPeople", pickPeople);
    	mav.addObject("amount", amount);
		mav.addObject("pickDays", pickDays);
    	
    	mav.addObject("txTid", txTid);
    	mav.addObject("status", status);
    	mav.addObject("method", method);
    	mav.addObject("tossPaymentJson", tossPaymentJson);
		
		mav.addObject("consecuriveDiscountAmount", consecuriveDiscountAmount);
		mav.addObject("extraPersonDefualtCharge", extraPersonDefualtCharge);
		mav.addObject("extraPersonConsecuriveCharge", extraPersonConsecuriveCharge);

		try {
			mav.setViewName("/front/order/process");
		} catch (Exception e) {
			mav.setViewName("/front/order/error");
		}

		return mav;
	}

    @PostMapping("/success")
    public ModelAndView order_success_sheet(
    		HttpServletRequest request
    		,HttpSession session
    		, @RequestParam(value="productSerial") String productSerial
    		, @RequestParam(value="productTourId") Long productTourId
    		, @RequestParam(value="productType", defaultValue = "product") String productType
    		, @RequestParam(value="pickPeople") Integer pickPeople
    		, @RequestParam(value="pickDate") String[] pickDate
    		, @RequestParam(value="productPrice") Integer[] productPrice
    		, @RequestParam(value="optionId") Integer[] optionId
    		, @RequestParam(value="optionName") String[] optionName
			, @RequestParam(value="optionOneCode") String[] optionOneCode
			, @RequestParam(value="pickCapacity") Integer pickCapacity
			, @RequestParam(value="userIslandLifeId", defaultValue = "0", required = false) Integer userIslandLifeId
    		, @RequestParam(value="paymentType") String paymentType
    		, @RequestParam(value="orderId") String orderId
    		, @RequestParam(value="paymentKey") String paymentKey
    		, @Param(value="usePoint") Long usePoint
    		, @RequestParam(value="amount") Long amount
    		, @RequestParam(value="txTid", defaultValue = "") String txTid
    		, @RequestParam(value="status", defaultValue = "") String status
    		, @RequestParam(value="method", defaultValue = "") String method
    		, @RequestParam(value="tossPaymentJson", defaultValue = "") String tossPaymentJson	//토스페이결제내역(json)
    		, Reservation resev
    		){
    	//paymentType=NORMAL&orderId=8GdYg1HXQ41nEUT1S1kwk&paymentKey=tgen_20241031111348Br4m8&amount=256000

    	ModelAndView mav = new ModelAndView();

    	HashMap<String, Object> params = new HashMap<>();

    	TransactionStatus txStatus =  transactionManager.getTransaction(new DefaultTransactionDefinition());

        try{
        	params.put( "productSerial", productSerial );
        	params.put( "productTourId", productTourId );

        	params.put("productUseYn", "Y");
        	params.put("regcyYn", "N");
        	params.put("deleteYn", "N");

        	ProductInfo product = productService.selectOneProduct( params );
        	ArrayList<ProductTourImages> imageList = productService.selectListProductImages(params);
        	/*상품정보*/
        	mav.addObject("imageList", imageList);
        	mav.addObject("product", product);

        	params.clear();
        	params.put("payMoid", orderId );

        	int cnt = pageService.selectCountReservation( params );
        	Integer userPointUsedLogId = null;

        	if( cnt == 0 ) {
	        	resev
	        		.addUserEmail(getBaseUserEmail())
					.addCreateId(getBaseUserEmail())
					.addOrderType( paymentType )
					.addReservationCode ( "3" ) //결제완료로..
					.addProductSerial( productSerial )
					.addProductTourId( productTourId )
					//.addOrderDate( uco.getOrderDate() )
					//.addOptionTime( uco.getOptionTime() )
					//.addProductCount( productCount )
					.addTotalAmount(amount )
					.addPayMoid(orderId)
					.addPickCapacity( pickCapacity )
				;

	        	pageService.insertReservation( resev );

	    		UserCustomerOrder uco = new UserCustomerOrder()
	    			.addPayMoid( orderId )
	    			.addProductCount( pickPeople )
	    			.addUserEmail( getBaseUserEmail() )
	    			.addCreateId( getBaseUserEmail() )
	    			.addTid( paymentKey )
	    			//.addOrderDate( pickDate )
	    			.addPayType( paymentType )
	    			.addPayMethod( payMethod )
	    			.addProductAmt( amount  )
	    			.addReservationId( resev.getId() )
	    			.addTossPaymentJson( tossPaymentJson )
    			;

	    		ArrayList<UserCustomerOrderList> listUcol = new ArrayList<UserCustomerOrderList>();
	    		for (int i=0;i<pickDate.length;i++) {

	    			Integer _opid = null;
	    			Integer _price = null;

	    			if( i < optionId.length  ) {
	    				_opid = optionId[i];
	    				_price = productPrice[i];
	    			} else {
	    				_opid = 0;
	    				_price = 0;
	    			}

	    			UserCustomerOrderList ucol = new UserCustomerOrderList()
	    					.addUserEmail( getBaseUserEmail() )
	    					.addCreateId( getBaseUserEmail() )
	    					.addReservationId( resev.getId() )
	    					.addProductSerial( productSerial )
	    					.addProductTourId( productTourId.intValue() )
	    					.addPriceOptionId( _opid )
	    					.addProductPrice( _price )
	    					.addOrderDate( pickDate[i] )
	    					.addProductCount( pickPeople )
	    					.prdProductAmt()
	    					.addReservationId( resev.getId() )
	    					.addPayMoid( orderId )
	    				;
					listUcol.add( ucol );
				}

				UserCustomerPayment payDataG = new UserCustomerPayment()
					.addUserEmail(getBaseUserEmail())
					.addReservationId( resev.getId() )
					.addPayMoid( orderId )
					.addPayAmount( amount + usePoint )
					.addPayDivision("G")
					.addPayComment("최초 결제금액 등록")
					.addCreateId( getBaseUserEmail() )
				;

				UserCustomerPayment payDataD = new UserCustomerPayment()
					.addUserEmail(getBaseUserEmail())
					.addReservationId( resev.getId() )
					.addPayMoid( orderId )
					.addPayAmount( amount )
					.addPayDivision("D")
					.addPayComment("결제금액")
					.addCreateId( getBaseUserEmail() )
				;

				UserCustomerOrderHistory _ucoh = new UserCustomerOrderHistory()
					.addUserEmail(getBaseUserEmail())
					//.addCreateId( getBaseUserEmail() )
					.addPayMoid(orderId)
					.addPayStatusType( "PAY" )
					.addAmt( amount )
					.addPayTid(txTid)
					.addStatusResultCode(status)
					.addStatusResultMsg(method)
				;


				System.out.println( uco );
				pageService.insertUserCustomerOrder(uco);
				for( UserCustomerOrderList ucol : listUcol ) {
					System.out.println( ucol );
					pageService.insertUserCustomerOrderList(ucol);
				}

				pageService.insertUserCustomerOrderHistory(_ucoh);
				pageService.insertUserCustomerPayment(payDataG); //받을금액
				pageService.insertUserCustomerPayment(payDataD); //토스페이 결제금액.
				if( usePoint > 0 ) {
					UserCustomerPayment payDataDbyUsePoint = new UserCustomerPayment()
						.addUserEmail(getBaseUserEmail())
						.addReservationId( resev.getId() )
						.addPayMoid( orderId )
						.addPayAmount( usePoint )
						.addPayDivision("D")
						.addPayComment("포인트 사용금액")
						.addCreateId( getBaseUserEmail() )
					;
					pageService.insertUserCustomerPayment(payDataDbyUsePoint);//포인트 사용금액.
					userPointUsedLogId = userPointService.useUserPoint(getBaseUserEmail(), "포인트결제", Long.valueOf(usePoint).intValue(), getBaseUserEmail());

					resev.setUserPointUsedLogId(userPointUsedLogId);

					pageService.updateReservation( resev );
				}


				//구매 포인트 적용 확인
				HashMap<String, Object> param = new HashMap<>();
				param.put("accruedCode", "payment");
				ArrayList<UserPointSet> pointSetList = userPointService.getUserPointSettingList(param);
				if(pointSetList != null){
					for (UserPointSet pointSet : pointSetList) {
						UserPointAccrued pointAccrued = new UserPointAccrued();
						pointAccrued.setUserEmail(getBaseUserEmail());
						pointAccrued.setAccruedType(pointSet.getAccruedType());
						pointAccrued.setAccruedCode(pointSet.getAccruedCode());
						pointAccrued.setAccruedReason(pointSet.getAccruedReason() + " (주문번호:" + uco.getPayMoid() + ")");
						if(pointSet.getAccruedType().equals("percent")){
							pointAccrued.setPointAccrued((int) (amount*pointSet.getAccruedPoint()/100));
						}else{
							pointAccrued.setPointAccrued(pointSet.getAccruedPoint());
						}
						if(pointSet.getExpiredDay() != null) {
							pointAccrued.setExpireDate(userPointService.convertExpireDayToDate(pointSet.getExpiredDay(), "add"));
						}
						pointAccrued.setPaymentMoid(orderId);
						pointAccrued.setCreateId(getBaseUserEmail());
						userPointService.createUserPoint(pointAccrued);
					}
				}else{
					logger.debug("Login Point Disabled");
				}

				//구매 뱃지 적용 확인
				if(product.getMenuSubType().equals("stay")){
					param.put("automationType", "jamsisum");
				}else{
					param.put("automationType", "program");
				}
				param.put("userEmail", getBaseUserEmail());
				ArrayList<BadgeContents> badgeList = badgeManageService.selectBadgeListByAutomationType(param);
				if(badgeList != null){
					int paymentCount = productService.getProductPaymentTypeCount(param);
					for (BadgeContents badge : badgeList){
						if(paymentCount >= badge.getBadgeAutomationCount()){
							badgeManageService.badgeAcquire(getBaseUserEmail(),getBaseUserEmail(), badge);
						}
					}
				}
        	}

        	resev = pageService.selectOneReservation( params );
        	mav.addObject("resev", resev);

			//예약확정 문자보냄.
			HashMap<String, Object> smsPolicyParams = new HashMap<>();
			smsPolicyParams.put("policySendScheduleType", "completeRsv");
			smsPolicyParams.put("useYn", "Y");
			smsPolicyParams.put("deleteYn", "N");

			String stayMessage = "";
			if ( product.getMenuSubType().equals("stay")) {
				String categoryCommonTitle = product.getCategoryCommonTitle().trim();
				if( categoryCommonTitle.contains("순무민박")) {
					stayMessage = "[잠시섬 안내 문자-아삭아삭순무민박] \n" + //
									"\n" + //
									"어서오시겨! 강화유니버스입니다. \n" + //
									"잠시섬에서 함께 시간을 보낼 수 있어 무척 감사하고 반갑습니다:)\n" + //
									"\n" + //
									"★잠시섬 사전 튜토리얼★ \n" + //
									"\n" + //
									"① 웰컴퀴즈 / 여행자보험 가입 \n" + //
									"- https://www.guniverse.net/guidebook?id=55 \n" + //
									"\n" + //
									"② 잠시섬 참가자 가이드북 확인 \n" + //
									"- https://www.guniverse.net/guidebook \n" + //
									"- 숙박, 프로그램, 여행 정보가 담긴 중요한 잠시섬 참여자 가이드북입니다. 꼭 읽어주세요! \n" + //
									"\n" + //
									"③ 참여 가능한 프로그램 일정 / 예약 필수! \n" + //
									"- https://www.guniverse.net/program/all \n" + //
									"\n" + //
									"④ 나만의 영감모임을 열고 싶나요? \n" + //
									"- https://docs.google.com/document/d/1Bg_cMcfgPFzZnq8STjFO8NAKdDHuTy6NTdOtERu75Pg/edit?tab=t.0#heading=h.vmz3gqi00rzs \n" + //
									"- 해당 가이드를 살펴본 후, 등록 부탁드립니다! \n" + //
									"\n" + //
									"⑤ 강화유니버스 약속문 확인 \n" + //
									"- https://www.guniverse.net/guidebook?id=60 \n" + //
									"- 약속문을 함께해주지 않을 경우, 퇴실 조치될 수 있습니다. \n" + //
									"\n" + //
									"______\n" + //
									"\n" + //
									"★잠시섬 숙소 안내★\n" + //
									"\n" + //
									"- 숙소명 : 아삭아삭순무민박 \n" + //
									"- 숙소 정보 : https://www.guniverse.net/guidebook?id=18 \n" + //
									"- 길찾기 : https://naver.me/FXZ1FMfl \n" + //
									"\n" + //
									"* 체크인 당일에 체크인 관련 문자가 나갑니다.*";
				} else if (categoryCommonTitle.contains("스테이아삭")) {
					stayMessage = "[잠시섬 안내 문자-스테이아삭] \n" + //
									"\n" + //
									"어서오시겨! 강화유니버스입니다. \n" + //
									"잠시섬에서 함께 시간을 보낼 수 있어 무척 감사하고 반갑습니다:)\n" + //
									"\n" + //
									"★잠시섬 사전 튜토리얼★ \n" + //
									"\n" + //
									"① 웰컴퀴즈 / 여행자보험 가입 \n" + //
									"- https://www.guniverse.net/guidebook?id=55 \n" + //
									"\n" + //
									"② 잠시섬 참가자 가이드북 확인 \n" + //
									"- https://www.guniverse.net/guidebook \n" + //
									"- 숙박, 프로그램, 여행 정보가 담긴 중요한 잠시섬 참여자 가이드북입니다. 꼭 읽어주세요!\n" + //
									"\n" + //
									"③ 참여 가능한 프로그램 일정 / 예약 필수! \n" + //
									"- https://www.guniverse.net/program/all \n" + //
									"\n" + //
									"④ 나만의 영감모임을 열고 싶나요? \n" + //
									"- https://docs.google.com/document/d/1Bg_cMcfgPFzZnq8STjFO8NAKdDHuTy6NTdOtERu75Pg/edit?tab=t.0#heading=h.vmz3gqi00rzs \n" + //
									"- 해당 가이드를 살펴본 후, 등록 부탁드립니다! \n" + //
									"\n" + //
									"⑤ 강화유니버스 약속문 확인 \n" + //
									"- https://www.guniverse.net/guidebook?id=60 \n" + //
									"- 약속문을 함께해주지 않을 경우, 퇴실 조치될 수 있습니다. \n" + //
									"______\n" + //
									"\n" + //
									"★잠시섬 숙소 안내★\n" + //
									"\n" + //
									"- 숙소명 : 스테이아삭\n" + //
									"- 숙소 정보 : https://www.guniverse.net/guidebook?id=19 \n" + //
									"- 길찾기 :  https://naver.me/xrcsJPN1   \n" + //
									"\n" + //
									"* 체크인 당일에 체크인 관련 문자가 나갑니다.*";
				} else if (categoryCommonTitle.contains("잠시섬빌리지")) {
					stayMessage = "[잠시섬 안내 문자-잠시섬빌리지 ] \n" + //
									"\n" + //
									"어서오시겨! 강화유니버스입니다. \n" + //
									"잠시섬에서 함께 시간을 보낼 수 있어 무척 감사하고 반갑습니다:)\n" + //
									"\n" + //
									"★잠시섬 사전 튜토리얼★ \n" + //
									"\n" + //
									"① 웰컴퀴즈 / 여행자보험 가입 \n" + //
									"- https://www.guniverse.net/guidebook?id=55 \n" + //
									"\n" + //
									"② 잠시섬 참가자 가이드북 확인 \n" + //
									"- https://www.guniverse.net/guidebook \n" + //
									"- 숙박, 프로그램, 여행 정보가 담긴 중요한 잠시섬 참여자 가이드북입니다. 꼭 읽어주세요!\n" + //
									"\n" + //
									"③ 참여 가능한 프로그램 일정 / 예약 필수! \n" + //
									"- https://www.guniverse.net/program/all \n" + //
									"\n" + //
									"④ 나만의 영감모임을 열고 싶나요? \n" + //
									"- https://docs.google.com/document/d/1Bg_cMcfgPFzZnq8STjFO8NAKdDHuTy6NTdOtERu75Pg/edit?tab=t.0#heading=h.vmz3gqi00rzs \n" + //
									"- 해당 가이드를 살펴본 후, 등록 부탁드립니다! \n" + //
									"\n" + //
									"⑤ 강화유니버스 약속문 확인 \n" + //
									"- https://www.guniverse.net/guidebook?id=60 \n" + //
									"- 약속문을 함께해주지 않을 경우, 퇴실 조치될 수 있습니다. \n" + //
									"\n" + //
									"______\n" + //
									"\n" + //
									"★잠시섬 숙소 안내★\n" + //
									"\n" + //
									"- 숙소명 : 잠시섬빌리지\n" + //
									"- 숙소 정보 : https://www.guniverse.net/guidebook?id=20\n" + //
									"- 길찾기 :  https://naver.me/GOzRvu1H \n" + //
									"\n" + //
									"* 체크인 당일에 체크인 관련 문자가 나갑니다:)*";
				}
			}

			
			String programMessage = "안녕하세요. 강화유니버스입니다.\n" + //
								"프로그램을 신청해주셔서 감사합니다. \n" + //
								"\n" + //
								"▶프로그램명 : " + product.getProductTitle() + "\n" + //
								"▶시간 : " + product.getProductRunStartTime() +"~"+ product.getProductRunEndTime() + "\n" +
								"\n" + //
								"프로그램 노쇼 시에는, 그 어떤 프로그램도 참여가 어려우니 꼭 신중하게 선택해주시고, 참여가 어려우면 꼭 취소 부탁드립니다! \n" + //
								"\n" + //
								"프로그램 하루 전에, 관련해서 안내 문자를 드릴 예정입니다:)\n" + //
								"그럼, 곧 만나요! 김시합니다.";
			String sendMessage = product.getMenuSubType().equals("stay") ? stayMessage : programMessage	;				

			SmsPolicy smsPolicy = alertManageMapper.selectOneSmsPolicy(smsPolicyParams);
			if ( smsPolicy != null ) {
				ArrayList<NCloudSmsMessage> messages = new ArrayList<>();
                if ( "Y".equals(smsPolicy.getUseYn())
                    && "Y".equals(smsPolicy.getUserSendYn()) ) {
                        NCloudSmsMessage msg1 = new NCloudSmsMessage();
                        msg1.setTo(resev.getUserMobile().replaceAll("-", ""));
                        msg1.setContent(String.format(
										sendMessage
                                    ));
                        msg1.setMessageType("LMS");
                        msg1.setSendReservationId(resev.getId());
                        msg1.setSendMessageType("completeRsv");
                        messages.add(msg1);
                }
                if ( "Y".equals(smsPolicy.getUseYn())
                    && "Y".equals(smsPolicy.getAdminSendYn()) ) {
                      

                        HashMap<String, Object> recivedParam = new HashMap<>();
                        recivedParam.put("useYn", "Y");
                        ArrayList<SmsRecivedUser> recivedUsers = adminMapper.selectListSmsRecivedUser(recivedParam);
						

                        for (SmsRecivedUser recivedUser : recivedUsers) {
							NCloudSmsMessage msg2 = new NCloudSmsMessage();
                            HashMap<String, Object> userParam = new HashMap<>();
                            userParam.put("role", "ADMIN");
                            userParam.put("userEmail", recivedUser.getUserEmail());

                            msg2.setTo(userService.selectUserListByRole(userParam).get(0).getUserMobile().replaceAll("-", ""));
                            msg2.setContent("자동 문자발송 서비스입니다.\n[" + resev.getProductTitle() + "]를 예약한 고객님에게 안내 문자를 전송하였습니다.");
                            msg2.setMessageType("LMS");
                            msg2.setSendReservationId(resev.getId());
                            msg2.setSendMessageType("completeRsv");
                            messages.add(msg2);
                        }
                }

                nCloudApiService.sendMultipleSmsMessage(messages, "admin");
			}

        	transactionManager.commit(txStatus);

            mav.setViewName("/front/order/success");
        }catch(Exception e){
        	transactionManager.rollback(txStatus);
            logger.error(e.getMessage());
            e.printStackTrace();
        }
        return mav;
    }

    @GetMapping("/success")
    public ModelAndView order_success_get_mav(
    		HttpServletRequest request
    		,HttpSession session
    		, @RequestParam(value="id") String id
		){
    	//paymentType=NORMAL&orderId=8GdYg1HXQ41nEUT1S1kwk&paymentKey=tgen_20241031111348Br4m8&amount=256000

    	ModelAndView mav = new ModelAndView();

    	HashMap<String, Object> params = new HashMap<>();

        try{
        	params.put("id", id);
        	Reservation resev = pageService.selectOneReservation( params );
        	mav.addObject("resev", resev);

        	params.put( "productSerial", resev.getProductSerial() );
        	params.put( "productTourId", resev.getProductTourId() );

        	params.put("productUseYn", "Y");
        	params.put("regcyYn", "N");
        	params.put("deleteYn", "N");

        	ProductInfo product = productService.selectOneProduct( params );
        	ArrayList<ProductTourImages> imageList = productService.selectListProductImages(params);
        	/*상품정보*/
        	mav.addObject("imageList", imageList);
        	mav.addObject("product", product);

            mav.setViewName("/front/order/success");
        }catch(Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
        }
        return mav;
    }

    @RequestMapping("/fail")
    public ModelAndView order_fail_sheet(
    		HttpServletRequest request
    		,HttpSession session
    		,@RequestParam(value="requestData") String requestData
    		,@RequestParam(value="responseData") String responseData
    		){
    	//paymentType=NORMAL&orderId=8GdYg1HXQ41nEUT1S1kwk&paymentKey=tgen_20241031111348Br4m8&amount=256000

    	ModelAndView mav = new ModelAndView();

    	HashMap<String, Object> params = new HashMap<>();

        try{
        	mav.addObject("requestData", requestData);
        	mav.addObject("responseData", responseData);

            mav.setViewName("/front/order/fail");
        }catch(Exception e){
            logger.error(e.getMessage());
            e.printStackTrace();
        }
        return mav;
    }

	/*
	 * //Todo 예약 취소 구현
	 * 구현 시, 뱃지 및 포인트 취소 같이 구현 필요
	 *
	 *
	 *
	 *
			//포인트 적용 취소
			HashMap<String, Object> param = new HashMap<>();
			param.put("accruedCode", "payment");
			ArrayList<UserPointSet> pointSetList = userPointService.getUserPointSettingList(param);
			if(pointSetList != null){
				param.put("paymentMoid", uco.getPayMoid());
				UserPointAccrued pointAccrued = userPointService.selectUserPointAccruedById(param);
				param.put("userEmail", getBaseUserEmail());
				param.put("accruedId", pointAccrued.getId());
				UserPointRecord pointRecord = userPointService.selectOneUserPointRecord(param);
				userPointService.cancelUserPoint(pointRecord.getId, pointAccrued);
			}

			//구매 뱃지 취소 확인
			if(product.getMenuSubType().equals("stay")){
				param.put("automationType", "jamsisum");
			}else{
				param.put("automationType", "program");
			}
			param.put("userEmail", getBaseUserEmail());
			ArrayList<BadgeContents> badgeList = badgeManageService.selectBadgeListByAutomationType(param);
			if(badgeList != null){
				int paymentCount = productService.getProductPaymentTypeCount(param);
				for (BadgeContents badge : badgeList){
					if(paymentCount == badge.getBadgeAutomationCount()-1){	//예약취소로 부여 직전과 숫자가 같아졌을 때
						badgeManageService.badgeCancel(getBaseUserEmail(), badge);
					}
				}
			}
	 */
}
