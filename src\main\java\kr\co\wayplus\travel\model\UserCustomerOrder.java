package kr.co.wayplus.travel.model;

import com.fasterxml.jackson.annotation.JsonInclude;

import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserCustomerOrder extends CommonDataSet {
    private int rownum;

    private String payMoid;	//거래 주문번호
    private String userEmail;	//사용자ID(이메일ID)
    private Long reservationId;	//판매 상품 순번
    private Long productAmt;	//상품판매금액
    private Long productDeliveryAmt;	//상품 배달비 금액
    private int productCount;	//상품건수
    private String orderStatus;	//주문상태
    private String orderStatusName;
    private String orderDate;	//주문일자
    private String optionTime;	//상품판매금액
    private Long cancelAmt;  //취소금액
    private Integer cancelProductCount;	//취소 상품 개수

    private String statusResultCode;	//결제 상태 코드: 거래완료시
	private String statusResultMsg;	//결제 상태 결과 메세지: 거래완료시
	private String mid;	//상점ID(나이스의 상점 ID정보)
	private String tid;	//거래ID(나이스): 거래완료시
	private String method;	//결제방법
	private String macketId;	//결재 상품ID(나이스 제공)

	private String vbankCode; //가상계좌 은행코드
	private String vbankName; //가상계좌 은행명
	private String vbankNum; //가상계좌 번호
	private String vbankExpDate; //가상계좌 만료일
	private String vbankExpTime; //가상계좌 만료시

	private String vbankCancelBankCode; //가상계좌 은행코드
	private String vbankCancelBankNum; //가상계좌 번호
	private String vbankCancelBankName; //가상계좌 은행이름

	private String payType;
	private String payMethod;
	private String tossPaymentJson;
	private String tossPaymentCancelJson;

    public UserCustomerOrder addUserEmail(String userEmail) {
    	this.userEmail = userEmail;
    	return this;
    }
    public UserCustomerOrder addReservationId(Long id) {
    	this.reservationId = id;
		return this;
	}
    public UserCustomerOrder addPayMoid(String payMoid) {
    	this.payMoid = payMoid;
    	return this;
    }
    public UserCustomerOrder addOrderStatus(String orderStatus) {
    	this.orderStatus = orderStatus;
    	return this;
    }
    public UserCustomerOrder addOrderStatusName(String resultMsg) {
		this.orderStatusName = resultMsg;
		return this;
	}
    public UserCustomerOrder addOrderDate(String orderDate) {
    	this.orderDate = orderDate;
    	return this;
    }
    public UserCustomerOrder addOptionTime(String optionTime) {
    	this.optionTime = optionTime;
    	return this;
    }
    public UserCustomerOrder addProductAmt(Long totalAmt) {
    	this.productAmt = totalAmt;
    	return this;
    }
    public UserCustomerOrder addProductDeliveryAmt(Long productDeliveryAmt) {
    	this.productDeliveryAmt = productDeliveryAmt;
    	return this;
    }
    public UserCustomerOrder addProductCount(int productCount) {
    	this.productCount = productCount;
		return this;
	}
    public UserCustomerOrder addCreateId(String createId) {
    	setCreateId(createId);
    	return this;
    }
    public UserCustomerOrder addLastUpdateID(String userEmail) {
    	setLastUpdateId(userEmail);
    	return this;
    }
    public UserCustomerOrder addCancelAmt(long CancelAmt) {
    	this.cancelAmt = CancelAmt;
    	return this;
	}
    public UserCustomerOrder addStatusResultCode(String statusResultCode) {
		this.statusResultCode = statusResultCode;
		return this;
	}
	public UserCustomerOrder addStatusResultMsg(String statusResultMsg) {
		this.statusResultMsg = statusResultMsg;
		return this;
	}
	public UserCustomerOrder addMid(String mid) {
		this.mid = mid;
		return this;
	}
	public UserCustomerOrder addTid(String tid) {
		this.tid = tid;
		return this;
	}
    public UserCustomerOrder addMethod(String method) {
		this.method = method;
		return this;
	}
	public UserCustomerOrder addMacketId(String macketId) {
		this.macketId = macketId;
		return this;
	}
	public UserCustomerOrder addPayType(String payType) {
		this.payType = payType;
		return this;
	}
	public UserCustomerOrder addPayMethod(String payMethod) {
		this.payMethod = payMethod;
		return this;
	}

	public UserCustomerOrder addVbankInfo(String code,String name, String num,String date,String time) {
		this.vbankCode = code;
		this.vbankName = name;
		this.vbankNum = num;
		this.vbankExpDate = date;
		this.vbankExpTime = time;
		return this;
	}

	public UserCustomerOrder addVbankCancelInfo(String code,String num) {
		this.vbankCancelBankCode = code;
		this.vbankCancelBankNum = num;
		return this;
	}
	public UserCustomerOrder addTossPaymentJson(String tossPaymentJson) {
		this.tossPaymentJson = tossPaymentJson;
		return this;
	}
}
