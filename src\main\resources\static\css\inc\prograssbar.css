.progressbar{
 	display: none;
 	justify-content: center;
 	align-items: center;
 	min-height: 100vh;
 	pointer-events: none;
}

div.data_processing{position: absolute;top: 40vh;left: 50%;height: 128px !important;width: 128px !important;margin-left: -100px;margin-top: -26px;text-align: center;padding: 0 !important;z-index: 1000;
    display: flex;justify-content: center;align-content: center;align-items: center;border-radius: 64px;background: #ffffffdd;

}
/*div.data_processing span{position: absolute;top: 45%;left: 30%;}*/
div.data_processing:before{position: absolute;content: '';background: url('/images/progress/loading.gif');width: 128px;height: 128px;}
div.dataTables_processing{
	display: none;
	content: '';
/* 	background: url('/images/progress/loading.gif') no-repeat 35px 0px; */
/* 	height: 128px; */
/* 	line-height: 94px; */
/* 	background-color: #fff; */
}
div.data_exceldown{
   position: absolute;
   top: 40vh;
   left: 50%;
   width: 150px !important;
   margin-left: -75px;
   margin-top: -30px;
   text-align: center;
   padding: 0 !important;
   z-index: 1000;
}
div.data_exceldown:before{
   content: '';
   background: url('/images/progress/loading.gif');
   width: 128px;
   height: 128px;
   margin: 11px;
}

div.data_exceldown .context_wrap{
   position: absolute;
   width: 100%;
   height: 100%;
   letter-spacing: -2px;
}
div.data_exceldown .context_wrap span{
	position: absolute;
   width: 100%;
   text-align: center;
   left: 0px;
}

div.data_exceldown .context_wrap .fileName{
   top: 35%;
/*    word-break: keep-all; */
   white-space: nowrap;
}
div.data_exceldown .context_wrap .prograssTitle{
   top: 52%;
}
