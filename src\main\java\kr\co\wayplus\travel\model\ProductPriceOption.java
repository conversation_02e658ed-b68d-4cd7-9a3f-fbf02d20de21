package kr.co.wayplus.travel.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import kr.co.wayplus.travel.base.model.CommonDataSet;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@ToString
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductPriceOption extends CommonDataSet {
	private int  productTourId
				, priceId
				, priceOptionId
				, optionSettingId
				, copiedPriceOptionId
				, optionOrder
				, productPrice
				, policyInventory
				, priceSale
				, priceNormal;
	private String startDate
				, endDate
				, productSerial
				, priceSetType
				, priceSetDate
				, aloneYn
				, priceSequence
				, optionSequence
				, optionType
				, optionGroupName
				, optionName
				, optionDesc
				, maxCapacity
				, maxQuantity
				, optionGroupCode
				, useYn
				, addBasicOptionId
				, gubn
				, earlyBirdPrice
				, earlyBirdPriceName
				, productCode
				, regacyYn
				, optionOneCode
				, travelDate;
	private Long consecuriveDiscountAmount //연박할인 금액/비율
				, extraPersonDefualtCharge //연박할인 금액/비율
				, extraPersonConsecuriveCharge //연박할인 금액/비율;
				;
	private List<BasicPriceList> basicPriceList;
	private List<FixPriceList> fixPriceList;
	private Map<Integer, List<DayList>> dayList;
	private List<Integer> priceIdList;
	private List<Integer> deleteBasicPriceOptionList;

	//virtual
	private int minimumPrice;
	private int orderCount 	// 전체주문
				, remainCount
				, specialCapacity //특가재고
				, specialOrderCount // 특가주문
				, orderPrice
				, specialOrderPrice;
	private String rsvPossible //전체 예약가능 여부
					, specialRsvPossible //특가 예약가능 여부
					, date
					, specialOptionOneCode
					, specialPrice;

	@ToString
	@Getter
	@Setter
	public static class BasicPriceList extends CommonDataSet {
		private int priceOptionId
					, priceId
					, productTourId
					, priceSequence
					, optionSequence
					, optionSettingId
					, copiedPriceOptionId
					, policyInventory
					, priceSale
					, priceNormal;
		private String priceSetDate
					, productSerial
					, optionGroupName
					, optionName
					, optionDesc
					, maxCapacity
					, maxQuantity
					, priceSetType
					, aloneYn
					, useYn
					, optionGroupCode
					, productCode
					, regacyYn
					, optionOneCode;
	}
	@ToString
	@Getter
	@Setter
	public static class DayList extends CommonDataSet {
		private int priceOptionId
					, optionSettingId
					, priceId
					, productTourId
					, priceSequence
					, copiedPriceOptionId
					, policyInventory
					, priceSale
					, priceNormal;
		private String priceSetDate
					, productSerial
					, optionGroupName
					, optionName
					, maxCapacity
					, maxQuantity
					, priceSetType
					, aloneYn
					, optionGroupCode
					, productPrice
					, useYn
					, deleteYn
					, operationDays
					, productCode
					, regacyYn
					, optionOneCode;
	}

	@ToString
	@Getter
	@Setter
	public static class FixPriceList extends CommonDataSet {
		private int priceOptionId
				, priceId
				, productTourId
				, priceSequence
				, optionSequence
				, optionSettingId
				, copiedPriceOptionId
				, priceSale
				, priceNormal;
		private String priceSetDate
				, productSerial
				, productPrice
				, optionGroupName
				, optionName
				, optionDesc
				, maxCapacity
				, maxQuantity
				, priceSetType
				, aloneYn
				, optionGroupCode
				, useYn
				, addBasicOptionId
				, startDate
				, endDate
				, productCode
				, regacyYn
				, optionOneCode;
		private Long consecuriveDiscountAmount //연박할인 금액/비율
				, extraPersonDefualtCharge //연박할인 금액/비율
				, extraPersonConsecuriveCharge //연박할인 금액/비율;
				;
	}
}