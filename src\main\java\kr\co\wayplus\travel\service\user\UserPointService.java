package kr.co.wayplus.travel.service.user;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import kr.co.wayplus.travel.mapper.user.UserPointMapper;
import kr.co.wayplus.travel.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.regex.Pattern;

@Service
public class UserPointService {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final UserPointMapper pointMapper;

    public UserPointService(UserPointMapper pointMapper) {
        this.pointMapper = pointMapper;
    }

    public int getUserPointSettingListCount(HashMap<String, Object> param) {
        return pointMapper.selectUserPointSettingListCount(param);
    }

    public ArrayList<UserPointSet> getUserPointSettingList(HashMap<String, Object> param) {
        return pointMapper.selectUserPointSettingList(param);
    }

    public UserPointSet getUserPointSettingById(String id) {
        return pointMapper.selectUserPointSettingById(id);
    }

    public void insertUserPointSetting(UserPointSet pointSet) {
        pointMapper.insertUserPointSetting(pointSet);
    }

    public void updateUserPointSetting(UserPointSet pointSet) {
        pointMapper.updateUserPointSetting(pointSet);
    }

    public int getUserPointRecordListCount(HashMap<String, Object> param) {
        return pointMapper.selectUserPointRecordListCount(param);
    }

    public ArrayList<UserPointRecord> getUserPointRecordList(HashMap<String, Object> param) {
        return pointMapper.selectUserPointRecordList(param);
    }

    public int getUserPointAccruedListCount(HashMap<String, Object> param) {
        return pointMapper.selectUserPointAccruedListCount(param);
    }

    public ArrayList<UserPointAccrued> getUserPointAccruedList(HashMap<String, Object> param) {
        return pointMapper.selectUserPointAccruedList(param);
    }

    public UserPointAccrued getUserPointAccruedById(HashMap<String, Object> param) {
        return pointMapper.selectUserPointAccruedById(param);
    }

    public int getUserPointUsedListCount(HashMap<String, Object> param) {
        return pointMapper.selectUserPointUsedListCount(param);
    }

    public ArrayList<UserPointUsed> getUserPointUsedList(HashMap<String, Object> param) {
        return pointMapper.selectUserPointUsedList(param);
    }

    public ArrayList<UserPointExchange> getUserPointExchangeList(HashMap<String, Object> param) {
        return pointMapper.selectUserPointExchangeList(param);
    }

    public int getCountUserPointExchangeList(HashMap<String, Object> param) {
        return pointMapper.selectCountUserPointExchangeList(param);
    }

    /**
     * 사용자 회원가입에 따른 포인트를 기록한다.
     * @param user
     */
    public void createJoinPoint(LoginUser user) {
        logger.debug("Join Point Create Start");
        ArrayList<UserPointSet> pointSetList = pointMapper.selectUserPointSetByTypeCode("join");
        if(pointSetList != null){
            logger.debug("Join Point Enabled");
            for (UserPointSet  pointSet : pointSetList) {
                createUserPoint(pointSet, user.getUserEmail(), "JOIN", 0);
            }
        }else{
            logger.debug("Join Point Disabled");
        }
        logger.debug("Join Point Create End");
    }

    /**
     * 사용자 로그인에 따른 포인트를 기록한다.
     * @param user
     */
    public void createLoginPoint(LoginUser user) {
        logger.debug("Login Point Create Start");
        ArrayList<UserPointSet> pointSetList = pointMapper.selectUserPointSetByTypeCode("login");
        if(pointSetList != null){
            for (UserPointSet  pointSet : pointSetList) {
                createUserPoint(pointSet, user.getUserEmail(), "LOGIN", 0);
            }
        }else{
            logger.debug("Login Point Disabled");
        }
        logger.debug("Login Point Create End");
    }

    /**
     * 사용자 포인트를 생성한다.
     * @param pointAccrued
     */
    @Transactional
    public void createUserPoint(UserPointAccrued pointAccrued) {
        pointMapper.insertPointAccruedLog(pointAccrued);
        UserPointRecord pointRecord = new UserPointRecord();
        pointRecord.setUserEmail(pointAccrued.getUserEmail());
        pointRecord.setAccruedId(pointAccrued.getId());
        pointRecord.setAccruedType(pointAccrued.getAccruedType());
        pointRecord.setAccruedReason(pointAccrued.getAccruedReason());
        pointRecord.setExpireDate(pointAccrued.getExpireDate());
        pointRecord.setPointAccrued(pointAccrued.getPointAccrued());
        pointRecord.setPointRemain(pointAccrued.getPointAccrued());
        pointRecord.setCreateId(pointAccrued.getCreateId());
        pointMapper.insertPointRecord(pointRecord);
    }

    /**
     * 설정에 따른 사용자 포인트를 생성한다.
     * @param pointSet
     */
    @Transactional
    public void createUserPoint(UserPointSet pointSet, String userEmail, String createId, Integer boardContentsId) {
        logger.debug("==================== Create User Point START ====================");
        logger.debug(String.format("Create User Point By Setting And Record. SetNo: %s, User: %s, PointCode: %s", pointSet.getId(), userEmail, pointSet.getAccruedCode()));
        UserPointAccrued pointAccrued = new UserPointAccrued();
        pointAccrued.setUserEmail(userEmail);
        pointAccrued.setAccruedCode(pointSet.getAccruedCode());
        pointAccrued.setAccruedType(pointSet.getAccruedType());
        pointAccrued.setAccruedReason(pointSet.getAccruedReason());
        if ( boardContentsId != null ) {
            pointAccrued.setBoardContentsId(boardContentsId);
        }
        if(pointSet.getExpiredDay() != null){
            pointAccrued.setExpireDate(convertExpireDayToDate(pointSet.getExpiredDay(), "add"));
        }
        pointAccrued.setPointAccrued(pointSet.getAccruedPoint());
        pointAccrued.setCreateId(createId);

        boolean duplicateCheck = true;
        int duplicateCount = -1;
        if(pointSet.getDuplicateYn().equals("Y") && pointSet.getDuplicatePeriod() != null){
            logger.debug("Point Set Duplicate Check Enabled.");
            pointAccrued.setDuplicatePeriod(convertExpireDayToDate(pointSet.getDuplicatePeriod(), "sub"));
        }

        duplicateCount = pointMapper.selectUserPointCreateLogCount(pointAccrued);
        if(pointSet.getDuplicateYn().equals("Y") && pointSet.getDuplicatePeriod() != null){
            if(duplicateCount < pointSet.getDuplicateCount()){
                duplicateCheck = false;
            }
        }else{
            if(duplicateCount == 0) duplicateCheck = false;
        }

        if(duplicateCheck){
            logger.debug(String.format("Already Create User Point. CheckDate: %s, DuplicateCheckStatus: %s, DuplicateCount: %s", pointAccrued.getDuplicatePeriod(), pointSet.getDuplicateYn(), duplicateCount));
        }else{
            logger.debug(String.format("Create Point User: %s, PointCode: %s, Point: %s, Expire: %s", pointAccrued.getUserEmail(), pointSet.getAccruedCode(), pointSet.getAccruedPoint(), pointSet.getExpiredDay()));
            pointMapper.insertPointAccruedLog(pointAccrued);
            UserPointRecord pointRecord = new UserPointRecord();
            pointRecord.setUserEmail(pointAccrued.getUserEmail());
            pointRecord.setAccruedId(pointAccrued.getId());
            pointRecord.setAccruedType(pointAccrued.getAccruedType());
            pointRecord.setAccruedReason(pointAccrued.getAccruedReason());
            pointRecord.setExpireDate(pointAccrued.getExpireDate());
            pointRecord.setPointAccrued(pointAccrued.getPointAccrued());
            pointRecord.setPointRemain(pointAccrued.getPointAccrued());
            pointRecord.setCreateId(pointAccrued.getCreateId());
            pointMapper.insertPointRecord(pointRecord);
        }
        logger.debug("==================== Create User Point END ====================");
    }


    /**
     * 사용자 포인트를 취소 처리한다.
     * @param id
     * @param pointAccrued
     */
    @Transactional
    public void cancelUserPoint(int id, UserPointAccrued pointAccrued) {
        pointMapper.updateUserPointAccruedCancel(pointAccrued);
        UserPointRecord pointRecord = new UserPointRecord();
        pointRecord.setId(id);
        pointRecord.setAccruedId(pointAccrued.getId());
        pointRecord.setDeleteId(pointAccrued.getCancelId());
        pointRecord.setDeleteCode(pointAccrued.getCancelCode());
        pointRecord.setDeleteYn("Y");
        pointMapper.updateUserPointRecordDelete(pointRecord);
    }

    /**
     * 사용자 포인트를 사용한다.
     * 선종선출(먼저 종료되는 것 우선 사용)된다.
     * @param userEmail
     * @param usedType
     * @param usedPoint
     * @param operatorId
     */
    @Transactional
    public int useUserPoint(String userEmail, String usedType, int usedPoint, String operatorId) throws RuntimeException{
        logger.debug("User Point Use Proc Start");
        HashMap<String, Object> param = new HashMap<>();
        param.put("userEmail", userEmail);
        param.put("excludeExpired", "true");
        UserPointRecord remainPoint = pointMapper.selectUserPointRecordSummary(param);
        logger.debug(String.format("User: %s Is Remain Point %s Use Request Point %s", userEmail, remainPoint.getPointRemain(), usedPoint));
        if(remainPoint.getPointRemain() < usedPoint){
            logger.debug("User Point Use Proc Error : Not Enough Point ");
            throw new RuntimeException("포인트가 충분하지 않습니다.");
        }else{
            param.put("used", "true");
            param.put("accruedType", "notGiftExchange");
            ArrayList<UserPointRecord> userPointRecords = pointMapper.selectUserPointRecordList(param);
            Gson gson = new Gson();
            int usedRemainPoint = usedPoint;
            HashMap<String, Object> detailRecord = new HashMap<>();
            ArrayList<UserPointRecord> beforeRecords = new ArrayList<>();
            ArrayList<UserPointRecord> afterRecords = new ArrayList<>();
            for (UserPointRecord pointRecord : userPointRecords){
                logger.debug(String.format("Record Point : %s , Used Remain Point : %s", pointRecord.getPointRemain(), usedRemainPoint));
                logger.debug(String.format("Current Record : %s", gson.toJson(pointRecord)));
                pointRecord.setLastUpdateId(operatorId);
                UserPointRecord beforeRecord = new UserPointRecord();
                UserPointRecord afterRecord = new UserPointRecord();
                if(pointRecord.getPointRemain() >= usedRemainPoint){
                    beforeRecord.setId(pointRecord.getId());
                    beforeRecord.setAccruedId(pointRecord.getAccruedId());
                    beforeRecord.setPointRemain(pointRecord.getPointRemain());
                    beforeRecord.setPointUsed(pointRecord.getPointUsed());
                    beforeRecord.setLastUpdateId(pointRecord.getLastUpdateId());
                    beforeRecords.add(beforeRecord);

                    pointRecord.setPointRemain(pointRecord.getPointRemain() - usedRemainPoint);
                    pointRecord.setPointUsed(pointRecord.getPointUsed() + usedRemainPoint);
                    pointMapper.updateUserPointRecordUsed(pointRecord);

                    afterRecord.setId(pointRecord.getId());
                    afterRecord.setAccruedId(pointRecord.getAccruedId());
                    afterRecord.setPointRemain(pointRecord.getPointRemain());
                    afterRecord.setPointUsed(pointRecord.getPointUsed());
                    afterRecord.setLastUpdateId(pointRecord.getLastUpdateId());
                    afterRecords.add(afterRecord);
                    usedRemainPoint = 0;
                    break;
                }else{
                    beforeRecord.setId(pointRecord.getId());
                    beforeRecord.setAccruedId(pointRecord.getAccruedId());
                    beforeRecord.setPointRemain(pointRecord.getPointRemain());
                    beforeRecord.setPointUsed(pointRecord.getPointUsed());
                    beforeRecord.setLastUpdateId(pointRecord.getLastUpdateId());
                    beforeRecords.add(beforeRecord);

                    usedRemainPoint = usedRemainPoint - pointRecord.getPointRemain();
                    pointRecord.setPointUsed(pointRecord.getPointUsed() + pointRecord.getPointRemain());
                    pointRecord.setPointRemain(0);
                    pointMapper.updateUserPointRecordUsed(pointRecord);

                    afterRecord.setId(pointRecord.getId());
                    afterRecord.setAccruedId(pointRecord.getAccruedId());
                    afterRecord.setPointRemain(pointRecord.getPointRemain());
                    afterRecord.setPointUsed(pointRecord.getPointUsed());
                    afterRecord.setLastUpdateId(pointRecord.getLastUpdateId());
                    afterRecords.add(afterRecord);
                }
            }
            detailRecord.put("before", beforeRecords);
            detailRecord.put("after", afterRecords);

            UserPointUsed pointUsed = new UserPointUsed();
            pointUsed.setUserEmail(userEmail);
            pointUsed.setUsedType(usedType);
            pointUsed.setPointUsed(usedPoint);
            pointUsed.setCreateId(operatorId);
            pointUsed.setDetailRecord(gson.toJson(detailRecord));
            pointMapper.insertPointUsedLog(pointUsed);
            logger.debug("User Point Use Proc End");

            return pointUsed.getId();
        }
    }

    public UserPointRecord getUserExpiringPointRecordSummary(HashMap<String, Object> param) {
        return pointMapper.selectUserExpiringPointRecordSummary(param);
    }

    public String convertExpireDayToDate(String expiredDay, String addType) {
        if(Pattern.matches("^\\d+[dDwWmMyY]{1}$", expiredDay)){
            int add = Integer.parseInt(expiredDay.substring(0, expiredDay.length()-1));
            String dayFormat = expiredDay.substring(expiredDay.length()-1, expiredDay.length());
            logger.debug(String.format("set day format ", dayFormat));
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            if(addType.equals("sub")){
                add = add*-1;
            }
            switch (dayFormat){
                case "d": case "D":
                    cal.add(Calendar.DATE, add);
                    break;
                case "w": case "W":
                    cal.add(Calendar.WEEK_OF_YEAR, add);
                    break;
                case "m": case "M":
                    cal.add(Calendar.MONTH, add);
                    break;
                case "y": case "Y":
                    cal.add(Calendar.YEAR, add);
                    break;
            }
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            return dateFormat.format(cal.getTime());
        }else{
            logger.debug("Expire Day Expression Error");
            return null;
        }
    }

    public ArrayList<CodeItem> getCodeItemList(String pointType) {
        return pointMapper.selectCodeItemList(pointType);
    }

    public ArrayList<UserPointRecord> getUserPointRecord(HashMap<String, Object> param) {
        return pointMapper.selectUserPointRecord(param);
    }
    public UserPointRecord getUserPointRecordSummary(HashMap<String, Object> param) {
        return pointMapper.selectUserPointRecordSummary(param);
    }

    public int getUserPointRecordGroupListCount(HashMap<String, Object> param) {
        return pointMapper.selectUserPointRecordGroupListCount(param);
    }

    public ArrayList<UserPointRecord> getUserPointRecordGroupList(HashMap<String, Object> param) {
        return pointMapper.selectUserPointRecordGroupList(param);
    }

    public void updateUserPointSettingDeleteYn(UserPointSet pointSet) {
        pointMapper.updateUserPointSettingDeleteYn(pointSet);
    }

    public UserPointRecord getOneUserPointRecord(HashMap<String, Object> pointParam) {
        return pointMapper.selectOneUserPointRecord(pointParam);
    }

    public void insertUserPointExchange(UserPointExchange userPointExchange) {
        pointMapper.insertUserPointExchange(userPointExchange);
    }

    @Transactional
    public void cancelConfirmUserPointExchange(UserPointExchange userPointExchange) {
        try {
            pointMapper.updateUserPointExchange(userPointExchange);

            if (userPointExchange.getPointExchangeConfirmYn().equals("N")) {
                this.refundUserUsedPoint(userPointExchange.getUserPointUsedLogId(), "giftCardExchangeCancel", userPointExchange.getCreateId());
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 사용자 포인트를 환불처리한다.
     * @param userPointUsedLogId //포인트 사용당시 insert한 DB의 user_point_used_log 테이블의 id 값.
     * @param cancelCode 
     */
    @Transactional
    public void refundUserUsedPoint(int userPointUsedLogId, String cancelCode, String createId) throws JsonMappingException, JsonProcessingException {
        String managerId = createId;
        String detailRecode = "";

        HashMap<String, Object> param = new HashMap<>();
        param.put("userPointUsedLogId", userPointUsedLogId);
        param.put("cancelCode", cancelCode);
        param.put("cancelId", managerId);

        UserPointUsed userPointUsed = pointMapper.selectOneUserPointUsedLog(param);
        param.put("userEmail", userPointUsed.getUserEmail());

        detailRecode = userPointUsed.getDetailRecord();

        ObjectMapper mapper = new ObjectMapper();
        JsonNode jsonNode = mapper.readTree(detailRecode);
        JsonNode beforeArray = jsonNode.get("before");
        JsonNode afterArray = jsonNode.get("after");

        HashMap<String, Object> detailRefundRecordParam = new HashMap<>();
        ArrayList<UserPointCancel> detailRefundRecords = new ArrayList<>();
        int totalCalcPointRemain = 0;
        Gson gson = new Gson();
        for (JsonNode beforeItem : beforeArray) {
            //db에 저장된 before 부분
            int beforeId = beforeItem.get("id").asInt();
            int beforeAccruedId = beforeItem.get("accruedId").asInt();
            int beforePointRemain = beforeItem.get("pointRemain").asInt();
            //이 때 before 기준으로 id값을 after에서도 같은 것을 찾는다.
            for (JsonNode afterItem : afterArray) {
                if (afterItem.get("id").asInt() == beforeId) {
                    int afterPointRemain = afterItem.get("pointRemain").asInt();
                    int calcPointRemain = beforePointRemain - afterPointRemain;
                    //이 둘의 pointRemain값을 뺀다.
                    //뺀값을 recode 테이블의 remain에 더하고 used에 뺀다.
                    param.put("id", beforeId);
                    param.put("accruedId", beforeAccruedId);
                    UserPointRecord pointRecord = pointMapper.selectOneUserPointRecord(param);
                    if (pointRecord != null) {
                        if (pointRecord.getIsExpired().equals("N")) {
                            int refundPointRemain = pointRecord.getPointRemain() + calcPointRemain;
                            int refundPointUsed = pointRecord.getPointUsed() - calcPointRemain;

                            pointRecord.setPointRemain(refundPointRemain);
                            pointRecord.setPointUsed(refundPointUsed);
                            pointRecord.setUserEmail(userPointUsed.getUserEmail());
                            pointRecord.setAccruedId(beforeAccruedId);
                            pointRecord.setLastUpdateId(managerId);

                            pointMapper.updateUserPointRecordUsed(pointRecord);
                        }

                        //userPointUsedLog 테이블의 id에 cancel 시킨다
                        param.put("id", userPointUsedLogId);
                        param.put("cancelCode", cancelCode);
                        param.put("cancelId", managerId);
                        pointMapper.updateUserPointUsedLog(param);
                    
                        //point_cancel_log  insert
                        totalCalcPointRemain += calcPointRemain;
                        UserPointCancel detailRefundRecord = new UserPointCancel();
                        detailRefundRecord.setUserPointUsedLogId(userPointUsedLogId);
                        detailRefundRecord.setPointCancel(calcPointRemain);
                        detailRefundRecords.add(detailRefundRecord);

                        detailRefundRecordParam.put("refundInfo", detailRefundRecords);
                    }
                }
            }
            
        }
        UserPointCancel pointCancel = new UserPointCancel();
        pointCancel.setUserPointUsedLogId(userPointUsedLogId);
        pointCancel.setUserEmail(userPointUsed.getUserEmail());
        pointCancel.setDetailRefundRecord(gson.toJson(detailRefundRecordParam));
        pointCancel.setPointCancel(totalCalcPointRemain);
        pointCancel.setCancelCode(cancelCode);
        pointCancel.setCancelId(managerId);
        pointCancel.setCreateId(managerId);
        pointMapper.insertUserPointCancelLog(pointCancel);
    }

    @Transactional
    public void userPointExchangeService(UserPointExchange userPointExchange) {
        int usedId =this.useUserPoint(userPointExchange.getUserEmail(), "giftCardExchange"
        , userPointExchange.getExchangePoint() * userPointExchange.getPointExchangeCount(), userPointExchange.getUserEmail());

        userPointExchange.setUserPointUsedLogId(usedId);
        this.insertUserPointExchange(userPointExchange);
    }

    public ArrayList<UserPointRecord> getUserPointRecordUsedCancelList(HashMap<String, Object> pointParam) {
        return pointMapper.selectUserPointRecordUsedCancelList(pointParam);
    }

    public String getUserPointExchangeCodeByUseId(int id) {
        return pointMapper.selectUserPointExchangeCodeByUseId(id);
    }

    /**
     * 포인트 만료 체크를 수행한다.
     * 현재 시간을 기준으로 만료된 포인트를 찾아 만료 처리한다.
     */
    @Transactional
    public void pointExpiredCheck() {
        logger.debug("Point Expired Check Start");
        
        // 현재 시간 기준으로 만료된 포인트 레코드 조회
        ArrayList<UserPointRecord> expiredPointRecords = pointMapper.selectExpiredPointRecords();
    
        if (expiredPointRecords != null && !expiredPointRecords.isEmpty()) {
            logger.debug("Found " + expiredPointRecords.size() + " expired point records");
            
            for (UserPointRecord record : expiredPointRecords) {
                // 이미 만료 처리된 레코드인지 확인
                if (pointMapper.isPointRecordAlreadyExpired(record.getId())) {
                    logger.debug(String.format("Skip already expired point record - User: %s, PointId: %s", 
                            record.getUserEmail(), record.getId()));
                    continue;
                }
                
                if (record.getPointAccrued() > 0) {
                    // 만료 처리 - 남은 포인트를 만료 포인트로 이동
                    int expiredPoint = record.getPointAccrued();
                    record.setPointExpired(expiredPoint);
                    record.setPointRemain(0);
                    record.setLastUpdateId("admin");
                    
                    // 포인트 레코드 업데이트
                    pointMapper.updatePointRecordExpired(record);
                    
                    // 만료 로그 기록
                    UserPointExpired pointExpired = new UserPointExpired();
                    pointExpired.setUserPointRecodeId(record.getId());
                    pointExpired.setUserEmail(record.getUserEmail());
                    pointExpired.setPointExpired(expiredPoint);
                    pointExpired.setExpiredDate(record.getExpireDate());
                    pointExpired.setCreateId("admin");
                    pointMapper.insertUserPointExpiredLog(pointExpired);
                    
                    logger.debug(String.format("Expired Point - User: %s, PointId: %s, ExpiredPoint: %s, ExpireDate: %s", 
                            record.getUserEmail(), record.getId(), expiredPoint, record.getExpireDate()));
                }
            }
        } else {
            logger.debug("No expired point records found");
        }
        
        logger.debug("Point Expired Check End");
    }
}
