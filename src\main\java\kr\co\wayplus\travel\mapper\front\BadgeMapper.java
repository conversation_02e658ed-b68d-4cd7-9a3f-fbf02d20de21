package kr.co.wayplus.travel.mapper.front;

import kr.co.wayplus.travel.model.*;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

@Mapper
@Repository
public interface BadgeMapper {
	/**
	 * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
	 */
	//	<!--################################### #{BadgeContents} ################################### -->
	BadgeContents selectOneBadgeContents(HashMap<String, Object> param);
	void insertUserBagdeContents(BoardContents boardContents);
	//	<!--################################### #{badgeAcquireHistory} ################################### -->
	void insertUserBadgeAcquireHistory(HashMap<String, Object> badgeParam);
	
}
