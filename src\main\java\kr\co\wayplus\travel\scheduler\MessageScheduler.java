package kr.co.wayplus.travel.scheduler;

import kr.co.wayplus.travel.service.common.MessageSenderService;

import java.time.LocalDateTime;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;

@Component
public class MessageScheduler {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    protected static String ACTIVE_PROFILE;

    private final MessageSenderService messageSenderService;

    public MessageScheduler(MessageSenderService messageSenderService) {
        this.messageSenderService = messageSenderService;
        ACTIVE_PROFILE = System.getProperty("spring.profiles.active");
    }

    // @Scheduled(cron = "0 */10 * * * *")  //매 시간 10분단위.
    // @Scheduled(fixedDelay = 60000, initialDelay = 5000)
    // @Async
    // public void productMessageSendCheck() {
    //     try {
    //         logger.debug("Send Message Scheduled Start");
    //         messageSenderService.productMessageSendCheckService();
    //         logger.debug("Send Message Scheduled End");
    //     } catch (Exception e) {
    //         logger.error("Message Schedule Error: ", e);
    //     }
    // }

    // 매일 오전 9시에 실행
    @Scheduled(cron = "0 0 9 * * *")
    @Async
    public void sendNotifications() {
        try {
            logger.debug("Send Message Scheduled Start");
            messageSenderService.productMessageSendCheckService();
            logger.debug("Send Message Scheduled End");
        } catch (Exception e) {
            logger.error("Message Schedule Error: ", e);
        }
    }

    /**
     * 메시지 발송 검사
     */
    //@Scheduled(cron="0 */5 * * * *")
    public void sendCheck(){
        logger.debug("Send Message Scheduled Start");
        messageSenderService.popMessageQueueSendMessage();
        logger.debug("Send Message Scheduled End");
    }
}
