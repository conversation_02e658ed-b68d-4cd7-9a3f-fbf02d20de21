package kr.co.wayplus.travel.mapper.manage;

import java.util.ArrayList;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import kr.co.wayplus.travel.model.ProductInfo;

@Mapper
@Repository
public interface StatisticsMapper {

	/*################################################statistics################################################*/
	ArrayList<Map<String, Object>> selectListStatisticConnectDate(Map<String, Object> paramMap);
	Map<String, Object> selectListStatisticConnectInfo(Map<String, Object> paramMap);

	ArrayList<Map<String, Object>> selectListStatisticProductDate(Map<String, Object> paramMap);
	Map<String, Object> selectListStatisticProductInfo(Map<String, Object> paramMap);

	ArrayList<String> selectListStatisticProductItemY(Map<String, Object> paramMap);

	ArrayList<Map<String, Object>>  selectListStatisticProgramDate(Map<String, Object> paramMap);
	ArrayList<String> selectListStatisticProgramItemY(Map<String, Object> paramMap);

	ArrayList<Map<String, Object>>  selectListStatisticJamsiislandDate(Map<String, Object> paramMap);
	ArrayList<Map<String, Object>> selectListStatisticJamsiislandGender(Map<String, Object> paramMap);
	ArrayList<Map<String, Object>> selectListStatisticJamsiislandRegion(Map<String, Object> paramMap);
	ArrayList<Map<String, Object>> selectListStatisticJamsiislandAmount(Map<String, Object> paramMap);
	ArrayList<Map<String, Object>> selectListStatisticJamsiislandAge(Map<String, Object> paramMap);
	ArrayList<String> selectListStatisticJamsiislandItemY(Map<String, Object> paramMap);

	ArrayList<String> selectListStatisticDateY(Map<String, Object> paramMap);
	ArrayList<Map<String, Object>> selectListStatisticBoard(Map<String, Object> paramMap);
	ArrayList<Map<String, Object>> selectListStatisticMission(Map<String, Object> paramMap);
	ArrayList<Map<String, Object>> selectListStatisticBadge(Map<String, Object> paramMap);
	ArrayList<Map<String, Object>> selectListIslandlife(Map<String, Object> paramMap);

	ArrayList<Map<String, Object>> selectListStatisticDemographyGender(Map<String, Object> paramMap);
	ArrayList<Map<String, Object>> selectListStatisticDemographyAge(Map<String, Object> paramMap);
	ArrayList<Map<String, Object>> selectListStatisticDemographyRegion(Map<String, Object> paramMap);
}