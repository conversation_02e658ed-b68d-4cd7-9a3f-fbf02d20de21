package kr.co.wayplus.travel.mapper.manage;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import kr.co.wayplus.travel.model.PlaceSpot;
import kr.co.wayplus.travel.model.Survey;
import kr.co.wayplus.travel.model.SurveyImage;
import kr.co.wayplus.travel.model.SurveyQuestion;
import kr.co.wayplus.travel.model.SurveyQuestionAnswer;
import kr.co.wayplus.travel.model.SurveyRecommand;
import kr.co.wayplus.travel.model.SurveyResult;


@Mapper
@Repository
public interface SurveyManageMapper {
	/**
	 * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
	 */

	//	<!--################################### Survey###################################-->
	int selectCountSurvey(HashMap<String, Object> paramMap);
	ArrayList<Survey> selectListSurvey(HashMap<String, Object> paramMap);
	Survey selectOneSurvey(HashMap<String, Object> paramMap);
	void insertSurvey(Survey param) throws SQLException;
	void updateSurvey(Survey param) throws SQLException;
	void deleteSurvey(Survey param) throws SQLException;
	//	<!--################################### SurveyQuestion###################################-->
	int selectCountSurveyQuestion(HashMap<String, Object> paramMap);
	ArrayList<SurveyQuestion> selectListSurveyQuestion(HashMap<String, Object> paramMap);
	SurveyQuestion selectOneSurveyQuestion(HashMap<String, Object> paramMap);
	void insertSurveyQuestion(SurveyQuestion param) throws SQLException;
	void updateSurveyQuestion(SurveyQuestion param) throws SQLException;
	void deleteSurveyQuestion(SurveyQuestion param) throws SQLException;
	//	<!--################################### SurveyImage ###################################-->
	int selectCountSurveyImage(HashMap<String, Object> paramMap);
	ArrayList<PlaceSpot> selectListSurveyImage(HashMap<String, Object> paramMap);
	SurveyImage selectOneSurveyImage(HashMap<String, Object> paramMap);
	void insertSurveyImage(SurveyImage sqi) throws SQLException;
	void updateSurveyImage(SurveyImage sqi) throws SQLException;
	void deleteSurveyImage(SurveyImage sqi) throws SQLException;

	//	<!--################################### SurveyQuestionAnswer###################################-->
	int selectCountSurveyQuestionAnswer(HashMap<String, Object> paramMap);
	ArrayList<SurveyQuestionAnswer> selectListSurveyQuestionAnswer(HashMap<String, Object> paramMap);
	SurveyQuestionAnswer selectOneSurveyQuestionAnswer(HashMap<String, Object> paramMap);
	void insertSurveyQuestionAnswer(SurveyQuestionAnswer param) throws SQLException;
	void updateSurveyQuestionAnswer(SurveyQuestionAnswer param) throws SQLException;
	void deleteSurveyQuestionAnswer(SurveyQuestionAnswer param) throws SQLException;
	//	<!--################################### SurveyResult###################################-->
	int selectCountSurveyRecommand(HashMap<String, Object> paramMap);
	ArrayList<SurveyRecommand> selectListSurveyRecommand(HashMap<String, Object> paramMap);
	SurveyRecommand selectOneSurveyRecommand(HashMap<String, Object> paramMap);
	void insertSurveyRecommand(SurveyRecommand param) throws SQLException;
	void updateSurveyRecommand(SurveyRecommand param) throws SQLException;
	void deleteSurveyRecommand(SurveyRecommand param) throws SQLException;
	//	<!--################################### SurveyResult###################################-->
	int selectCountSurveyResult(HashMap<String, Object> paramMap);
	ArrayList<SurveyResult> selectListSurveyResult(HashMap<String, Object> paramMap);
	SurveyResult selectOneSurveyResult(HashMap<String, Object> paramMap);
	void insertSurveyResult(SurveyResult param) throws SQLException;
	void updateSurveyResult(SurveyResult param) throws SQLException;
	void deleteSurveyResult(SurveyResult param) throws SQLException;

}
