<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.front.ProductMapper">
	<!--
		 * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
	-->
	<!--################################### #{테이블이름} ###################################-->
	<!--################################### Product_tour ###################################-->
	<!--  상품 개수	-->
	<select id="selectCountProduct" parameterType="HashMap" resultType="int">
		SELECT COUNT(*)
		FROM product_tour pt
		LEFT JOIN menu_user mu on mu.menu_id = pt.product_menu_id
		<where>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productUseYn)'>AND pt.product_use_yn = #{productUseYn}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(regacyYn)'>AND pt.regacy_yn = #{regacyYn}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)'>AND pt.delete_yn = #{deleteYn}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)">AND (mu.upper_menu_id = #{menuId} or mu.menu_id = #{menuId})</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productLangType)" >	AND product_lang_type=#{productLangType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)">AND pt.product_tour_id = #{productTourId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productMenuId)">AND pt.product_menu_id=#{productMenuId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCategoryId)">AND pt.product_category_id=#{productCategoryId}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus) and !"ALL".equals(productStatus)'>AND product_status LIKE CONCAT(#{productStatus},'%')</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subCategory) and subCategory != 'ALL'">AND ( mu.menu_id = #{subCategory} or pt.product_category_id = #{subCategory})</if>


			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
				<if test="searchType.equals('title')">AND pt.product_title LIKE CONCAT('%', #{searchKey}, '%')</if>
				<if test="searchType.equals('tag')">AND pt.product_tag LIKE CONCAT('%', #{searchKey}, '%')</if>
				AND concat(pt.product_title, COALESCE(pt.product_tag, '')) LIKE CONCAT('%', #{searchKey}, '%')
			</if>

			<if test='"popular".equals(productType)'>AND pt.isPopular = 'Y'</if>
			<if test='isMainExpose'>and pt.product_menu_id in (select menu_id from menu_user a where main_expose_type is not null)</if>
		</where>
	</select>

	<!-- 프로그램 상품 목록 -->
	<select id="selectListProgram" parameterType="map" resultType="kr.co.wayplus.travel.model.ProductInfo">
	SELECT *
	FROM (
		with
		fixmin as (
			SELECT
			'fix' gubn,
			pOption.product_tour_id,
			MIN(CASE WHEN price_sale != 0 THEN price_sale ELSE price_normal END ) AS product_price, pOption.option_name
			FROM product_tour_price_fix_set AS pFitx
			LEFT JOIN product_tour_price_option AS pOption on pOption.price_option_id = pFitx.price_option_id
			WHERE pOption.use_yn = 'Y'
			AND pOption.delete_yn = 'N' AND pOption.option_sequence = 0 AND pOption.option_group_code IS NOT NULL
			group by pOption.product_tour_id),
		daymin as (
			SELECT
			'day' gubn,
			pOption.product_tour_id,
			MIN(CASE WHEN price_sale != 0 THEN price_sale ELSE price_normal END ) AS product_price, pOption.option_name
			FROM product_tour_price_set AS pPrice
			LEFT JOIN product_tour_price_option AS pOption on pOption.price_option_id = pPrice.price_option_id
			WHERE  pOption.use_yn = 'Y'
			AND pOption.delete_yn = 'N'
			AND pOption.option_group_code IS NOT NULL
			AND price_set_type = 'samePrice'
			AND pPrice.price_set_date >= DATE_FORMAT(now(),'%Y-%m-%d')
			group by pOption.product_tour_id),
		mix as (
			select *
		      from(
				select *,
				       (case when gubn = 'day' then 0 else 1 end) sort,
				       RANK() OVER (PARTITION BY product_tour_id ORDER BY product_tour_id, sort) as ranking
				 from (
				    select * from fixmin
					union all
					select * from daymin )b
				 order by sort) a
				where ranking = 1),
		origin_fix as (
			select
				*
			from
				(
				select
					*,
					(case
						when gubn = 'day' then 0
						else 1
					end) sort,
					RANK() OVER (PARTITION BY product_tour_id
				ORDER BY
					product_tour_id,
					sort) as ranking
				from
					(
					select
						*
					from
						fixmin )b
				order by
					sort) a
		)
		SELECT pt.*,
			mu.upper_menu_id,
			case when mu.main_expose_type is null and mu.upper_menu_id is not null then (select main_expose_type from menu_user u where u.menu_id = mu.upper_menu_id)
				 else mu.main_expose_type end main_expose_type,
			mp.product_price, mp.option_name, mp.gubn, CAST(op.product_price AS DOUBLE) AS origin_product_price,
			(SELECT menu_url FROM menu_user WHERE menu_id = pt.product_menu_id) menuUrl,
			(SELECT menu_name FROM menu_user WHERE menu_id = pt.product_menu_id) categoryTitle,
			pc.category_title subCategoryTitle,
			pc.category_class subCategoryClass,
			(case when mu.upper_menu_id is not null then (select umu.menu_url from menu_user umu where umu.menu_id = mu.upper_menu_id ) else mu.menu_url end) menu_url,
			concat( (case when mu.upper_menu_id is not null
						  then
							(case when menu_type != 'out-link'
							      then (select menu_url from menu_user b where b.menu_id = mu.upper_menu_id)
							      else '' end )
						  else '' end ), menu_url  ) full_menu_url,
			(CASE WHEN (
				SELECT COUNT(*)
				FROM user_favorites uf
				WHERE user_email = #{likeUserEmail}
				AND delete_yn = 'N'
				AND pt.product_serial = uf.product_serial
				AND uf.comment_id IS NULL
			) != '0' then 'Y'
					 else 'N' END) as user_favorite,
			( SELECT id
			FROM user_favorites uf
			WHERE user_email = #{likeUserEmail}
			AND delete_yn = 'N'
			AND pt.product_serial = uf.product_serial
			AND uf.comment_id IS NULL ) user_favorite_id
		FROM product_tour pt
		LEFT JOIN menu_user mu on mu.menu_id = pt.product_menu_id
		LEFT JOIN mix mp on pt.product_tour_id = mp.product_tour_id
		LEFT JOIN origin_fix op on pt.product_tour_id = op.product_tour_id
		LEFT JOIN product_common_category pc on pc.product_menu_id = pt.product_menu_id AND pc.product_category_id = pt.product_category_id
		<where>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productUseYn)'>AND pt.product_use_yn = #{productUseYn}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(regacyYn)'>AND pt.regacy_yn = #{regacyYn}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)'>AND pt.delete_yn = #{deleteYn}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productLangType)" >	AND product_lang_type=#{productLangType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)">AND pt.product_tour_id = #{productTourId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productMenuId)">AND pt.product_menu_id = #{productMenuId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCategoryId)">AND pt.product_category_id IN (${productCategoryId})</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus) and !"ALL".equals(productStatus)'>AND pt.product_status LIKE CONCAT(#{productStatus},'%')</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subCategory) and subCategory != 'ALL'">AND ( mu.menu_id = #{subCategory} or pt.product_category_id = #{subCategory})</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
				<if test="searchType.equals('title')">AND pt.product_title LIKE CONCAT('%', #{searchKey}, '%')</if>
				<if test="searchType.equals('tag')">AND pt.product_tag LIKE CONCAT('%', #{searchKey}, '%')</if>
				AND concat(pt.product_title, COALESCE(pt.product_tag, '')) LIKE CONCAT('%', #{searchKey}, '%')
			</if>
			<if test='"popular".equals(productType)'>AND pt.isPopular = 'Y'</if>
			<if test='isMainExpose'>and pt.product_menu_id in ( select a.menu_id from menu_user a join menu_user b on b.main_expose_type is not null and (a.menu_id = b.menu_id or a.upper_menu_id = b.menu_id) )</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuIds)">
				AND pt.product_menu_id IN<foreach collection="menuIds" item="item" index="index" open="(" separator="," close=")">#{item}</foreach>
			</if>
			AND (
				(pt.product_show_start_date IS NOT NULL
                    AND pt.product_show_end_date IS NULL
                    AND pt.product_show_start_date &lt;= CURDATE())
                OR (pt.product_show_start_date IS NULL
                    AND pt.product_show_end_date IS NOT NULL
                    AND CURDATE() &lt;= pt.product_show_end_date)
                OR (pt.product_show_start_date IS NOT NULL
                    AND pt.product_show_end_date IS NOT NULL
                    AND pt.product_show_start_date &lt;= CURDATE()
                    AND pt.product_show_end_date >= CURDATE())
                OR pt.product_show_start_date IS NULL AND pt.product_show_end_date IS NULL
            )
		</where>
		<choose>
			<when test='"Y".equals(orderYn)'>ORDER BY pt.product_sort_order ASC</when>
			<otherwise>ORDER BY pt.product_sort_order, pt.product_tour_id DESC</otherwise>
		</choose>

		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
	    	ORDER BY
	        <choose>
	            <when test="sort=='rownum'" >	menu_id	</when>
	            <otherwise>rownum</otherwise>
	        </choose>
	        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
		</if>
	) b
	WHERE b.upper_menu_id = #{programUpperMenuId} AND b.gubn ='day'
	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
		LIMIT #{itemStartPosition}, #{pagePerSize}
	</if>
	</select>

	<!--상품 목록-->
	<select id="selectListProduct" parameterType="map" resultType="kr.co.wayplus.travel.model.ProductInfo">
		with
		fixmin as (
			SELECT
			'fix' gubn,
			pOption.product_tour_id,
			MIN(CASE WHEN price_sale != 0 THEN price_sale ELSE price_normal END ) AS product_price, pOption.option_name
			FROM product_tour_price_fix_set AS pFitx
			LEFT JOIN product_tour_price_option AS pOption on pOption.price_option_id = pFitx.price_option_id
			WHERE pOption.use_yn = 'Y'
			AND pOption.delete_yn = 'N' AND pOption.option_sequence = 0 AND pOption.option_group_code IS NOT NULL
			AND pFitx.start_date IS NULL AND pFitx.end_date IS NULL
			group by pOption.product_tour_id),
		daymin as (
			SELECT
			'day' gubn,
			pOption.product_tour_id,
			MIN(CASE WHEN price_sale != 0 THEN price_sale ELSE price_normal END ) AS product_price, pOption.option_name
			FROM product_tour_price_set AS pPrice
			LEFT JOIN product_tour_price_option AS pOption on pOption.price_option_id = pPrice.price_option_id
			WHERE  pOption.use_yn = 'Y'
			AND pOption.delete_yn = 'N'
			AND pOption.option_group_code IS NOT NULL
			AND pPrice.price_set_date >= DATE_FORMAT(now(),'%Y-%m-%d')
			group by pOption.product_tour_id),
		mix as (
			select *
		      from(
				select *,
				       (case when gubn = 'day' then 0 else 1 end) sort,
				       RANK() OVER (PARTITION BY product_tour_id ORDER BY product_tour_id, sort) as ranking
				 from (
				    select * from fixmin
					union all
					select * from daymin )b
				 order by sort) a
				where ranking = 1),
		origin_fix as (
			select
				*
			from
				(
				select
					*,
					(case
						when gubn = 'day' then 0
						else 1
					end) sort,
					RANK() OVER (PARTITION BY product_tour_id
				ORDER BY
					product_tour_id,
					sort) as ranking
				from
					(
					select
						*
					from
						fixmin )b
				order by
					sort) a
		)
		SELECT pt.*,
			mu.upper_menu_id,
			case when mu.main_expose_type is null and mu.upper_menu_id is not null then (select main_expose_type from menu_user u where u.menu_id = mu.upper_menu_id)
				 else mu.main_expose_type end main_expose_type,
			mp.product_price, mp.option_name, mp.gubn, CAST(op.product_price AS DOUBLE) AS origin_product_price,
			(SELECT menu_url FROM menu_user WHERE menu_id = pt.product_menu_id) menuUrl,
			(SELECT menu_name FROM menu_user WHERE menu_id = pt.product_menu_id) categoryTitle,
			pc.category_title subCategoryTitle,
			pc.category_class subCategoryClass,
			(case when mu.upper_menu_id is not null then (select umu.menu_url from menu_user umu where umu.menu_id = mu.upper_menu_id ) else mu.menu_url end) menu_url,
			concat( (case when mu.upper_menu_id is not null
						  then
							(case when menu_type != 'out-link'
							      then (select menu_url from menu_user b where b.menu_id = mu.upper_menu_id)
							      else '' end )
						  else '' end ), menu_url  ) full_menu_url,
			(CASE WHEN (
				SELECT COUNT(*)
				FROM user_favorites uf
				WHERE user_email = #{likeUserEmail}
				AND delete_yn = 'N'
				AND pt.product_serial = uf.product_serial
				AND uf.comment_id IS NULL
			) != '0' then 'Y'
					 else 'N' END) as user_favorite,
			( SELECT id
			FROM user_favorites uf
			WHERE user_email = #{likeUserEmail}
			AND delete_yn = 'N'
			AND pt.product_serial = uf.product_serial
			AND uf.comment_id IS NULL ) user_favorite_id
		FROM product_tour pt
		LEFT JOIN menu_user mu on mu.menu_id = pt.product_menu_id
		LEFT JOIN mix mp on pt.product_tour_id = mp.product_tour_id
		LEFT JOIN origin_fix op on pt.product_tour_id = op.product_tour_id
		LEFT JOIN product_common_category pc on pc.product_menu_id = pt.product_menu_id AND pc.product_category_id = pt.product_category_id
		<where>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productUseYn)'>AND pt.product_use_yn = #{productUseYn}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(regacyYn)'>AND pt.regacy_yn = #{regacyYn}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)'>AND pt.delete_yn = #{deleteYn}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)">AND (mu.upper_menu_id = #{menuId} or mu.menu_id = #{menuId})</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productLangType)" >	AND product_lang_type=#{productLangType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)">AND pt.product_tour_id = #{productTourId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productMenuId)">AND pt.product_menu_id = #{productMenuId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCategoryId)">AND pt.product_category_id IN (${productCategoryId})</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus) and !"ALL".equals(productStatus)'>AND pt.product_status LIKE CONCAT(#{productStatus},'%')</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subCategory) and subCategory != 'ALL'">AND ( mu.menu_id = #{subCategory} or pt.product_category_id = #{subCategory})</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
				<if test="searchType.equals('title')">AND pt.product_title LIKE CONCAT('%', #{searchKey}, '%')</if>
				<if test="searchType.equals('tag')">AND pt.product_tag LIKE CONCAT('%', #{searchKey}, '%')</if>
				AND concat(pt.product_title, COALESCE(pt.product_tag, '')) LIKE CONCAT('%', #{searchKey}, '%')
			</if>
			<if test='"popular".equals(productType)'>AND pt.isPopular = 'Y'</if>
			<if test='isMainExpose'>and pt.product_menu_id in ( select a.menu_id from menu_user a join menu_user b on b.main_expose_type is not null and (a.menu_id = b.menu_id or a.upper_menu_id = b.menu_id) )</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuIds)">
				AND pt.product_menu_id IN<foreach collection="menuIds" item="item" index="index" open="(" separator="," close=")">#{item}</foreach>
			</if>
			AND (
				(pt.product_show_start_date IS NOT NULL
                    AND pt.product_show_end_date IS NULL
                    AND pt.product_show_start_date &lt;= CURDATE())
                OR (pt.product_show_start_date IS NULL
                    AND pt.product_show_end_date IS NOT NULL
                    AND CURDATE() &lt;= pt.product_show_end_date)
                OR (pt.product_show_start_date IS NOT NULL
                    AND pt.product_show_end_date IS NOT NULL
                    AND pt.product_show_start_date &lt;= CURDATE()
                    AND pt.product_show_end_date >= CURDATE())
                OR pt.product_show_start_date IS NULL AND pt.product_show_end_date IS NULL
            )
		</where>
		<choose>
			<when test='"Y".equals(orderYn)'>ORDER BY pt.product_sort_order ASC</when>
			<otherwise>ORDER BY pt.create_date DESC</otherwise>
		</choose>

		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
	    	ORDER BY
	        <choose>
	            <when test="sort=='rownum'" >	menu_id	</when>
	            <otherwise>rownum</otherwise>
	        </choose>
	        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
		</if>

		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
			LIMIT #{itemStartPosition}, #{pagePerSize}
		</if>
	</select>

	<select id="selectOneProduct" parameterType="map" resultType="kr.co.wayplus.travel.model.ProductInfo">
		with
		fixmin as (
			SELECT
				'fix' gubn,
				pOption.price_option_id,
				pOption.product_tour_id,
				MIN(CASE WHEN price_sale != 0 THEN price_sale ELSE price_normal END ) AS product_price, null as early_bird_price,
				pOption.option_name, MAX(pOption.max_capacity) as max_capacity,
				DATE_FORMAT(now(),'%Y-%m-%d') set_date
			FROM product_tour_price_fix_set AS pFitx
			LEFT JOIN product_tour_price_option AS pOption on pOption.price_option_id = pFitx.price_option_id
			WHERE pOption.use_yn = 'Y'
				AND pOption.delete_yn = 'N' AND pOption.option_sequence = 0 AND pOption.option_group_code IS NOT NULL
				AND pFitx.start_date IS NULL AND pFitx.end_date IS NULL
				group by pOption.product_tour_id),
		daymin as (
			SELECT
				'day' gubn,
				pOption.price_option_id,
				pOption.product_tour_id,
				MIN(CASE WHEN price_sale != 0 THEN price_sale ELSE price_normal END ) AS product_price,
				( SELECT MIN(ptps2.price_normal)
				  FROM product_tour_price_option as ptpo2
				  LEFT JOIN product_tour_price_set as ptps2 on ptps2.price_option_id = ptpo2.price_option_id
	 			  WHERE ptpo2.option_name = '얼리버드 특가' AND ptps2.price_set_date >= DATE_FORMAT(now(), '%Y-%m-%d')
				  <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)">
				  AND ptpo2.product_tour_id = #{productTourId}</if>
				) AS early_bird_price,
				pOption.option_name, MAX(pOption.max_capacity) as max_capacity,
				max(pPrice.price_set_date) set_date
			FROM product_tour_price_set AS pPrice
				LEFT JOIN product_tour_price_option AS pOption on pOption.price_option_id = pPrice.price_option_id
			WHERE  pOption.use_yn = 'Y'
				AND pOption.delete_yn = 'N'
				AND pOption.option_group_code IS NOT NULL
				AND pPrice.price_set_date >= DATE_FORMAT(now(),'%Y-%m-%d')
				group by pOption.product_tour_id),
		mix as (
			select *
		      from(
				select *,
				       (case when gubn = 'day' then 0 else 1 end) sort,
				       RANK() OVER (PARTITION BY product_tour_id ORDER BY product_tour_id, sort) as ranking
				 from (
				    select * from fixmin
					union all
					select * from daymin )b
				 order by sort) a
				where ranking = 1),
		origin_fix as (
			select
				*
			from
			(
			select
				*,
				(case
					when gubn = 'day' then 0
					else 1
				end) sort,
				RANK() OVER (PARTITION BY product_tour_id
			ORDER BY
				product_tour_id,
				sort) as ranking
			from
				(
				select
					*
				from
					fixmin)b
			order by
				sort) a
		)
		SELECT pt.*,
				mu.upper_menu_id,
				mu.menu_url,
				mu.menu_type, mu.menu_sub_type,
				case when mu.main_expose_type is null and mu.upper_menu_id is not null then (select main_expose_type from menu_user u where u.menu_id = mu.upper_menu_id) else mu.main_expose_type end main_expose_type,
				mp.price_option_id,
				mp.product_price,
				mp.early_bird_price,
				mp.option_name,
				mp.max_capacity,
				mp.gubn price_set_type,
				mp.price_set_date,
				CAST(op.product_price AS DOUBLE) AS origin_product_price,
				(SELECT menu_name FROM menu_user WHERE menu_id = pt.product_menu_id) categoryTitle,
				pc.category_title subCategoryTitle,
				pc.category_class subCategoryClass,
				(CASE WHEN (
							SELECT COUNT(*)
							FROM user_favorites uf
							WHERE user_email = #{likeUserEmail}
							AND delete_yn = 'N'
							AND pt.product_serial = uf.product_serial
							AND uf.comment_id IS NULL
							) != '0' then 'Y'
						else 'N' END) as user_favorite,
				( SELECT id
					FROM user_favorites uf
					WHERE user_email = #{likeUserEmail}
					AND delete_yn = 'N'
					AND pt.product_serial = uf.product_serial
					AND uf.comment_id IS NULL ) user_favorite_id,
					CASE WHEN pc.category_title IS NULL THEN '' ELSE pc.category_title END as category_common_title
		FROM product_tour pt
		LEFT JOIN menu_user mu on mu.menu_id = pt.product_menu_id
		LEFT JOIN(
			select price_option_id, product_tour_id, product_price, early_bird_price,option_name,
				   max_capacity, gubn, set_date price_set_date, sort
			  from mix a
			 order by sort) mp on pt.product_tour_id = mp.product_tour_id
		LEFT JOIN product_common_category pc on pc.product_menu_id = pt.product_menu_id AND pc.product_category_id = pt.product_category_id
		LEFT JOIN origin_fix op on pt.product_tour_id = op.product_tour_id
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)" > AND mu.menu_type=#{menuType}	</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productUseYn)'>AND pt.product_use_yn = #{productUseYn}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(regacyYn)'>AND pt.regacy_yn = #{regacyYn}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)'>AND pt.delete_yn = #{deleteYn}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)">AND (mu.upper_menu_id = #{menuId} or mu.menu_id = #{menuId})</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subCategory)">AND mu.menu_id = #{subCategory}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerialOld)">and pt.product_serial = #{productSerialOld}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productLangType)" >	AND product_lang_type=#{productLangType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)">AND pt.product_tour_id = #{productTourId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productMenuId)">AND pt.product_menu_id = #{productMenuId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCategoryId)">AND pt.product_category_id=#{productCategoryId}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus) and !"ALL".equals(productStatus)'>AND pt.product_status LIKE CONCAT(#{productStatus},'%')</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)">
				AND pt.product_serial = #{productSerial} AND pt.regacy_yn = 'N' AND pt.product_use_yn = 'Y' AND pt.delete_yn = 'N'</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
				<if test="searchType.equals('title')">
					AND pt.product_title LIKE CONCAT('%', #{searchKey}, '%')
				</if>
				<if test="searchType.equals('tag')">
					AND pt.product_tag LIKE CONCAT('%', #{searchKey}, '%')
				</if>
			</if>
			<if test='"popular".equals(productType)'>AND pt.isPopular = 'Y'</if>
			<if test='isMainExpose'>and pt.product_menu_id in ( select a.menu_id from menu_user a join menu_user b on b.main_expose_type is not null and (a.menu_id = b.menu_id or a.upper_menu_id = b.menu_id) )</if>
		</where>
		<choose>
			<when test='"Y".equals(orderYn)'>ORDER BY pt.product_sort_order ASC</when>
			<otherwise>ORDER BY pt.product_tour_id DESC</otherwise>
		</choose>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
			LIMIT #{itemStartPosition}, #{pagePerSize}
		</if>
	</select>

	<select id="selectOneProductTourId" parameterType="HashMap" resultType="kr.co.wayplus.travel.model.ProductInfo">
		SELECT product_tour_id
		FROM product_tour
		WHERE product_serial = #{productSerial}
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)">AND product_tour_id = #{productTourId}</if>
		<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productUseYn)'>AND product_use_yn = #{productUseYn}</if>
		<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(regacyYn)'>AND regacy_yn = #{regacyYn}</if>
		<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)'>AND delete_yn = #{deleteYn}</if>
	</select>

	<!--################################### recommand ###################################-->
	<!--  상품 개수	-->
	<select id="selectCountProductRecommend" parameterType="HashMap" resultType="int">
		SELECT COUNT(*)
		  FROM product_tour pt
		  LEFT JOIN menu_user mu on mu.menu_id = pt.product_menu_id
		  left join(
		   select product_serial, count(*) rcnt
		     from reservation r
		    group by product_serial, product_tour_id ) r on pt.product_serial = r.product_serial
		  left join(
		  select uf.product_serial, count(*) fcnt
		    from user_favorites uf
		   where delete_yn = 'N'
		     and product_serial is not null
		 group by uf.product_serial ) f  on pt.product_serial = f.product_serial
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)" >	AND mu.menu_type=#{menuType}	</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productUseYn)'>AND pt.product_use_yn = #{productUseYn}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(regacyYn)'>AND pt.regacy_yn = #{regacyYn}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)'>AND pt.delete_yn = #{deleteYn}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)">AND (mu.upper_menu_id = #{menuId} or mu.menu_id = #{menuId})</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productLangType)" >	AND product_lang_type=#{productLangType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)">AND pt.product_tour_id = #{productTourId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productMenuId)">AND pt.product_menu_id=#{productMenuId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCategoryId)">AND pt.product_category_id=#{productCategoryId}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus) and !"ALL".equals(productStatus)'>AND product_status LIKE CONCAT(#{productStatus},'%')</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subCategory) and subCategory != 'ALL'">AND ( mu.menu_id = #{subCategory} or pt.product_category_id = #{subCategory})</if>
		</where>
	</select>

	<!--상품 목록-->
	<select id="selectListProductRecommend" parameterType="map" resultType="kr.co.wayplus.travel.model.ProductInfo">
	   SELECT pt.*,
			  mu.menu_type,
			  pc.category_title subCategoryTitle,
			  pc.category_class subCategoryClass,
			  concat( (case when mu.upper_menu_id is not null
							then
								(case when menu_type != 'out-link'
								      then (select menu_url from menu_user b where b.menu_id = mu.upper_menu_id)
								      else '' end )
							else '' end ), menu_url  ) full_menu_url
			  ,ifnull(r.rcnt,0) rcnt , ifnull(f.fcnt,0) fcnt, ifnull(r.rcnt,0) + ifnull(f.fcnt,0) dcnt
			FROM product_tour pt
			LEFT JOIN menu_user mu on mu.menu_id = pt.product_menu_id
			LEFT JOIN product_common_category pc on pc.product_menu_id = pt.product_menu_id AND pc.product_category_id = pt.product_category_id
			left join(
			   select product_serial, count(*) rcnt
			     from reservation r
			    group by product_serial ) r on pt.product_serial = r.product_serial
			left join(
			  select uf.product_serial, count(*) fcnt
			    from user_favorites uf
			   where delete_yn = 'N'
			     and product_serial is not null
			 group by uf.product_serial ) f  on pt.product_serial = f.product_serial
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isProfileRecom) and isProfileRecom'>
			left join(
			  select pri.recommand_date, product_serial, age_group, gender, purpose, score
			    from product_recommand_info pri
			   inner join(
				select recommand_date recommand_date
				  from product_recommand_info
				 group by recommand_date
				  order by recommand_date desc limit 1 ) last on pri.recommand_date = last.recommand_date
				<where>
					<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ageGroup)'>AND pri.age_group = #{ageGroup}</if>
					<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(gender)'>AND pri.gender = #{gender}</if>
					<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(purpose)'>AND pri.purpose = #{purpose}</if>
				</where>
				) pri on pt.product_serial = pri.product_serial
			</if>
		<where>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)'>AND mu.menu_type = #{menuType}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productUseYn)'>AND pt.product_use_yn = #{productUseYn}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productUseYn)'>AND pt.product_use_yn = #{productUseYn}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(regacyYn)'>AND pt.regacy_yn = #{regacyYn}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)'>AND pt.delete_yn = #{deleteYn}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)">AND (mu.upper_menu_id = #{menuId} or mu.menu_id = #{menuId})</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)">AND pt.product_tour_id = #{productTourId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productMenuId)">AND pt.product_menu_id = #{productMenuId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCategoryId)">AND pt.product_category_id IN (${productCategoryId})</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus) and !"ALL".equals(productStatus)'>AND pt.product_status LIKE CONCAT(#{productStatus},'%')</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(subCategory) and subCategory != 'ALL'">AND ( mu.menu_id = #{subCategory} or pt.product_category_id = #{subCategory})</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
				<if test="searchType.equals('title')">AND pt.product_title LIKE CONCAT('%', #{searchKey}, '%')</if>
				<if test="searchType.equals('tag')">AND pt.product_tag LIKE CONCAT('%', #{searchKey}, '%')</if>
				AND concat(pt.product_title, COALESCE(pt.product_tag, '')) LIKE CONCAT('%', #{searchKey}, '%')
			</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(exceptList)'>AND pt.product_serial not in
				<foreach item="item" index="index" collection="exceptList" separator="," open="(" close=")">#{item}</foreach>
			</if>
			<if test='"popular".equals(productType)'>AND pt.isPopular = 'Y'</if>
			<if test='isMainExpose'>and pt.product_menu_id in ( select a.menu_id from menu_user a join menu_user b on b.main_expose_type is not null and (a.menu_id = b.menu_id or a.upper_menu_id = b.menu_id) )</if>
			AND mu.menu_sub_type != 'stay'
		</where>

		<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isDefualtRecom) and isDefualtRecom'>
	    	ORDER BY ifnull(r.rcnt,0) + ifnull(f.fcnt,0) desc
		</if>
		<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isDefualtRecom) and isDefualtRecom'>
	    	ORDER BY score desc, ifnull(r.rcnt,0) + ifnull(f.fcnt,0) desc
		</if>

		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
			LIMIT #{itemStartPosition}, #{pagePerSize}
		</if>
	</select>


	<select id="selectListProductCategory" parameterType="HashMap" resultType="kr.co.wayplus.travel.model.ProductCategory">
		SELECT product_category_id, product_menu_id, category_title, category_note, category_class, subcategory, use_yn
		  FROM product_common_category
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productMenuId)" >and product_menu_id = #{productMenuId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >and use_yn = #{useYn}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >and delete_yn = #{deleteYn}</if>
		</where>
		 ORDER BY sort_order ASC
	</select>

	<select id="selectListProductLangType" parameterType="map" resultType="ProductInfo">
		SELECT distinct product_menu_id, product_lang_type, ci.name product_lang_name
		  FROM product_tour pt
		  LEFT JOIN code_item ci on pt.product_lang_type = ci.code and ci.upper_code = 'productLangType'
		  LEFT JOIN menu_user mu on mu.menu_id = pt.product_menu_id
		<where>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productUseYn)'>AND pt.product_use_yn = #{productUseYn}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(regacyYn)'>AND pt.regacy_yn = #{regacyYn}</if>
			<if test='@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)'>AND pt.delete_yn = #{deleteYn}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)">AND (mu.upper_menu_id = #{menuId} or mu.menu_id=#{menuId})</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)">and pt.product_serial = #{productSerial}</if>
		</where>
	</select>



	<!--상품 가격 옵션 리스트-->
	<select id="selectOnePriceOption" parameterType="HashMap" resultType="ProductPriceOption">
		with
		 list(product_tour_id,price_option_id,option_name,max_quantity,max_capacity,gubn,price_set_date, product_price) as (
		 select product_tour_id,price_option_id,option_name,max_quantity,max_capacity,gubn,price_set_date, ifnull(pd_product_price, pf_product_price) product_price
		   from (
			select
			    po.product_tour_id,
				po.price_option_id,
				po.option_name,
				po.max_quantity,
				po.max_capacity,
				ifnull(pd.gubn, pf.gubn) gubn,
				ifnull(pd.price_set_date, pf.price_set_date) price_set_date,
				(CASE WHEN pd.price_sale != 0 THEN pd.price_sale ELSE pd.price_normal END ) AS pd_product_price,
				(CASE WHEN pf.price_sale != 0 THEN pf.price_sale ELSE pf.price_normal END ) AS pf_product_price
			  from product_tour_price_option po
			  left join (SELECT price_option_id, 'day' gubn, price_set_date, price_set_type, price_sale, price_normal FROM product_tour_price_set) pd on po.price_option_id = pd.price_option_id
			  left join (SELECT price_option_id, 'fix' gubn, '' price_set_date, price_sale, price_normal  FROM product_tour_price_fix_set) pf on po.price_option_id = pf.price_option_id
			 where po.delete_yn ='N' and use_yn = 'Y') a)
		  ,cklist(product_tour_id,price_option_id,option_name,max_quantity,max_capacity,gubn,price_set_date,product_price,target) as (
			select *
			  from (
				select DISTINCT product_tour_id,price_option_id,option_name,max_quantity,max_capacity,gubn, price_set_date, product_price
				       , case when gubn = 'day' and price_set_date = #{travelDate} then 'day'
				              when gubn = 'fix' then 'fix' end target
				  from list
				  <where>
				   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	and product_tour_id=#{productTourId}	</if>
				   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(priceOptionId)" >	and price_option_id=#{priceOptionId}	</if>
				   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_tour_id in(select product_tour_id from product_tour where product_serial = #{productSerial} and regacy_yn ='N' and product_use_yn = 'Y' and delete_yn = 'N')	</if>
				  </where>
				 ) a
			 where target is not null )
		  ,daycheck(isDay) as (
		  select case when count(target) > 0 and target ='day' then true else false end isDay
		    from cklist
		    group by target)
		  select *
		    from cklist, daycheck dk
		   where gubn = ifnull(case when dk.isday then 'day' end, 'fix')
		   order by price_option_id
	</select>

	<!--숙소 캘린더에 예약 가능 여부 체크를 위한 리스트.-->
	<select id="selectListPriceOptionCalendarPossible" parameterType="HashMap" resultType="ProductPriceOption">
		with
		 list(product_tour_id,price_option_id,option_name,option_one_code,start_date,
			 end_date,max_quantity,max_capacity,gubn,price_set_date, product_price) as (
		 select product_tour_id,price_option_id,option_name,option_one_code,start_date,
			 end_date,max_quantity,max_capacity,gubn,price_set_date, ifnull(pd_product_price, pf_product_price) product_price
		   from (
			select
			    po.product_tour_id,
				po.price_option_id,
				po.option_name,
				po.option_one_code,
				po.max_quantity,
				po.max_capacity,
				pf.start_date,
				pf.end_date,
				ifnull(pd.gubn, pf.gubn) gubn,
				ifnull(pd.price_set_date, pf.price_set_date) price_set_date,
				(CASE WHEN pd.price_sale != 0 THEN pd.price_sale ELSE pd.price_normal END ) AS pd_product_price,
				(CASE WHEN pf.price_sale != 0 THEN pf.price_sale ELSE pf.price_normal END ) AS pf_product_price
			  from product_tour_price_option po
			  left join (SELECT price_option_id, 'day' gubn, price_set_date, price_set_type, price_sale, price_normal FROM product_tour_price_set) pd on po.price_option_id = pd.price_option_id
			  left join (SELECT price_option_id, 'fix' gubn, '' price_set_date, price_sale, price_normal,
			  start_date, end_date  FROM product_tour_price_fix_set) pf on po.price_option_id = pf.price_option_id
			 where po.delete_yn ='N' and use_yn = 'Y') a)
		  ,cklist(product_tour_id,price_option_id,option_name,option_one_code,max_quantity,max_capacity,gubn,price_set_date,product_price,start_date,
			end_date,target) as (
			select *
			  from (
				select DISTINCT product_tour_id,price_option_id,option_name,option_one_code,max_quantity,max_capacity,gubn, price_set_date, product_price, start_date, end_date
				       , case when gubn = 'day' and price_set_date = #{travelDate} then 'day'
				              when gubn = 'fix' then 'fix' end target
				  from list
				  <where>
				   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	and product_tour_id=#{productTourId}	</if>
				   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(priceOptionId)" >	and price_option_id=#{priceOptionId}	</if>
				   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_tour_id in(select product_tour_id from product_tour where product_serial = #{productSerial} and regacy_yn ='N' and product_use_yn = 'Y' and delete_yn = 'N')	</if>
				  </where>
				 ) a
			 where target is not null )
		  ,daycheck(isDay) as (
		  select case when count(target) > 0 and target ='day' then true else false end isDay
		    from cklist
		    group by target)
		  select *
		    from cklist, daycheck dk
		   where gubn = ifnull(case when dk.isday then 'day' end, 'fix') AND option_one_code IS NOT NULL
		   order by price_option_id DESC
	</select>

	<!--프로그램 캘린더에 예약 가능 여부 체크를 위한 리스트.-->
	<select id="selectListPriceOptionProgramCalendarPossible" parameterType="HashMap" resultType="ProductPriceOption">
		with
		 list(product_tour_id,price_option_id,option_name,option_one_code,max_quantity,max_capacity,gubn,price_set_date, product_price) as (
		 select product_tour_id,price_option_id,option_name,option_one_code,max_quantity,max_capacity,gubn,price_set_date, ifnull(pd_product_price, pf_product_price) product_price
		   from (
			select
			    po.product_tour_id,
				po.price_option_id,
				po.option_name,
				po.max_quantity,
				po.max_capacity,
				po.option_one_code,
				ifnull(pd.gubn, pf.gubn) gubn,
				ifnull(pd.price_set_date, pf.price_set_date) price_set_date,
				(CASE WHEN pd.price_sale != 0 THEN pd.price_sale ELSE pd.price_normal END ) AS pd_product_price,
				(CASE WHEN pf.price_sale != 0 THEN pf.price_sale ELSE pf.price_normal END ) AS pf_product_price
			  from product_tour_price_option po
			  left join (SELECT price_option_id, 'day' gubn, price_set_date, price_set_type, price_sale, price_normal FROM product_tour_price_set) pd on po.price_option_id = pd.price_option_id
			  left join (SELECT price_option_id, 'fix' gubn, '' price_set_date, price_sale, price_normal  FROM product_tour_price_fix_set) pf on po.price_option_id = pf.price_option_id
			 where po.delete_yn ='N' and use_yn = 'Y') a)
		  ,cklist(product_tour_id,price_option_id,option_name,option_one_code,max_quantity,max_capacity,gubn,price_set_date,product_price,target) as (
			select *
			  from (
				select DISTINCT product_tour_id,price_option_id,option_name,option_one_code,max_quantity,max_capacity,gubn, price_set_date, product_price
				       , case when gubn = 'day' then 'day' end target
				  from list
				  <where>
				   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	and product_tour_id=#{productTourId}	</if>
				   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(priceOptionId)" >	and price_option_id=#{priceOptionId}	</if>
				   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_tour_id in(select product_tour_id from product_tour where product_serial = #{productSerial} and regacy_yn ='N' and product_use_yn = 'Y' and delete_yn = 'N')	</if>
				  </where>
				 ) a
			 where target is not null )
		  ,daycheck(isDay) as (
		  select case when count(target) > 0 and target ='day' then true else false end isDay
		    from cklist
		    group by target)
		  select *
		    from cklist, daycheck dk
		   where gubn = ifnull(case when dk.isday then 'day' end, 'fix') AND option_one_code IS NOT NULL
		   AND price_set_date > DATE_ADD(CURRENT_DATE(), INTERVAL 2 DAY)
		   order by gubn asc
	</select>


	<!--특정 상품에 속한 고정가격 리스트 (가격최저값)-->
	<select id="selectOneProductFixPriceList" parameterType="map" resultType="kr.co.wayplus.travel.model.ProductPriceOption$FixPriceList">
		SELECT
		    MIN(CASE WHEN price_sale != 0 THEN price_sale ELSE price_normal END ) AS product_price
		FROM product_tour_price_fix_set AS pFitx
			LEFT JOIN product_tour_price_option AS pOption on pOption.price_option_id = pFitx.price_option_id
		WHERE pOption.product_tour_id = #{productTourId} AND pOption.use_yn = 'Y'
			AND pOption.delete_yn = 'N' AND pOption.option_sequence = 0 AND pOption.option_group_code IS NOT NULL
	</select>
	<!--특정 상품에 속한 고정가격 리스트-->
	<select id="selectListProductFixPrice" parameterType="map" resultType="kr.co.wayplus.travel.model.ProductPriceOption$FixPriceList">
		SELECT
		    *
		FROM product_tour_price_fix_set AS pFitx
			LEFT JOIN product_tour_price_option AS pOption on pOption.price_option_id = pFitx.price_option_id
		WHERE pOption.product_tour_id = #{productTourId} AND pOption.use_yn = 'Y'
			AND pOption.delete_yn = 'N' AND pOption.option_sequence = 0 AND pOption.option_group_code IS NOT NULL
	</select>

	<!--특정 상품에 속한 요일별가격 리스트 (가격최저값)-->
	<select id="selectOneProductDayPriceList" parameterType="map" resultType="kr.co.wayplus.travel.model.ProductPriceOption$DayList">
		SELECT
		    MIN(CASE WHEN price_sale != 0 THEN price_sale ELSE price_normal END ) AS product_price
		FROM product_tour_price_set AS pPrice
			LEFT JOIN product_tour_price_option AS pOption on pOption.price_option_id = pPrice.price_option_id
		WHERE pOption.product_tour_id = #{productTourId} AND pOption.use_yn = 'Y'
			AND pOption.delete_yn = 'N' AND pOption.option_group_code IS NOT NULL
	</select>
	<!--특정 상품에 속한 요일별가격 리스트-->
	<select id="selectListProductDayPrice" parameterType="map" resultType="kr.co.wayplus.travel.model.ProductPriceOption$DayList">
		SELECT
		    *
		FROM product_tour_price_set AS pPrice
			LEFT JOIN product_tour_price_option AS pOption on pOption.price_option_id = pPrice.price_option_id
		<where>
		  <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(priceSetDate)">AND pPrice.price_set_date = str_to_date(#{priceSetDate},'%Y-%m-%d')</if>
		  AND pOption.product_tour_id = #{productTourId}
		  AND pOption.use_yn = 'Y'
		  AND pOption.delete_yn = 'N'
		  AND pOption.option_group_code IS NOT NULL
		</where>

	</select>
	<!--상품에 이미지 리스트-->
	<select id="selectListProductImages" parameterType="HashMap" resultType="kr.co.wayplus.travel.model.ProductTourImages">
		SELECT image_id, product_tour_id,
			   service_type, upload_path, upload_filename,
			   file_extension, file_size, file_mimetype,
			   origin_filename, create_id, create_date,
			   last_update_id, last_update_date
		FROM product_tour_images
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(imageId)">and image_id IN (#{imageId})</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)">and product_tour_id IN (#{productTourId})</if>
		</where>
	</select>
	<!--세부일정 리스트-->
	<select id="selectListProductDetailSchedule" parameterType="map" resultType="kr.co.wayplus.travel.model.ProductDetailSchedule">
		SELECT ptd.*
			   , ptd.attach_image AS image_num_list
			   , ps.ts_id
			   , ps.ts_title
			   , ps.ts_append
		FROM product_tour_detail AS ptd
		LEFT JOIN place_spot AS ps on ps.ts_id = ptd.tourspot_id
		WHERE product_tour_id = #{productTourId} AND delete_yn = 'N' AND detail_category != 'golfOption' AND detail_category != 'stayOption'
		ORDER BY detail_category, detail_sequence ASC
	</select>

	<!--상품 가격 옵션 리스트-->
	<select id="selectListPriceOption" parameterType="HashMap" resultType="ProductPriceOption">
		with
		 list(product_tour_id,price_option_id,option_name,option_one_code,gubn,price_set_date, product_price
				, start_date, end_date, consecurive_discount_amount, extra_person_defualt_charge, extra_person_consecurive_charge
		 ) as (
		 select product_tour_id,price_option_id,option_name,option_one_code,gubn,price_set_date, ifnull(pd_product_price, pf_product_price) product_price
		        , start_date, end_date, consecurive_discount_amount, extra_person_defualt_charge, extra_person_consecurive_charge
		   from (
			select
			    po.product_tour_id,
				po.price_option_id,
				po.option_name,
				po.option_one_code,
				ifnull(pd.gubn, pf.gubn) gubn,
				ifnull(pd.price_set_date, pf.price_set_date) price_set_date,
				(CASE WHEN pd.price_sale != 0 THEN pd.price_sale ELSE pd.price_normal END ) AS pd_product_price,
				(CASE WHEN pf.price_sale != 0 THEN pf.price_sale ELSE pf.price_normal END ) AS pf_product_price
				, ifnull(pf.start_date, pd.start_date) start_date
				, ifnull(pf.end_date, pd.end_date) end_date
				, ifnull(pf.consecurive_discount_amount, pd.consecurive_discount_amount) consecurive_discount_amount
				, ifnull(pf.extra_person_defualt_charge, pd.extra_person_defualt_charge) extra_person_defualt_charge
				, ifnull(pf.extra_person_consecurive_charge, pd.extra_person_consecurive_charge) extra_person_consecurive_charge
			  from product_tour_price_option po
			  left join (SELECT price_option_id, 'day' gubn, price_set_date, price_set_type, price_sale, price_normal
			                    , '' start_date, '' end_date, '' consecurive_discount_amount, '' extra_person_defualt_charge, '' extra_person_consecurive_charge
			               FROM product_tour_price_set) pd on po.price_option_id = pd.price_option_id
			  left join (SELECT price_option_id, 'fix' gubn, '' price_set_date, price_sale, price_normal
			                    , start_date, end_date, consecurive_discount_amount, extra_person_defualt_charge, extra_person_consecurive_charge
			               FROM product_tour_price_fix_set) pf on po.price_option_id = pf.price_option_id WHERE use_yn = 'Y' AND delete_yn = 'N') a )
		  ,cklist as (
			select *
			  from (
				select DISTINCT b.product_serial, a.product_tour_id, price_option_id, option_name,option_one_code,gubn, price_set_date, product_price
				       , case when gubn = 'day' and
				              (
				                <choose>
				                  <when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
				                    price_set_date between #{startDate} and #{endDate}
				                  </when>
				                  <otherwise>
				                    price_set_date = #{selectedDate}
				                  </otherwise>
				                </choose>
				              ) then 'day'
				              when gubn = 'fix' then 'fix' end price_set_type
				       , start_date, end_date, consecurive_discount_amount, extra_person_defualt_charge, extra_person_consecurive_charge
				  from list a
				  left join product_tour b on a.product_tour_id = b.product_tour_id
				 where a.product_tour_id in(#{productTourId})) a
			 where price_set_type is not null )
		  ,daycheck(isDay) as (
		  select case when count(price_set_type) > 0 and price_set_type ='day' then true else false end isDay
		    from cklist
		    group by price_set_type)
		  select product_serial, product_tour_id, price_option_id, option_name, option_one_code,gubn, price_set_date, MiN(product_price) as product_price, price_set_type,isDay
		         , start_date, end_date, consecurive_discount_amount, extra_person_defualt_charge, extra_person_consecurive_charge
		    from cklist, daycheck dk
		   where gubn = ifnull(case when dk.isday then 'day' end, 'fix')
		   GROUP BY price_set_date
		   order by price_option_id, price_set_date
	</select>

		<!--상품 가격 옵션 리스트-->
	<select id="selectListPriceOptionStayOnly" parameterType="HashMap" resultType="ProductPriceOption">
		with
		 list(product_tour_id,price_option_id,option_name, option_one_code,gubn,price_set_date, product_price
				, start_date, end_date, consecurive_discount_amount, extra_person_defualt_charge, extra_person_consecurive_charge
		 ) as (
		 select product_tour_id,price_option_id,option_name, option_one_code,gubn,price_set_date, ifnull(pd_product_price, pf_product_price) product_price
		        , start_date, end_date, consecurive_discount_amount, extra_person_defualt_charge, extra_person_consecurive_charge
		   from (
			select
			    po.product_tour_id,
				po.price_option_id,
				po.option_name,
				po.option_one_code,
				ifnull(pd.gubn, pf.gubn) gubn,
				ifnull(pd.price_set_date, pf.price_set_date) price_set_date,
				(CASE WHEN pd.price_sale != 0 THEN pd.price_sale ELSE pd.price_normal END ) AS pd_product_price,
				(CASE WHEN pf.price_sale != 0 THEN pf.price_sale ELSE pf.price_normal END ) AS pf_product_price
				, ifnull(pf.start_date, pd.start_date) start_date
				, ifnull(pf.end_date, pd.end_date) end_date
				, ifnull(pf.consecurive_discount_amount, pd.consecurive_discount_amount) consecurive_discount_amount
				, ifnull(pf.extra_person_defualt_charge, pd.extra_person_defualt_charge) extra_person_defualt_charge
				, ifnull(pf.extra_person_consecurive_charge, pd.extra_person_consecurive_charge) extra_person_consecurive_charge
			  from product_tour_price_option po
			  left join (SELECT price_option_id, 'day' gubn, price_set_date, price_set_type, price_sale, price_normal
			                    , '' start_date, '' end_date, '' consecurive_discount_amount, '' extra_person_defualt_charge, '' extra_person_consecurive_charge
			               FROM product_tour_price_set) pd on po.price_option_id = pd.price_option_id
			  left join (SELECT price_option_id, 'fix' gubn, '' price_set_date, price_sale, price_normal
			                    , start_date, end_date, consecurive_discount_amount, extra_person_defualt_charge, extra_person_consecurive_charge
			               FROM product_tour_price_fix_set) pf on po.price_option_id = pf.price_option_id WHERE use_yn = 'Y' AND delete_yn = 'N' ) a )
		  ,cklist as (
			select *
			  from (
				select DISTINCT b.product_serial, a.product_tour_id, price_option_id, option_name,option_one_code,gubn, price_set_date, product_price
				       , case when gubn = 'day' and
				              (
				                <choose>
				                  <when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(endDate)">
				                    price_set_date between #{startDate} and #{endDate}
				                  </when>
				                  <otherwise>
				                    price_set_date = #{selectedDate}
				                  </otherwise>
				                </choose>
				              ) then 'day'
				              when gubn = 'fix' then 'fix' end price_set_type
				       , start_date, end_date, consecurive_discount_amount, extra_person_defualt_charge, extra_person_consecurive_charge
				  from list a
				  left join product_tour b on a.product_tour_id = b.product_tour_id
				 where a.product_tour_id in(#{productTourId})) a
			 where price_set_type is not null )
		  ,daycheck(isDay) as (
		  select case when count(price_set_type) > 0 and price_set_type ='day' then true else false end isDay
		    from cklist
		    group by price_set_type)
		  select product_serial, product_tour_id, price_option_id, option_name, option_one_code,gubn, price_set_date,product_price, price_set_type
		         , start_date, end_date, consecurive_discount_amount, extra_person_defualt_charge, extra_person_consecurive_charge
		    from cklist
		   order by price_option_id, price_set_date
	</select>

	<select id="selectListProductTourDetailImage" parameterType="map" resultType="ProductDetailScheduleImage">
        SELECT *
        FROM product_tour_detail_images
        <where>
	        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(detailId)">and detail_id = #{detailId}</if>
	        <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(detailIds)">
		        and detail_id IN
		        <foreach collection="detailId" item="item" open="(" separator="," close=")">
		            #{item}
		        </foreach>
	        </if>
        </where>
    </select>

   	<select id="selectSummaryProductInventory" parameterType="HashMap" resultType="ProductInventory">
	SELECT store_count,
	       release_count,
	       (store_count - release_count) inventory_count
	  FROM(
        SELECT sum(store_count) store_count,
               sum(release_count) release_count
          FROM product_inventory a
         <where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_serial=#{productSerial}	</if>
			and delete_yn='N'
         </where>) a
	</select>

	<insert id="insertProductInventory" parameterType="ProductInventory" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO product_inventory
        <set>
        	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	id=#{id},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	product_serial=#{productSerial},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(storeCount)" >	store_count=#{storeCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(releaseCount)" >	release_count=#{releaseCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(note)" >	note=#{note},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			create_date = now()
		</set>
    </insert>

	<!-- 상품 댓글 등록. 이 프로젝트용-->
	<insert id="insertProductComment" parameterType="ProductComment">
		INSERT INTO product_comment
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentId)" >	comment_id=#{commentId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperCommentId)" >	upper_comment_id=#{upperCommentId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	product_serial=#{productSerial},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail}, create_id=#{userEmail},
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	content=#{content},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(favoriteCount)" >	favorite_count=#{favoriteCount},	</if>
			create_date = now()
		</set>
	</insert>

	<!-- 상품 댓글 리스트. 이 프로젝트용-->
	<select id="selectListProductComment" parameterType="HashMap" resultType="ProductComment">
		SELECT *
		FROM product_comment
		WHERE use_yn = 'Y'
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" > AND product_serial=#{productSerial}	</if>
		ORDER BY create_date DESC
	</select>

	<!-- 상품 댓글 조회 -->
	<select id="selectProductComment" parameterType="HashMap" resultType="ProductComment">
	    SELECT ROW_NUMBER() OVER(ORDER BY comment_id asc) AS rownum
			,pct.* ,u.user_profile_image,
			(CASE WHEN (
				SELECT COUNT(*)
				FROM user_favorites uf
				WHERE user_email = #{likeUserEmail}
				AND uf.delete_yn = 'N'
				AND pct.product_serial = uf.product_serial
				AND pct.comment_id = uf.comment_id
			) != '0' then 'Y'
			ELSE 'N' END) as user_favorite,
			( SELECT id
			  FROM user_favorites uf
			  WHERE user_email = #{likeUserEmail}
			  AND uf.delete_yn = 'N'
			  AND pct.product_serial = uf.product_serial
			  AND pct.comment_id = uf.comment_id ) user_favorite_id
		FROM
			product_comment pct
		LEFT JOIN user u on
			u.user_email = pct.create_id
		WHERE
			pct.comment_id IN ( WITH RECURSIVE comment AS (
			SELECT
				comment_id
			FROM
				product_comment
			WHERE
				comment_id IN (
				SELECT
					*
				FROM
					(
					SELECT
						pct.comment_id
					FROM
						product_comment pct
					WHERE
						pct.delete_yn = 'N'
						and pct.product_serial = #{productSerial}
						and pct.upper_comment_id = 0
					ORDER BY
						pct.create_date DESC
					LIMIT 0,10	) as productComment )
		UNION ALL
			SELECT
				t.comment_id
			FROM
				product_comment t
			INNER JOIN comment h ON
				t.upper_comment_id = h.comment_id )
			SELECT
				comment_id
			FROM
				comment)
		ORDER BY
			comment_id DESC
	</select>

	<!-- 상품 댓글 총 개수 -->
	<select id="selectCountProductComment" parameterType="HashMap" resultType="int">
	    SELECT COUNT(*)
	    FROM product_comment
	    WHERE use_yn = 'Y'
	    <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)">
	        AND product_serial = #{productSerial}
	    </if>
	</select>

	<!-- 상품 댓글 좋아요 업데이트 -->
	<update id="updateProductCommentFavorite" parameterType="ProductComment">
	    UPDATE product_comment
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(favoriteCalcType)">
				<choose>
					<when test="favoriteCalcType == 'plus'">
						favorite_count = favorite_count + 1,
					</when>
					<when test="favoriteCalcType == 'minus'">
						favorite_count = favorite_count - 1,
					</when>
				</choose>
			</if>
		</set>
	    WHERE comment_id = #{commentId}
	</update>

	<!-- 상품 댓글 좋아요 업데이트 -->
	<update id="updateProductViewCount" parameterType="HashMap">
	    UPDATE product_tour pt
		<set>
			product_view_count = product_view_count + 1
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)">AND pt.product_tour_id = #{productTourId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)">AND pt.product_serial = #{productSerial} AND pt.regacy_yn = 'N' AND pt.product_use_yn = 'Y' AND pt.delete_yn = 'N'</if>
		</where>
	</update>

	<!-- 청풍용 -->
	<select id="selectEarlyBirdPrice" parameterType="HashMap" resultType="ProductPriceOption">
		SELECT  *
		FROM product_tour_price_option ptpo
		LEFT JOIN product_tour_price_fix_set ptpfs on ptpfs.price_option_id = ptpo.price_option_id
		WHERE product_tour_id = #{productTourId} AND start_date IS NOT NULL AND end_date IS NOT NULL
		AND ptpfs.end_date >= NOW() AND ptpo.use_yn = 'Y' AND ptpo.delete_yn = 'N'
	</select>
	<select id="selectOneEarlyBirdPrice" parameterType="HashMap" resultType="ProductPriceOption">
	SELECT MIN(a.price_normal) as earlyBirdPrice, a.option_name as earlyBirdPriceName, start_date, end_date
	FROM (
		SELECT
			price_normal, ptpo.option_name, start_date, end_date
		FROM product_tour_price_option ptpo
		LEFT JOIN product_tour_price_fix_set ptpfs on ptpfs.price_option_id = ptpo.price_option_id
		WHERE product_tour_id = #{productTourId} AND start_date IS NOT NULL AND end_date IS NOT NULL
		AND ptpfs.end_date >= NOW() AND ptpo.use_yn = 'Y' AND ptpo.delete_yn = 'N'
		AND NOW() &lt;= DATE(end_date)
		) as a GROUP BY a.option_name
	ORDER BY start_date asc
	LIMIT 1
	</select>

	<select id="selectProductPaymentTypeCount" parameterType="HashMap" resultType="Integer">
		SELECT count(*)
		  FROM reservation rv
		  	   LEFT JOIN product_tour pt ON rv.product_serial = pt.product_serial AND rv.product_tour_id = pt.product_tour_id
		  	   LEFT JOIN menu_user mu ON pt.product_menu_id = mu.menu_id
		 WHERE reservation_code IN ('3', '4', '5') AND cancel_yn = 'N'
		       AND user_email = #{userEmail}
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(jamsisum)">
				   AND mu.menu_sub_type = 'stay'
				</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(program)">
				   AND mu.menu_sub_type != 'stay'
				</if>
	</select>

	<select id="selectOneProductOperationDay" parameterType="HashMap" resultType="kr.co.wayplus.travel.model.ProductPriceOption$DayList">
		SELECT
		    DISTINCT CASE DAYOFWEEK(ptps.price_set_date)
		        WHEN 1 THEN '일요일'
		        WHEN 2 THEN '월요일'
		        WHEN 3 THEN '화요일'
		        WHEN 4 THEN '수요일'
		        WHEN 5 THEN '목요일'
		        WHEN 6 THEN '금요일'
		        WHEN 7 THEN '토요일'
		    END AS operation_days
		FROM product_tour_price_option ptpo
		LEFT JOIN product_tour_price_set ptps on ptps.price_option_id = ptpo.price_option_id
		WHERE product_tour_id = #{productTourId} AND ptps.price_set_type = 'samePrice'
		AND ptpo.delete_yn = 'N'  AND ptps.delete_yn = 'N'
		AND ptpo.use_yn = 'Y'  AND ptps.use_yn = 'Y'
		ORDER BY operation_days DESC
	</select>

	<select id="selectOnePriceOptionById" parameterType="string" resultType="ProductPriceOption">
		SELECT *
		FROM product_tour_price_option
		WHERE price_option_id = #{priceOptionId} AND delete_yn = 'N' AND use_yn = 'Y'
	</select>
</mapper>