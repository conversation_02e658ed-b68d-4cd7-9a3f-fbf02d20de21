<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.wayplus.travel.mapper.front.PageMapper">
<!--
	 * 테이블별로 Select(count,list,one), Insert, Update, Delete 순으로 펑션 정리 희망!!!
-->
	<select id="selectUserPageFooterInfo" resultType="SettingCompanyInfo">
		SELECT site_name, company_name, company_owner,
			   company_registration_no,
			   company_mail_order_registration_no,
			   company_tourism_registration_no,
			   company_tell, company_fax, company_email,
			   company_cs_time, company_cs_tell,
			   company_address_zipcode, company_address_jibun,
			   company_address_road, company_address_detail,
			   company_latitude, company_longitude
		FROM setting_company_info
		ORDER BY id DESC LIMIT 1
	</select>

	<select id="selectMainNoticePopupLayerList" parameterType="HashMap" resultType="MainNoticePopup">
		SELECT id, time_key,
			   notice_type, content_type,
			   image_id_pc, image_url_pc,
			   image_background_pc, image_text_pc, visible_yn_pc,
			   image_id_mobile, image_url_mobile,
			   image_background_mobile, image_text_mobile, visible_yn_mobile,
			   title_text, notice_text,
			   link_url, link_target,
			   one_day_yn, one_week_yn,
			   start_date, expire_date,
			   order_number, use_yn
		  FROM main_notice_popup
		 WHERE delete_yn = 'N' AND use_yn = 'Y' AND notice_type = 'layer'
			   AND (start_date IS NULL OR start_date &lt; #{now})
			   AND (expire_date IS NULL OR expire_date &gt; #{now})
			   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(list)">
				AND time_key NOT IN<foreach collection="list" item="item" separator="," open="(" close=")">#{item}</foreach>
			   </if>
		 ORDER BY order_number, id DESC
	</select>

	<select id="selectMainNoticePopupBarList" parameterType="HashMap" resultType="MainNoticePopup">
		SELECT id, time_key,
			   notice_type, content_type,
			   image_id_pc, image_url_pc,
			   image_background_pc, image_text_pc, visible_yn_pc,
			   image_id_mobile, image_url_mobile,
			   image_background_mobile, image_text_mobile, visible_yn_mobile,
			   title_text, notice_text,
			   link_url, link_target,
			   one_day_yn, one_week_yn,
			   start_date, expire_date,
			   order_number, use_yn
		  FROM main_notice_popup
		 WHERE delete_yn = 'N' AND use_yn = 'Y' AND notice_type = 'notice-bar'
			   AND (start_date IS NULL OR start_date &lt; #{now})
			   AND (expire_date IS NULL OR expire_date &gt; #{now})
			   <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(list)">
				AND time_key NOT IN<foreach collection="list" item="item" separator="," open="(" close=")">#{item}</foreach>
			   </if>
		 ORDER BY order_number, id DESC
	</select>

	<select id="selectNavbar" resultType="SettingNavbar">
		SELECT id, expand_type,
			   ci_image_id, ci_image_url,
			   background_color, border_color, font_color,
			   create_id, create_date
		  FROM setting_navbar
		 ORDER BY id DESC LIMIT 1
	</select>

	<select id="selectUserMenuList" resultType="MenuUser">
		with recursive tree as (
			select
				p.menu_id,p.upper_menu_id,p.menu_id root_menu_id, 1 as menu_depth, p.menu_name
				, concat(IFNULL(p.upper_menu_id,''),'_',p.menu_sort,'|',p.menu_id) ordSeq
				, @subMenuCnt := (select count(*) from menu_user b where b.upper_menu_id = p.menu_id) subMenuCount
				, concat( (
					case when p.upper_menu_id is not null
						 then (case when p.menu_type != 'out-link'
						 			then (select menu_url from menu_user b where b.menu_id = p.upper_menu_id) else '' end )
						 			else '' end ), p.menu_url ) full_menu_url
				, p.menu_url, p.menu_type, p.menu_sub_type, p.menu_action
				, p.menu_sort, p.menu_desc, p.use_yn, p.navbar_yn, p.virtual_yn, p.delete_yn
				, p.main_expose_type, p.menu_acronym
			from menu_user p
			where p.upper_menu_id is null
		union all
			select
				c.menu_id,c.upper_menu_id, p.root_menu_id, 1 + menu_depth as depth, c.menu_name
				, concat(IFNULL(p.ordSeq,''),'_',c.menu_sort,'|',c.menu_id) ordSeq
				, @subMenuCnt := (select count(*) from menu_user b where b.upper_menu_id = c.menu_id) subMenuCount
				, concat( (
					case when c.upper_menu_id is not null
						 then (case when c.menu_type != 'out-link'
						 			then p.full_menu_url else '' end )
						 			else '' end ), c.menu_url ) full_menu_url
				, c.menu_url, c.menu_type, c.menu_sub_type, c.menu_action
				, c.menu_sort, c.menu_desc, c.use_yn, c.navbar_yn, c.virtual_yn, c.delete_yn
				, c.main_expose_type, c.menu_acronym
			from menu_user c
			inner join tree p on p.menu_id = c.upper_menu_id)
		SELECT *
		  FROM tree
		 WHERE  use_yn = 'Y'
		   AND delete_yn = 'N'
		 ORDER BY ordSeq
	</select>

	<select id="selectUserMenuListOld" resultType="MenuUser">
		SELECT menu_id, upper_menu_id, menu_name, menu_url, menu_type, menu_sub_type, menu_sort, menu_desc, use_yn, navbar_yn, main_expose_type,menu_acronym,
			   @subMenuCnt := (select count(*) from menu_user b where b.upper_menu_id = a.menu_id) subMenuCount,
			   CASE WHEN upper_menu_id IS NULL THEN 0
					ELSE (SELECT CASE WHEN upper_menu_id IS NULL THEN 1 ELSE 2 END FROM menu_user mu2 WHERE mu2.menu_id = a.upper_menu_id)
				   END depth,
			   case when @subMenuCnt > 0 then
				   ifnull((select concat('/',m3.menu_type, m3.menu_url)
				      from menu_user m3
				     WHERE m3.upper_menu_id = a.menu_id
			 	       AND delete_yn = 'N' AND use_yn = 'Y' AND navbar_yn = 'Y'
				     order by m3.menu_sort LIMIT 1), '') else '' end sub_menu_url,
			   concat( (case when a.upper_menu_id is not null
			                 then
								(case when menu_type != 'out-link'
								      then (select menu_url from menu_user b where b.menu_id = a.upper_menu_id)
								      else '' end )
			                 else '' end ), menu_url  ) full_menu_url
		FROM menu_user a
		WHERE delete_yn = 'N' AND use_yn = 'Y'
		ORDER BY concat(IFNULL(upper_menu_id,''),'|',menu_sort,'|',menu_id),  upper_menu_id, menu_sort
	</select>

	<!-- 청풍용 -->
	<select id="selectListMenuUserUpperMenuId" parameterType="HashMap" resultType="MenuUser">
		SELECT *
		FROM menu_user as mu
		WHERE mu.menu_type = #{menuType} and upper_menu_id IS NOT NULL
		GROUP BY mu.upper_menu_id
		ORDER BY upper_menu_id ASC
	</select>

	<!-- 청풍용 -->
	<select id="selectListProductByUpperMenuId" parameterType="HashMap" resultType="ProductInfo">
		SELECT pt.product_thumbnail, pt.product_title, pt.product_serial, pt.product_favorite_count
		, (SELECT menu_name FROM menu_user as mu2 where menu_id = #{upperMenuId}) as menu_name, pc.category_title as subcategoryTitle
		, concat( (case when mu.upper_menu_id is not null
						  then
							(case when menu_type != 'out-link'
							      then (select menu_url from menu_user b where b.menu_id = mu.upper_menu_id)
							      else '' end )
						  else '' end ), menu_url  ) full_menu_url
		, mu.menu_sort as move_menu_sort
	    , (CASE WHEN (
			SELECT COUNT(*)
			FROM user_favorites uf
			WHERE user_email = #{likeUserEmail}
			AND delete_yn = 'N'
			AND pt.product_serial = uf.product_serial
		) != '0' then 'Y'
					else 'N' END) as user_favorite,
		( SELECT id
		FROM user_favorites uf
		WHERE user_email = #{likeUserEmail}
		AND delete_yn = 'N'
		AND pt.product_serial = uf.product_serial ) user_favorite_id
		FROM product_tour pt
		LEFT JOIN menu_user mu on mu.menu_id = pt.product_menu_id
		LEFT JOIN product_common_category pc on pc.product_menu_id = pt.product_menu_id AND pc.product_category_id = pt.product_category_id
		WHERE pt.product_menu_id IN (SELECT
			menu_id
		FROM
			menu_user as mu
		WHERE
			mu.menu_type = 'product' and upper_menu_id = #{upperMenuId}
		ORDER BY
			upper_menu_id ASC
		)
		AND pt.product_use_yn  = 'Y' and pt.regacy_yn = 'N' and pt.delete_yn = 'N' AND product_title like concat('%',#{searchKey},'%')
		ORDER BY CAST(SUBSTRING_INDEX(product_serial , '-', 1) AS UNSIGNED) DESC,
    			 CAST(SUBSTRING_INDEX(product_serial , '-', -1) AS UNSIGNED) DESC;
	</select>

	<select id="selectListMenuUser" parameterType="HashMap" resultType="MenuUser">
		SELECT *
		  FROM(
			SELECT @rownum:=@rownum+1 AS rownum,
				   menu_id, upper_menu_id, menu_name, menu_url, menu_type, menu_sub_type, menu_action,
				   menu_sort, menu_acronym,menu_desc, navbar_yn, use_yn, delete_yn,
				   main_expose_yn, main_expose_type, create_id, create_date, last_update_id, last_update_date,
				   concat( (case when a.upper_menu_id is not null
				                 then
									(case when menu_type != 'out-link'
										      then (select menu_url from menu_user b where b.menu_id = a.upper_menu_id)
										      else '' end )
				                 else '' end ), menu_url  ) full_menu_url
			  FROM menu_user a
			  join (SELECT @rownum:= 0) rnum
			<where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(isExpose)" >	and main_expose_yn = 'Y' and main_expose_type is not null	</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fullMenuUrl)" >	and full_menu_url=#{fullMenuUrl}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fullMenuUri)" >	and concat('/',menu_type,full_menu_url)=#{fullMenuUri}	</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperMenuId)" >	and upper_menu_id=#{upperMenuId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuName)" >	and menu_name=#{menuName}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuUrl)" >	and menu_url=#{menuUrl}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)" >	and menu_type=#{menuType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSubType)" >	and menu_sub_type=#{menuSubType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuAction)" >	and menu_Action=#{menuAction}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSort)" >	and menu_sort=#{menuSort}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuAcronym)" >	and menu_acronym=#{menuAcronym}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuDesc)" >	and menu_desc=#{menuDesc}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(navbarYn)" >	and navbar_yn=#{navbarYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mainExposeYn)" >	and main_expose_yn=#{mainExposeYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mainExposeType)" >	and main_expose_type=#{mainExposeType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
				and delete_yn='N'
			 </where>
		  ) a
	</select>

	<select id="selectOneMenuUser" parameterType="HashMap" resultType="MenuUser">
		with recursive tree as (
			select
				p.menu_id,p.upper_menu_id,p.menu_id root_menu_id,  1 as menu_depth,  p.menu_name
				, concat(IFNULL(p.upper_menu_id,''),'_',p.menu_sort,'|',p.menu_id) ordSeq
				, @subMenuCnt := (select count(*) from menu_user b where b.upper_menu_id = p.menu_id) subMenuCount
				, concat( (
					case when p.upper_menu_id is not null
						 then (case when p.menu_type != 'out-link'
						 			then (select menu_url from menu_user b where b.menu_id = p.upper_menu_id) else '' end )
						 			else '' end ), p.menu_url ) full_menu_url
				, p.menu_url, p.menu_type, p.menu_sub_type, p.menu_action
				, p.menu_sort, p.menu_desc, p.use_yn, p.navbar_yn, p.delete_yn, p.hide_yn, p.virtual_yn
				, p.main_expose_type, p.menu_acronym
			from menu_user p
			where p.upper_menu_id is null
		union all
			select
				c.menu_id,c.upper_menu_id, p.root_menu_id, 1 + menu_depth as depth, c.menu_name
				, concat(IFNULL(p.ordSeq,''),'_',c.menu_sort,'|',c.menu_id) ordSeq
				, @subMenuCnt := (select count(*) from menu_user b where b.upper_menu_id = c.menu_id) subMenuCount
				, concat( (
					case when c.upper_menu_id is not null
						 then (case when c.menu_type != 'out-link'
						 			then p.full_menu_url else '' end )
						 			else '' end ), c.menu_url ) full_menu_url
				, c.menu_url, c.menu_type, c.menu_sub_type, c.menu_action
				, c.menu_sort, c.menu_desc, c.use_yn, c.navbar_yn, c.delete_yn, c.hide_yn, c.virtual_yn
				, c.main_expose_type, c.menu_acronym
			from menu_user c
			inner join tree p on p.menu_id = c.upper_menu_id)
		select a.*,
		       b.place_code
		  from tree a
		  left join menu_connect_place b on a.menu_id = b.menu_id
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fullMenuUrl)" >	and full_menu_url=#{fullMenuUrl}	</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fullMenuUri)" >	and concat('/',menu_type,full_menu_url)=#{fullMenuUri}	</if>

         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperMenuId)" >	and upper_menu_id=#{upperMenuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuName)" >	and menu_name=#{menuName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuUrl)" >	and menu_url=#{menuUrl}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)" >	and menu_type=#{menuType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSubType)" >	and menu_sub_type=#{menuSubType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuAction)" >	and menu_Action=#{menuAction}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSort)" >	and menu_sort=#{menuSort}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuAcronym)" >	and menu_acronym=#{menuAcronym}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuDesc)" >	and menu_desc=#{menuDesc}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(navbarYn)" >	and navbar_yn=#{navbarYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mainExposeYn)" >	and main_expose_yn=#{mainExposeYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mainExposeType)" >	and main_expose_type=#{mainExposeType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			and delete_yn='N'
         </where>
	</select>
	<select id="selectOneMenuUserOld" parameterType="HashMap" resultType="MenuUser">
		with list as (
			 SELECT menu_id, upper_menu_id, menu_name, menu_url, menu_type, menu_sub_type, menu_sort, menu_acronym,menu_desc, navbar_yn, use_yn,
			       main_expose_yn, main_expose_type, delete_yn, create_id, create_date, last_update_id, last_update_date,
			       concat( (case when a.upper_menu_id is not null
				                 then
									(case when menu_type != 'out-link'
										      then (select menu_url from menu_user b where b.menu_id = a.upper_menu_id)
										      else '' end )
				                 else '' end ), menu_url  ) full_menu_url
			  FROM menu_user a
			  join (SELECT @rownum:= 0) rnum)
		select *
		  from list
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fullMenuUrl)" >	and full_menu_url=#{fullMenuUrl}	</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fullMenuUri)" >	and concat('/',menu_type,full_menu_url)=#{fullMenuUri}	</if>

         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)" >	and menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperMenuId)" >	and upper_menu_id=#{upperMenuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuName)" >	and menu_name=#{menuName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuUrl)" >	and menu_url=#{menuUrl}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuType)" >	and menu_type=#{menuType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSubType)" >	and menu_sub_type=#{menuSubType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuSort)" >	and menu_sort=#{menuSort}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuAcronym)" >	and menu_acronym=#{menuAcronym}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuDesc)" >	and menu_desc=#{menuDesc}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(navbarYn)" >	and navbar_yn=#{navbarYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mainExposeYn)" >	and main_expose_yn=#{mainExposeYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mainExposeType)" >	and main_expose_type=#{mainExposeType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			and delete_yn='N'
         </where>
	</select>

<!--################################### policy ###################################-->
	<select id="selectOnePolicy" parameterType="HashMap" resultType="Policy">
		SELECT id, title, content, policy_type, policy_version, use_yn, create_id, create_date, last_update_id, last_update_date, delete_yn, delete_id, delete_date
		  FROM policy
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and title=#{title}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	and content=#{content}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(policyType)" >	and policy_type=#{policyType}
				<if test="isLastPolicy" >	and id in (SELECT max(id) max_id FROM policy where policy_type = #{policyType})	</if>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(policyVersion)" >	and policy_version=#{policyVersion}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>

		</where>
	</select>
<!--################################### Inquiry ###################################-->

<!--################################### InquiryContent ###################################-->
	 <select id="selectCountInquiryContent" parameterType="HashMap" resultType="Integer">
		SELECT count(a.id)
		  FROM inquiry_content a
		  join (select id, title category_name, code categoryCode from inquiry_category) b on b.id = a.category_id
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and a.id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and title like concat('%',#{searchKey},'%')</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)" >	and a.category_id=#{categoryId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryCode)" >	and b.categoryCode=#{categoryCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and a.title=#{title}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	and a.content=#{content}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(comment)" >	and a.comment=#{comment}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentDate)" >	and a.comment_date=#{commentDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	and a.apply_code=#{applyCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(consultingMethod)" >	and a.consulting_method=#{consultingMethod}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tags)" >	and a.tags=#{tags}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	and a.secret_yn=#{secretYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupJson)" >	and a.group_json=#{groupJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airJson)" >	and a.air_json=#{airJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	and a.vehicle_json=#{vehicleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	and a.berth_json=#{berthJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelStartingPoint)" >	and a.travel_starting_point=#{travelStartingPoint}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	and a.travel_schedule_json=#{travelScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	and a.air_type_request=#{airTypeRequest}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	and a.air_schedule_json=#{airScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	and a.vehicle_type=#{vehicleType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	and a.vehicle_count=#{vehicleCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	and a.berth_type=#{berthType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerName)" >	and a.customer_name=#{customerName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerPass)" >	and a.customer_pass=#{customerPass}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerPhone)" >	and a.customer_phone=#{customerPhone}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerEmail)" >	and a.customer_email=#{customerEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and a.create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and a.create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and a.last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and a.last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
		 </where>
	</select>

	<select id="selectListInquiryContent" parameterType="HashMap" resultType="InquiryContent">
		SELECT *
		  FROM(
			SELECT @rownum:=@rownum+1 AS rownum,
					a.id, category_id, b.category_name,
					title, content, comment,
					comment_date, apply_code,
					case when apply_code = 0 then '접수' when apply_code = 1 then '처리중' when apply_code = 2 then '답변완료' end apply_code_name,
					cancel_yn,consulting_method, tags,
					secret_yn, group_json, air_json, vehicle_json, berth_json,
					travel_starting_point, travel_schedule_json, air_type_request, air_schedule_json,
					vehicle_type, vehicle_count, berth_type, ifnull(customer_name,'') customer_name, customer_pass, customer_phone,
					customer_email, create_id, create_date, last_update_id, last_update_date, delete_yn, delete_id, delete_date,
				   u.user_name, u.user_mobile
			  FROM inquiry_content a
			  left join (select id, title category_name, code categoryCode from inquiry_category) b on b.id = a.category_id
			  left join `user` u on a.create_id = u.user_email
			  join (SELECT @rownum:= 0) rnum
			 <where>
			 	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and a.id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and title like concat('%',#{searchKey},'%')</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)" >	and a.category_id=#{categoryId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryCode)" >	and b.categoryCode=#{categoryCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and a.title=#{title}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	and a.content=#{content}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(comment)" >	and a.comment=#{comment}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentDate)" >	and a.comment_date=#{commentDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	and a.apply_code=#{applyCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(consultingMethod)" >	and a.consulting_method=#{consultingMethod}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tags)" >	and a.tags=#{tags}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	and a.secret_yn=#{secretYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupJson)" >	and a.group_json=#{groupJson}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airJson)" >	and a.air_json=#{airJson}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	and a.vehicle_json=#{vehicleJson}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	and a.berth_json=#{berthJson}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelStartingPoint)" >	and a.travel_starting_point=#{travelStartingPoint}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	and a.travel_schedule_json=#{travelScheduleJson}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	and a.air_type_request=#{airTypeRequest}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	and a.air_schedule_json=#{airScheduleJson}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	and a.vehicle_type=#{vehicleType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	and a.vehicle_count=#{vehicleCount}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	and a.berth_type=#{berthType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerName)" >	and a.customer_name=#{customerName}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerPass)" >	and a.customer_pass=#{customerPass}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerPhone)" >	and a.customer_phone=#{customerPhone}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerEmail)" >	and a.customer_email=#{customerEmail}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and a.create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and a.create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and a.last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and a.last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
			 </where>

			 <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
				ORDER BY
				<choose>
					<when test="sort=='id'" >	a.a.id	</when>
					<when test="sort=='categoryId'" >	a.category_id	</when>
					<when test="sort=='title'" >	a.title	</when>
					<when test="sort=='content'" >	a.content	</when>
					<when test="sort=='comment'" >	a.comment	</when>
					<when test="sort=='commentDate'" >	a.comment_date	</when>
					<when test="sort=='applyCode'" >	a.apply_code	</when>
					<when test="sort=='consultingMethod'" >	a.consulting_method	</when>
					<when test="sort=='tags'" >	a.tags	</when>
					<when test="sort=='secretYn'" >	a.secret_yn	</when>
					<when test="sort=='groupJson'" >	a.group_json	</when>
					<when test="sort=='airJson'" >	a.air_json	</when>
					<when test="sort=='vehicleJson'" >	a.vehicle_json	</when>
					<when test="sort=='berthJson'" >	a.berth_json	</when>
					<when test="sort=='travelStartingPoint'" >	a.travel_starting_point	</when>
					<when test="sort=='travelScheduleJson'" >	a.travel_schedule_json	</when>
					<when test="sort=='airTypeRequest'" >	a.air_type_request	</when>
					<when test="sort=='airScheduleJson'" >	a.air_schedule_json	</when>
					<when test="sort=='vehicleType'" >	a.vehicle_type	</when>
					<when test="sort=='vehicleCount'" >	a.vehicle_count	</when>
					<when test="sort=='berthType'" >	a.berth_type	</when>
					<when test="sort=='customerName'" >	a.customer_name	</when>
					<when test="sort=='customerPass'" >	a.customer_pass	</when>
					<when test="sort=='customerPhone'" >	a.customer_phone	</when>
					<when test="sort=='customerEmail'" >	a.customer_email	</when>
					<when test="sort=='createId'" >	a.create_id	</when>
					<when test="sort=='createDate'" >	a.create_date	</when>
					<when test="sort=='lastUpdateId'" >	a.last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	a.last_update_date	</when>
					<when test="sort=='deleteYn'" >	a.delete_yn	</when>
					<when test="sort=='deleteId'" >	a.delete_id	</when>

					<otherwise>rownum</otherwise>
				</choose>
				<choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
				ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
				<choose>
					<when test="item.sort=='id'" >	a.id	</when>
					<when test="item.sort=='categoryId'" >	a.category_id	</when>
					<when test="item.sort=='title'" >	a.title	</when>
					<when test="item.sort=='content'" >	a.content	</when>
					<when test="item.sort=='comment'" >	a.comment	</when>
					<when test="item.sort=='commentDate'" >	a.comment_date	</when>
					<when test="item.sort=='applyCode'" >	a.apply_code	</when>
					<when test="item.sort=='consultingMethod'" >	a.consulting_method	</when>
					<when test="item.sort=='tags'" >	a.tags	</when>
					<when test="item.sort=='secretYn'" >	a.secret_yn	</when>
					<when test="item.sort=='groupJson'" >	a.group_json	</when>
					<when test="item.sort=='airJson'" >	a.air_json	</when>
					<when test="item.sort=='vehicleJson'" >	a.vehicle_json	</when>
					<when test="item.sort=='berthJson'" >	a.berth_json	</when>
					<when test="item.sort=='travelStartingPoint'" >	a.travel_starting_point	</when>
					<when test="item.sort=='travelScheduleJson'" >	a.travel_schedule_json	</when>
					<when test="item.sort=='airTypeRequest'" >	a.air_type_request	</when>
					<when test="item.sort=='airScheduleJson'" >	a.air_schedule_json	</when>
					<when test="item.sort=='vehicleType'" >	a.vehicle_type	</when>
					<when test="item.sort=='vehicleCount'" >	a.vehicle_count	</when>
					<when test="item.sort=='berthType'" >	a.berth_type	</when>
					<when test="item.sort=='customerName'" >	a.customer_name	</when>
					<when test="item.sort=='customerPass'" >	a.customer_pass	</when>
					<when test="item.sort=='customerPhone'" >	a.customer_phone	</when>
					<when test="item.sort=='customerEmail'" >	a.customer_email	</when>
					<when test="item.sort=='createId'" >	a.create_id	</when>
					<when test="item.sort=='createDate'" >	a.create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	a.last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	a.last_update_date	</when>
					<when test="item.sort=='deleteYn'" >	a.delete_yn	</when>
					<when test="item.sort=='deleteId'" >	a.delete_id	</when>

				</choose>
				<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>

			<if test="sort == null or sortOrder == null">
				order by a.id desc
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
		 LIMIT #{itemStartPosition}, #{pagePerSize}
		 </if>
	</select>

	<select id="selectOneInquiryContent" parameterType="HashMap" resultType="InquiryContent">
		SELECT a.id, category_id, b.category_name,
				title, content, comment,
				comment_date, apply_code,
				case when apply_code = 0 then '접수' when apply_code = 1 then '접수확인' when apply_code = 2 then '처리중' when apply_code = 3 then '답변완료' end apply_code_name,
				cancel_yn, consulting_method, tags,
				secret_yn, group_json, air_json, vehicle_json, berth_json,
				travel_starting_point, travel_schedule_json, air_type_request, air_schedule_json,
				vehicle_type, vehicle_count, berth_type, customer_name, customer_pass, customer_phone,
				customer_email, create_id, create_date, last_update_id, last_update_date, delete_yn, delete_id, delete_date,
				u.user_name, u.user_mobile
		  FROM inquiry_content a
		  left join (select id, title category_name from inquiry_category) b on b.id = a.category_id
		  left join `user` u on a.create_id = u.user_email
		  join (SELECT @rownum:= 0) rnum
		 <where>
		 	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and a.id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)" >	and category_id=#{categoryId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and title=#{title}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	and content=#{content}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(comment)" >	and comment=#{comment}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentDate)" >	and comment_date=#{commentDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	and apply_code=#{applyCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(consultingMethod)" >	and consulting_method=#{consultingMethod}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tags)" >	and tags=#{tags}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	and secret_yn=#{secretYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupJson)" >	and group_json=#{groupJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airJson)" >	and air_json=#{airJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	and vehicle_json=#{vehicleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	and berth_json=#{berthJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelStartingPoint)" >	and travel_starting_point=#{travelStartingPoint}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	and travel_schedule_json=#{travelScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	and air_type_request=#{airTypeRequest}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	and air_schedule_json=#{airScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	and vehicle_type=#{vehicleType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	and vehicle_count=#{vehicleCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	and berth_type=#{berthType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerName)" >	and customer_name=#{customerName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerPass)" >	and customer_pass=#{customerPass}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerPhone)" >	and customer_phone=#{customerPhone}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerEmail)" >	and customer_email=#{customerEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
		 </where>
	</select>

	<insert id="insertInquiryContent" parameterType="InquiryContent" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO inquiry_content
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	cancel_yn=#{cancelYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)" >	category_id=#{categoryId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	title=#{title},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	content=#{content},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(comment)" >	comment=#{comment},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentDate)" >	comment_date=#{commentDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	apply_code=#{applyCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(consultingMethod)" >	consulting_method=#{consultingMethod},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tags)" >	tags=#{tags},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	product_serial=#{productSerial},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(priceOptionJson)" >	price_option_json=#{priceOptionJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationYn)" >	reservation_yn=#{reservationYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	secret_yn=#{secretYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupJson)" >	group_json=#{groupJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airJson)" >	air_json=#{airJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	vehicle_json=#{vehicleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	berth_json=#{berthJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelStartingPoint)" >	travel_starting_point=#{travelStartingPoint},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	travel_schedule_json=#{travelScheduleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	air_type_request=#{airTypeRequest},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	air_schedule_json=#{airScheduleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	vehicle_type=#{vehicleType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	vehicle_count=#{vehicleCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	berth_type=#{berthType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerName)" >	customer_name=#{customerName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerPass)" >	customer_pass=#{customerPass},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerPhone)" >	customer_phone=#{customerPhone},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerEmail)" >	customer_email=#{customerEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			create_date = now()
		</set>
	</insert>

	<update id="updateInquiryContent" parameterType="InquiryContent">
		UPDATE inquiry_content
		<set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	cancel_yn=#{cancelYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)" >	category_id=#{categoryId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	title=#{title},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(content)" >	content=#{content},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(comment)" >	comment=#{comment},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(commentDate)" >	comment_date=#{commentDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	apply_code=#{applyCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(consultingMethod)" >	consulting_method=#{consultingMethod},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tags)" >	tags=#{tags},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	product_serial=#{productSerial},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(priceOptionJson)" >	price_option_json=#{priceOptionJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationYn)" >	reservation_yn=#{reservationYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(secretYn)" >	secret_yn=#{secretYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupJson)" >	group_json=#{groupJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airJson)" >	air_json=#{airJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	vehicle_json=#{vehicleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	berth_json=#{berthJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelStartingPoint)" >	travel_starting_point=#{travelStartingPoint},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	travel_schedule_json=#{travelScheduleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	air_type_request=#{airTypeRequest},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	air_schedule_json=#{airScheduleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	vehicle_type=#{vehicleType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	vehicle_count=#{vehicleCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	berth_type=#{berthType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerName)" >	customer_name=#{customerName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerPass)" >	customer_pass=#{customerPass},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerPhone)" >	customer_phone=#{customerPhone},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(customerEmail)" >	customer_email=#{customerEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
		</where>
	</update>

	<update id="restoreInquiryContent" parameterType="InquiryContent">
		UPDATE inquiry_content
		   SET delete_yn='N'
		WHERE id = #{id}
	</update>

	<update id="deleteInquiryContent" parameterType="InquiryContent">
		UPDATE inquiry_content
		   SET delete_yn='Y'
		WHERE id = #{id}
	</update>


<!--################################### InquiryCategory ###################################-->
	<select id="selectCountInquiryCategory" parameterType="HashMap" resultType="Integer">
		SELECT count(id) FROM inquiry_category
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and title=#{title}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(note)" >	and note=#{note}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderNum)" >	and order_num=#{orderNum}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationYn)" >	and reservation_yn=#{reservationYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationSwitchYn)" >	and reservation_switch_yn=#{reservationSwitchYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupYn)" >	and group_yn=#{groupYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	and start_date=#{startDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	and expire_date=#{expireDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
		 </where>
	</select>

	<select id="selectListInquiryCategory" parameterType="HashMap" resultType="InquiryCategory">
		SELECT *
		  FROM(
			SELECT @rownum:=@rownum+1 AS rownum, id, title, note, order_num, use_yn, reservation_yn,reservation_switch_yn, group_yn, delete_yn, start_date, expire_date, create_id, create_date, last_update_id, last_update_date
			  FROM inquiry_category a
			  join (SELECT @rownum:= 0) rnum
			 <where>
			 	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and title=#{title}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(note)" >	and note=#{note}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderNum)" >	and order_num=#{orderNum}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationYn)" >	and reservation_yn=#{reservationYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationSwitchYn)" >	and reservation_switch_yn=#{reservationSwitchYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupYn)" >	and group_yn=#{groupYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	and start_date=#{startDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	and expire_date=#{expireDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>

			 </where>

			 <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
				ORDER BY
				<choose>
					<when test="sort=='id'" >	id	</when>
					<when test="sort=='title'" >	title	</when>
					<when test="sort=='note'" >	note	</when>
					<when test="sort=='orderNum'" >	order_num	</when>
					<when test="sort=='useYn'" >	use_yn	</when>
					<when test="sort=='groupYn'" >	group_yn	</when>
					<when test="sort=='deleteYn'" >	delete_yn	</when>
					<when test="sort=='startDate'" >	start_date	</when>
					<when test="sort=='expireDate'" >	expire_date	</when>
					<when test="sort=='createId'" >	create_id	</when>
					<when test="sort=='createDate'" >	create_date	</when>
					<when test="sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
					<otherwise>rownum</otherwise>
				</choose>
				<choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
				ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
				<choose>
					<when test="item.sort=='id'" >	id	</when>
					<when test="item.sort=='title'" >	title	</when>
					<when test="item.sort=='note'" >	note	</when>
					<when test="item.sort=='orderNum'" >	order_num	</when>
					<when test="item.sort=='useYn'" >	use_yn	</when>
					<when test="item.sort=='groupYn'" >	group_yn	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='startDate'" >	start_date	</when>
					<when test="item.sort=='expireDate'" >	expire_date	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
				</choose>
				<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
		 LIMIT #{itemStartPosition}, #{pagePerSize}
		 </if>
	</select>

	<select id="selectOneInquiryCategory" parameterType="HashMap" resultType="InquiryCategory">
		SELECT id, title, note, order_num, use_yn,reservation_yn,reservation_switch_yn, group_yn, delete_yn, start_date, expire_date, create_id, create_date, last_update_id, last_update_date
		  FROM inquiry_category
		 <where>
		 	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(title)" >	and title=#{title}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(note)" >	and note=#{note}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderNum)" >	and order_num=#{orderNum}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationYn)" >	and reservation_yn=#{reservationYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationSwitchYn)" >	and reservation_switch_yn=#{reservationSwitchYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(groupYn)" >	and group_yn=#{groupYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(startDate)" >	and start_date=#{startDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(expireDate)" >	and expire_date=#{expireDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
		 </where>
	</select>
	 <select id="selectCountReservation" parameterType="HashMap" resultType="Integer">
        SELECT count(id)
          FROM (
			SELECT
				id,
				@travel := json_value(travel_schedule_json,'$.data.travelSchedule') travel_schedule_dt,
				@travelF := substring_index(@travel, ' ~ ',1) as datef,
				@travelT :=substring_index(@travel, ' ~ ',-1) as datet
			FROM reservation a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateType)">
				<choose>
					<when test='dateType eq "travelFrom"'>@travelF between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d') </when>
					<when test='dateType eq "travelTo"'>@travelT between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d') </when>
					<when test='dateType eq "receipt"'>a.create_date between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d')</when>
				</choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and concat(user_email,user_name,user_mobile) like concat('%',#{searchKey},'%')</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and a.pay_moid=#{payMoid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and a.user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)" >	and user_name=#{userName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPhone)" >	and user_mobile=#{userMobile}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	and berth_type=#{berthType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and a.pay_moid=#{payMoid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_serial=#{productSerial}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	and apply_code=#{applyCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationCode)" >	and reservation_code=#{reservationCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	and cancel_yn=#{cancelYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelCode)" >	and cancel_code=#{cancelCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	and berth_json=#{berthJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	and travel_schedule_json=#{travelScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	and air_type_request=#{airTypeRequest}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	and air_schedule_json=#{airScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	and vehicle_type=#{vehicleType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	and vehicle_count=#{vehicleCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	and vehicle_json=#{vehicleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(usePoint)" >	and use_point=#{usePoint}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(totalAmount)" >	and total_amount=#{totalAmount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
         </where>) a
    </select>

	<select id="selectListReservation" parameterType="HashMap" resultType="Reservation">
		SELECT *
		  FROM(
	        SELECT @rownum:=@rownum+1 AS rownum,
	        		a.id, a.user_email,
	        		IFNULL(a.user_name, u.user_name) user_name, price_option_json,
	        		IFNULL(IFNULL(a.user_mobile, u.user_mobile),'~연락처없음~') user_mobile,
	        		@travel := json_value(travel_schedule_json,'$.data.travelSchedule') travel_schedule_dt,
	        		@travelF := substring_index(@travel, ' ~ ',1) as datef,
					@travelT :=substring_index(@travel, ' ~ ',-1) as datet,
	        		berth_type, a.product_serial,a.product_tour_id,ifNUll(p.product_title, '~상품미선택~') product_title, apply_code,
	        		reservation_code, b.name reservation_code_name, cancel_yn, cancel_code, berth_json,
	        		travel_schedule_json, air_type_request, air_schedule_json,
	        		vehicle_type, vehicle_count, vehicle_json, use_point, total_amount,
	        		a.create_id, a.create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.delete_id, a.delete_date
	          FROM reservation a
	          left join product_tour p on a.product_serial = p.product_serial and a.product_tour_id = p.product_tour_id
	          left join code_item b on a.reservation_code = b.code and b.upper_code = 'reservationType'
	          left join `user` u on a.user_email = u.user_email
	          join (SELECT @rownum:= 0) rnum
	         <where>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(dateType)">
					<choose>
						<when test="dateType eq 'travelFrom'">@travelF between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d %H:%i:%s') </when>
						<when test="dateType eq 'travelTo'">@travelT between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d %H:%i:%s') </when>
						<when test="dateType eq 'receipt'">a.create_date between DATE_FORMAT(#{dateFrom}, '%Y-%m-%d') and DATE_FORMAT(#{dateTo}, '%Y-%m-%d %H:%i:%s')</when>
					</choose>
				</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and concat(user_email,user_name,user_mobile) like concat('%',#{searchKey},'%')</if>

	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and id=#{id}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and a.user_email=#{userEmail}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)" >	and a.user_name=#{userName}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPhone)" >	and a.user_mobile=#{userMobile}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	and a.berth_type=#{berthType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and a.pay_moid=#{payMoid}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and a.product_serial=#{productSerial}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	and a.apply_code=#{applyCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationCode)" >	and a.reservation_code=#{reservationCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	and a.cancel_yn=#{cancelYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelCode)" >	and a.cancel_code=#{cancelCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	and a.berth_json=#{berthJson}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	and a.travel_schedule_json=#{travelScheduleJson}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	and a.air_type_request=#{airTypeRequest}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	and a.air_schedule_json=#{airScheduleJson}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	and a.vehicle_type=#{vehicleType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	and a.vehicle_count=#{vehicleCount}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	and a.vehicle_json=#{vehicleJson}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(usePoint)" >	and a.use_point=#{usePoint}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(totalAmount)" >	and a.total_amount=#{totalAmount}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and a.create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and a.create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and a.last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and a.last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and a.delete_date=#{deleteDate}	</if>
	         </where>
             <if test="sort eq null or sortOrder eq null">
	         	ORDER BY id desc
	         </if>
	         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort=='travelScheduleDt'" >	travel_schedule_dt	</when>
		            <when test="sort=='productTitle'" >	p.product_title	</when>
		            <when test="sort=='id'" >	id	</when>
					<when test="sort=='userEmail'" >	user_email	</when>
					<when test="sort=='userName'" >	user_name	</when>
					<when test="sort=='userMobile'" >	user_mobile	</when>
					<when test="sort=='berthType'" >	berth_type	</when>
					<when test="sort=='productSerial'" >	product_serial	</when>
					<when test="sort=='applyCode'" >	apply_code	</when>
					<when test="sort=='reservationCode'" >	reservation_code	</when>
					<when test="sort=='cancelYn'" >	cancel_yn	</when>
					<when test="sort=='cancelCode'" >	cancel_code	</when>
					<when test="sort=='berthJson'" >	berth_json	</when>
					<when test="sort=='travelScheduleJson'" >	travel_schedule_json	</when>
					<when test="sort=='airTypeRequest'" >	air_type_request	</when>
					<when test="sort=='airScheduleJson'" >	air_schedule_json	</when>
					<when test="sort=='vehicleType'" >	vehicle_type	</when>
					<when test="sort=='vehicleCount'" >	vehicle_count	</when>
					<when test="sort=='vehicleJson'" >	vehicle_json	</when>
					<when test="sort=='totalAmount'" >	total_amount	</when>
					<when test="sort=='createId'" >	create_id	</when>
					<when test="sort=='createDate'" >	create_date	</when>
					<when test="sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="sort=='deleteYn'" >	delete_yn	</when>
					<when test="sort=='deleteId'" >	delete_id	</when>
					<when test="sort=='deleteDate'" >	delete_date	</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort=='travelScheduleDt'" >	travel_schedule_dt	</when>
		            <when test="item.sort=='productTitle'" >	p.product_title	</when>
		            <when test="item.sort=='id'" >	id	</when>
					<when test="item.sort=='userEmail'" >	user_email	</when>
					<when test="item.sort=='userName'" >	user_name	</when>
					<when test="item.sort=='userMobile'" >	user_mobile	</when>
					<when test="item.sort=='berthType'" >	berth_type	</when>
					<when test="item.sort=='productSerial'" >	product_serial	</when>
					<when test="item.sort=='applyCode'" >	apply_code	</when>
					<when test="item.sort=='reservationCode'" >	reservation_code	</when>
					<when test="item.sort=='cancelYn'" >	cancel_yn	</when>
					<when test="item.sort=='cancelCode'" >	cancel_code	</when>
					<when test="item.sort=='berthJson'" >	berth_json	</when>
					<when test="item.sort=='travelScheduleJson'" >	travel_schedule_json	</when>
					<when test="item.sort=='airTypeRequest'" >	air_type_request	</when>
					<when test="item.sort=='airScheduleJson'" >	air_schedule_json	</when>
					<when test="item.sort=='vehicleType'" >	vehicle_type	</when>
					<when test="item.sort=='vehicleCount'" >	vehicle_count	</when>
					<when test="item.sort=='vehicleJson'" >	vehicle_json	</when>
					<when test="item.sort=='totalAmount'" >	total_amount	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='deleteId'" >	delete_id	</when>
					<when test="item.sort=='deleteDate'" >	delete_date	</when>

		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>
	<select id="selectOneReservation" parameterType="HashMap" resultType="Reservation">
         SELECT @rownum:=@rownum+1 AS rownum,
        		a.id, a.user_email,
        		IFNULL(a.user_name, u.user_name) user_name,
        		IFNULL(a.user_mobile, u.user_mobile) user_mobile,
        		@travel := json_value(travel_schedule_json,'$.data.travelSchedule') travel_schedule_dt,
        		@travelF := substring_index(@travel, ' ~ ',1) as datef,
				@travelT :=substring_index(@travel, ' ~ ',-1) as datet,
        		berth_type, a.product_serial,a.product_tour_id,ifNUll(p.product_title, '~상품미선택~') product_title, apply_code,
        		reservation_code, b.name reservation_code_name, cancel_yn, cancel_code, berth_json,
        		travel_schedule_json, air_type_request, air_schedule_json,
        		vehicle_type, vehicle_count, vehicle_json, use_point, total_amount,comment,
        		a.create_id, a.create_date, a.last_update_id, a.last_update_date, a.delete_yn, a.delete_id, a.delete_date
        		, price_option_json
          FROM reservation a
          left join product_tour p on a.product_serial = p.product_serial and a.product_tour_id = p.product_tour_id
          left join code_item b on a.reservation_code = b.code and b.upper_code = 'reservationType'
          left join `user` u on a.user_email = u.user_email
          join (SELECT @rownum:= 0) rnum
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)" >	and a.id=#{id}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and a.user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)" >	and a.user_name=#{userName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPhone)" >	and a.user_mobile=#{userMobile}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	and a.berth_type=#{berthType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and a.product_serial=#{productSerial}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and a.pay_moid=#{payMoid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	and a.apply_code=#{applyCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationCode)" >	and a.reservation_code=#{reservationCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	and a.cancel_yn=#{cancelYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelCode)" >	and a.cancel_code=#{cancelCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	and a.berth_json=#{berthJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	and a.travel_schedule_json=#{travelScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	and a.air_type_request=#{airTypeRequest}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	and a.air_schedule_json=#{airScheduleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	and a.vehicle_type=#{vehicleType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	and a.vehicle_count=#{vehicleCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	and a.vehicle_json=#{vehicleJson}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(totalAmount)" >	and a.total_amount=#{totalAmount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and a.create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and a.create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and a.last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and a.last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and a.delete_date=#{deleteDate}	</if>
         </where>
	</select>

	<insert id="insertReservation" parameterType="Reservation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO reservation
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)" >	user_name=#{userName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userMobile)" >	user_mobile=#{userMobile},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(priceOptionJson)" >	price_option_json=#{priceOptionJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	berth_type=#{berthType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	product_serial=#{productSerial},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	product_tour_id=#{productTourId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	pay_moid=#{payMoid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	apply_code=#{applyCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationCode)" >	reservation_code=#{reservationCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	cancel_yn=#{cancelYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelCode)" >	cancel_code=#{cancelCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	berth_json=#{berthJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	travel_schedule_json=#{travelScheduleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	air_type_request=#{airTypeRequest},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	air_schedule_json=#{airScheduleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	vehicle_type=#{vehicleType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	vehicle_count=#{vehicleCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	vehicle_json=#{vehicleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(usePoint)" >	use_point=#{usePoint},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPointUsedLogId)" >	user_point_used_log_id=#{userPointUsedLogId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(totalAmount)" >	total_amount=#{totalAmount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(comment)" >	comment=#{comment},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(categoryId)" >	category_id=#{categoryId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pickCapacity)" >	pick_capacity=#{pickCapacity},	</if>
			create_date = now()
		</set>
    </insert>

    <update id="updateReservation" parameterType="Reservation">
        UPDATE reservation
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userName)" >	user_name=#{userName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userMobile)" >	user_mobile=#{userMobile},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthType)" >	berth_type=#{berthType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	product_serial=#{productSerial},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	product_tour_id=#{productTourId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	pay_moid=#{payMoid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(applyCode)" >	apply_code=#{applyCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationCode)" >	reservation_code=#{reservationCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelYn)" >	cancel_yn=#{cancelYn},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelCode)" >	cancel_code=#{cancelCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(berthJson)" >	berth_json=#{berthJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(travelScheduleJson)" >	travel_schedule_json=#{travelScheduleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airTypeRequest)" >	air_type_request=#{airTypeRequest},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(airScheduleJson)" >	air_schedule_json=#{airScheduleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleType)" >	vehicle_type=#{vehicleType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleCount)" >	vehicle_count=#{vehicleCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vehicleJson)" >	vehicle_json=#{vehicleJson},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(usePoint)" >	use_point=#{usePoint},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userPointUsedLogId)" >	user_point_used_log_id=#{userPointUsedLogId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(totalAmount)" >	total_amount=#{totalAmount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(comment)" >	comment=#{comment},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	create_date=#{createDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
		</where>
    </update>

    <update id="restoreReservation" parameterType="Reservation">
        UPDATE reservation
           SET delete_yn='N'
        WHERE id = #{id}
    </update>

    <update id="deleteReservation" parameterType="Reservation">
        UPDATE reservation
           SET delete_yn='Y'
        WHERE id = #{id}
    </update>
<!--################################### CodeItem ###################################-->
	 <select id="selectCountCodeItem" parameterType="HashMap" resultType="Integer">
		with recursive tree as (
			select 1 as lev, concat(code_sort,'|',code) idpath, CAST(concat(0,'-') as varchar(100) ) pid,
				   code, upper_code, name, code_desc, code_acronym, code_depth, code_sort, use_yn, create_id, create_date, last_update_id, last_update_date, delete_yn, delete_id, delete_date
			  from code_item a
			 where upper_code = ''
			union all
			select 1+lev as lev, concat(c.idpath,'->',p.code_sort,'|',p.code) as idpath, concat(c.code, '-') as pid,
				   p.code,	p.upper_code,	p.name,	p.code_desc,p.code_acronym,	p.code_depth,	p.code_sort,	p.use_yn,	p.create_id,	p.create_date,	p.last_update_id,	p.last_update_date,	p.delete_yn,	p.delete_id,	p.delete_date
			  from code_item p
			  inner join tree c on p.upper_code = c.code)
		select count(*)
		  from tree
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(code)" >	and code=#{code}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperCode)" >	and upper_code=#{upperCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(name)" >	and name=#{name}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeDesc)" >	and code_desc=#{codeDesc}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeDepth)" >	and code_depth=#{codeDepth}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeAcronym)" >	and code_acronym=#{codeAcronym}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeSort)" >	and code_sort=#{codeSort}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		 </where>
	</select>

	<select id="selectListCodeItem" parameterType="HashMap" resultType="CodeItem">
		with recursive tree as (
			select
				1 as lev,concat(code_sort, '|', code) idpath,CAST(concat(0, '-') as varchar(100) ) pid,
				id, upper_id,
				code_depth,code,upper_code,name,code_desc ,code_acronym,code_sort,use_yn,
				create_id,create_date,last_update_id,last_update_date,delete_yn,delete_id,delete_date
			from code_item a
			where upper_code = ''
		union all
			select
				1 + lev as lev, concat(p.idpath, '->', c.code_sort, '|', c.code) as idpath, concat(p.code, '-') as pid,
				c.id, c.upper_id,
				c.code_depth,c.code,c.upper_code,c.name,c.code_desc ,c.code_acronym,c.code_sort,c.use_yn,
				c.create_id,c.create_date,c.last_update_id,c.last_update_date,c.delete_yn,c.delete_id,c.delete_date
			from code_item c
			inner join tree p on p.id = c.upper_id)
		SELECT *
		  FROM(
			select @rownum:=@rownum+1 AS rownum, lev, idpath, pid,
				   (select count(code) from code_item c where c.upper_code = tree.code) ccnt,
				   code, COALESCE(upper_code,'') upper_code, name, code_desc , COALESCE(code_acronym,'') code_acronym, code_depth, code_sort, use_yn,
				   create_id, create_date, last_update_id, last_update_date, delete_yn, delete_id, delete_date
			  from tree
			  join (SELECT @rownum:= 0) rnum
			 <where>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(code)" >	and code=#{code}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperCode)" >	and upper_code=#{upperCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(name)" >	and name=#{name}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeDesc)" >	and code_desc=#{codeDesc}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeDepth)" >	and code_depth=#{codeDepth}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeAcronym)" >	and code_acronym=#{codeAcronym}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeSort)" >	and code_sort=#{codeSort}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
			 </where>

			 <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
				ORDER BY
				<choose>
					<when test="sort=='code'" >	code	</when>
					<when test="sort=='upperCode'" >	upper_code	</when>
					<when test="sort=='name'" >	name	</when>
					<when test="sort=='codeDesc'" >	code_desc	</when>
					<when test="sort=='codeDepth'" >	code_depth	</when>
					<when test="sort=='codeAcronym'" >	code_acronym	</when>
					<when test="sort=='codeSort'" >	code_sort	</when>
					<when test="sort=='useYn'" >	use_yn	</when>
					<when test="sort=='createId'" >	create_id	</when>
					<when test="sort=='createDate'" >	create_date	</when>
					<when test="sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="sort=='deleteYn'" >	delete_yn	</when>
					<when test="sort=='deleteId'" >	delete_id	</when>
					<when test="sort=='deleteDate'" >	delete_date	</when>

					<otherwise>rownum</otherwise>
				</choose>
				<choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="!@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) or !@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
				ORDER BY idpath
			</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
				ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
				<choose>
					<when test="item.sort=='code'" >	code	</when>
					<when test="item.sort=='upperCode'" >	upper_code	</when>
					<when test="item.sort=='name'" >	name	</when>
					<when test="item.sort=='codeDesc'" >	code_desc	</when>
					<when test="item.sort=='codeDepth'" >	code_depth	</when>
					<when test="item.sort=='codeAcronym'" >	code_acronym	</when>
					<when test="item.sort=='codeSort'" >	code_sort	</when>
					<when test="item.sort=='useYn'" >	use_yn	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='deleteId'" >	delete_id	</when>
					<when test="item.sort=='deleteDate'" >	delete_date	</when>
				</choose>
				<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
		 LIMIT #{itemStartPosition}, #{pagePerSize}
		 </if>
	</select>

	<select id="selectOneCodeItem" parameterType="HashMap" resultType="CodeItem">
		SELECT code, upper_code, name, code_desc, code_acronym, code_depth, sort, use_yn, create_id, create_date, last_update_id, last_update_date, delete_yn, delete_id, delete_date
		  FROM code_item a
		  join (SELECT @rownum:= 0) rnum
		 <where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(code)" >	and code=#{code}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(upperCode)" >	and upper_code=#{upperCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(name)" >	and name=#{name}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeDesc)" >	and code_desc=#{codeDesc}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeDepth)" >	and code_depth=#{codeDepth}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(codeAcronym)" >	and code_acronym=#{codeAcronym}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort)" >	and sort=#{sort}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >	and use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
		 </where>
	</select>

    <select id="selectListBannerCategory" parameterType="HashMap" resultType="BannerCategory">
		SELECT banner_category_id, menu_id, banner_type, category_title, use_yn, delete_yn
		  FROM main_banner_category
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)">and menu_id = #{menuId} </if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)" >and use_yn = #{useYn}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >and delete_yn = #{deleteYn}</if>
		</where>
        ORDER BY order_num ASC
	</select>

	<select id="selectListMainBannerImage" parameterType="HashMap" resultType="MainBannerImage">
		SELECT id, menu_id, banner_type,
			   image_id_pc, image_url_pc,
			   image_fit_pc, visible_yn_pc,
			   image_id_mobile, image_url_mobile,
			   image_fit_mobile, visible_yn_mobile,
			   image_text_top, image_text_top_font, image_text_top_color,
			   image_text_top_x, image_text_top_y,
			   image_text_mid, image_text_mid_font, image_text_mid_color,
			   image_text_mid_x, image_text_mid_y,
			   image_text_bot, image_text_bot_font, image_text_bot_color,
			   image_text_bot_x, image_text_bot_y,
			   link_url, link_target, link_title, link_tag_type,
			   start_date, expire_date, use_yn,
			   order_number, create_id, create_date,
			   last_update_id, last_update_date
		  FROM main_banner_image
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(bannerType)">and banner_type = #{bannerType}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)">and menu_id = #{menuId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)">
				<if test="useYn != 'ALL'">
					AND use_yn = #{useYn}
				</if>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(visibleYnPc)" >and visible_yn_pc = #{visibleYnPc}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >and delete_yn = #{deleteYn}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
				AND (
						image_text_top LIKE CONCAT('%', #{searchKey}, '%')
						OR image_text_mid LIKE CONCAT('%', #{searchKey}, '%')
						OR image_text_bot LIKE CONCAT('%', #{searchKey}, '%')
					)
			</if>
			AND (
					(start_date IS NOT NULL
						AND expire_date IS NULL
						AND start_date &lt;= DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') )
					OR (start_date IS NULL
						AND expire_date IS NOT NULL
						AND DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') &lt;= expire_date)
					OR (start_date IS NOT NULL
						AND expire_date IS NOT NULL
						AND start_date &lt;= DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')
						AND expire_date >= DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'))
					OR expire_date IS NULL AND start_date IS NULL
			)
			AND delete_yn = 'N'
		</where>
		 ORDER BY order_number, id DESC
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectListMainBannerImageAndCategory" parameterType="HashMap" resultType="MainBannerImage">
		SELECT id, mbi.menu_id, mbi.banner_type,
			   mbi.image_id_pc, mbi.image_url_pc,
			   mbi.image_fit_pc, mbi.visible_yn_pc,
			   mbi.image_id_mobile, mbi.image_url_mobile,
			   mbi.image_fit_mobile, mbi.visible_yn_mobile,
			   mbi.image_text_top, mbi.image_text_top_font, mbi.image_text_top_color,
			   mbi.image_text_top_x, mbi.image_text_top_y,
			   mbi.image_text_mid, mbi.image_text_mid_font, mbi.image_text_mid_color,
			   mbi.image_text_mid_x, mbi.image_text_mid_y,
			   mbi.image_text_bot, mbi.image_text_bot_font, mbi.image_text_bot_color,
			   mbi.image_text_bot_x, mbi.image_text_bot_y,
			   mbi.link_url, mbi.link_target, mbi.link_title, mbi.link_tag_type,
			   mbi.start_date, mbi.expire_date, mbi.use_yn,
			   mbi.order_number, mbi.create_id, mbi.create_date,
			   mbi.last_update_id, mbi.last_update_date,
			   mbc.banner_category_id, mbc.category_title
		  FROM main_banner_image as mbi
		  LEFT JOIN main_banner_category mbc on mbc.menu_id = mbi.menu_id and mbc.banner_type = mbi.banner_type
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(bannerType)">and mbi.banner_type = #{bannerType}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)">and mbi.menu_id = #{menuId}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)">
				<if test="useYn != 'ALL'">
					AND mbi.use_yn = #{useYn}
				</if>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">
				AND (
						image_text_top LIKE CONCAT('%', #{searchKey}, '%')
						OR image_text_mid LIKE CONCAT('%', #{searchKey}, '%')
						OR image_text_bot LIKE CONCAT('%', #{searchKey}, '%')
					)
			</if>
			AND (
				(mbi.start_date IS NOT NULL
					AND mbi.expire_date IS NULL
					AND mbi.start_date &lt;= DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') )
				OR (mbi.start_date IS NULL
					AND mbi.expire_date IS NOT NULL
					AND DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') &lt;= mbi.expire_date)
				OR (mbi.start_date IS NOT NULL
					AND mbi.expire_date IS NOT NULL
					AND mbi.start_date &lt;= DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')
					AND mbi.expire_date >= DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'))
				OR mbi.expire_date IS NULL AND mbi.start_date IS NULL
			)
			AND mbi.delete_yn = 'N'
		</where>
		 ORDER BY mbi.order_number, mbi.id ASC
	</select>
<!--################################### place_spot ###################################-->
	<select id="selectCountPlaceSpot" parameterType="HashMap" resultType="Integer">
		SELECT count(*)
		  FROM place_spot a
		  <if test="isConnect">join menu_connect_place mcp on a.place_type = mcp.place_code</if>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType) and
					  @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey) ">
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType=='tsTitle')" >and ts_title LIKE CONCAT('%', #{searchKey}, '%')</if>
			</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentType) and contentType neq 'ALL'">and content_type_id=#{contentType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(location) and location neq 'ALL'">and location_code=#{location}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(titleLike)">and ts_title like concat('%',#{titleLike},'%')</if>

			<if test="isConnect and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)">and mcp.menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsId)">and ts_id=#{tsId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsStnid)">and ts_stnid=#{tsStnid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsTitle)">and ts_title=#{tsTitle}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsTel1)">and ts_tel1=#{tsTel1}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsManager)">and ts_manager=#{tsManager}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsTel2)">and ts_tel2=#{tsTel2}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsZipcode)">and ts_zipcode=#{tsZipcode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsRoad)">and ts_road=#{tsRoad}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsJibun)">and ts_jibun=#{tsJibun}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsDetail)">and ts_detail=#{tsDetail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsExtra)">and ts_extra=#{tsExtra}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsLocationCode)">and ts_location_code=#{tsLocationCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsLatitude)">and ts_latitude=#{tsLatitude}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsLongitude)">and ts_longitude=#{tsLongitude}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCompanynum)">and ts_companynum=#{tsCompanynum}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsEmployee)">and ts_employee=#{tsEmployee}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsHomepage)">and ts_homepage=#{tsHomepage}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsInside)">and ts_inside=#{tsInside}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsSummary)">and ts_summary=#{tsSummary}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsHelp)">and ts_help=#{tsHelp}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsBirthday)">and ts_birthday=#{tsBirthday}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsHoliday)">and ts_holiday=#{tsHoliday}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsExiting)">and ts_exiting=#{tsExiting}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsExitingInwon)">and ts_exiting_inwon=#{tsExitingInwon}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsStart)">and ts_start=#{tsStart}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsFinish)">and ts_finish=#{tsFinish}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsPersonnel)">and ts_personnel=#{tsPersonnel}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsUseday)">and ts_useday=#{tsUseday}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsUsetime)">and ts_usetime=#{tsUsetime}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsInfo)">and ts_info=#{tsInfo}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsYoutubeCode)">and ts_youtube_code=#{tsYoutubeCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsAppend)">and ts_append=#{tsAppend}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWriteId)">and ts_write_id=#{tsWriteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsUdateId)">and ts_udate_id=#{tsUdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWritdate)">and ts_writdate=#{tsWritdate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsMale)">and ts_male=#{tsMale}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsFemale)">and ts_female=#{tsFemale}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts10s)">and ts_10s=#{ts10s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts20s)">and ts_20s=#{ts20s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts30s)">and ts_30s=#{ts30s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts40s)">and ts_40s=#{ts40s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts50s)">and ts_50s=#{ts50s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts60s)">and ts_60s=#{ts60s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsRecreation)">and ts_recreation=#{tsRecreation}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsEpicurism)">and ts_epicurism=#{tsEpicurism}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsExperience)">and ts_experience=#{tsExperience}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCulture)">and ts_culture=#{tsCulture}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsLeports)">and ts_leports=#{tsLeports}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsOther)">and ts_other=#{tsOther}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsAlone)">and ts_alone=#{tsAlone}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsFriend)">and ts_friend=#{tsFriend}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCouple)">and ts_couple=#{tsCouple}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsParents)">and ts_parents=#{tsParents}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsChild)">and ts_child=#{tsChild}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsGroup)">and ts_group=#{tsGroup}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCategory1)">and ts_category1=#{tsCategory1}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCategory2)">and ts_category2=#{tsCategory2}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCategory3)">and ts_category3=#{tsCategory3}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCategory4)">and ts_category4=#{tsCategory4}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCategory5)">and ts_category5=#{tsCategory5}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsPets)">and ts_pets=#{tsPets}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsParking)">and ts_parking=#{tsParking}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsDisabled)">and ts_disabled=#{tsDisabled}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsNursing)">and ts_nursing=#{tsNursing}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsStroller)">and ts_stroller=#{tsStroller}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCredit)">and ts_credit=#{tsCredit}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWeatherRain)">and ts_weather_rain=#{tsWeatherRain}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWeatherSnow)">and ts_weather_snow=#{tsWeatherSnow}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWeatherFog)">and ts_weather_fog=#{tsWeatherFog}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWeatherGale)">and ts_weather_gale=#{tsWeatherGale}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsSeasonSpring)">and ts_season_spring=#{tsSeasonSpring}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsSeasonSummer)">and ts_season_summer=#{tsSeasonSummer}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsSeasonAutumn)">and ts_season_autumn=#{tsSeasonAutumn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsSeasonWinter)">and ts_season_winter=#{tsSeasonWinter}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsEditdate)">and ts_editdate=#{tsEditdate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsEnrolChk)">and ts_enrol_chk=#{tsEnrolChk}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(areaType)">and a.area_type=#{areaType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(placeType)">and a.place_type=#{placeType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)">and use_yn=#{useYn}	</if>
			and ts_delete_yn = 'N'
		</where>
	</select>

	<select id="selectListPlaceSpot" parameterType="HashMap" resultType="PlaceSpot">
	SELECT * FROM (
		SELECT @rownum:=@rownum+1 AS rownum,
		       <if test="isConnect">mcp.menu_id,</if>
		       ts_id, ts_stnid, ts_title, ts_tel1, ts_manager, ts_tel2, ts_zipcode, ts_road, ts_jibun, ts_help,
		       ts_writdate, ts_editdate, ts_delete_yn, ts_append, area_type, place_type, a.use_yn,
		       gis_map_level,
		       b.name place_type_name, c.name area_type_name
		  FROM place_spot a
		  left join code_item b on b.code = a.place_type and b.upper_code = 'placeType'
		  left join code_item c on c.code = a.area_type and c.upper_code = a.place_type
		  join (SELECT @rownum:= 0) rnum
		  <if test="isConnect">join menu_connect_place mcp on a.place_type = mcp.place_code</if>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType) and
					  @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey) ">
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchType=='tsTitle')" >and ts_title LIKE CONCAT('%', #{searchKey}, '%')</if>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pointA) and
					  @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pointB) and
					  @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(distance)">
				and <![CDATA[ST_Distance_Sphere(POINT(#{pointA}, #{pointB}), POINT(ts_longitude, ts_latitude)) between 1 and #{distance}]]>
			</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(contentType) and contentType neq 'ALL'">and content_type_id=#{contentType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(location) and location neq 'ALL'">and location_code=#{location}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(titleLike)">and ts_title like concat('%',#{titleLike},'%')</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(searchKey)">and ts_title like concat('%',#{searchKey},'%')</if>
			<if test="isConnect and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(menuId)">and mcp.menu_id=#{menuId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsId)">and ts_id=#{tsId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(placeType)">and place_type=#{placeType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsStnid)">and ts_stnid=#{tsStnid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsTitle)">and ts_title=#{tsTitle}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsTel1)">and ts_tel1=#{tsTel1}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsManager)">and ts_manager=#{tsManager}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsTel2)">and ts_tel2=#{tsTel2}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsZipcode)">and ts_zipcode=#{tsZipcode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsRoad)">and ts_road=#{tsRoad}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsJibun)">and ts_jibun=#{tsJibun}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsDetail)">and ts_detail=#{tsDetail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsExtra)">and ts_extra=#{tsExtra}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsLocationCode)">and ts_location_code=#{tsLocationCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsLatitude)">and ts_latitude=#{tsLatitude}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsLongitude)">and ts_longitude=#{tsLongitude}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCompanynum)">and ts_companynum=#{tsCompanynum}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsEmployee)">and ts_employee=#{tsEmployee}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsHomepage)">and ts_homepage=#{tsHomepage}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsInside)">and ts_inside=#{tsInside}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsSummary)">and ts_summary=#{tsSummary}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsHelp)">and ts_help=#{tsHelp}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsBirthday)">and ts_birthday=#{tsBirthday}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsHoliday)">and ts_holiday=#{tsHoliday}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsExiting)">and ts_exiting=#{tsExiting}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsExitingInwon)">and ts_exiting_inwon=#{tsExitingInwon}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsStart)">and ts_start=#{tsStart}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsFinish)">and ts_finish=#{tsFinish}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsPersonnel)">and ts_personnel=#{tsPersonnel}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsUseday)">and ts_useday=#{tsUseday}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsUsetime)">and ts_usetime=#{tsUsetime}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsInfo)">and ts_info=#{tsInfo}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsYoutubeCode)">and ts_youtube_code=#{tsYoutubeCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsAppend)">and ts_append=#{tsAppend}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWriteId)">and ts_write_id=#{tsWriteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsUdateId)">and ts_udate_id=#{tsUdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWritdate)">and ts_writdate=#{tsWritdate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsMale)">and ts_male=#{tsMale}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsFemale)">and ts_female=#{tsFemale}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts10s)">and ts_10s=#{ts10s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts20s)">and ts_20s=#{ts20s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts30s)">and ts_30s=#{ts30s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts40s)">and ts_40s=#{ts40s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts50s)">and ts_50s=#{ts50s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts60s)">and ts_60s=#{ts60s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsRecreation)">and ts_recreation=#{tsRecreation}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsEpicurism)">and ts_epicurism=#{tsEpicurism}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsExperience)">and ts_experience=#{tsExperience}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCulture)">and ts_culture=#{tsCulture}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsLeports)">and ts_leports=#{tsLeports}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsOther)">and ts_other=#{tsOther}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsAlone)">and ts_alone=#{tsAlone}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsFriend)">and ts_friend=#{tsFriend}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCouple)">and ts_couple=#{tsCouple}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsParents)">and ts_parents=#{tsParents}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsChild)">and ts_child=#{tsChild}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsGroup)">and ts_group=#{tsGroup}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCategory1)">and ts_category1=#{tsCategory1}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCategory2)">and ts_category2=#{tsCategory2}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCategory3)">and ts_category3=#{tsCategory3}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCategory4)">and ts_category4=#{tsCategory4}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCategory5)">and ts_category5=#{tsCategory5}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsPets)">and ts_pets=#{tsPets}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsParking)">and ts_parking=#{tsParking}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsDisabled)">and ts_disabled=#{tsDisabled}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsNursing)">and ts_nursing=#{tsNursing}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsStroller)">and ts_stroller=#{tsStroller}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCredit)">and ts_credit=#{tsCredit}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWeatherRain)">and ts_weather_rain=#{tsWeatherRain}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWeatherSnow)">and ts_weather_snow=#{tsWeatherSnow}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWeatherFog)">and ts_weather_fog=#{tsWeatherFog}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWeatherGale)">and ts_weather_gale=#{tsWeatherGale}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsSeasonSpring)">and ts_season_spring=#{tsSeasonSpring}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsSeasonSummer)">and ts_season_summer=#{tsSeasonSummer}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsSeasonAutumn)">and ts_season_autumn=#{tsSeasonAutumn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsSeasonWinter)">and ts_season_winter=#{tsSeasonWinter}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsEditdate)">and ts_editdate=#{tsEditdate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsEnrolChk)">and ts_enrol_chk=#{tsEnrolChk}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(areaType)">and a.area_type=#{areaType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)">and a.use_yn=#{useYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(placeType)">and a.place_type=#{placeType}	</if>

			and ts_delete_yn = 'N'
		</where>
		ORDER BY ts_id desc) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOnePlaceSpot" parameterType="HashMap" resultType="PlaceSpot">
		SELECT ts_id, ts_stnid, ts_title, ts_tel1, ts_manager, ts_tel2, ts_zipcode, ts_road, ts_jibun,
		       ts_detail, ts_extra, ts_location_code, ts_latitude, ts_longitude, ts_companynum, ts_employee,
		       ts_homepage, ts_inside, ts_summary, ts_help, to_char(ts_birthday,'YYYY-MM-DD') str_ts_birthday, ts_holiday, ts_exiting, ts_exiting_inwon,
		       to_char(ts_start,'HH24:MI:SS') str_ts_start, to_char(ts_finish,'HH24:MI:SS') str_ts_finish, ts_personnel, ts_useday, ts_usetime, ts_info, ts_youtube_code, ts_append,
		       ts_write_id, ts_udate_id, ts_writdate, ts_male, ts_female, ts_10s, ts_20s, ts_30s, ts_40s, ts_50s, ts_60s,
		       ts_recreation, ts_epicurism, ts_experience, ts_culture, ts_leports, ts_other, ts_alone, ts_friend, ts_couple,
		       ts_parents, ts_child, ts_group, ts_category1, ts_category2, ts_category3, ts_category4, ts_category5, ts_pets,
		       ts_parking, ts_disabled, ts_nursing, ts_stroller, ts_credit, ts_weather_rain, ts_weather_snow, ts_weather_fog,
		       ts_weather_gale, ts_season_spring, ts_season_summer, ts_season_autumn, ts_season_winter, ts_editdate, ts_enrol_chk, ts_evid,
		       area_type, place_type, use_yn,
		       gis_map_level
		  FROM place_spot
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsId)">and ts_id=#{tsId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsStnid)">and ts_stnid=#{tsStnid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsTitle)">and ts_title=#{tsTitle}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsTel1)">and ts_tel1=#{tsTel1}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsManager)">and ts_manager=#{tsManager}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsTel2)">and ts_tel2=#{tsTel2}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsZipcode)">and ts_zipcode=#{tsZipcode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsRoad)">and ts_road=#{tsRoad}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsJibun)">and ts_jibun=#{tsJibun}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsDetail)">and ts_detail=#{tsDetail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsExtra)">and ts_extra=#{tsExtra}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsLocationCode)">and ts_location_code=#{tsLocationCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsLatitude)">and ts_latitude=#{tsLatitude}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsLongitude)">and ts_longitude=#{tsLongitude}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCompanynum)">and ts_companynum=#{tsCompanynum}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsEmployee)">and ts_employee=#{tsEmployee}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsHomepage)">and ts_homepage=#{tsHomepage}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsInside)">and ts_inside=#{tsInside}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsSummary)">and ts_summary=#{tsSummary}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsHelp)">and ts_help=#{tsHelp}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsBirthday)">and ts_birthday=#{tsBirthday}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsHoliday)">and ts_holiday=#{tsHoliday}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsExiting)">and ts_exiting=#{tsExiting}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsExitingInwon)">and ts_exiting_inwon=#{tsExitingInwon}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsStart)">and ts_start=#{tsStart}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsFinish)">and ts_finish=#{tsFinish}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsPersonnel)">and ts_personnel=#{tsPersonnel}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsUseday)">and ts_useday=#{tsUseday}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsUsetime)">and ts_usetime=#{tsUsetime}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsInfo)">and ts_info=#{tsInfo}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsYoutubeCode)">and ts_youtube_code=#{tsYoutubeCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsAppend)">and ts_append=#{tsAppend}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWriteId)">and ts_write_id=#{tsWriteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsUdateId)">and ts_udate_id=#{tsUdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWritdate)">and ts_writdate=#{tsWritdate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsMale)">and ts_male=#{tsMale}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsFemale)">and ts_female=#{tsFemale}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts10s)">and ts_10s=#{ts10s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts20s)">and ts_20s=#{ts20s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts30s)">and ts_30s=#{ts30s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts40s)">and ts_40s=#{ts40s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts50s)">and ts_50s=#{ts50s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(ts60s)">and ts_60s=#{ts60s}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsRecreation)">and ts_recreation=#{tsRecreation}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsEpicurism)">and ts_epicurism=#{tsEpicurism}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsExperience)">and ts_experience=#{tsExperience}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCulture)">and ts_culture=#{tsCulture}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsLeports)">and ts_leports=#{tsLeports}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsOther)">and ts_other=#{tsOther}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsAlone)">and ts_alone=#{tsAlone}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsFriend)">and ts_friend=#{tsFriend}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCouple)">and ts_couple=#{tsCouple}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsParents)">and ts_parents=#{tsParents}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsChild)">and ts_child=#{tsChild}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsGroup)">and ts_group=#{tsGroup}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCategory1)">and ts_category1=#{tsCategory1}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCategory2)">and ts_category2=#{tsCategory2}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCategory3)">and ts_category3=#{tsCategory3}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCategory4)">and ts_category4=#{tsCategory4}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCategory5)">and ts_category5=#{tsCategory5}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsPets)">and ts_pets=#{tsPets}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsParking)">and ts_parking=#{tsParking}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsDisabled)">and ts_disabled=#{tsDisabled}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsNursing)">and ts_nursing=#{tsNursing}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsStroller)">and ts_stroller=#{tsStroller}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsCredit)">and ts_credit=#{tsCredit}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWeatherRain)">and ts_weather_rain=#{tsWeatherRain}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWeatherSnow)">and ts_weather_snow=#{tsWeatherSnow}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWeatherFog)">and ts_weather_fog=#{tsWeatherFog}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsWeatherGale)">and ts_weather_gale=#{tsWeatherGale}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsSeasonSpring)">and ts_season_spring=#{tsSeasonSpring}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsSeasonSummer)">and ts_season_summer=#{tsSeasonSummer}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsSeasonAutumn)">and ts_season_autumn=#{tsSeasonAutumn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsSeasonWinter)">and ts_season_winter=#{tsSeasonWinter}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsEditdate)">and ts_editdate=#{tsEditdate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsEnrolChk)">and ts_enrol_chk=#{tsEnrolChk}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(areaType)">and a.area_type=#{areaType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(placeType)">and a.place_type=#{placeType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(useYn)">and use_yn=#{useYn}	</if>
		</where>
	</select>
    <!--################################### place_spot_image ###################################-->
	<select id="selectCountPlaceSpotImage" parameterType="HashMap" resultType="Integer">
		SELECT count(*)
		  FROM place_spot_image
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsId)" >	and ts_id=#{tsId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>

		</where>
	</select>

	<select id="selectListPlaceSpotImage" parameterType="HashMap" resultType="PlaceSpotImage">
	SELECT * FROM (
		SELECT @rownum:=@rownum+1 AS rownum,
		       file_id, ts_id, service_type, upload_path, upload_filename, file_extension, file_size, file_mimetype, origin_filename, upload_id, upload_date, sort_order
		  FROM place_spot_image
		  join (SELECT @rownum:= 0) rnum
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsId)" >	and ts_id=#{tsId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>
		</where>
		ORDER BY sort_order asc) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>

	<select id="selectOnePlaceSpotImage" parameterType="HashMap" resultType="PlaceSpotImage">
		SELECT file_id, ts_id, service_type, upload_path, upload_filename, file_extension, file_size, file_mimetype, origin_filename, upload_id, upload_date
		  FROM place_spot_image
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileId)" >	and file_id=#{fileId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tsId)" >	and ts_id=#{tsId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(serviceType)" >	and service_type=#{serviceType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadPath)" >	and upload_path=#{uploadPath}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadFilename)" >	and upload_filename=#{uploadFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileExtension)" >	and file_extension=#{fileExtension}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileSize)" >	and file_size=#{fileSize}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(fileMimetype)" >	and file_mimetype=#{fileMimetype}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(originFilename)" >	and origin_filename=#{originFilename}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadId)" >	and upload_id=#{uploadId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(uploadDate)" >	and upload_date=#{uploadDate}	</if>
		</where>
	</select>

	<!--################################### setting_alliance_contents ###################################-->

	<select id="selectListAllianceContents" parameterType="map" resultType="SettingAllianceContents">
		SELECT *
		FROM(
		SELECT @rownum:=@rownum+1 AS rownum,
				ac.id, reg_date, title, IFNULL(link, '') AS link, use_yn, delete_yn
		        , delete_id, delete_date, create_id, create_date, last_update_id, last_update_date
				, IFNULL(ai.origin_filename, '') AS origin_filename
				, ai.upload_filename
		FROM setting_alliance_contents AS ac
		LEFT JOIN setting_alliance_image AS ai on ai.id = ac.id
		join (SELECT @rownum:= 0) rnum
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and ac.id = #{id}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(titleLike)">and title like concat('%',#{titleLike},'%')</if>
		</where>

		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
			ORDER BY
			<choose>
				<when test="sort == 'id'">id</when>
				<when test="sort == 'regDate'">reg_date</when>
				<when test="sort == 'title'">title</when>
				<when test="sort == 'link'">link</when>
				<when test="sort == 'originFilename'">origin_filename</when>
				<when test="sort == 'useYn'">use_yn</when>
				<when test="sort == 'deleteYn'">delete_yn</when>
				<when test="sort == 'lastUpdateDate'">last_update_date</when>
				<otherwise>rownum</otherwise>
			</choose>
			<choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
			ORDER BY <foreach item="item" index="index" collection="listSort" separator=",">
			<choose>
				<when test="item.sort == 'id'">id</when>
				<when test="item.sort == 'reg_date'">reg_date</when>
				<when test="item.sort == 'title'">title</when>
				<when test="item.sort == 'link'">link</when>
				<when test="item.sort == 'origin_filename'">origin_filename</when>
				<when test="item.sort == 'use_yn'">use_yn</when>
				<when test="item.sort == 'delete_yn'">delete_yn</when>
				<when test="item.sort == 'last_update_date'">last_update_date</when>
			</choose>
			<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
		</if>
		) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
			LIMIT #{itemStartPosition}, #{pagePerSize}
		</if>
	</select>

	<!--################################### UserCustomerPayment ###################################-->
	<insert id="insertUserCustomerPayment" parameterType="UserCustomerPayment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_customer_payment
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	reservation_id=#{reservationId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<choose>
				<when test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payDate)">pay_date=#{payDate},</when>
				<otherwise>pay_date=now(),</otherwise>
			</choose>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	pay_moid=#{payMoid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payDivision)" >	pay_division=#{payDivision},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMethod)" >	pay_method=#{payMethod},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payComment)" >	pay_comment=#{payComment},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payAmount)" >	pay_amount=#{payAmount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mid)" >	mid=#{mid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tid)" >	tid=#{tid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusCode)" >	status_code=#{statusCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusName)" >	status_name=#{statusName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			create_date = now()
		</set>
    </insert>

    <update id="updateUserCustomerPayment" parameterType="UserCustomerPayment">
        UPDATE user_customer_payment
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	reservation_id=#{reservationId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payDate)" >	pay_date=#{payDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payDivision)" >	pay_division=#{payDivision},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMethod)" >	pay_method=#{payMethod},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payComment)" >	pay_comment=#{payComment},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payAmount)" >	pay_amount=#{payAmount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mid)" >	mid=#{mid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tid)" >	tid=#{tid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusCode)" >	status_code=#{statusCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusName)" >	status_name=#{statusName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and id = #{id}</if>
		</where>
    </update>

    <update id="restoreUserCustomerPayment" parameterType="UserCustomerPayment">
        UPDATE user_customer_payment
           SET delete_yn='N'
        WHERE id = #{id}
    </update>

    <update id="deleteUserCustomerPayment" parameterType="UserCustomerPayment">
        UPDATE user_customer_payment
           SET delete_yn='Y'
        WHERE id = #{id}
    </update>
	<!--################################### userCustomerOrder ###################################-->
	 <select id="selectCountUserCustomerOrder" parameterType="HashMap" resultType="Integer">
        SELECT count(pay_moid)
          FROM (
			SELECT pay_moid
			FROM user_customer_order a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid=#{payMoid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	and product_amt=#{productAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	and product_delivery_amt=#{productDeliveryAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	and product_count=#{productCount}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	and product_status=#{productStatus}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderStatus)" >	and order_status=#{orderStatus}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	and order_date=#{orderDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderTime)" >	and order_Time=#{orderTime}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCode)" >	and vbank_code=#{bankCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankName)" >	and vbank_name=#{vbankName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankNum)" >	and vbank_num=#{vbankNum}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpDate)" >	and vbank_exp_date=#{vbankExpDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpTime)" >	and vbank_exp_time=#{vbankExpTime}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankCode)" >	and vbank_cancel_bank_code=#{vbankCancelBankCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankNum)" >	and vbank_cancel_bank_num=#{vbankCancelBankNum}	</if>

         </where>) a
    </select>

	<select id="selectListUserCustomerOrder" parameterType="HashMap" resultType="UserCustomerOrder">
		SELECT *
		  FROM(
	        SELECT @rownum:=@rownum+1 AS rownum,
					a.*,
					b.product_title,
					b.product_thumbnail,
					(case when mu.upper_menu_id is not null then (select umu.menu_url from menu_user umu where umu.menu_id = mu.upper_menu_id ) else mu.menu_url end) menu_url
			  FROM user_customer_order a
			  left join product_tour b on a.product_serial  = b.product_serial and a.product_tour_id  = b.product_tour_id
			  LEFT JOIN menu_user mu on mu.menu_id = b.product_menu_id
			  join (SELECT @rownum:= 0) rnum
	         <where>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid=#{payMoid}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	and product_amt=#{productAmt}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	and product_delivery_amt=#{productDeliveryAmt}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	and product_count=#{productCount}	</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	and product_status=#{productStatus}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderStatus)" >	and order_status=#{orderStatus}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	and order_date=#{orderDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderTime)" >	and order_Time=#{orderTime}	</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>

				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCode)" >	and vbank_code=#{bankCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankName)" >	and vbank_name=#{vbankName}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankNum)" >	and vbank_num=#{vbankNum}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpDate)" >	and vbank_exp_date=#{vbankExpDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpTime)" >	and vbank_exp_time=#{vbankExpTime}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankCode)" >	and vbank_cancel_bank_code=#{vbankCancelBankCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankNum)" >	and vbank_cancel_bank_num=#{vbankCancelBankNum}	</if>
	         </where>

	         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort=='travelScheduleDt'" >	travel_schedule_dt	</when>
		            <when test="sort=='productTitle'" >	p.product_title	</when>
		            <when test="sort=='id'" >	id	</when>
					<when test="sort=='userEmail'" >	user_email	</when>
					<when test="sort=='userName'" >	user_name	</when>
					<when test="sort=='userMobile'" >	user_mobile	</when>
					<when test="sort=='berthType'" >	berth_type	</when>
					<when test="sort=='productSerial'" >	product_serial	</when>
					<when test="sort=='applyCode'" >	apply_code	</when>
					<when test="sort=='reservationCode'" >	reservation_code	</when>
					<when test="sort=='cancelYn'" >	cancel_yn	</when>
					<when test="sort=='cancelCode'" >	cancel_code	</when>
					<when test="sort=='berthJson'" >	berth_json	</when>
					<when test="sort=='travelScheduleJson'" >	travel_schedule_json	</when>
					<when test="sort=='airTypeRequest'" >	air_type_request	</when>
					<when test="sort=='airScheduleJson'" >	air_schedule_json	</when>
					<when test="sort=='vehicleType'" >	vehicle_type	</when>
					<when test="sort=='vehicleCount'" >	vehicle_count	</when>
					<when test="sort=='vehicleJson'" >	vehicle_json	</when>
					<when test="sort=='totalAmount'" >	total_amount	</when>
					<when test="sort=='createId'" >	create_id	</when>
					<when test="sort=='createDate'" >	create_date	</when>
					<when test="sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="sort=='deleteYn'" >	delete_yn	</when>
					<when test="sort=='deleteId'" >	delete_id	</when>
					<when test="sort=='deleteDate'" >	delete_date	</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort=='travelScheduleDt'" >	travel_schedule_dt	</when>
		            <when test="item.sort=='productTitle'" >	p.product_title	</when>
		            <when test="item.sort=='id'" >	id	</when>
					<when test="item.sort=='userEmail'" >	user_email	</when>
					<when test="item.sort=='userName'" >	user_name	</when>
					<when test="item.sort=='userMobile'" >	user_mobile	</when>
					<when test="item.sort=='berthType'" >	berth_type	</when>
					<when test="item.sort=='productSerial'" >	product_serial	</when>
					<when test="item.sort=='applyCode'" >	apply_code	</when>
					<when test="item.sort=='reservationCode'" >	reservation_code	</when>
					<when test="item.sort=='cancelYn'" >	cancel_yn	</when>
					<when test="item.sort=='cancelCode'" >	cancel_code	</when>
					<when test="item.sort=='berthJson'" >	berth_json	</when>
					<when test="item.sort=='travelScheduleJson'" >	travel_schedule_json	</when>
					<when test="item.sort=='airTypeRequest'" >	air_type_request	</when>
					<when test="item.sort=='airScheduleJson'" >	air_schedule_json	</when>
					<when test="item.sort=='vehicleType'" >	vehicle_type	</when>
					<when test="item.sort=='vehicleCount'" >	vehicle_count	</when>
					<when test="item.sort=='vehicleJson'" >	vehicle_json	</when>
					<when test="item.sort=='totalAmount'" >	total_amount	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='deleteId'" >	delete_id	</when>
					<when test="item.sort=='deleteDate'" >	delete_date	</when>

		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>
	<select id="selectOneUserCustomerOrder" parameterType="HashMap" resultType="UserCustomerOrder">
         SELECT @rownum:=@rownum+1 AS rownum,
        		a.*
          FROM user_customer_order a
          join (SELECT @rownum:= 0) rnum
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid=#{payMoid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and reservation_id=#{reservationId}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	and product_amt=#{productAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	and product_delivery_amt=#{productDeliveryAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	and product_count=#{productCount}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	and product_status=#{productStatus}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderStatus)" >	and order_status=#{orderStatus}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderStatusName)" >	and order_status_name=#{orderStatusName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	and order_date=#{orderDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderTime)" >	and order_Time=#{orderTime}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCode)" >	and vbank_code=#{bankCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankName)" >	and vbank_name=#{vbankName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankNum)" >	and vbank_num=#{vbankNum}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpDate)" >	and vbank_exp_date=#{vbankExpDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpTime)" >	and vbank_exp_time=#{vbankExpTime}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankCode)" >	and vbank_cancel_bank_code=#{vbankCancelBankCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankNum)" >	and vbank_cancel_bank_num=#{vbankCancelBankNum}	</if>
         </where>
	</select>

	<insert id="insertUserCustomerOrder" parameterType="UserCustomerOrder">
        INSERT INTO user_customer_order
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	pay_moid=#{payMoid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	reservation_id=#{reservationId},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	product_amt=#{productAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	product_delivery_amt=#{productDeliveryAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	product_count=#{productCount},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderStatus)" >	order_status=#{orderStatus},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderStatusName)" >	order_status_name=#{orderStatusName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	order_date=#{orderDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(optionTime)" >	option_time=#{optionTime},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultCode)" >	status_result_code = #{statusResultCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultMsg)" >	status_result_msg = #{statusResultMsg},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(method)" >	method = #{method},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(macketId)" >	macket_id = #{macketId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mid)" >	mid = #{mid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tid)" >	tid = #{tid},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCode)" >	vbank_code=#{bankCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankName)" >	vbank_name=#{vbankName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankNum)" >	vbank_num=#{vbankNum},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpDate)" >	vbank_exp_date=#{vbankExpDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpTime)" >	vbank_exp_time=#{vbankExpTime},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankCode)" >	vbank_cancel_bank_code=#{vbankCancelBankCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankNum)" >	vbank_cancel_bank_num=#{vbankCancelBankNum},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payType)" >	pay_type=#{payType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMethod)" >	pay_method=#{payMethod},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tossPaymentJson)" >	toss_payment_json=#{tossPaymentJson},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			create_date = now()
		</set>
    </insert>

    <update id="updateUserCustomerOrder" parameterType="UserCustomerOrder">
        UPDATE user_customer_order
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	product_amt=#{productAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	product_delivery_amt=#{productDeliveryAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	product_count=#{productCount},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderStatus)" >	order_status=#{orderStatus},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderStatusName)" >	order_status_name=#{orderStatusName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	order_date=#{orderDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(optionTime)" >	option_time=#{optionTime},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultCode)" >	status_result_code = #{statusResultCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultMsg)" >	status_result_msg = #{statusResultMsg},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(method)" >	method = #{method},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(macketId)" >	macket_id = #{macketId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(mid)" >	mid = #{mid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tid)" >	tid = #{tid},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCode)" >	vbank_code=#{bankCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankName)" >	vbank_name=#{vbankName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankNum)" >	vbank_num=#{vbankNum},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpDate)" >	vbank_exp_date=#{vbankExpDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankExpTime)" >	vbank_exp_time=#{vbankExpTime},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankCode)" >	vbank_cancel_bank_code=#{vbankCancelBankCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(vbankCancelBankNum)" >	vbank_cancel_bank_num=#{vbankCancelBankNum},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payType)" >	pay_type=#{payType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMethod)" >	pay_method=#{payMethod},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(tossPaymentJson)" >	toss_payment_json=#{tossPaymentJson},	</if>

			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)">and pay_moid=#{payMoid}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >and reservation_id=#{reservationId}	</if>
		</where>
    </update>

    <update id="restoreUserCustomerOrder" parameterType="UserCustomerOrder">
        UPDATE user_customer_order_list
           SET delete_yn='N'
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)">and pay_moid=#{payMoid}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >and user_email=#{userEmail}</if>
		</where>
    </update>

    <update id="deleteUserCustomerOrder" parameterType="UserCustomerOrder">
        UPDATE user_customer_order_list
           SET delete_yn='Y'
        <where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)">and pay_moid=#{payMoid}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >and user_email=#{userEmail}</if>
		</where>
    </update>
	<!--################################### userCustomerOrderList ###################################-->
	 <select id="selectCountUserCustomerOrderList" parameterType="HashMap" resultType="Integer">
        SELECT count(pay_product_seq)
          FROM (
			SELECT pay_product_seq
			FROM user_customer_order_list a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payProductSeq)" >	and pay_product_seq=#{payProductSeq}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid=#{payMoid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_serial=#{productSerial}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	and product_tour_id=#{productTourId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(priceOptionId)" >	and price_option_id=#{priceOptionId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productName)" >	and product_name=#{productName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	and product_count=#{productCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	and product_amt=#{productAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	and product_delivery_amt=#{productDeliveryAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	and product_status=#{productStatus}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	and order_date=#{orderDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
         </where>) a
    </select>

    <select id="selectSummryUserCustomerOrderList" parameterType="HashMap" resultType="Integer">
        SELECT ifnull(sum(product_count),0)
          FROM (
			SELECT product_count
			FROM user_customer_order_list a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payProductSeq)" >	and pay_product_seq=#{payProductSeq}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid=#{payMoid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_serial=#{productSerial}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	and product_tour_id=#{productTourId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(priceOptionId)" >	and price_option_id=#{priceOptionId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productName)" >	and product_name=#{productName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	and product_count=#{productCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	and product_amt=#{productAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	and product_delivery_amt=#{productDeliveryAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	and product_status=#{productStatus}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	and order_date=#{orderDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
         </where>) a
    </select>

	<select id="selectListUserCustomerOrderList" parameterType="HashMap" resultType="UserCustomerOrderList">
		SELECT *
		  FROM(
	        SELECT @rownum:=@rownum+1 AS rownum,
					a.*
					, c.option_name
					, b.product_title
					, b.product_thumbnail
					, (case when mu.upper_menu_id is not null then (select umu.menu_url from menu_user umu where umu.menu_id = mu.upper_menu_id ) else mu.menu_url end) menu_url
					, pc.category_title subCategoryTitle
					, pc.category_class subCategoryClass
			  FROM user_customer_order_list a
			  left join product_tour b on a.product_serial  = b.product_serial and a.product_tour_id  = b.product_tour_id
			  left join product_tour_price_option c on a.product_tour_id  = c.product_tour_id and a.price_option_id = c.price_option_id
			  LEFT JOIN product_common_category pc on pc.product_menu_id = b.product_menu_id AND pc.product_category_id = b.product_category_id
			  LEFT JOIN menu_user mu on mu.menu_id = b.product_menu_id
			  join (SELECT @rownum:= 0) rnum
	         <where>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and a.reservation_id=#{reservationId}	</if>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payProductSeq)" >	and a.pay_product_seq=#{payProductSeq}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and a.user_email=#{userEmail}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and a.pay_moid=#{payMoid}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and a.product_serial=#{productSerial}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	and a.product_tour_id=#{productTourId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productName)" >	and a.product_name=#{productName}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	and a.product_count=#{productCount}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	and a.product_amt=#{productAmt}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	and a.product_delivery_amt=#{productDeliveryAmt}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	and a.product_status=#{productStatus}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelProductCount)" >	and a.cancel_product_count=#{cancelProductCount}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and a.create_id=#{createId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and a.create_date=#{createDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and a.last_update_id=#{lastUpdateId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and a.last_update_date=#{lastUpdateDate}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and a.delete_yn=#{deleteYn}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and a.delete_id=#{deleteId}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and a.delete_date=#{deleteDate}	</if>
	         </where>

	         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort=='travelScheduleDt'" >	travel_schedule_dt	</when>
		            <when test="sort=='productTitle'" >	p.product_title	</when>
		            <when test="sort=='id'" >	id	</when>
					<when test="sort=='userEmail'" >	user_email	</when>
					<when test="sort=='userName'" >	user_name	</when>
					<when test="sort=='userMobile'" >	user_mobile	</when>
					<when test="sort=='berthType'" >	berth_type	</when>
					<when test="sort=='productSerial'" >	product_serial	</when>
					<when test="sort=='applyCode'" >	apply_code	</when>
					<when test="sort=='reservationCode'" >	reservation_code	</when>
					<when test="sort=='cancelYn'" >	cancel_yn	</when>
					<when test="sort=='cancelCode'" >	cancel_code	</when>
					<when test="sort=='berthJson'" >	berth_json	</when>
					<when test="sort=='travelScheduleJson'" >	travel_schedule_json	</when>
					<when test="sort=='airTypeRequest'" >	air_type_request	</when>
					<when test="sort=='airScheduleJson'" >	air_schedule_json	</when>
					<when test="sort=='vehicleType'" >	vehicle_type	</when>
					<when test="sort=='vehicleCount'" >	vehicle_count	</when>
					<when test="sort=='vehicleJson'" >	vehicle_json	</when>
					<when test="sort=='totalAmount'" >	total_amount	</when>
					<when test="sort=='createId'" >	create_id	</when>
					<when test="sort=='createDate'" >	create_date	</when>
					<when test="sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="sort=='deleteYn'" >	delete_yn	</when>
					<when test="sort=='deleteId'" >	delete_id	</when>
					<when test="sort=='deleteDate'" >	delete_date	</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort=='travelScheduleDt'" >	travel_schedule_dt	</when>
		            <when test="item.sort=='productTitle'" >	p.product_title	</when>
		            <when test="item.sort=='id'" >	id	</when>
					<when test="item.sort=='userEmail'" >	user_email	</when>
					<when test="item.sort=='userName'" >	user_name	</when>
					<when test="item.sort=='userMobile'" >	user_mobile	</when>
					<when test="item.sort=='berthType'" >	berth_type	</when>
					<when test="item.sort=='productSerial'" >	product_serial	</when>
					<when test="item.sort=='applyCode'" >	apply_code	</when>
					<when test="item.sort=='reservationCode'" >	reservation_code	</when>
					<when test="item.sort=='cancelYn'" >	cancel_yn	</when>
					<when test="item.sort=='cancelCode'" >	cancel_code	</when>
					<when test="item.sort=='berthJson'" >	berth_json	</when>
					<when test="item.sort=='travelScheduleJson'" >	travel_schedule_json	</when>
					<when test="item.sort=='airTypeRequest'" >	air_type_request	</when>
					<when test="item.sort=='airScheduleJson'" >	air_schedule_json	</when>
					<when test="item.sort=='vehicleType'" >	vehicle_type	</when>
					<when test="item.sort=='vehicleCount'" >	vehicle_count	</when>
					<when test="item.sort=='vehicleJson'" >	vehicle_json	</when>
					<when test="item.sort=='totalAmount'" >	total_amount	</when>
					<when test="item.sort=='createId'" >	create_id	</when>
					<when test="item.sort=='createDate'" >	create_date	</when>
					<when test="item.sort=='lastUpdateId'" >	last_update_id	</when>
					<when test="item.sort=='lastUpdateDate'" >	last_update_date	</when>
					<when test="item.sort=='deleteYn'" >	delete_yn	</when>
					<when test="item.sort=='deleteId'" >	delete_id	</when>
					<when test="item.sort=='deleteDate'" >	delete_date	</when>

		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>
	<select id="selectOneUserCustomerOrderList" parameterType="HashMap" resultType="UserCustomerOrderList">
         SELECT @rownum:=@rownum+1 AS rownum,
        		a.*
          FROM user_customer_order_list a
          join (SELECT @rownum:= 0) rnum
         <where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	and a.reservation_id=#{reservationId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payProductSeq)" >	and pay_product_seq=#{payProductSeq}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email=#{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid=#{payMoid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	and product_serial=#{productSerial}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	and product_tour_id=#{productTourId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(priceOptionId)" >	and price_option_id=#{priceOptionId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productName)" >	and product_name=#{productName}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	and product_count=#{productCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	and product_amt=#{productAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	and product_delivery_amt=#{productDeliveryAmt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	and product_status=#{productStatus}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelProductCount)" >	and cancel_product_count=#{cancelProductCount}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	and create_id=#{createId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createDate)" >	and create_date=#{createDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	and last_update_id=#{lastUpdateId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateDate)" >	and last_update_date=#{lastUpdateDate}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)" >	and delete_yn=#{deleteYn}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteId)" >	and delete_id=#{deleteId}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteDate)" >	and delete_date=#{deleteDate}	</if>
         </where>
	</select>

	<insert id="insertUserCustomerOrderList" parameterType="UserCustomerOrderList" useGeneratedKeys="true" keyProperty="payProductSeq">
        INSERT INTO user_customer_order_list
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payProductSeq)" >	pay_product_seq=#{payProductSeq},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	reservation_id =#{reservationId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	pay_moid=#{payMoid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	product_serial=#{productSerial},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	product_tour_id=#{productTourId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(priceOptionId)" >	price_option_id=#{priceOptionId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productName)" >	product_name=#{productName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productPrice)" >	product_price=#{productPrice},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	product_count=#{productCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	product_amt=#{productAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	product_delivery_amt=#{productDeliveryAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	product_status=#{productStatus},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	order_date=#{orderDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(optionPack)" >	option_pack=#{optionPack},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(optionTime)" >	option_time=#{optionTime},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelProductCount)" >	cancel_product_count=#{cancelProductCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(createId)" >	create_id=#{createId},	</if>
			create_date = now()
		</set>
    </insert>

    <update id="updateUserCustomerOrderList" parameterType="UserCustomerOrderList">
        UPDATE user_customer_order_list
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payProductSeq)" >	pay_product_seq=#{payProductSeq},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	reservation_id =#{reservationId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	pay_moid=#{payMoid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	product_serial=#{productSerial},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	product_tour_id=#{productTourId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productName)" >	product_name=#{productName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productPrice)" >	product_price=#{productPrice},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	product_count=#{productCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	product_amt=#{productAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	product_delivery_amt=#{productDeliveryAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	product_status=#{productStatus},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	order_date=#{orderDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(optionPack)" >	option_pack=#{optionPack},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(optionTime)" >	option_time=#{optionTime},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelProductCount)" >	cancel_product_count=#{cancelProductCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payProductSeq)" > and pay_product_seq=#{payProductSeq}</if>
		</where>
    </update>

    <update id="updateAllUserCustomerOrderList" parameterType="UserCustomerOrderList">
        UPDATE user_customer_order_list
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(reservationId)" >	reservation_id =#{reservationId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productSerial)" >	product_serial=#{productSerial},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productTourId)" >	product_tour_id=#{productTourId},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productName)" >	product_name=#{productName},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productPrice)" >	product_price=#{productPrice},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productCount)" >	product_count=#{productCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productAmt)" >	product_amt=#{productAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productDeliveryAmt)" >	product_delivery_amt=#{productDeliveryAmt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(productStatus)" >	product_status=#{productStatus},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(orderDate)" >	order_date=#{orderDate},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(optionPack)" >	option_pack=#{optionPack},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(optionTime)" >	option_time=#{optionTime},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(cancelProductCount)" >	cancel_product_count=#{cancelProductCount},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(lastUpdateId)" >	last_update_id=#{lastUpdateId},	</if>
			last_update_date = now()
		</set>
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	pay_moid=#{payMoid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email=#{userEmail},	</if>
		</where>
    </update>

    <update id="restoreUserCustomerOrderList" parameterType="UserCustomerOrderList">
        UPDATE user_customer_order_list
           SET delete_yn='N'
        WHERE id = #{id}
    </update>

    <update id="deleteUserCustomerOrderList" parameterType="UserCustomerOrderList">
        UPDATE user_customer_order_list
           SET delete_yn='Y'
        WHERE id = #{id}
    </update>

	<!--################################### userCustomerOrderHistory ###################################-->
	 <select id="selectCountUserCustomerOrderHistory" parameterType="HashMap" resultType="Integer">
        SELECT count(history_seq)
          FROM (
			SELECT history_seq
			FROM user_customer_order_history a
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(historySeq)" >	and hisoty_seq = #{histroySeq}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email = #{userEmail}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid = #{payMoid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payStatusType)" >	and pay_status_type = #{payStatusType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payTid)" >	and pay_tid = #{payTid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(method)" >	and method = #{method}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(amt)" >	and amt = #{amt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultCode)" >	and status_result_code = #{statusResultCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultMsg)" >	and status_result_msg = #{statusResultMsg}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payStatusDttm)" >	and pay_status_dttm = #{payStatusDttm}	</if>
         </where>) a
    </select>

	<select id="selectListUserCustomerOrderHistory" parameterType="HashMap" resultType="userCustomerOrderHistory">
		SELECT *
		  FROM(
	        SELECT @rownum:=@rownum+1 AS rownum,
					a.*,
					b.product_title,
					b.product_thumbnail,
					(case when mu.upper_menu_id is not null then (select umu.menu_url from menu_user umu where umu.menu_id = mu.upper_menu_id ) else mu.menu_url end) menu_url
			  FROM user_customer_order_history a
			  left join product_tour b on a.product_serial  = b.product_serial and a.product_tour_id  = b.product_tour_id
			  LEFT JOIN menu_user mu on mu.menu_id = b.product_menu_id
			  join (SELECT @rownum:= 0) rnum
	         <where>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(historySeq)" >	and hisoty_seq = #{histroySeq}	</if>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email = #{userEmail}	</if>
	         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid = #{payMoid}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payStatusType)" >	and pay_status_type = #{payStatusType}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payTid)" >	and pay_tid = #{payTid}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(method)" >	and method = #{method}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(amt)" >	and amt = #{amt}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultCode)" >	and status_result_code = #{statusResultCode}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultMsg)" >	and status_result_msg = #{statusResultMsg}	</if>
				<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payStatusDttm)" >	and pay_status_dttm = #{payStatusDttm}	</if>
	         </where>

	         <if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
		    	ORDER BY
		        <choose>
		            <when test="sort=='historySeq'" >	hisoty_seq	</when>
		            <when test="sort=='payStatusType'" >	pay_status_type	</when>
		            <when test="sort=='payTid'" >	pay_tid	</when>
					<when test="sort=='method'" >	method	</when>
					<when test="sort=='amt'" >	amt	</when>
					<when test="sort=='statusResultCode'" >	status_result_code	</when>
					<when test="sort=='statusResultMsg'" >	status_result_msg	</when>
					<when test="sort=='payStatusDttm'" >	pay_status_dttm	</when>
		            <otherwise>rownum</otherwise>
		        </choose>
		        <choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
			</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
		    	ORDER BY  <foreach item="item" index="index" collection="listSort" separator=",">
		    	<choose>
		            <when test="item.sort=='historySeq'" >	hisoty_seq	</when>
		            <when test="item.sort=='payStatusType'" >	pay_status_type	</when>
		            <when test="item.sort=='payTid'" >	pay_tid	</when>
					<when test="item.sort=='method'" >	method	</when>
					<when test="item.sort=='amt'" >	amt	</when>
					<when test="item.sort=='statusResultCode'" >	status_result_code	</when>
					<when test="item.sort=='statusResultMsg'" >	status_result_msg	</when>
					<when test="item.sort=='payStatusDttm'" >	pay_status_dttm	</when>
		        </choose>
		    	<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
			</if>
			) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
         LIMIT #{itemStartPosition}, #{pagePerSize}
         </if>
	</select>
	<select id="selectOneUserCustomerOrderHistory" parameterType="HashMap" resultType="userCustomerOrderHistory">
         SELECT @rownum:=@rownum+1 AS rownum,
        		a.*
          FROM user_customer_order_history a
          join (SELECT @rownum:= 0) rnum
         <where>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(historySeq)" >	and hisoty_seq = #{historySeq}	</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	and user_email = #{userEmail}	</if>
         	<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	and pay_moid = #{payMoid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payStatusType)" >	and pay_status_type = #{payStatusType}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payTid)" >	and pay_tid = #{payTid}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(method)" >	and method = #{method}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(amt)" >	and amt = #{amt}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultCode)" >	and status_result_code = #{statusResultCode}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultMsg)" >	and status_result_msg = #{statusResultMsg}	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payStatusDttm)" >	and pay_status_dttm = #{payStatusDttm}	</if>
         </where>
	</select>

	<insert id="insertUserCustomerOrderHistory" parameterType="userCustomerOrderHistory" useGeneratedKeys="true" keyProperty="historySeq">
        INSERT INTO user_customer_order_history
        <set>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(userEmail)" >	user_email = #{userEmail},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payTid)" >	pay_tid = #{payTid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payMoid)" >	pay_moid = #{payMoid},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(payStatusType)" >	pay_status_type = #{payStatusType},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(method)" >	method = #{method},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(amt)" >	amt = #{amt},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultCode)" >	status_result_code = #{statusResultCode},	</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(statusResultMsg)" >	status_result_msg = #{statusResultMsg},	</if>
			pay_status_dttm = now()
		</set>
    </insert>

    <select id="selectCountAwardsContents" parameterType="map" resultType="int">
		SELECT COUNT(*)
		FROM setting_awards_contents AS ac
		LEFT JOIN setting_awards_image AS ai on ai.id = ac.id
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and ac.id = #{id}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(titleLike)">and title like concat('%',#{titleLike},'%')</if>
		</where>
	</select>
	<!-- AwardsContents -->
	<select id="selectListAwardsContents" parameterType="map" resultType="SettingAwardsContents">
		SELECT *
		FROM(
		SELECT @rownum:=@rownum+1 AS rownum,
		ac.id, reg_date, type, title, use_yn, delete_yn
		, delete_id, delete_date, create_id, create_date, last_update_id, last_update_date
		, IFNULL(ai.origin_filename, '') AS origin_filename
		, ai.upload_filename
		FROM setting_awards_contents AS ac
		LEFT JOIN setting_awards_image AS ai on ai.id = ac.id
		join (SELECT @rownum:= 0) rnum
		<where>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(id)">and ac.id = #{id}</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(titleLike)">and title like concat('%',#{titleLike},'%')</if>
			<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(deleteYn)">and delete_yn = #{deleteYn}</if>
		</where>

		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sort) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(sortOrder)">
			ORDER BY
			<choose>
				<when test="sort == 'id'">id</when>
				<when test="sort == 'regDate'">reg_date</when>
				<when test="sort == 'title'">title</when>
				<when test="sort == 'originFilename'">origin_filename</when>
				<when test="sort == 'useYn'">use_yn</when>
				<when test="sort == 'deleteYn'">delete_yn</when>
				<when test="sort == 'lastUpdateDate'">last_update_date</when>
				<otherwise>rownum</otherwise>
			</choose>
			<choose><when test="sortOrder == 'asc'">ASC</when><when test="sortOrder == 'desc'">DESC</when></choose>
		</if>
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(listSort)">
			ORDER BY <foreach item="item" index="index" collection="listSort" separator=",">
			<choose>
				<when test="item.sort == 'id'">id</when>
				<when test="item.sort == 'regDate'">reg_date</when>
				<when test="item.sort == 'title'">title</when>
				<when test="item.sort == 'originFilename'">origin_filename</when>
				<when test="item.sort == 'useYn'">use_yn</when>
				<when test="item.sort == 'deleteYn'">delete_yn</when>
				<when test="item.sort == 'lastUpdateDate'">last_update_date</when>
			</choose>
			<choose><when test="item.sortOrder == 'asc'">ASC</when><when test="item.sortOrder == 'desc'">DESC</when></choose></foreach>
		</if>
		) a
		<if test="@kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(itemStartPosition) and @kr.co.wayplus.travel.util.MybatisUtil@isNotEmpty(pagePerSize)">
			LIMIT #{itemStartPosition}, #{pagePerSize}
		</if>
	</select>
</mapper>