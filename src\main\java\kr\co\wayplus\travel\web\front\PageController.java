package kr.co.wayplus.travel.web.front;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.BoardAttachFile;
import kr.co.wayplus.travel.model.BoardCategory;
import kr.co.wayplus.travel.model.BoardContents;
import kr.co.wayplus.travel.model.BoardSetting;
import kr.co.wayplus.travel.model.CodeItem;
import kr.co.wayplus.travel.model.ContentsItem;
import kr.co.wayplus.travel.model.InquiryCategory;
import kr.co.wayplus.travel.model.InquiryContent;
import kr.co.wayplus.travel.model.Islandlife;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.MenuUser;
import kr.co.wayplus.travel.model.PagingDTO;
import kr.co.wayplus.travel.model.PlaceSpot;
import kr.co.wayplus.travel.model.ProductCategory;
import kr.co.wayplus.travel.model.ProductDetailSchedule;
import kr.co.wayplus.travel.model.ProductInfo;
import kr.co.wayplus.travel.model.ProductPriceOption;
import kr.co.wayplus.travel.model.ProductPriceOption.DayList;
import kr.co.wayplus.travel.model.ProductPriceOption.FixPriceList;
import kr.co.wayplus.travel.model.ProductTourImages;
import kr.co.wayplus.travel.model.Reservation;
import kr.co.wayplus.travel.model.SortData;
import kr.co.wayplus.travel.model.UserCustomerOrder;
import kr.co.wayplus.travel.model.UserCustomerOrderList;
import kr.co.wayplus.travel.model.UserPointRecord;
import kr.co.wayplus.travel.service.front.BoardService;
import kr.co.wayplus.travel.service.front.ContentsService;
import kr.co.wayplus.travel.service.front.MemberService;
import kr.co.wayplus.travel.service.front.MenuService;
import kr.co.wayplus.travel.service.front.PageService;
import kr.co.wayplus.travel.service.front.ProductService;
import kr.co.wayplus.travel.service.user.UserPointService;
import kr.co.wayplus.travel.service.user.UserService;

@Controller
public class PageController	extends BaseController {

	@Value("${cookie-set.domain}")
	private String cookieDomain;
	@Value("${cookie-set.prefix}")
	private String cookieName;
	@Value("${pg.tosspay.server.key}")
	private String pgTosspayServerKey;
	@Value("${spring.servlet.multipart.max-request-size}")
	String maxRequestSize;

	public static final String EXCLUDED_PATHS = "^(?!css|js|plugin|font|images|error|upload|file$).+";

	private final Logger logger = LoggerFactory.getLogger(getClass());

	private final PageService pageService;
	private final ProductService productService;
	private final BoardService boardService;
	private final MemberService memberService;
	private final UserService userService;
	private final UserPointService userPointService;
	private final ContentsService contentService;
	private final MenuService menuService;

	@Autowired
	public PageController(
			PageService pageService,
			ProductService productService,
			BoardService boardService,
			MemberService memberService,
			UserService userService,
			UserPointService userPointService,
			ContentsService contentService,
			MenuService menuService) {
		this.pageService = pageService;
		this.productService = productService;
		this.boardService = boardService;
		this.memberService = memberService;
		this.userService = userService;
		this.userPointService = userPointService;
		this.contentService = contentService;
		this.menuService = menuService;
	}
	/***** 정적 처리용 *****/
	@GetMapping(value = {"", "/", "/index"})
	public ModelAndView index(HttpServletRequest request){
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> param = new HashMap<>();
		param.put("useYn", "Y");
		param.put("bannerType", "MAIN");
		param.put("menuId", "0");
		mav.addObject("bannerList", pageService.selectListMainBannerImage(param));
		param.put("bannerType", "poster");
		param.put("menuId", "3");
        //mav.addObject("subBannerList", pageService.selectListMainBannerImageAndCategory(param));
        mav.addObject("rollingTopBannerList_poster", pageService.selectListMainBannerImage(param));

		param.put("isExpose", true);
		List<MenuUser> listExposeMenuUser = pageService.selectListMenuUser(param);
		mav.addObject("listExposeMenuUser", listExposeMenuUser);
		HashMap<String, Object> resultData = new HashMap<>();
		mav.addObject("exposeProduct", resultData);

		for (MenuUser menuUser : listExposeMenuUser) {
			resultData.put(menuUser.getMenuId()+ "_menu", menuUser);

			if( menuUser.getMenuType().equals("product")  ) {
				ArrayList<ProductInfo> productList = productService.selectListProductByMenuId( menuUser.getMenuId() );
				resultData.put(menuUser.getMenuId()+ "_mainProductList", productList);
				//resultData.put(menuUser.getMenuId()+ "_path", String.format( "front/inc/fragment :: %s", menuUser.getMainExposeType() ));
			}else if( menuUser.getMenuType().equals("board")  ) {
				BoardSetting boardSetting = boardService.selectOneBoardSettingByMenuId( menuUser.getMenuId() );
				if( boardSetting != null ) {
					param.put("boardId", boardSetting.getId() );
					param.put("deleteYn", "N");
					param.put("sort", "createDate");
					param.put("sortOrder", "desc");
					param.put("itemStartPosition", 0);
					param.put("pagePerSize", 3);
					ArrayList<BoardContents> boardContentsList = boardService.selectListBoardContents(param);
					resultData.put(menuUser.getMenuId()+ "_mainBoardList", boardContentsList);
				}
			}
		}

		param.clear();
        param.put("useYn", "Y");
        param.put("deleteYn", "N");
        param.put("typeCode", "mission");
        BoardSetting boardSetting = boardService.selectOneBoardSetting(param);
        param.put("boardId", boardSetting.getId() == null ? 0 : boardSetting.getId() );
        mav.addObject("missionMonthList", boardService.selectMostMissionMonth(param));
        mav.addObject("missionUserList", boardService.selectMostMissionUser(param));

		mav.setViewName("/front/index");
		return mav;
	}

	@GetMapping(value = {"/search"})
	public ModelAndView search(
			HttpServletRequest request,
			@RequestParam(value="searchKey", defaultValue=" ") String searchKey,
			@RequestParam(value="start", defaultValue="0") int start,
			@RequestParam(value="length", defaultValue="12") int length,
			@RequestParam(value="page", defaultValue="1") int page){
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> param = new HashMap<>();
		param.put("searchKey", searchKey);
		param.put("productUseYn", "Y");
		param.put("regcyYn", "N");
		param.put("deleteYn", "N");
		if ( searchKey.equals(" ") ) {
			mav.addObject("productLists",  new ArrayList<ProductInfo>());
		} else {
			//menu_type이 'program'인 최상위 메뉴 조회. 메뉴의 '프로그램' 메뉴가 컨텐츠형인데 이 하위에 프로그램형 이 있기때문.
			List<List<ProductInfo>> productLists = new ArrayList<>();
			param.put("menuType", "product");
			List<MenuUser> listProgramMenuUser = pageService.selectListMenuUserUpperMenuId(param);
			for (MenuUser menuUser : listProgramMenuUser) {
				param.put("upperMenuId", menuUser.getUpperMenuId());
				param.put("likeUserEmail", getBaseUserEmail());
				List<ProductInfo> productList = pageService.selectListProductByUpperMenuId(param);
				productLists.add(productList);
			}
			mav.addObject("productLists", productLists);
		}


		// int totalCount = productService.selectCountProduct( param );

		// PagingDTO paging = new PagingDTO(totalCount, page, 0, length);
		// param.put("itemStartPosition", paging.getItemStartPosition());
		// param.put("pagePerSize", paging.getPagePerSize());
		// ArrayList<ProductInfo> productList = productService.selectListProduct( param );

		// mav.addObject("productList", productList);
		// mav.addObject("paging", paging);

		mav.addObject("param", param);
		mav.setViewName("/front/search");
		return mav;
	}

	@GetMapping(value = "/terms/privacy")
	public ModelAndView termsPrivacy(){
		ModelAndView mav = new ModelAndView();

		mav.addObject("policy", pageService.selectOnePolicyByPolicyType( "4" ));
		mav.setViewName("/front/sub/terms/terms");
		return mav;
	}

	@GetMapping(value = "/terms/usage")
	public ModelAndView termsUsage(){
		ModelAndView mav = new ModelAndView();
		mav.addObject("policy", pageService.selectOnePolicyByPolicyType( "3" ));
		mav.setViewName("/front/sub/terms/terms");
		return mav;
	}

	@GetMapping("/settings/awards-list")
	@ResponseBody
	public HashMap<String, Object> awards_list_ajax(HttpServletRequest request,
													@RequestParam(value="start", defaultValue="0") int start,
													@RequestParam(value="length", defaultValue="12") int length,
													@Param(value="titleLike") String titleLike){
		HashMap<String, Object> retrunMap = new HashMap<>();

		try {
			HashMap<String, Object> paramMap = new HashMap<>();

			List<SortData> listSort = getListOrder(request);
			paramMap.put("listSort", listSort);

			if(length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
			}

			paramMap.put("titleLike", titleLike);
			paramMap.put("deleteYn", "N");

			int totalCount = pageService.selectCountAwardsContents(paramMap);

			retrunMap.put("recordsTotal", totalCount);
			retrunMap.put("recordsFiltered", totalCount);
			retrunMap.put("data", pageService.selectListAwardsContents(paramMap));

			retrunMap.put("result", "success");
			retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
			e.printStackTrace();
		}

		return retrunMap;
	}

//	<!--################################### CodeItem ###################################-->
	@GetMapping("/code-list")
	@ResponseBody
	public HashMap<String, Object> code_list_ajax(HttpServletRequest request,
			@ModelAttribute CodeItem ci,
			@RequestParam(value="start", defaultValue="0") int start,
			@RequestParam(value="length", defaultValue="15") int length,
			@Param(value="code") String code,
			@Param(value="useYn") String useYn
			){
		HashMap<String, Object> retrunMap = new HashMap<>();

		try {
			HashMap<String, Object> paramMap = new HashMap<>();

			List<SortData> listSort = getListOrder(request);
			paramMap.put("listSort", listSort);

			if(length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
			}

			paramMap.put("code", ci.getCode());
			paramMap.put("useYn", useYn);
			paramMap.put("upperCode", ci.getUpperCode());

			int totalCount = pageService.selectCountCodeItem(paramMap);

			retrunMap.put("recordsTotal", totalCount);
			retrunMap.put("recordsFiltered", totalCount);
			retrunMap.put("data", pageService.selectListCodeItem(paramMap));

			retrunMap.put("result", "success");
			retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

		return retrunMap;
	}

	@PostMapping("/spot-list")
	@ResponseBody
   	public HashMap<String, Object> placeSpot_list(
   			HttpServletRequest request,
   			PlaceSpot ts,
   			@RequestParam(value="start", defaultValue="0") int start,
    		@RequestParam(value="length", defaultValue="10") int length,
    		@RequestParam(value="page", defaultValue="1") int page,
    		@Param(value="spotId") String spotId,
    		@Param(value="titleLike") String titleLike,
    		@Param(value="contentLike") String contentLike){
   		HashMap<String, Object> resultMap = new HashMap<>();

   		try{
   			HashMap<String, Object> paramMap = new HashMap<>();

   			List<SortData> listSort = getListOrder(request);
   			paramMap.put("listSort", listSort);

   			if(ts.getAreaType() != null) paramMap.put("areaType", ts.getAreaType());
   			if(ts.getPlaceType() != null) paramMap.put("placeType", ts.getPlaceType());

   			paramMap.put("tsId", spotId);
			paramMap.put("titleLike", titleLike);
			paramMap.put("contentLike", contentLike);

   			int totalCount = pageService.selectCountPlaceSpot(paramMap);
   			PagingDTO paging = new PagingDTO(totalCount, page, 0, length);
   			if(length >= 0) {
   				paramMap.put("itemStartPosition", paging.getItemStartPosition());
   				paramMap.put("pagePerSize", paging.getPagePerSize());
   			}

   			ArrayList<PlaceSpot> lists = pageService.selectListPlaceSpot(paramMap);

   			resultMap.put("recordsTotal", totalCount);
   			resultMap.put("list", lists);
   			resultMap.put("paging", paging);

   			resultMap.put("result", "success");
   			resultMap.put("message", "처리되었습니다.");
   		}catch (Exception e){
   			logger.error(e.getMessage());
   			resultMap.put("result", "error");
//	   			resultMap.put("message", "처리중 오류가 발생하였습니다.");
   			resultMap.put("error", e.getMessage());
   			resultMap.put("message", e.getMessage());
   		}

   		return resultMap;
   	}

	@PostMapping("/island-life-list")
	@ResponseBody
	public HashMap<String, Object> lslandLifeList(
			HttpServletRequest request,
			@RequestParam(value="id", defaultValue="0") int id){
		HashMap<String, Object> resultMap = new HashMap<>();

		try{
			HashMap<String, Object> paramMap = new HashMap<>();

			paramMap.put("id", id);
			paramMap.put("useYn", "Y");
			paramMap.put("deleteYn", "N");

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
			resultMap.put("islandLifeList", userService.selectListIslandlife(paramMap));
		}catch (Exception e){
			logger.error(e.getMessage());
			resultMap.put("result", "error");
//	   			resultMap.put("message", "처리중 오류가 발생하였습니다.");
			resultMap.put("error", e.getMessage());
			resultMap.put("message", e.getMessage());
		}

		return resultMap;
	}


	/***** 동적 처리용 *****/
	@RequestMapping("/{path1:"+EXCLUDED_PATHS+"}/{path2}")
	public ModelAndView dynamicPath2(
			HttpServletRequest request

			, @PathVariable String path1
			, @PathVariable String path2

			, HashMap<String, Object> param

			, @RequestParam(value="action", defaultValue="") String action
			, @RequestParam(value="lang", defaultValue="kr") String lang
			, @RequestParam(value="id", defaultValue="") Integer id
			, @RequestParam(value="serial", defaultValue="") String serial
			, @RequestParam(value="email", defaultValue="") String email
			, @RequestParam(value="page", defaultValue="1") int page
			, @RequestParam(value="pageSize", defaultValue="12") int pageSize

			, @RequestParam(value="searchType", defaultValue="") String searchType
			, @RequestParam(value="searchKey", defaultValue="") String searchKey
			, @RequestParam(value="keyword", defaultValue="") String keyword
			, @RequestParam(value="categoryId", defaultValue="0") int categoryId
			) {

		ModelAndView mav = new ModelAndView();

		setCommonParams(param, action, lang, id, serial, page, pageSize, searchType, searchKey, keyword, categoryId, email);

		commonPathFinder(request, mav, param, path1, path2);

		return mav;
	}

	@RequestMapping("/{path1:"+EXCLUDED_PATHS+"}")
	public ModelAndView dynamicPath3(
			HttpServletRequest request
			, @PathVariable String path1

			, HashMap<String, Object> param

			, @RequestParam(value="action", defaultValue="") String action
			, @RequestParam(value="lang", defaultValue="kr") String lang
			, @RequestParam(value="id", defaultValue="") Integer id
			, @RequestParam(value="serial", defaultValue="") String serial
			, @RequestParam(value="email", defaultValue="") String email
			, @RequestParam(value="page", defaultValue="1") int page
			, @RequestParam(value="pageSize", defaultValue="12") int pageSize

			, @RequestParam(value="searchType", defaultValue="") String searchType
			, @RequestParam(value="searchKey", defaultValue="") String searchKey
			, @RequestParam(value="keyword", defaultValue="") String keyword
			, @RequestParam(value="categoryId", defaultValue="0") int categoryId
			) {
		ModelAndView mav = new ModelAndView();

		setCommonParams(param, action, lang, id, serial, page, pageSize, searchType, searchKey, keyword, categoryId, email);

		commonPathFinder(request, mav, param, path1);

		return mav;
	}

	@RequestMapping("/{path1:"+EXCLUDED_PATHS+"}/{path2}/{path3}")
	public ModelAndView dynamicPath3(
			HttpServletRequest request
			, @PathVariable String path1
			, @PathVariable String path2
			, @PathVariable String path3

			, HashMap<String, Object> param

			, @RequestParam(value="action", defaultValue="") String action
			, @RequestParam(value="lang", defaultValue="kr") String lang
			, @RequestParam(value="id", defaultValue="") Integer id
			, @RequestParam(value="serial", defaultValue="") String serial
			, @RequestParam(value="email", defaultValue="") String email
			, @RequestParam(value="page", defaultValue="1") int page
			, @RequestParam(value="pageSize", defaultValue="12") int pageSize

			, @RequestParam(value="searchType", defaultValue="") String searchType
			, @RequestParam(value="searchKey", defaultValue="") String searchKey
			, @RequestParam(value="keyword", defaultValue="") String keyword
			, @RequestParam(value="categoryId", defaultValue="0") int categoryId
			) {
		ModelAndView mav = new ModelAndView();

		setCommonParams(param, action, lang, id, serial, page, pageSize, searchType, searchKey, keyword, categoryId, email);

		commonPathFinder(request, mav, param, path1, path2, path3);

		return mav;
	}
	@RequestMapping("/{path1:^(?!css|js|plugin|font|images|error|upload$).+}/{path2}/{path3}/{path4}")
	public ModelAndView dynamicPath4(
			HttpServletRequest request

			, @PathVariable String path1
			, @PathVariable String path2
			, @PathVariable String path3
			, @PathVariable String path4

			, HashMap<String, Object> param

			, @RequestParam(value="action", defaultValue="") String action
			, @RequestParam(value="lang", defaultValue="kr") String lang
			, @RequestParam(value="id", defaultValue="") Integer id
			, @RequestParam(value="serial", defaultValue="") String serial
			, @RequestParam(value="email", defaultValue="") String email
			, @RequestParam(value="page", defaultValue="1") int page
			, @RequestParam(value="pageSize", defaultValue="12") int pageSize

			, @RequestParam(value="searchType", defaultValue="") String searchType
			, @RequestParam(value="searchKey", defaultValue="") String searchKey
			, @RequestParam(value="keyword", defaultValue="") String keyword
			, @RequestParam(value="categoryId", defaultValue="0") int categoryId
			) {
		ModelAndView mav = new ModelAndView();

		setCommonParams(param, action, lang, id, serial, page, pageSize, searchType, searchKey, keyword, categoryId, email);

		commonPathFinder(request, mav, param, path1, path2, path3, path4);

		return mav;
	}


	private void setCommonParams(
	        HashMap<String, Object> param,
	        String action,
	        String lang,
	        Integer id,
	        String serial,
	        int page,
	        int pageSize,
	        String searchType,
	        String searchKey,
	        String keyword,
	        int categoryId,
			String email
			) {

	    // Action 파라미터 설정
	    HashMap<String, Object> pAction = new HashMap<>();
	    pAction.put("action", action);
	    pAction.put("lang", lang);
	    pAction.put("id", id);
	    pAction.put("email", email);
	    pAction.put("serial", serial);
	    pAction.put("page", page);
	    pAction.put("pageSize", pageSize);

	    // Search 파라미터 설정
	    HashMap<String, Object> pSearch = new HashMap<>();
	    pSearch.put("searchType", searchType);
	    pSearch.put("searchKey", searchKey);
	    pSearch.put("keyword", keyword);
	    pSearch.put("categoryId", categoryId);

	    // Param에 추가
	    param.put("action", pAction);
	    param.put("search", pSearch);
	}

	private void commonPathFinder(HttpServletRequest request, ModelAndView mav, HashMap<String, Object> searchParams, String ... arrPath) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		HttpSession session = request.getSession();
		HashMap<String, Object> param = new HashMap<>();
		String strFullUrl = "";
		int menuDepth = 0;
		for (String strPath : arrPath) { strFullUrl += "/"+strPath; menuDepth++; }

		MenuUser menu = pageService.getMenuUserFullMenuUrl( strFullUrl );

		HashMap<String, Object> pAction = (HashMap<String, Object>) searchParams.get("action");
		HashMap<String, Object> pSearch = (HashMap<String, Object>) searchParams.get("search");

		String action = (String) pAction.get("action");
		String lang   = (String) pAction.get("lang");
		Integer id	  = (Integer) pAction.get("id");
		String email  = (String) pAction.get("email");
		String serial = (String) pAction.get("serial");

		if(menu.getMenuAction() != null && action.equals(""))
			action = menu.getMenuAction();

		int page	  = (int) pAction.get("page");
		int pageSize  = (int) pAction.get("pageSize");

		HashMap<String, Object> p = new HashMap<>();

		if( pSearch.containsKey("searchType") ) p.put("searchType", pSearch.get("searchType") );
		if( pSearch.containsKey("searchKey") )  p.put("searchKey", pSearch.get("searchKey") );
		if( pSearch.containsKey("keyword") )    p.put("keyword", pSearch.get("keyword") );
		if( pSearch.containsKey("categoryId") ) p.put("categoryId", pSearch.get("categoryId") );

		/*--------------------------------------------*/

		String dateType = request.getParameter("dateType");
		String dateFrom = request.getParameter("dateFrom");
		String dateTo   = request.getParameter("dateTo");

		if( pSearch.containsKey("dateType") ) p.put("dateType", dateType );
		if( pSearch.containsKey("dateFrom") ) p.put("dateFrom", dateFrom );
		if( pSearch.containsKey("dateTo") )   p.put("dateTo", dateTo );

		mav.addObject("p", p);
		mav.addObject("nowMenu", menu );
		mav.addObject("maxRequestSize", maxRequestSize);

		String menuType = menu.getMenuType();
		String menuSubType = menu.getMenuSubType();

		logger.info( "######################################################" );
		logger.info( "#\t"+ arrPath.length + ",\t" + strFullUrl +"\t#" );
		logger.info( "#\t"+ menuType + "\t#" );
		logger.info( "#\t"+ menuSubType +"\t#" );
		logger.info( "#\t"+ action +"\t#" );
		logger.info( "#\t"+ menuDepth +"\t#" );
		logger.info( "######################################################" );

		/*
		Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		LoginUser user = null;

		if(_user instanceof LoginUser) {
			user = (LoginUser)_user;
					//pc.setCreateId( user.getUserEmail() );
		}
		*/

		try {
			switch( menuType ) {
			case "contents":
				switch( menuSubType ) {
					default:
					case "menuContent":
						//contentService.
						HashMap<String, Object> rtm = new HashMap<>();
						param.put( "useYn", "Y" );
						param.put( "deleteYn", "N" );
						contentService.selectList(param, rtm);

						mav.addObject("tree", rtm.get("data") );

						ContentsItem item = null;
						param.put( "isLast", true );
						if( id != null) {
							param.put( "contentId", id );
							item = contentService.selectOneContentsItem(param);
						} else {
							// param.put( "contentId", "1" );
							// item = contentService.selectOneContentsItem(param);
							item = new ContentsItem();
						}
						mav.addObject("cont", item);


						mav.setViewName( "front/sub/"+menuSubType + "/cont" );
						break;
					case "content":
					System.out.println("action , " + action);
						switch( action ) {
							case "list" :
								HashMap<String, Object> inspirationParam = new HashMap<>();
								inspirationParam.put("typeCode", "Inspiration" );
								inspirationParam.put("menuUrl", "/Inspiration" );
								BoardSetting boardSetting = boardService.selectOneBoardSetting( inspirationParam );
								inspirationParam.put("boardId", boardSetting.getId() );
								inspirationParam.put("assembleDateRange", "month");
								inspirationParam.put("useYn", "Y");
								inspirationParam.put("deleteYn", "N");
								inspirationParam.put("sort", "id");
								inspirationParam.put("sortOrder", "desc");
								inspirationParam.put("itemStartPosition", 0);
								inspirationParam.put("pagePerSize", 4);

								mav.addObject("inspirationList", boardService.selectListBoardContents(inspirationParam));

								HashMap<String, Object> reviewCategoryParam = new HashMap<>();
								reviewCategoryParam.put("typeCode", "review" );
								BoardSetting reviewBoardSetting = boardService.selectOneBoardSetting( reviewCategoryParam );
								reviewCategoryParam.put("boardId", reviewBoardSetting.getId() );
								reviewCategoryParam.put("useYn", "Y");
								reviewCategoryParam.put("deleteYn", "N");
								reviewCategoryParam.put("sort", "order_num");
								reviewCategoryParam.put("sortOrder", "asc");
								mav.addObject("reviewCategoryList", boardService.selectListBoardCategory(reviewCategoryParam));
								break;
							default:
							break;
						}

						mav.setViewName( "front/sub/contents"+strFullUrl+"/"+action );
						break;
					case "place":
						switch( action ) {
						default:
						case "list" :
							int totalCount = pageService.selectCountPlaceSpot(param);

							PagingDTO pagingInquriy = new PagingDTO(totalCount, page, 0, pageSize);
							param.put("itemStartPosition", pagingInquriy.getItemStartPosition());
							param.put("pagePerSize", pagingInquriy.getPagePerSize());
							ArrayList<PlaceSpot> list = pageService.selectListPlaceSpot(param);

							mav.addObject("p", param);
							mav.addObject("paging", pagingInquriy);
							mav.addObject("list", list);

							mav.setViewName( "front/sub/contents-place"+strFullUrl );
							break;
						case "view" :
							param.put("id", id);
							InquiryContent content = pageService.selectOneInquiryContent(param);
							mav.addObject("content", content);
							mav.setViewName( "front/sub/contents-place"+strFullUrl+"-"+action );
							break;
						}

						//mav.setViewName( "front/sub/contents-place"+strFullUrl );
						break;
				}
				break;
			case "program":

				switch( menuSubType ) {
				default:
				case "custom":
					switch( strFullUrl ) {
						case "/b2b/contact":
							HashMap<String, Object> paramMap = new HashMap<>();
							paramMap.put("deleteYn", "N");
							ArrayList<InquiryCategory> categories = pageService.selectListInquiryCategory(paramMap);
							mav.addObject("categories", categories);
							break;
						case "/jamsiisland/application":
							HashMap<String, Object> menuParamMap = new HashMap<>();
							menuParamMap.put("menuName", "프로그램 신청");
							menuParamMap.put("menuType", "product");
							mav.addObject("applyMenu", pageService.selectOneMenuUser(menuParamMap));
							mav.setViewName( "front/sub/program-jamsiisland-application" );
							break;
					}

					mav.setViewName( "front/sub/program"+strFullUrl );
					break;
				case "mypage":
					logger.info( "case ======= mypage" );
					String userVerify = getCookies( request, "user_verify" );

					// if( userVerify != null && userVerify.equals("success") ) {
					// 	String userEmail = getBaseUserEmail();
					// 	mav.addObject("userInfo", memberService.selectUserByUserid( userEmail ) );
					// } else {
					// 	mav.addObject("userInfo", null );
					// }

					mav.addObject("userVerify", userVerify);
					LoginUser userInfo = memberService.selectUserByUserid( getBaseUserEmail() );

					mav.addObject("userInfo",  userInfo);

					switch( strFullUrl ) {
						case "/member/badge":
							logger.debug( "case ======= mypage.badge" );
							HashMap<String, Object> badgeParam = new HashMap<>();
							badgeParam.put("userEmail", getBaseUserEmail());
							mav.addObject("userBadgeViewList", userService.selectListUserBadgeAcquireHistory(badgeParam));
							break;
						case "/member/set-profile":
							String loginType = (String) session.getAttribute("loginType");
							mav.addObject("loginType", loginType);
							logger.debug( "case ======= mypage.set-profile" );
							break;
						case "/member/set-island-life":
							logger.debug( "case ======= mypage.set-island-life" );
							param.put("useYn", "Y");
							param.put("deleteYn", "N");
							mav.addObject("islandLifeList", userService.selectListIslandlife(param));
							break;
						case "/member/complete-mission":
							logger.debug( "case ======= mypage.complete-mission" );
							HashMap<String, Object> missionParam = new HashMap<>();
							missionParam.put("typeCode", "mission");
							BoardSetting missionBoardSetting = boardService.selectOneBoardSettingTypeCode(missionParam);
							missionParam.put("boardId", missionBoardSetting.getId());
							missionParam.put("useYn", "Y");
							missionParam.put("deleteYn", "N");
							missionParam.put("likeUserEmail", getBaseUserEmail());
							missionParam.put("userEmail", getBaseUserEmail());
							missionParam.put("sort", "id");
							missionParam.put("sortOrder", "desc");
							missionParam.put("itemStartPosition", 0);
							missionParam.put("pagePerSize", 12);
							missionParam.put("isUpperBoardId", true);
							mav.addObject("userCompleteMissionList", boardService.selectListBoardContents(missionParam));
							break;
						case "/member/point":
							logger.debug( "case ======= mypage.point" );
							HashMap<String, Object> pointParam = new HashMap<>();
							pointParam.put("userEmail", getBaseUserEmail());
							pointParam.put("excludeExpired", "true");
							mav.addObject("userPoint", userPointService.getUserPointRecordSummary(pointParam));
							pointParam.put("createDateDesc", "true");
							ArrayList<UserPointRecord> pointList = userPointService.getUserPointRecordUsedCancelList(pointParam);
							for(UserPointRecord record : pointList){
								if(record.getAccruedType() != null
										&& record.getAccruedType().equals("giftCardExchange")){
									record.setExchangeCode(userPointService.getUserPointExchangeCodeByUseId(record.getId()));
								}
							}
							mav.addObject("userRecordPointUserCancelList",pointList);
							break;
						case "/member/pay":
							logger.debug( "case ======= mypage.payment" );
							HashMap<String, Object> paymentParam = new HashMap<>();
							paymentParam.put("userEmail", getBaseUserEmail());
							mav.addObject("userPaymentList", new LoginUser());

							break;
						case "/member/qna/list":
							logger.debug( "case ======= mypage.qna" );
							switch( action ) {
								case "view" :
									param.put("id", id);
									InquiryContent content = pageService.selectOneInquiryContent(param);
									mav.addObject("content", content);
									strFullUrl = "/member/qna/view";
									break;
							}
							HashMap<String, Object> qnaParam = new HashMap<>();
							qnaParam.put("createId", getBaseUserEmail());
							qnaParam.put("deleteYn", "N");
							mav.addObject("userQnaList", pageService.selectListInquiryContent(qnaParam));
							break;
						case "/member/wrote-board":
							logger.debug( "case ======= mypage.wrote-board" );
							HashMap<String, Object> wroteBoardViewParam = new HashMap<>();
							wroteBoardViewParam.put("userEmail", getBaseUserEmail());
							wroteBoardViewParam.put("typeCode", "review,Inspiration");
							ArrayList<BoardSetting> boardSetting = boardService.selectListBoardSettingTypeCode(wroteBoardViewParam);
							wroteBoardViewParam.put("boardId", boardSetting.stream().map(bs -> bs.getId().toString()).collect(Collectors.joining(",")));

							int totalCount = boardService.selectCountWroteBoardContents( wroteBoardViewParam );
							PagingDTO paging = new PagingDTO(totalCount, page, 0, 20);
							wroteBoardViewParam.put("itemStartPosition", paging.getItemStartPosition());
							wroteBoardViewParam.put("pagePerSize", paging.getPagePerSize());

							mav.addObject("paging", paging);
							mav.addObject("wroteBoardView", boardService.selectListWroteBoardContents(wroteBoardViewParam));
							break;
						case "/member/wrote-board/wrote-Inspiration":
							logger.debug( "case ======= mypage.wrote-board-Inspiration" );
							HashMap<String, Object> wroteInspirationViewParam = new HashMap<>();
							wroteInspirationViewParam.put("typeCode", "Inspiration");
							BoardSetting inspirationBoardSetting = boardService.selectOneBoardSettingTypeCode(wroteInspirationViewParam);
							wroteInspirationViewParam.put("boardId", inspirationBoardSetting.getId());
							wroteInspirationViewParam.put("userEmail", getBaseUserEmail());

							int inspirationTotalCount = boardService.selectCountWroteBoardContents( wroteInspirationViewParam );
							PagingDTO inspirationPaging = new PagingDTO(inspirationTotalCount, page, 0, 10);
							wroteInspirationViewParam.put("itemStartPosition", inspirationPaging.getItemStartPosition());
							wroteInspirationViewParam.put("pagePerSize", inspirationPaging.getPagePerSize());

							mav.addObject("paging", inspirationPaging);
							mav.addObject("wroteInspirationView", boardService.selectListWroteBoardContents(wroteInspirationViewParam));
							break;
						case "/member/wrote-board/wrote-review":
							logger.debug( "case ======= mypage.wrote-board-review" );
							HashMap<String, Object> wroteReviewViewParam = new HashMap<>();
							wroteReviewViewParam.put("typeCode", "review");
							BoardSetting reviewBoardSetting = boardService.selectOneBoardSettingTypeCode(wroteReviewViewParam);
							wroteReviewViewParam.put("boardId", reviewBoardSetting.getId());
							wroteReviewViewParam.put("userEmail", getBaseUserEmail());

							int reviewTotalCount = boardService.selectCountWroteBoardContents( wroteReviewViewParam );
							PagingDTO reviewPaging = new PagingDTO(reviewTotalCount, page, 0, 10);
							wroteReviewViewParam.put("itemStartPosition", reviewPaging.getItemStartPosition());
							wroteReviewViewParam.put("pagePerSize", reviewPaging.getPagePerSize());

							mav.addObject("paging", reviewPaging);
							mav.addObject("wroteReviewView", boardService.selectListWroteBoardContents(wroteReviewViewParam));
						break;
						default:
							logger.debug( "case ======= mypage.default" );
							//사용자 작성 게시글 ( 게시판명이 '인사' 인  메뉴제외 )
							HashMap<String, Object> wroteBoardParam = new HashMap<>();
							wroteBoardParam.put("userEmail", getBaseUserEmail());
							wroteBoardParam.put("boardMenuTitle", "인사");
							mav.addObject("wroteBoardList", boardService.selectListWroteBoardContents(wroteBoardParam));
							HashMap<String, Object> userParam = new HashMap<>();
							userParam.put("userEmail", getBaseUserEmail());
							//사용자 뱃지
							userParam.put("itemStartPosition", 0);
							userParam.put("pagePerSize", 6);
							mav.addObject("userBadgeList", userService.selectListUserBadgeAcquireHistory(userParam));
							//사용자 섬살이 유형
							userParam.put("id", userInfo.getUserIslandLifeId());
							ArrayList<Islandlife> islandlifeList = null;
							if( userInfo.getUserIslandLifeId() != null ) {
								userParam.put("deleteYn", "N");
								islandlifeList = userService.selectListIslandlife(userParam);
							}
							mav.addObject("userIslandLife", islandlifeList);
							//사용자 댓글 개수
							mav.addObject("userCommentCount", userService.selectCountUserComment(userParam));
							//사용자 완료 미션
							userParam.put("itemStartPosition", 0);
							userParam.put("pagePerSize", 5);
							userParam.put("typeCode", "mission");
							BoardSetting userCompleteMissionBoardSetting = boardService.selectOneBoardSettingTypeCode(userParam);
							userParam.put("boardId", userCompleteMissionBoardSetting.getId());

							mav.addObject("userCompleteMissionList", userService.selectListUserCompleteMission(userParam));
							//사용자 포인트 현황
							userParam.put("excludeExpired", "true");
							mav.addObject("userPoint", userPointService.getUserPointRecordSummary(userParam));
							//사용자 정보
							String emailParam = getBaseUserEmail();
							LoginUser oneUserInfo = memberService.selectUserByUserid(emailParam);
							mav.addObject("oneUserInfo", oneUserInfo);
							break;
					}

					mav.setViewName( "front/sub/program"+strFullUrl );
					break;
				case "estimate":
				case "inquiry":
					switch( action ) {
					default:
					case "list" :
						param.put("createId", getBaseUserEmail());
						param.put("categoryCode", menuSubType);

						int totalCount = pageService.selectCountInquiryContent(param);

						PagingDTO pagingInquriy = new PagingDTO(totalCount, page, 0, pageSize);
						param.put("itemStartPosition", pagingInquriy.getItemStartPosition());
						param.put("pagePerSize", pagingInquriy.getPagePerSize());
						ArrayList<InquiryContent> list = pageService.selectListInquiryContent(param);

						mav.addObject("p", param);
						mav.addObject("paging", pagingInquriy);
						mav.addObject("list", list);

						mav.setViewName( "front/sub/program"+strFullUrl );
						break;
					case "edit" :
					case "view" :
						param.put("id", id);

						InquiryContent content = pageService.selectOneInquiryContent(param);

						mav.addObject("content", content);

//						HashMap<String, Object> paramMapSearch = new HashMap<>();
//						paramMapSearch.put("boardId", id);

//						 mav.addObject("listAttach",  pageService.selectListBoardAttachFile(paramMapSearch));
//										HttpSession session = request.getSession();
//										boolean secretPass = false;
//										Object sessionSecretPass = session.getAttribute( "secretPass"+id);
//
//										if(content.getSecretYn().equals("Y")) {
//											if(_user instanceof LoginUser) {
//												LoginUser user = (LoginUser)_user;
//
//												 if( user.getUserRole().equals("manage") ||
//													user.getUserRole().equals("admin")) {
//													/*권한으로 패스*/
//													secretPass = true;
//												} else if( user.getUserEmail().equals( content.getCreateId()) ||
//														   user.getUserEmail().equals( content.getCustomerEmail()) )  {
//													secretPass = true;
//												}
//											} else if ( sessionSecretPass!=null && (boolean)sessionSecretPass ){
//												secretPass = true;
//												//session.setAttribute( "secretPass"+id, null);
//											}
//										} else {
//											secretPass = true;
//										}
//										mav.addObject("secretPass", secretPass);

						mav.setViewName( "front/sub/program"+strFullUrl+"-"+action );
						break;
					}

					break;
				case "pay":
					param.put("createId", getBaseUserEmail());
					//param.put("createId", "<EMAIL>");
					switch( action ) {
					default:
					case "list" :
						if( dateType != null ) {

							if( !dateType.equals("all") ) {
								param.put("dateType", "receipt" );
								param.put("dateFrom", dateFrom);
								param.put("dateTo",   dateTo);
							}
						} else {
							LocalDateTime today = LocalDateTime.now();
							LocalDateTime monthAgo = today.minusMonths(1);

							dateFrom = monthAgo.format(formatter);
							dateTo = today.format(formatter);
							dateType = "1";

							param.put("dateType", "receipt" );
							param.put("dateFrom", dateFrom);
							param.put("dateTo",   dateTo );
						}

						 p.put("dateType", dateType );
						 p.put("dateFrom", dateFrom );
						 p.put("dateTo", dateTo );

						int totalCount = pageService.selectCountReservation(param);

						PagingDTO pagingRer = new PagingDTO(totalCount, page, 0, pageSize);
						param.put("itemStartPosition", pagingRer.getItemStartPosition());
						param.put("pagePerSize", pagingRer.getPagePerSize());
						param.put("deleteYn","N");
						ArrayList<Reservation> list = pageService.selectListReservation(param);

						for (Reservation rsvt : list) {
							param.remove("createId");
							param.put("reservationId", rsvt.getId() );

							HashMap<String, Object> paramProduct = new HashMap<>();
							paramProduct.put("productSerial", serial);
							paramProduct.put("productTourId", rsvt.getProductTourId());

							//paramProduct.put("menuType", menuType);

							ProductInfo productInfo =  productService.selectOneProduct( paramProduct );
							UserCustomerOrder orderInfo = pageService.selectOneUserCustomerOrder(param);
							ArrayList<UserCustomerOrderList> orderlist = pageService.selectListUserCustomerOrderList(param);

							rsvt.setProductInfo( productInfo );
							rsvt.setOrderInfo( orderInfo );
							rsvt.setOrderList( orderlist );
						}

						//param.remove("payMethod");

						//param.put("searchType",searchType);
						//param.put("targetType",targetType);

						//mav.addObject("p", param);
						mav.addObject("paging", pagingRer);
						mav.addObject("list", list);

						mav.setViewName( "front/sub/program"+strFullUrl );
						break;
					case "view" :
						param.put("id", id);

						Reservation data = pageService.selectOneReservation(param);
						param.put("reservationId", data.getId() );
						//param.put("payMoid", id);
						HashMap<String, Object> paramProduct = new HashMap<>();
						paramProduct.put("productSerial", serial);
						paramProduct.put("productTourId", data.getProductTourId());

						if( menuType != null ) {
							paramProduct.put("menuType", menuType);
							if ( menuType.equals("program") ) {
								paramProduct.put("menuType", "product");
							}
						}

						ProductInfo productInfo =  productService.selectOneProductByProductSerialWithTourId( paramProduct );
						UserCustomerOrder orderInfo = pageService.selectOneUserCustomerOrder(param);
						ArrayList<UserCustomerOrderList> orderlist = pageService.selectListUserCustomerOrderList(param);

						data.setProductInfo( productInfo );
						data.setOrderInfo( orderInfo );
						data.setOrderList( orderlist );

						logger.info( "/###########################/" );

						LocalDateTime nowDate    = LocalDateTime.now();

						logger.info( "오늘일자 : "+nowDate.format(DateTimeFormatter.ISO_DATE_TIME) );
						logger.info( "여행일자 : "+orderInfo.getOrderDate() );
						logger.info( "결제일자 : "+orderInfo.getCreateDate() );

						logger.info( "/###########################/" );

						mav.addObject("data", data);
						mav.addObject("pgTosspaySKey", pgTosspayServerKey);
						mav.setViewName( "front/sub/program"+strFullUrl+"-"+action );
						break;
					}

					break;

				case "board":
					BoardSetting boardSetting = boardService.selectOneBoardSettingByMenuId( menu.getMenuId() );
					mav.addObject("setting", boardSetting);
					if (boardSetting != null) {
						switch (action) {
							default:
								break;
							case "list":
								if (strFullUrl.contains("/portfolio")) {
									HashMap<String, Object> portfolioParam = new HashMap<>();
									portfolioParam.put("boardId", boardSetting.getId());
									portfolioParam.put("deleteYn", "N");
									portfolioParam.put("sort", "id");
									portfolioParam.put("sortOrder", "desc");
									ArrayList<BoardContents> boardContentsList = boardService.selectListBoardContents(portfolioParam);
									int viewId = 0;
									if ( boardContentsList.size() > 0 ) {
										viewId = boardContentsList.get(0).getId();
									}
									mav.setViewName("redirect:/b2b/portfolio?action=view&id=" + viewId);

									break;
								}
								String keyword = (String) pSearch.get("keyword");
								int categoryId = (int) pSearch.get("categoryId");

								param.put("boardId", boardSetting.getId());
								param.put("keyword", keyword);
								param.put("deleteYn", "N");
								param.put("createId", getBaseUserEmail() );

								if (categoryId > 0)
									param.put("categoryId", categoryId);

								int totalFaqCount = boardService.selectCountBoardContents(param);

								PagingDTO pagingBod = new PagingDTO(totalFaqCount, page, 0, boardSetting.getDefaultPageSize());
								param.put("itemStartPosition", pagingBod.getItemStartPosition());
								param.put("pagePerSize", pagingBod.getPagePerSize());

								ArrayList<BoardContents> boardContentsList = boardService.selectListBoardContents(param);
								mav.addObject("list", boardContentsList);
								mav.addObject("paging", pagingBod);


								mav.setViewName( "front/sub/program"+strFullUrl );
								break;
							case "view" :
								param.put("id", id);

								BoardContents boardContents = boardService.selectOneBoardContents(param);

								ArrayList<BoardAttachFile> attachs = boardService.selectListBoardAttachFile(param);

								mav.addObject("contents", boardContents);
								mav.addObject("attachList", attachs);
								HashMap<String, Object> portfolioParam = new HashMap<>();
								portfolioParam.put("boardId", boardSetting.getId());
								portfolioParam.put("deleteYn", "N");
								ArrayList<BoardContents> boardContentsViewList = boardService.selectListBoardContents(portfolioParam);
								mav.addObject("list", boardContentsViewList);

								mav.setViewName("front/sub/program" + strFullUrl + "-" + action);
								break;

							/*view 페이지 설정처리 할예정, 분리가 필요하면.*/
							/*
							switch( boardSetting.getTypeCode() ) {
							case "gallery":
								mav.setViewName( "front/sub/board/"+menuSubType+"/"+action );
								break;
							case "faq":
								mav.setViewName( "front/sub/board/"+menuSubType+"/"+action );
								break;
							case "qna":
								mav.setViewName( "front/sub/board/"+menuSubType+"/"+action );
								break;
							case "inquiry":
								mav.setViewName( "front/sub/board/"+menuSubType+"/"+action );
								break;
							case "review":
								mav.setViewName( "front/sub/board/"+menuSubType+"/"+action );
								break;
							case "common":
							default:
								mav.setViewName( "front/sub/board/"+boardSetting.getTypeCode()+"/"+action );
								break;
							}
							 */
						}
						break;
					} else {
						mav.setViewName("error/500");
					}
					break;
				}
				break;
			case "product":
				/* 공통내역 */
				ArrayList<ProductInfo> productLang = null;

				param.put("productUseYn", "Y");
				param.put("regacyYn", "N");
				param.put("deleteYn", "N");
				//<<이 프로젝트용
				Object object = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
				if( object instanceof LoginUser _user) {
					param.put("likeUserEmail", _user.getUsername());
				}

				String mavName = "";
				switch(menuSubType) {
					case "stay":
						param.put("productMenuId", menu.getMenuId());
						ArrayList<ProductInfo> productStayList = productService.selectListProduct( param );
						for (ProductInfo product : productStayList) {
							//상품 이미지정보
							HashMap<String, Object> paramStay = new HashMap<>();
							paramStay.put("productTourId", product.getProductTourId());
							product.setStayImageList(productService.selectListProductImages(paramStay));
							ProductPriceOption productPriceOption = productService.selectOneEarlyBirdPrice(paramStay);
							if (productPriceOption != null) {
								product.setEarlyBirdPrice(productPriceOption.getEarlyBirdPrice());
								product.setEarlyBirdPriceName(productPriceOption.getEarlyBirdPriceName());
							}
						}

						mav.addObject("productStayList", productStayList);
						mav.addObject("productCategorys", productService.selectListProductCategoryByMenuId(menu.getMenuId()));

						HashMap<String, Object> reviewCategoryParam = new HashMap<>();
						reviewCategoryParam.put("typeCode", "review" );
						BoardSetting reviewBoardSetting = boardService.selectOneBoardSetting( reviewCategoryParam );
						reviewCategoryParam.put("boardId", reviewBoardSetting.getId() );
						reviewCategoryParam.put("useYn", "Y");
						reviewCategoryParam.put("deleteYn", "N");
						reviewCategoryParam.put("sort", "order_num");
						reviewCategoryParam.put("sortOrder", "asc");
						mav.addObject("reviewCategoryList", boardService.selectListBoardCategory(reviewCategoryParam));

						mavName = "front/sub/product/jamsiisland/stay";
					break;
					default:
						/* 각 action별 지정 */
						switch( action ) {
							default:
							case "list" :
								// 카테고리 목록 조회
								ArrayList<ProductCategory> listCategory = productService.selectListProductCategoryByMenuId( menu.getMenuId() );
								mav.addObject("listCategory", listCategory);

								// 상품 검색 파라미터 설정
								setupProductSearchParameters(param, menu, pSearch);

								// 상품 목록 조회
								ArrayList<ProductInfo> allProductList = productService.selectListProduct(param);

								// 각 상품의 예약 가능 여부 검증
								for (ProductInfo product : allProductList) {
									validateAndSetReservationFlag(product);
								}

								// 예약 상태에 따른 필터링
								allProductList = applyReservationFilter(allProductList, request, mav);

								// 페이징 처리 및 최종 목록 생성
								ArrayList<ProductInfo> productList = applyPagingToProductList(allProductList, page, mav);
								mav.addObject("productList", productList);

								mavName = "front/sub/product/"+action;
							break;
						// end of switch( action )
						}
						break;
				// end of switch(menuSubType)
				}
				if (action.equals("view")) {
					HashMap<String, Object> paramProduct = new HashMap<>();
					paramProduct.put("productSerial", serial);
					paramProduct.put("productUseYn", "Y");
					paramProduct.put("regcyYn", "N");
					paramProduct.put("deleteYn", "N");
					paramProduct.put("likeUserEmail", getBaseUserEmail());

					//삭제된상품 조회
					if(request.getParameter("productTourId") != null) {
						HashMap<String, Object> idParam = new HashMap<>();
						idParam.put("productTourId", request.getParameter("productTourId"));
						ProductInfo productInfo = productService.selectOneProduct( idParam );

						if ( productInfo != null ) {
							mav.addObject("deletedProduct", "Y");
						}
					}


					ProductInfo productInfoTourId =  productService.selectOneProductTourId( paramProduct );

					if( productInfoTourId != null) {
						paramProduct.put("productTourId", productInfoTourId.getProductTourId() );

						productService.updateProductViewCount(paramProduct);

						ProductInfo productInfo = productService.selectOneProduct( paramProduct );
						paramProduct.put("productSerial", productInfo.getProductSerial() );

						//상품 이미지정보
						ArrayList<ProductTourImages> imageList = productService.selectListProductImages(paramProduct);
						//상품 고정가격 옵션
						ArrayList<FixPriceList> optionPriceListFix = productService.selectListProductFixPrice( paramProduct );
						//상품 일자별 가격옵션
						ArrayList<DayList> optionPriceListDay	  = productService.selectListProductDayPrice( paramProduct );
						//세부일정용
						ArrayList<ProductDetailSchedule> productDetailScheduleList = productService.selectListProductDetailSchedule(paramProduct);

						//청풍용. 얼리버드(가칭. 일반적인 특가라 칭함) 특가 구하기.
						HashMap<String, Object> earlyBirdParam = new HashMap<>();
						earlyBirdParam.put("productTourId", productInfo.getProductTourId());
						mav.addObject("earlyBirdPriceList", productService.selectEarlyBirdPrice(earlyBirdParam));

						HashMap<String, Object> detailParamMap = new HashMap<>();
						ArrayList<String> listDay = new ArrayList<>();
						ArrayList<String> detailIdList = new ArrayList<>();

						if ( productDetailScheduleList.size() > 0 ) {
							for ( ProductDetailSchedule productDetailSchedule : productDetailScheduleList ) {
								detailIdList.add(String.valueOf(productDetailSchedule.getDetailId()));
							}

							for (ProductDetailSchedule pds : productDetailScheduleList) {
								if( !detailParamMap.containsKey(pds.getDetailCategory()) ) {
									detailParamMap.put( pds.getDetailCategory(), pds );
									listDay.add( pds.getDetailCategory() );
								}

								detailParamMap.put("detailId", pds.getDetailId() );
								pds.setListProductDetailScheduleImage( productService.selectListProductTourDetailImage(detailParamMap) );
							}

							mav.addObject("listDay", listDay);
							mav.addObject("detailScheduleList", productDetailScheduleList);
						}

						ArrayList<PlaceSpot> placeList = pageService.selectListPlaceSpot(null);
						//ArrayList<PlaceSpot> placeList = pageService.selectListPlaceSpotWithDistanceSearch(null);

						for (ProductTourImages item : imageList) {
							if( productInfo.getProductThumbnail().contains( item.getUploadFilename() )  ) {
								mav.addObject("thumbnail", productInfo.getProductSerial() + "." + item.getFileExtension() );
								break;
							}
						}

						SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						Date currentDate = new Date();
						Date reservStartDate = null;
						Date reservEndDate = null;
						if (productInfo.getProductReservStartDate() != null) {
							reservStartDate = sdf.parse(productInfo.getProductReservStartDate());
						}
						if (productInfo.getProductReservEndDate() != null) {
							reservEndDate = sdf.parse(productInfo.getProductReservEndDate());
							// endDate의 시간을 23:59:59로 설정
							Calendar calendar = Calendar.getInstance();
							calendar.setTime(reservEndDate);
							calendar.set(Calendar.HOUR_OF_DAY, 23);
							calendar.set(Calendar.MINUTE, 59);
							calendar.set(Calendar.SECOND, 59);
							calendar.set(Calendar.MILLISECOND, 999);
							reservEndDate = calendar.getTime();
						}
						// 현재 날짜에 2일을 더한 날짜 계산
						Calendar minReservDate = Calendar.getInstance();
						minReservDate.setTime(currentDate);
						minReservDate.add(Calendar.DATE, 2);
						Date minDate = minReservDate.getTime();

						// 두 날짜 모두 null인 경우
						if (reservStartDate == null && reservEndDate == null) {
							productInfo.setPickPeopleCount(1);
							ArrayList<String> possiblelist = productService.getCalendarOneProgramResvNotCalendarPossibleDayList(productInfo);
							if ( possiblelist.size() > 0 ) {
								productInfo.setReservRangeFlag("Y");
							} else {
								productInfo.setReservRangeFlag("N");
							}
						}
						// reservStartDate만 null이 아닌 경우
						else if (reservStartDate != null && reservEndDate == null) {
							if (reservStartDate.before(currentDate)) {
								ArrayList<String> possiblelist = productService.getCalendarOneProgramResvNotCalendarPossibleDayList(productInfo);
								if ( possiblelist.size() > 0 ) {
									productInfo.setReservRangeFlag("Y");
								} else {
									productInfo.setReservRangeFlag("N");
								}
							} else {
								productInfo.setReservRangeFlag("N");
							}
						}
						// reservEndDate만 null이 아닌 경우
						else if (reservStartDate == null && reservEndDate != null) {
							if (reservEndDate.before(minDate)) {
								productInfo.setReservRangeFlag("N");
							} else {
								ArrayList<String> possiblelist = productService.getCalendarOneProgramResvNotCalendarPossibleDayList(productInfo);
								if ( possiblelist.size() > 0 ) {
									productInfo.setReservRangeFlag("Y");
								} else {
									productInfo.setReservRangeFlag("N");
								}
							}
						}
						// 두 날짜 모두 null이 아닌 경우
						else if (reservStartDate != null && reservEndDate != null) {
							if (reservStartDate.compareTo(currentDate) <= 0 && minDate.before(reservEndDate)) {
								ArrayList<String> possiblelist = productService.getCalendarOneProgramResvNotCalendarPossibleDayList(productInfo);
								if ( possiblelist.size() > 0 ) {
									productInfo.setReservRangeFlag("Y");
								} else {
									productInfo.setReservRangeFlag("N");
								}
							} else {
								productInfo.setReservRangeFlag("N");
							}
						}

						mav.addObject("product", productInfo);
						mav.addObject("imageList", imageList);

						mav.addObject("optionPriceListFix", optionPriceListFix);
						mav.addObject("optionPriceListDay", optionPriceListDay);
						paramProduct.clear();
						paramProduct.put("productTourId", productInfo.getProductTourId());
						mav.addObject("operationDays", productService.selectOneProductOperationDay( paramProduct ));

						mav.addObject("listDay", listDay);
						mav.addObject("detailScheduleList", productDetailScheduleList);

						mav.addObject("place_list", placeList);
						// mav.addObject("productComment", productService.selectListProductComment(paramProduct));

						ArrayList<ProductInfo> viewProductList = productService.selectListProduct( param );
						mav.addObject("viewProductList", viewProductList);

						param.put("useYn", "Y");
						param.put("deleteYn", "N");
						mav.addObject("islandLifeList", userService.selectListIslandlife(param));
					}

					mav.addObject("policyPrivacy", pageService.selectOnePolicyByPolicyType( "4" ));
					mav.addObject("policyRegulation", pageService.selectOnePolicyByPolicyType( "9" ));
					mav.addObject("policyStipulation", pageService.selectOnePolicyByPolicyType( "10" ));
					mav.addObject("policyNotice", pageService.selectOnePolicyByPolicyType( "11" ));
					mav.addObject("policyUniverseTerms", pageService.selectOnePolicyByPolicyType( "12" ));



					if ( menuSubType.equals("stay") ) {
						mavName = "front/sub/product/jamsiisland/stay-view";
					} else {
						mavName = "front/sub/product/view";
					}
				}
				mav.setViewName( mavName );
				//이 프로젝트용>>
			break;
			case "board": {
				//searchParam
				BoardSetting boardSetting = boardService.selectOneBoardSettingByMenuId( menu.getMenuId() );
				mav.addObject("setting", boardSetting);

				param.put("boardId", boardSetting.getId());
				ArrayList<BoardCategory> categorys = boardService.selectListBoardCategory(param);
				mav.addObject("categorys", categorys);

				Integer categoryId = (int) pSearch.get("categoryId");

				if ( categoryId != 0 ) {
					param.put("categoryId", categoryId);
				} else {
					for (BoardCategory cate : categorys) {
						if( cate.getDefaultYn().equals("Y") ) {
							param.put("categoryId", cate.getId());
							p.put("categoryId", cate.getId() );
							break;
						}
					}
				}

				HashMap<String, Object> paramBoard = new HashMap<>();
				paramBoard.put("menuId", menu.getRootMenuId());
				paramBoard.put("useYn", "Y");
				paramBoard.put("deleteYn", "Y");
				mav.addObject("communityMenuList", menuService.selectListCommunityMenu(paramBoard));

				if( boardSetting != null ) {
					//<<이 프로젝트용
					Object boardObject = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
					if( boardObject instanceof LoginUser _user) {
						param.put("likeUserEmail", _user.getUsername());
					}
					//이 프로젝트용>>
					switch( action ) {
					case "list" :
						param.put("useYn", "Y");
						param.put("deleteYn", "N");
						param.put("sort", "id");
						param.put("sortOrder", "desc");
						param.put("searchKey", pSearch.get("searchKey"));
						param.put("boardId", boardSetting.getId());
						param.put("isUpperBoardId", boardSetting.getSubContentYn().equals("Y"));

						if(categoryId > 0) {
							param.put("categoryId", categoryId);
							mav.addObject("categoryId", categoryId);
						}

						if ( "mission".equals(boardSetting.getTypeCode()) ) {
							param.put("isUpperBoardId", true);
							param.put("categoryId", null);
						}

						int totalFaqCount = boardService.selectCountBoardContents(param);

						PagingDTO paging = new PagingDTO(totalFaqCount, page, 0, boardSetting.getDefaultPageSize());
						param.put("itemStartPosition", paging.getItemStartPosition());
						param.put("pagePerSize", paging.getPagePerSize());

						ArrayList<BoardContents> boardContentsList = boardService.selectListBoardContents(param);
						for (BoardContents cont : boardContentsList) {
							param.put("contentId", cont.getId());
							cont.setImageList(boardService.selectListBoardAttachFile(param));
						}

						for (BoardContents cont : boardContentsList) {

							cont.setListComment( boardService.selectListBoardComment(param) );
						}

						mav.addObject("missionMonthList", boardService.selectMostMissionMonth(param));
						mav.addObject("missionUserList", boardService.selectMostMissionUser(param));

						HashMap<String, Object> userParam = new HashMap<>();
						userParam.put("likeUserEmail", getBaseUserEmail());
						userParam.put("role", "USER");
						userParam.put("userShowYn", "Y");

						int totalUserCount = userService.selectCountUserListByRole(userParam);

						PagingDTO userPaging = new PagingDTO(totalUserCount, page, 0, 12);
						userParam.put("itemStartPosition", userPaging.getItemStartPosition());
						userParam.put("pagePerSize", userPaging.getPagePerSize());
						
						mav.addObject("joinUserList", userService.selectUserListByRole(userParam));
						
						mav.addObject("list", boardContentsList);
						mav.addObject("paging", paging);

						param.clear();
						param.put("boardId", boardSetting.getId());
						param.put("useYn", "Y");
						param.put("deleteYn", "N");
						param.put("sort", "orderNum");
						param.put("sortOrder", "asc");
						categorys = boardService.selectListBoardCategory(param);
						mav.addObject("categorys", categorys);

						HashMap<String, Object> reviewCategoryParam = new HashMap<>();
						reviewCategoryParam.put("typeCode", "review" );
						BoardSetting reviewBoardSetting = boardService.selectOneBoardSetting( reviewCategoryParam );
						reviewCategoryParam.put("boardId", reviewBoardSetting.getId() );
						reviewCategoryParam.put("useYn", "Y");
						reviewCategoryParam.put("deleteYn", "N");
						reviewCategoryParam.put("sort", "order_num");
						reviewCategoryParam.put("sortOrder", "asc");
						mav.addObject("reviewCategoryList", boardService.selectListBoardProjectCategory(reviewCategoryParam));
						break;
					case "view":
						if(categoryId > 0) param.put("categoryId", categoryId);
						if ( "mission".equals(boardSetting.getTypeCode()) ) {
							param.put("isUpperBoardId", "");
							param.put("categoryId", null);
						}
						if (id != null) {
							param.put("id", id);
							BoardContents boardContents = boardService.selectOneBoardContents(param);
							param.put("contentId", id);
							ArrayList<BoardAttachFile> attachs = boardService.selectListBoardAttachFile(param);
							mav.addObject("contents", boardContents);
							mav.addObject("attaches", attachs);
						}
						else if ( email != null ) {
							String emailParam = (String) pAction.get("email");
							LoginUser oneUserInfo = memberService.selectUserByUserid(emailParam);
							mav.addObject("oneUserInfo", oneUserInfo);
							//사용자 작성 게시글 ( 게시판명이 '인사' 인  메뉴제외 )
							HashMap<String, Object> wroteBoardParam = new HashMap<>();
							wroteBoardParam.put("userEmail", emailParam);
							wroteBoardParam.put("boardMenuTitle", "인사");
							mav.addObject("wroteBoardList", boardService.selectListWroteBoardContents(wroteBoardParam));
							HashMap<String, Object> userEmailParam = new HashMap<>();
							userEmailParam.put("userEmail", emailParam);
							userEmailParam.put("itemStartPosition", 0);
							userEmailParam.put("pagePerSize", 6);
							mav.addObject("userBadgeList", userService.selectListUserBadgeAcquireHistory(userEmailParam));
							mav.addObject("userBadgeViewList", userService.selectListUserBadgeAcquireHistory(userEmailParam));
							HashMap<String, Object> userMissionParam = new HashMap<>();
							userMissionParam.put("userEmail", emailParam);
							userMissionParam.put("useYn", "Y");
							userMissionParam.put("deleteYn", "N");
							userMissionParam.put("typeCode", "mission");
							BoardSetting MissionBoardSetting = boardService.selectOneBoardSetting(userMissionParam);
							userMissionParam.put("boardId", MissionBoardSetting.getId());
							userMissionParam.put("itemStartPosition", 0);
							userMissionParam.put("pagePerSize", 5);
							userMissionParam.put("sort", "id");
							userMissionParam.put("sortOrder", "desc");
							mav.addObject("userCompleteMissionList", boardService.selectListBoardContents(userMissionParam));
							//사용자 댓글 개수
							mav.addObject("userCommentCount", userService.selectCountUserComment(userEmailParam));
						}
						else {
							mav.addObject("contents", new BoardContents());
						}
						break;
					case "form":
						param.put("boardId", boardSetting.getId());
						param.put("sort", "orderNum");
						param.put("sortOrder", "asc");
						categorys = boardService.selectListBoardCategory(param);
						mav.addObject("categorys", categorys);

						BoardContents boardContents = null;
						ArrayList<BoardAttachFile> attachs = null;

						if (id != null) {
							param.put("id", id);
							boardContents = boardService.selectOneBoardContents(param);

							param.put("contentId", id);
							attachs = boardService.selectListBoardAttachFile(param);

							mav.addObject("contents", boardContents);
							mav.addObject("attaches", attachs);
						} else {
							boardContents = new BoardContents();
							attachs = new ArrayList<BoardAttachFile>();

							mav.addObject("contents", boardContents);
							mav.addObject("attaches", attachs);
						}

						break;
					case "board-detail":
							HashMap<String, Object> boardDetailParam = new HashMap<>();
							boardDetailParam.put("userEmail", (String) pAction.get("email"));
							boardDetailParam.put("boardMenuTitle", "인사");
							mav.addObject("boardDetailList", boardService.selectListWroteBoardContents(boardDetailParam));
							mav.addObject("oneUserInfo", memberService.selectUserByUserid((String) pAction.get("email")));
						break;
					}
				}
				switch (strFullUrl) {
					case "/project":
						mav.setViewName( "front/sub/board/project/"+action );
						break;
					case "/jl-community/news":
						mav.setViewName( "front/sub/board/news/"+action );
						break;
					case "/jl-community/Inspiration":
						mav.setViewName( "front/sub/board/Inspiration/"+action );
						break;
					case "/jl-community/Inspiration/Inspiring":
						String viewName = "front/sub/board/Inspiration/Inspiring";
						if ( action.equals("form") ) {
							viewName = "front/sub/board/Inspiration/form";
						} else if ( action.equals("view") ) {
							viewName = "front/sub/board/Inspiration/view";
						}
						HashMap<String, Object> inspiringParam = new HashMap<>();
						inspiringParam.put("boardId", boardSetting.getId());
						inspiringParam.put("itemStartPosition", 0);
						inspiringParam.put("pagePerSize", 10);
						inspiringParam.put("useYn", "Y");
						inspiringParam.put("deleteYn", "N");
						inspiringParam.put("sort", "id");
						inspiringParam.put("sortOrder", "desc");
						inspiringParam.put("assembleDateRange", "month");
						int totalInspiringCount = boardService.selectCountBoardContents(inspiringParam);

						PagingDTO paging = new PagingDTO(totalInspiringCount, page, 0, boardSetting.getDefaultPageSize());
						inspiringParam.put("itemStartPosition", paging.getItemStartPosition());
						inspiringParam.put("pagePerSize", paging.getPagePerSize());
						mav.addObject("list", boardService.selectListBoardContents(inspiringParam));
						mav.addObject("paging", paging);
						mav.setViewName( viewName );
						break;
					case "/jl-community/Inspiration/finish-Inspiration":
						String finishViewName = "front/sub/board/Inspiration/finish-Inspiration";
						if ( action.equals("form") ) {
							finishViewName = "front/sub/board/Inspiration/form";
						} else if ( action.equals("view") ) {
							finishViewName = "front/sub/board/Inspiration/view";
						}
						HashMap<String, Object> finishInspiringParam = new HashMap<>();
						finishInspiringParam.put("boardId", boardSetting.getId());
						finishInspiringParam.put("itemStartPosition", 0);
						finishInspiringParam.put("pagePerSize", 10);
						finishInspiringParam.put("useYn", "Y");
						finishInspiringParam.put("deleteYn", "N");
						finishInspiringParam.put("sort", "id");
						finishInspiringParam.put("sortOrder", "desc");
						finishInspiringParam.put("assembleDateRange", "finish");
						int totalFinishInspiringCount = boardService.selectCountBoardContents(finishInspiringParam);
						PagingDTO finishInspiringPaging = new PagingDTO(totalFinishInspiringCount, page, 0, boardSetting.getDefaultPageSize());
						finishInspiringParam.put("itemStartPosition", finishInspiringPaging.getItemStartPosition());
						finishInspiringParam.put("pagePerSize", finishInspiringPaging.getPagePerSize());
						mav.addObject("list", boardService.selectListBoardContents(finishInspiringParam));
						mav.addObject("paging", finishInspiringPaging);
						mav.setViewName( finishViewName );
						break;
					/*
					case "faq":
						mav.setViewName( "front/sub/board/"+menuSubType+"/"+action );
						break;
					case "qna":
						mav.setViewName( "front/sub/board/"+menuSubType+"/"+action );
						break;
					case "inquiry":
						mav.setViewName( "front/sub/board/"+menuSubType+"/"+action );
						break;
					case "review":
						mav.setViewName( "front/sub/board/"+menuSubType+"/"+action );
						break;
					case "common":
					*/
					default:
						mav.setViewName( "front/sub/board/"+boardSetting.getTypeCode()+"/"+action );
						break;
				}

				break;
			}
			default:
				break;
			}
		} catch(Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage());
		}

//		mav.setViewName( "/front/sub/contents"+fullUrl );
	}

	@PutMapping("/popup/dismiss")
    @ResponseBody
    public HashMap<String, Object> popupDismiss (HttpServletResponse response,
                                          @RequestParam(value="timeKey", defaultValue="") String timeKey,
                                          @RequestParam(value="period", defaultValue="1") int period
    ){
        HashMap<String, Object> resultMap = new HashMap<>();
        try {
            Cookie cookie = new Cookie(cookieName + "popup." + timeKey, "0");
            cookie.setMaxAge(period * 24 * 60 * 60);
            cookie.setDomain(cookieDomain);
            cookie.setPath("/");
            response.addCookie(cookie);
            resultMap.put("result", "success");
            resultMap.put("message", "처리완료");
        }catch (Exception e){
            resultMap.put("result", "error");
            resultMap.put("message", e.getMessage());
            logger.error(e.getMessage());
            e.printStackTrace();
        }
        return resultMap;
    }

	//청풍용 html 가져오기
	@GetMapping("/page/scrape")
	@ResponseBody
    public String scrapeContent(@RequestParam String url) {
        return pageService.scrapeSPAContent(url);
    }

	/**
	 * 상품의 예약 가능 여부를 검증하고 설정합니다.
	 * 날짜뿐만 아니라 시간까지 정밀하게 비교하여 예약 가능 여부를 판단합니다.
	 *
	 * @param product 검증할 상품 정보
	 */
	private void validateAndSetReservationFlag(ProductInfo product) {
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date currentDateTime = new Date();
			Date reservStartDate = null;
			Date reservEndDate = null;

			// 예약 시작/종료 날짜 파싱
			if (product.getProductReservStartDate() != null) {
				reservStartDate = sdf.parse(product.getProductReservStartDate());
			}
			if (product.getProductReservEndDate() != null) {
				reservEndDate = sdf.parse(product.getProductReservEndDate());
				// endDate의 시간을 23:59:59로 설정
				Calendar calendar = Calendar.getInstance();
				calendar.setTime(reservEndDate);
				calendar.set(Calendar.HOUR_OF_DAY, 23);
				calendar.set(Calendar.MINUTE, 59);
				calendar.set(Calendar.SECOND, 59);
				calendar.set(Calendar.MILLISECOND, 999);
				reservEndDate = calendar.getTime();
			}

			// 예약 가능 여부 검증 (시간 고려)
			boolean isReservationAvailable = checkReservationAvailabilityWithTime(
				product, reservStartDate, reservEndDate, currentDateTime);

			// 운영일 검증
			if (isReservationAvailable) {
				isReservationAvailable = validateOperationDays(product);
			}

			product.setReservRangeFlag(isReservationAvailable ? "Y" : "N");

		} catch (Exception e) {
			// 예외 발생 시 예약 불가능으로 설정
			product.setReservRangeFlag("N");
		}
	}

	/**
	 * 시간을 고려한 정밀한 예약 가능 여부 검증
	 * 예약 가능 기간과 상품 시작 시간을 모두 고려하여 예약 가능 여부를 판단합니다.
	 *
	 * @param product 상품 정보
	 * @param reservStartDate 예약 시작 날짜
	 * @param reservEndDate 예약 종료 날짜
	 * @param currentDateTime 현재 날짜와 시간
	 * @return 예약 가능 여부
	 */
	private boolean checkReservationAvailabilityWithTime(ProductInfo product, Date reservStartDate,
			Date reservEndDate, Date currentDateTime) {

		// 먼저 예약 가능 기간 내에 있는지 확인
		if (!isWithinReservationPeriod(reservStartDate, reservEndDate, currentDateTime)) {
			return false;
		}

		// 예약 가능 기간 내에 있으면 시간 검증 수행
		return checkPossibleDayListWithTime(product, currentDateTime);
	}

	/**
	 * 현재 시간이 예약 가능 기간 내에 있는지 확인합니다.
	 *
	 * @param reservStartDate 예약 시작 날짜
	 * @param reservEndDate 예약 종료 날짜
	 * @param currentDateTime 현재 날짜와 시간
	 * @return 예약 가능 기간 내 여부
	 */
	private boolean isWithinReservationPeriod(Date reservStartDate, Date reservEndDate, Date currentDateTime) {
		// 두 날짜 모두 null인 경우 - 항상 예약 가능한 상품
		if (reservStartDate == null && reservEndDate == null) {
			return true;
		}

		// reservStartDate만 null이 아닌 경우 - 시작일부터 예약 가능
		if (reservStartDate != null && reservEndDate == null) {
			return reservStartDate.before(currentDateTime) || isSameDay(reservStartDate, currentDateTime);
		}

		// reservEndDate만 null이 아닌 경우 - 종료일까지 예약 가능
		if (reservStartDate == null && reservEndDate != null) {
			return reservEndDate.after(currentDateTime) || isSameDay(reservEndDate, currentDateTime);
		}

		// 두 날짜 모두 null이 아닌 경우 - 기간 내에서만 예약 가능
		if (reservStartDate != null && reservEndDate != null) {
			boolean afterStart = reservStartDate.before(currentDateTime) || isSameDay(reservStartDate, currentDateTime);
			boolean beforeEnd = reservEndDate.after(currentDateTime) || isSameDay(reservEndDate, currentDateTime);
			return afterStart && beforeEnd;
		}

		return false;
	}

	/**
	 * 두 날짜가 같은 날인지 확인합니다.
	 */
	private boolean isSameDay(Date date1, Date date2) {
		if (date1 == null || date2 == null) {
			return false;
		}

		Calendar cal1 = Calendar.getInstance();
		Calendar cal2 = Calendar.getInstance();
		cal1.setTime(date1);
		cal2.setTime(date2);

		return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
			   cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR);
	}

	/**
	 * 시간을 고려하여 가능한 날짜 목록을 확인합니다.
	 * 각 날짜별로 상품 시작 시간과 현재 시간을 비교합니다.
	 *
	 * @param product 상품 정보
	 * @param currentDateTime 현재 날짜와 시간
	 * @return 예약 가능 여부
	 */
	private boolean checkPossibleDayListWithTime(ProductInfo product, Date currentDateTime) {
		try {
			product.setPickPeopleCount(1);
			ArrayList<String> possibleList = productService.getCalendarOneProgramResvNotCalendarPossibleDayList(product);

			if (possibleList.size() == 0) {
				return false;
			}

			// 상품 시작 시간이 없으면 기본 검증만 수행
			if (isEmptyOrNull(product.getProductRunStartTime())) {
				return possibleList.size() > 0;
			}

			// 현재 날짜와 시간 정보
			Calendar currentCal = Calendar.getInstance();
			currentCal.setTime(currentDateTime);

			// 오늘 날짜 문자열 생성
			String todayStr = String.format("%04d-%02d-%02d",
				currentCal.get(Calendar.YEAR),
				currentCal.get(Calendar.MONTH) + 1,
				currentCal.get(Calendar.DAY_OF_MONTH));

			// 가능한 날짜 목록을 순회하며 예약 가능한 날짜가 있는지 확인
			for (String possibleDate : possibleList) {
				if (isDateTimeAvailable(possibleDate, todayStr, product, currentDateTime)) {
					return true; // 하나라도 예약 가능한 날짜가 있으면 true
				}
			}

			return false; // 모든 날짜가 예약 불가능하면 false

		} catch (Exception e) {
			// 예외 발생 시 안전하게 false 반환
			return false;
		}
	}

	/**
	 * 특정 날짜의 예약 가능 여부를 확인합니다.
	 * 오늘 날짜인 경우 시간을 비교하고, 미래 날짜는 항상 예약 가능합니다.
	 *
	 * @param possibleDate 확인할 날짜 (yyyy-MM-dd 형식)
	 * @param todayStr 오늘 날짜 (yyyy-MM-dd 형식)
	 * @param product 상품 정보
	 * @param currentDateTime 현재 날짜와 시간
	 * @return 해당 날짜의 예약 가능 여부
	 */
	private boolean isDateTimeAvailable(String possibleDate, String todayStr, ProductInfo product, Date currentDateTime) {
		try {
			// 오늘 날짜인 경우에만 시간 비교 수행
			if (possibleDate.equals(todayStr)) {
				// 오늘 날짜에서 상품 시작 시간이 이미 지났으면 예약 불가능
				return isProductTimeAvailable(product, currentDateTime);
			} else {
				// 미래 날짜는 항상 예약 가능
				return true;
			}
		} catch (Exception e) {
			return true; // 예외 발생 시 안전하게 예약 가능으로 처리
		}
	}

	/**
	 * 상품 시작 시간이 현재 시간보다 1분 이상 뒤에 있는지 확인합니다.
	 * product_run_start_time과 product_run_end_time 필드를 우선적으로 사용합니다.
	 *
	 * @param product 상품 정보
	 * @param currentDateTime 현재 날짜와 시간
	 * @return 예약 가능 여부 (true: 예약 가능, false: 예약 불가능)
	 */
	private boolean isProductTimeAvailable(ProductInfo product, Date currentDateTime) {
		try {
			String productStartTime = product.getProductRunStartTime();
			String productEndTime = product.getProductRunEndTime();

			// 상품 시작 시간이 없으면 예약 가능으로 처리
			if (isEmptyOrNull(productStartTime)) {
				return true;
			}

			// productRunStartTime과 productRunEndTime이 별도로 있는 경우 (우선 처리)
			if (!isEmptyOrNull(productEndTime)) {
				return checkSeparateTimeAvailability(productStartTime, productEndTime, currentDateTime);
			}

			// 시간 범위 처리 (productRunStartTime에 "~"가 포함된 경우)
			if (productStartTime.contains("~")) {
				return checkTimeRangeAvailability(productStartTime, currentDateTime);
			}

			// 단일 시간 처리
			return checkSingleTimeAvailability(productStartTime, currentDateTime);

		} catch (Exception e) {
			// 파싱 오류 등 예외 발생 시 안전하게 예약 가능으로 처리
			return true;
		}
	}

	/**
	 * 시간 범위 형태("14:00~16:00")의 예약 가능 여부를 확인합니다.
	 */
	private boolean checkTimeRangeAvailability(String timeRange, Date currentDateTime) {
		try {
			String[] rangeParts = timeRange.split("~");
			if (rangeParts.length != 2) {
				return true; // 형식이 잘못된 경우 안전하게 예약 가능으로 처리
			}

			String startTimeStr = rangeParts[0].trim();
			String endTimeStr = rangeParts[1].trim();

			// 시작 시간이 현재 시간보다 1분 이상 뒤에 있는지 확인
			return checkSingleTimeAvailability(startTimeStr, currentDateTime);

		} catch (Exception e) {
			return true;
		}
	}

	/**
	 * 별도 필드로 분리된 시작/종료 시간의 예약 가능 여부를 확인합니다.
	 * product_run_start_time과 product_run_end_time이 별도로 존재하는 경우 처리합니다.
	 *
	 * @param startTime 상품 시작 시간 (HH:mm 형식)
	 * @param endTime 상품 종료 시간 (HH:mm 형식)
	 * @param currentDateTime 현재 날짜와 시간
	 * @return 예약 가능 여부
	 */
	private boolean checkSeparateTimeAvailability(String startTime, String endTime, Date currentDateTime) {
		try {
			// 시작 시간이 현재 시간보다 1분 이상 뒤에 있는지 확인
			// 시간 범위가 있어도 시작 시간 기준으로 판단
			return checkSingleTimeAvailability(startTime, currentDateTime);

		} catch (Exception e) {
			return true;
		}
	}

	/**
	 * 단일 시간의 예약 가능 여부를 확인합니다.
	 */
	private boolean checkSingleTimeAvailability(String timeStr, Date currentDateTime) {
		try {
			// 시간 파싱 (HH:mm 형식)
			String[] timeParts = timeStr.split(":");
			if (timeParts.length != 2) {
				return true; // 형식이 잘못된 경우 안전하게 예약 가능으로 처리
			}

			int productHour = Integer.parseInt(timeParts[0].trim());
			int productMinute = Integer.parseInt(timeParts[1].trim());

			// 오늘 날짜의 상품 시작 시간으로 Calendar 생성
			Calendar productTimeCal = Calendar.getInstance();
			productTimeCal.setTime(currentDateTime); // 현재 날짜 기준
			productTimeCal.set(Calendar.HOUR_OF_DAY, productHour);
			productTimeCal.set(Calendar.MINUTE, productMinute);
			productTimeCal.set(Calendar.SECOND, 0);
			productTimeCal.set(Calendar.MILLISECOND, 0);

			// 현재 시간에 1분을 더한 시간 계산 (최소 1분 여유 필요)
			Calendar minRequiredTime = Calendar.getInstance();
			minRequiredTime.setTime(currentDateTime);
			minRequiredTime.add(Calendar.MINUTE, 1);

			// 상품 시작 시간이 현재 시간 + 1분보다 뒤에 있으면 예약 가능
			return productTimeCal.after(minRequiredTime);

		} catch (Exception e) {
			return true;
		}
	}

	/**
	 * 문자열이 null이거나 빈 문자열인지 확인합니다.
	 */
	private boolean isEmptyOrNull(String str) {
		return str == null || str.trim().isEmpty();
	}

	/**
	 * 운영일을 검증합니다.
	 */
	private boolean validateOperationDays(ProductInfo product) {
		HashMap<String, Object> paramProduct = new HashMap<>();
		paramProduct.put("productTourId", product.getProductTourId());
		ArrayList<DayList> dayList = productService.selectOneProductOperationDay(paramProduct);
		return dayList.size() > 0;
	}

	/**
	 * 예약 상태에 따라 상품 목록을 필터링합니다.
	 *
	 * @param allProductList 전체 상품 목록
	 * @param request HTTP 요청 객체
	 * @param mav ModelAndView 객체
	 * @return 필터링된 상품 목록
	 */
	private ArrayList<ProductInfo> applyReservationFilter(ArrayList<ProductInfo> allProductList,
			HttpServletRequest request, ModelAndView mav) {

		String reservRangeFlag = request.getParameter("reservRangeFlag");

		if (reservRangeFlag != null) {
			if ("end".equals(reservRangeFlag)) {
				// 예약 종료된 상품만 필터링
				allProductList = allProductList.stream()
					.filter(product -> "N".equals(product.getReservRangeFlag()))
					.collect(Collectors.toCollection(ArrayList::new));
			} else if ("ing".equals(reservRangeFlag)) {
				// 예약 진행 중인 상품만 필터링
				allProductList = allProductList.stream()
					.filter(product -> "Y".equals(product.getReservRangeFlag()))
					.collect(Collectors.toCollection(ArrayList::new));
			}
			mav.addObject("reservRangeFlag", reservRangeFlag);
		} else if (request.getRequestURL().toString().contains("program")) {
			// program URL인 경우 예약 가능한 상품만 표시
			allProductList = allProductList.stream()
				.filter(product -> "Y".equals(product.getReservRangeFlag()))
				.collect(Collectors.toCollection(ArrayList::new));
			mav.addObject("reservRangeFlag", "Y");
		}

		return allProductList;
	}

	/**
	 * 페이징을 적용하여 최종 상품 목록을 생성합니다.
	 *
	 * @param allProductList 전체 상품 목록
	 * @param page 현재 페이지 번호
	 * @param mav ModelAndView 객체
	 * @return 페이징 처리된 상품 목록
	 */
	private ArrayList<ProductInfo> applyPagingToProductList(ArrayList<ProductInfo> allProductList,
			int page, ModelAndView mav) {

		// 필터링된 전체 개수로 페이징 정보 계산
		int totalCount = allProductList.size();
		PagingDTO paging = new PagingDTO(totalCount, page, 0, 16);

		// 페이징 처리된 최종 목록 생성
		int start = paging.getItemStartPosition();
		int end = Math.min(start + paging.getPagePerSize(), allProductList.size());
		ArrayList<ProductInfo> productList = new ArrayList<>(allProductList.subList(start, end));

		// ModelAndView에 페이징 정보 추가
		mav.addObject("paging", paging);

		return productList;
	}

	/**
	 * 상품 조회를 위한 기본 파라미터를 설정합니다.
	 *
	 * @param param 파라미터 맵
	 * @param menu 메뉴 정보
	 * @param pSearch 검색 파라미터
	 */
	private void setupProductSearchParameters(HashMap<String, Object> param, MenuUser menu,
			Map<String, Object> pSearch) {

		// 기본 상품 조회 조건 설정
		param.put("productUseYn", "Y");
		param.put("regcyYn", "N");
		param.put("deleteYn", "N");
		param.put("orderYn", "Y");

		// 로그인 사용자 정보 추가
		Object object = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		if (object instanceof LoginUser _user) {
			param.put("likeUserEmail", _user.getUsername());
		}

		// 검색 조건 설정
		param.put("searchType", pSearch.get("searchType"));
		param.put("searchKey", pSearch.get("searchKey"));

		// 메뉴 ID 설정
		if ("N".equals(menu.getVirtualYn())) {
			param.put("menuId", menu.getMenuId());
		} else {
			param.put("menuId", menu.getRootMenuId());
		}

		// 카테고리 필터 설정
		if (pSearch.get("categoryId") != null) {
			param.put("productCategoryId", pSearch.get("keyword"));
		}
	}

	/**
	 * 시간 기반 예약 검증 기능 테스트용 메서드
	 * 개발/테스트 환경에서만 사용하세요.
	 *
	 * 사용 예시:
	 * - /test/reservation-time-validation?productRunStartTime=15:00
	 * - /test/reservation-time-validation?productRunStartTime=14:00~16:00&testCurrentTime=2024-06-30 15:00
	 * - /test/reservation-time-validation?productRunStartTime=14:00&productRunEndTime=16:00&testCurrentTime=2024-06-30 15:00
	 *
	 * 실제 시나리오 테스트:
	 * - 06-30일 15:00에 14:00~16:00 상품 예약 시도 (예약 불가능)
	 * - 06-30일 13:00에 14:00~16:00 상품 예약 시도 (예약 가능)
	 * - 07-01일 언제든지 14:00~16:00 상품 예약 시도 (예약 가능)
	 */
	@GetMapping("/test/reservation-time-validation")
	@ResponseBody
	public HashMap<String, Object> testReservationTimeValidation(
			@RequestParam(value = "productRunStartTime", defaultValue = "15:00") String productRunStartTime,
			@RequestParam(value = "productRunEndTime", required = false) String productRunEndTime,
			@RequestParam(value = "testCurrentTime", required = false) String testCurrentTime,
			@RequestParam(value = "reservEndDate", required = false) String reservEndDate) {

		HashMap<String, Object> result = new HashMap<>();

		try {
			// 테스트용 상품 정보 생성
			ProductInfo testProduct = new ProductInfo();
			testProduct.setProductRunStartTime(productRunStartTime);
			if (productRunEndTime != null && !productRunEndTime.trim().isEmpty()) {
				testProduct.setProductRunEndTime(productRunEndTime);
			}
			testProduct.setPickPeopleCount(1);

			// 현재 시간 또는 테스트 시간 설정
			Date currentDateTime;
			if (testCurrentTime != null && !testCurrentTime.trim().isEmpty()) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
				currentDateTime = sdf.parse(testCurrentTime);
			} else {
				currentDateTime = new Date();
			}

			// 예약 종료일 설정 (테스트용)
			Date testReservEndDate = null;
			if (reservEndDate != null && !reservEndDate.trim().isEmpty()) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				testReservEndDate = sdf.parse(reservEndDate + " 23:59:59");
			}

			// 전체 예약 검증 (예약 기간 + 시간 검증)
			boolean isAvailable;
			if (testReservEndDate != null) {
				isAvailable = checkReservationAvailabilityWithTime(testProduct, null, testReservEndDate, currentDateTime);
			} else {
				// 시간 기반 예약 가능 여부 검증만 수행
				isAvailable = isProductTimeAvailable(testProduct, currentDateTime);
			}

			// 결과 정보 구성
			SimpleDateFormat displayFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			result.put("success", true);
			result.put("productRunStartTime", productRunStartTime);
			if (productRunEndTime != null && !productRunEndTime.trim().isEmpty()) {
				result.put("productRunEndTime", productRunEndTime);
			}
			if (reservEndDate != null && !reservEndDate.trim().isEmpty()) {
				result.put("reservEndDate", reservEndDate);
			}
			result.put("currentDateTime", displayFormat.format(currentDateTime));
			result.put("isReservationAvailable", isAvailable);

			// 상세한 메시지 생성
			String message;
			if (isAvailable) {
				message = "✅ 예약 가능: ";
				if (testReservEndDate != null) {
					message += "예약 기간 내이고 상품 시작 시간이 현재 시간보다 1분 이상 뒤에 있습니다.";
				} else {
					message += "상품 시작 시간이 현재 시간보다 1분 이상 뒤에 있습니다.";
				}
			} else {
				message = "❌ 예약 불가능: ";
				if (testReservEndDate != null && testReservEndDate.before(currentDateTime)) {
					message += "예약 기간이 종료되었습니다.";
				} else {
					message += "상품 시작 시간이 현재 시간보다 1분 이내에 있거나 이미 지났습니다.";
				}
			}
			result.put("message", message);

			// 상세 정보 추가
			Calendar currentCal = Calendar.getInstance();
			currentCal.setTime(currentDateTime);

			String[] timeParts = productRunStartTime.split(":");
			if (timeParts.length == 2) {
				int productHour = Integer.parseInt(timeParts[0].trim());
				int productMinute = Integer.parseInt(timeParts[1].trim());

				Calendar productTimeCal = Calendar.getInstance();
				productTimeCal.setTime(currentDateTime);
				productTimeCal.set(Calendar.HOUR_OF_DAY, productHour);
				productTimeCal.set(Calendar.MINUTE, productMinute);
				productTimeCal.set(Calendar.SECOND, 0);
				productTimeCal.set(Calendar.MILLISECOND, 0);

				Calendar minRequiredTime = Calendar.getInstance();
				minRequiredTime.setTime(currentDateTime);
				minRequiredTime.add(Calendar.MINUTE, 1);

				result.put("productStartDateTime", displayFormat.format(productTimeCal.getTime()));
				result.put("minRequiredTime", displayFormat.format(minRequiredTime.getTime()));
				result.put("timeDifferenceMinutes",
					(productTimeCal.getTimeInMillis() - currentDateTime.getTime()) / (1000 * 60));
			}

		} catch (Exception e) {
			result.put("success", false);
			result.put("error", e.getMessage());
		}

		return result;
	}
}
