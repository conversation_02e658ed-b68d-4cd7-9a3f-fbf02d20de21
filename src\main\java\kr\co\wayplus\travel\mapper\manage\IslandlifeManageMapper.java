package kr.co.wayplus.travel.mapper.manage;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import kr.co.wayplus.travel.model.Islandlife;
import kr.co.wayplus.travel.model.IslandlifeAttachFile;

@Mapper
@Repository
public interface IslandlifeManageMapper {
	int selectCountIslandlife(HashMap<String, Object> paramMap);
	ArrayList<Islandlife> selectListIslandlife(HashMap<String, Object> paramMap);
	Islandlife selectOneIslandlife(HashMap<String, Object> paramMap);
	void insertIslandlife(Islandlife cnt) throws SQLException;
	void updateIslandlife(Islandlife cnt) throws SQLException;
	void deleteIslandlife(Islandlife cnt) throws SQLException;

	ArrayList<IslandlifeAttachFile> selectListIslandlifeAttachFile(HashMap<String, Object> paramMap);
	IslandlifeAttachFile selectOneIslandlifeAttachFile(HashMap<String, Object> paramMap);
	void insertIslandlifeAttachFile(IslandlifeAttachFile baf) throws SQLException;
	void deleteIslandlifeAttachFile(IslandlifeAttachFile baf) throws SQLException;
}