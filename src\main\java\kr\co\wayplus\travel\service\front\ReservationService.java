package kr.co.wayplus.travel.service.front;

import kr.co.wayplus.travel.mapper.front.ReservationMapper;
import kr.co.wayplus.travel.model.Reservation;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

@Service
public class ReservationService {

    private final ReservationMapper mapper;

    public ReservationService(ReservationMapper reservationMapper) {
        this.mapper = reservationMapper;
    }

    public int selectCountReservation(HashMap<String, Object> paramMap) {
        return mapper.selectCountReservation(paramMap);
    }

    public ArrayList<Reservation> selectListReservation(HashMap<String, Object> paramMap) {
        return mapper.selectListReservation(paramMap);
    }

    public Reservation selectLOneReservation(HashMap<String, Object> paramMap) {
        return mapper.selectOneReservation(paramMap);
    }

    public void insertReservation(Reservation reservation) throws SQLException {
        mapper.insertReservation(reservation);
    }

    public void updateReservation(Reservation reservation) throws SQLException{
        mapper.updateReservation(reservation);
    }
}
