package kr.co.wayplus.travel.model.excel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class ExcelMetadata {
    private String sheetName;
    private List<List<HeaderInfo>> headers;
    private Map<String, String> columnMapping; // DB 컬럼명과 Excel 컬럼명 매핑
    private int startRow;
    private int startCol;

    public static class ExcelMetadataBuilder {
        private List<List<HeaderInfo>> headers = new ArrayList<>();
        private Map<String, String> columnMapping = new HashMap<>();

        public ExcelMetadataBuilder addHeaderRow(List<HeaderInfo> headerRow) {
            this.headers.add(headerRow);
            return this;
        }

        public ExcelMetadataBuilder addColumnMapping(String dbColumn, String excelColumn) {
            this.columnMapping.put(dbColumn, excelColumn);
            return this;
        }
        public ExcelMetadataBuilder addColumnMapping(Map<String, String> columnMapping) {
        	this.columnMapping = columnMapping;
        	return this;
        }
    }
}