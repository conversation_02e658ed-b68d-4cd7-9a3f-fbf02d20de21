@charset "UTF-8";

.way-table{font-family: 'Pretendard';font-size: 0.938rem;margin-top: 6px !important;margin-bottom: 6px !important;}

.way-table thead th{border-top: 2px solid #333333;background-color:#EDEEF3; }
/*.way-table tbody th,.way-table tbody td{background-color:#FFF;}*/
.way-table tbody th,.way-table tbody td{vertical-align: middle;}
.way-table tbody tr:last-child th,
.way-table tbody tr:last-child td{border-bottom: 2px solid #333333;}
.way-table img:default {width: 100px;height: 100px;max-width: 100%;}
.way-table img.fit_w100 {max-width: 100%;}

.table-hover tbody tr{cursor: pointer;}

.table-menu{
	width: 100%;
	border: 1px solid #dee2e6;
	margin-bottom: 1rem;
    background-color: #EDEEF3;
}

.table-menu th, .table-menu td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
}

.table-menu thead th {
    vertical-align: middle;
    border-bottom: 2px solid #dee2e6;
    font-size: 1rem;
}

.table-menu th, .table-menu td {
    /*border: 1px solid #dee2e6;*/
    border-top: 2px solid #333333;
}

.table-menu thead th, .table-menu thead td {
    border-bottom-width: 2px;
}
.way-table .box-label{display: inline-block;min-width: 90%;color: #FFF;border: 1px solid #999999;border-radius: 5px;background-color: #999999;text-align: center;}
.way-table .toggle-group{z-index:1038;}
.way-table .box-label.checking,
.way-table .box-label.receipt           {background-color: #d8530e;border-color: #d8530e;}
.way-table .box-label.progressing       {background-color: #e30b0b;border-color: #e30b0b;}
.way-table .box-label.waiting           {background-color: #f30b0b;border-color: #f30b0b;}
.way-table .box-label.promise           {background-color: #108115;border-color: #108115;}
.way-table .box-label.complete          {background-color: #309115;border-color: #309115;}
.way-table .box-label.cancelRequest     {background-color: #CC2170;border-color: #CC2170;}
.way-table .box-label.cancelComplete    {background-color: #f30b0b;border-color: #f30b0b;}
.way-table .box-label.stay              {background-color: #0374F8;border-color: #0374F8;}
.way-table .box-label.package           {background-color: #048409;border-color: #048409;}

.way-table .balance{ color: #e30b0b; }

.way-table tr.linked {
    background-color: var(--way-color-blue);
}
.way-table tr.selected {
    background-color: #1177EE60 !important;
}
.way-table tr.linked td{ color:#eee; }
.user_badge_list_img {
    width: 17%;
}

@media screen and (min-width:1024px) {
	.way-table {
		/*table-layout: fixed;*/
		max-width: 100%;
	}
}
@media screen and (max-width:1000px) {
	.way-table th, .way-table td {
		font-size: 0.8rem;
	}
}