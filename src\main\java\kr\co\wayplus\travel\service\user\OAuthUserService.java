package kr.co.wayplus.travel.service.user;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;

import org.apache.commons.httpclient.util.HttpURLConnection;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.oauth2.client.userinfo.DefaultOAuth2UserService;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.OAuthAttributes;


@Service
public class OAuthUserService extends DefaultOAuth2UserService {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final UserService userService;
    private final UserPointService userPointService;

    public OAuthUserService(UserService userService, UserPointService userPointService) {
        this.userService = userService;
        this.userPointService = userPointService;
    }

    @Override
    public OAuth2User loadUser(OAuth2UserRequest userRequest) throws OAuth2AuthenticationException {
        logger.debug("OAuth2 Login Load User");

        OAuth2User oAuth2User = super.loadUser(userRequest);
        String registrationId = userRequest.getClientRegistration().getRegistrationId();
        String userNameAttributeName = userRequest.getClientRegistration().getProviderDetails().getUserInfoEndpoint().getUserNameAttributeName();
        OAuthAttributes attributes = OAuthAttributes.build(registrationId, userNameAttributeName, oAuth2User.getAttributes());

        LoginUser user = new LoginUser(AuthorityUtils.createAuthorityList("ROLE_USER"), attributes.getAttributes(), attributes.getNameAttributeKey());
        HashMap<String, Object> param = new HashMap<>();
        param.put("registrationId", registrationId);
        param.put("id", attributes.getId());
        if(userService.findUserCountById(param) > 0){
        	Map<String, Object> attrs = attributes.getAttributes();

            user = userService.loadUserByOAuthRegistration(registrationId, attributes.getId());
            user.setAttributes(attributes.getAttributes());
            user.setNameAttributeKey(userNameAttributeName);
            logger.debug(String.format("Find Exist User %s_%s", registrationId, attributes.getId()));

            switch (registrationId){
	            case "kakao":
	                //user.setKakaoToken(attributes.getId());
	                //user.setKakaoEmail(attributes.getEmail());
	            	
	            	
	            	
	                break;
	            case "naver":
	                user.setNaverToken(attributes.getId());
	                user.setNaverEmail(attributes.getEmail());

	                if( attrs.containsKey(userNameAttributeName) ) {
		                HashMap<String, Object> repsones = (HashMap<String, Object>) attrs.get(userNameAttributeName);

		                if( repsones.containsKey("gender") ) {
		                	user.setUserGender( repsones.get("gender").toString() );
		                }
		                if( repsones.containsKey("mobile") ) {
		                	user.setUserMobile( repsones.get("mobile").toString() );
		                }
		                if( repsones.containsKey("birthyear") && repsones.containsKey("birthday") ) {
		                	String _birthday = repsones.get("birthyear").toString() + "-" + repsones.get("birthday").toString();
		                	user.setUserBirthday(_birthday);
		                }
	                }

	                break;
	            case "google":
	                //user.setGoogleToken(attributes.getId());
	                //user.setGoogleEmail(attributes.getEmail());
	            	
	            	 String accessToken = userRequest.getAccessToken().getTokenValue();
	            	 Map<String, Object> map =  fetchAdditionalUserInfo(accessToken);
	            	 
	            	 
	            	/*
	            	 * {
	            	 * resourceName=people/118040876563493461871, 
	            	 * etag=%EgUBBwguNxoEAQIFByIMNCtpR2tqbTM2UFU9, 
	            	 * genders=[{metadata={primary=true, 
	            	 * source={type=PROFILE, id=118040876563493461871}}, value=male, formattedValue=Male}], 
	            	 * birthdays=[
	            	 *  {metadata={primary=true, source={type=PROFILE, id=118040876563493461871}},date={year=1985, month=5, day=3}}, 
	            	 *  {metadata={source={type=ACCOUNT, id=118040876563493461871}},date={year=1985, month=5, day=3}}]
	            	 * }
	            	 */
	            	 
	            	 
	            	 System.out.println( map.toString());
	            	 
	            	 
	            	
	            	if( map !=null ) {
	            		if( map.containsKey("genders") ) {
	            			List<Map<String, Object>> genders = (List<Map<String, Object>>) map.get("genders");
	            			if (genders != null && !genders.isEmpty()) {
	            				String gender = (String) genders.get(0).get("value");
	            				user.setUserGender( gender.toLowerCase().startsWith("m") ? "M":"F" );
	            			}
		                }
	            		if( map.containsKey("birthdays") ) {
	            	        List<Map<String, Object>> birthdays = (List<Map<String, Object>>) map.get("birthdays");

	            			if (birthdays != null && !birthdays.isEmpty()) {
	            	            for (Map<String, Object> birthday : birthdays) {
	            	                Map<String, Object> metadata = (Map<String, Object>) birthday.get("metadata");
	            	                if (metadata != null && Boolean.TRUE.equals(metadata.get("primary"))) {
	            	                    Map<String, Object> date = (Map<String, Object>) birthday.get("date");
	            	                    if (date != null) {
	            	                        int year = (int) date.getOrDefault("year", 0);
	            	                        int month = (int) date.get("month");
	            	                        int day = (int) date.get("day");
	            	                        user.setUserBirthday( year > 0 ? String.format("%04d-%02d-%02d", year, month, day) : String.format("--%02d-%02d", month, day) );
	            	                    }
	            	                }
	            	            }
	            	        }
	            		}
	            		if( map.containsKey("phonenumbers") ) {
	            			List<Map<String, Object>> phoneNumbers = (List<Map<String, Object>>) map.get("phoneNumbers");
	            	        if (phoneNumbers != null && !phoneNumbers.isEmpty()) {
	            	        	user.setUserMobile( (String)phoneNumbers.get(0).get("value") ); // 첫 번째 전화번호 반환
	            	        }
		                }
	            	}
	            	
	            	
	                break;
	        }

            try{
            	userService.updateUserInfo(user, false);
                logger.debug("Update User Info.");
            }catch (RuntimeException | InvalidAlgorithmParameterException | NoSuchPaddingException |
                    IllegalBlockSizeException | NoSuchAlgorithmException | BadPaddingException | InvalidKeyException e){
                logger.debug("Update User Info Error");
                logger.error(e.getMessage());
            }
        }else{
            //신규 유저
            user.setNewUserInfo(attributes.getEmail(), attributes.getName(), registrationId);
            user.setUserTokenId(String.format("%s_%s", registrationId, attributes.getId()));
            switch (registrationId){
                case "kakao":
                    user.setKakaoToken(attributes.getId());
                    user.setKakaoEmail(attributes.getEmail());
                    break;
                case "naver":
                    user.setNaverToken(attributes.getId());
                    user.setNaverEmail(attributes.getEmail());
                    break;
                case "google":
                    user.setGoogleToken(attributes.getId());
                    user.setGoogleEmail(attributes.getEmail());
                    break;
            }
            user.setUserPassword(user.getUserTokenId());
            try{
                userService.addUser(user, false);
                userPointService.createJoinPoint(user);
                logger.debug("Create New User");
            }catch (RuntimeException | InvalidAlgorithmParameterException | NoSuchPaddingException |
                    IllegalBlockSizeException | NoSuchAlgorithmException | BadPaddingException | InvalidKeyException e){
                logger.debug("Create New User Error");
                logger.error(e.getMessage());
            }
        }

        return user;
    }
    
    private static final String GOOGLE_PEOPLE_API_URL = "https://people.googleapis.com/v1/people/me?personFields=genders,birthdays,addresses";

    public static Map<String, Object> fetchAdditionalUserInfo(String accessToken) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + accessToken);
        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<Map> response = restTemplate.exchange(GOOGLE_PEOPLE_API_URL, HttpMethod.GET, entity, Map.class);
        return response.getBody();
    }
    
 // OAuth2 인증 후 추가 API 호출
//    private static void getPeopleApiInfo(String accessToken, Map<String, Object> userInfo) {
//        String reqURL = "https://people.googleapis.com/v1/people/me?personFields=birthdays,phoneNumbers,genders";
//        
//        try {
//            URL url = new URL(reqURL);
//            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
//            conn.setRequestMethod("GET");
//            conn.setRequestProperty("Authorization", "Bearer " + accessToken);
//            
//            int responseCode = conn.getResponseCode();
//            System.out.println("People API responseCode: " + responseCode);
//            
//            BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
//            String line = "";
//            String result = "";
//            
//            while ((line = br.readLine()) != null) {
//                result += line;
//            }
//            
//            JSONObject jObject = new JSONObject(result);
//            
//            // 전화번호 정보 저장
//            if (jObject.has("phoneNumbers")) {
//                JSONArray phoneNumbers = jObject.getJSONArray("phoneNumbers");
//                if (phoneNumbers.length() > 0) {
//                    userInfo.put("phoneNumber", phoneNumbers.getJSONObject(0).getString("value"));
//                }
//            }
//            
//            // 생년월일 정보 저장
//            if (jObject.has("birthdays")) {
//                JSONArray birthdays = jObject.getJSONArray("birthdays");
//                if (birthdays.length() > 0) {
//                    JSONObject date = birthdays.getJSONObject(0).getJSONObject("date");
//                    String birthday = date.getInt("year") + "-" + date.getInt("month") + "-" + date.getInt("day");
//                    userInfo.put("birthday", birthday);
//                }
//            }
//            
//            // 성별 정보 저장
//            if (jObject.has("genders")) {
//                JSONArray genders = jObject.getJSONArray("genders");
//                if (genders.length() > 0) {
//                    userInfo.put("gender", genders.getJSONObject(0).getString("value"));
//                }
//            }
//            
//            br.close();
//            
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }


}




