package kr.co.wayplus.travel.web.manage;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartRequest;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import kr.co.wayplus.travel.base.web.BaseController;
import kr.co.wayplus.travel.model.CodeInfo;
import kr.co.wayplus.travel.model.LoginUser;
import kr.co.wayplus.travel.model.MenuUser;
import kr.co.wayplus.travel.model.PagingDTO;
import kr.co.wayplus.travel.model.PlaceSpot;
import kr.co.wayplus.travel.model.ProductCategory;
import kr.co.wayplus.travel.model.ProductDetailSchedule;
import kr.co.wayplus.travel.model.ProductDetailScheduleImage;
import kr.co.wayplus.travel.model.ProductInfo;
import kr.co.wayplus.travel.model.ProductInventory;
import kr.co.wayplus.travel.model.ProductJsonWrapper;
import kr.co.wayplus.travel.model.ProductPostJson;
import kr.co.wayplus.travel.model.ProductPriceOption;
import kr.co.wayplus.travel.model.ProductRestPeriod;
import kr.co.wayplus.travel.model.ProductTemplate;
import kr.co.wayplus.travel.model.ProductTemplateFile;
import kr.co.wayplus.travel.model.ProductTourImages;
import kr.co.wayplus.travel.model.ProductTourLink;
import kr.co.wayplus.travel.model.Reservation;
import kr.co.wayplus.travel.service.manage.MenuManageService;
import kr.co.wayplus.travel.service.manage.PlaceManageService;
import kr.co.wayplus.travel.service.manage.ProductManageService;
import kr.co.wayplus.travel.util.ReservationUtil;

@Controller
@RequestMapping("/manage/product")
public class ProductManageController extends BaseController {
	private final Logger logger = LoggerFactory.getLogger(getClass());

	private final String tourTemplateUrl = "/manage/sub";

	final String addPath = "product/";

	@Value("${upload.file.path}")
	private String fileUploadPath;

	@Value("${upload.file.max-size}")
	int maxFileSize;

	@Value("${key.api.kakao.rest}")
	private String REST_API_KEY;

	private final MenuManageService svcMenu;
	private final ProductManageService svcProduct;
	private final PlaceManageService svcPlace;
	private final MenuManageService menuManageService;

	private final ReservationUtil reservationUtil;

	@Autowired
	private ProductManageController(
			MenuManageService svcMenu,
			ProductManageService svcProduct,
			PlaceManageService svcPlace,
			MenuManageService menuManageService,
			ReservationUtil reservationUtil) {
		this.svcMenu = svcMenu;
		this.svcProduct = svcProduct;
		this.svcPlace = svcPlace;
		this.menuManageService = menuManageService;
		this.reservationUtil = reservationUtil;
	}

	@PostMapping("/all-list")
	@ResponseBody
	public HashMap<String, Object> allProductList(
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
			@Param(value = "isNotMe") String isNotMe) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			HashMap<String, Object> paramMap = new HashMap<>();

			paramMap.put("isNotMe", isNotMe);
			paramMap.put("regacyYn", "N");

			int totalCount = svcProduct.selectCountProduct(paramMap);
			PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
			// paramMap.put("itemStartPosition", paging.getItemStartPosition());
			// paramMap.put("pagePerSize", paging.getPagePerSize());

			List<ProductInfo> productList = svcProduct.selectListProduct(paramMap);

			resultMap.put("totalCount", totalCount);
			resultMap.put("paging", paging);
			resultMap.put("data", productList);

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	/*
	 * ################################### 상품 목록화면
	 * #####################################
	 */
	@GetMapping("/{typeCode}")
	public ModelAndView productListPage(@PathVariable(value = "typeCode") String typeCode,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "pageSize", defaultValue = "5") int pageSize,
			@RequestParam(value = "productStatus", defaultValue = "") String productStatus,
			@RequestParam(value = "searchType", defaultValue = "") String searchType,
			@RequestParam(value = "searchKey", defaultValue = "") String searchKey,
			@RequestParam(value = "productMenuId", defaultValue = "") String productMenuId,
			@RequestParam(value = "productCategoryId", defaultValue = "") String productCategoryId) {
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> paramMap = new HashMap<>();

		// 메뉴 정보 불러오기
		String manageUrl = "/" + typeCode;

		paramMap.put("menuUrl", manageUrl);
		// paramMap.put("menuType","product");
		MenuUser menu = svcMenu.selectOneMenuUser(paramMap);
		paramMap.clear();

		mav.addObject("menuName", menu.getMenuName());
		String menuTitle = menu.getMenuName();
		String menuType = menu.getMenuType();
		String menuSubType = menu.getMenuSubType();

		int menu_id = menu.getMenuId().intValue();

		// category 불러오기(2depth) : meneuUser
		ArrayList<MenuUser> menuCategory = svcMenu.selectListMenuUserSubListById(menu_id, "product");

		paramMap.put("menuId", menu_id);
		mav.addObject("menuId", menu_id);

		if (!productMenuId.equals(""))
			mav.addObject("productMenuId", Integer.parseInt(productMenuId));

		mav.addObject("menuType", menuType);
		mav.addObject("menuSubType", menuSubType);
		mav.addObject("menuCategory", menuCategory);

		// categroy 불러오기(3depth) : productCommonCategroy
		if (!productMenuId.equals("")) {
			ArrayList<ProductCategory> subCategories = svcProduct
					.getSubCategoryListByMenuId(Integer.parseInt(productMenuId));
			mav.addObject("subCategoryList", subCategories);
		}
		paramMap.put("searchType", searchType);

		// 상품정보 불러오기
		if (menuType.equals("product") || menuCategory != null) {
			paramMap.put("menuTitle", menuTitle);

			paramMap.put("deleteYn", "N");
			paramMap.put("orderYn", "createDate");
			paramMap.put("productMenuId", productMenuId);
			paramMap.put("productCategoryId", productCategoryId);
			paramMap.put("searchKey", searchKey);
			paramMap.put("productStatus", productStatus.substring(0));
			paramMap.put("menuSubType", menuSubType);

			// 상품 개수
			int totalCount = svcProduct.selectCountProduct(paramMap);
			PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
			paramMap.put("itemStartPosition", paging.getItemStartPosition());
			paramMap.put("pagePerSize", paging.getPagePerSize());
			// 상품 리스트
			List<ProductInfo> productList = svcProduct.selectListProduct(paramMap);

			// model 담기
			mav.addObject("totalCount", totalCount);
			mav.addObject("paging", paging);
			mav.addObject("productList", productList);

			String returnUrl = tourTemplateUrl + "/product/" + "product-list";

			mav.addObject("typeCode", typeCode);
			mav.addObject("p", paramMap);

			mav.setViewName(returnUrl);
		}
		return mav;
	}

	/*
	 * ################################### 상품 관리
	 * #####################################
	 */
	// 상품 등록 페이지
	@GetMapping("/{typeCode}/registration")
	public ModelAndView productAddPage(
			@PathVariable("typeCode") String typeCode) {
		ModelAndView mav = new ModelAndView();
		HashMap<String, Object> paramMap = new HashMap<>();
		try {
			// 메뉴 정보 불러오기
			String manageUrl = "/" + typeCode;

			paramMap.put("menuUrl", manageUrl);
			// paramMap.put("menuType","product");
			MenuUser menu = svcMenu.selectOneMenuUser(paramMap);
			paramMap.clear();
			// 메뉴정보
			paramMap.put("useYn", "Y");
			paramMap.put("menuId", menu.getMenuId());
			paramMap.put("menuType", menu.getMenuType());
			ArrayList<MenuUser> menuList = menuManageService.selectListMenuUser(paramMap);
			int menu_id = menu.getMenuId().intValue();
			if (menuList.size() > 0) {
				mav.addObject("listUrl", "/" + menuList.get(0).getMenuType() + "/" + typeCode);
			}
			String menuType = menu.getMenuType();
			String menuSubType = menu.getMenuSubType();

			// menuCategory 불러오기
			ArrayList<MenuUser> menuCategory = svcMenu.selectListMenuUserSubListById(menu_id, "product");
			paramMap.put("menuCategory", menuCategory);
			mav.addObject("policyInventory", false);

			mav.addObject("menu", menu);
			mav.addObject("menuId", menu.getMenuId());
			mav.addObject("menuName", menu.getMenuName());
			mav.addObject("menuCategory", menuCategory);
			mav.addObject("REST_API_KEY", REST_API_KEY);
			mav.addObject("mode", "I");

			// String returnUrl = tourTemplateUrl+"/"+ menuType
			// +"/"+menuSubType+"/"+"product-add";
			// String returnUrl = tourTemplateUrl+"/"+ menuType +"/"+"product-form";
			String returnUrl = tourTemplateUrl + "/product/" + "product-form";

			if (menuType.equals("product") || menuCategory.size() > 0) {

				/* 상위가 product가 아닌데 하위메뉴가 product인경우만. */
				if (!menuType.equals("product") && menuCategory.size() > 0) {
					menuSubType = menuCategory.get(0).getMenuSubType();
				}

				paramMap.put("productTourId", paramMap.get("upperMenuId"));
				// 기본 가격 제공용
				List<ProductDetailSchedule> detailScheduleList = svcProduct.selectProductDetailScheduleList(paramMap);
				mav.addObject("dayBtnInfoList", svcProduct.selectProductDetailScheduleBtnList(paramMap));
				for (ProductDetailSchedule productDetailSchedule : detailScheduleList) {
					productDetailSchedule
							.setImageNumList(svcProduct.selectProductDetailScheduleImageList(productDetailSchedule));
				}
				mav.addObject("detailScheduleList", detailScheduleList);

				/* 초기가격정보 전달 */
				paramMap.put("option_type", menuSubType);
				List<ProductPriceOption.DayList> fixPriceOptionList = svcProduct.selectPriceOptionList(paramMap);
				mav.addObject("fixPriceOptionList", fixPriceOptionList);

				// logger.info(tourTemplateUrl+"/"+ menuType
				// +"/"+menuSubType+"/"+"product-add");

				mav.addObject("productType", menuSubType);

				mav.addObject("productId", null); // 상품시퀀스
				mav.addObject("productInfo", new ProductInfo()); // 상품모델(빈껍데기)
				mav.addObject("productImageList", null); // 상품이미지
				mav.addObject("addBasicOptionId", null);
				mav.addObject("priceSelectValue", "fix");
				mav.setViewName(returnUrl);
			}

		} catch (Exception e) {
			logger.error(e.getMessage());
		}

		return mav;
	}

	// 상품 수정 페이지
	// TODO.. 추후 productNum이 아닌 serialNumber로 바꿀것
	@GetMapping("/{typeCode}/modify/{productNum}")
	public ModelAndView productModify(
			HttpServletRequest request,
			RedirectAttributes re,
			@PathVariable String typeCode,
			@PathVariable int productNum) {
		ModelAndView mav = new ModelAndView();

		HashMap<String, Object> paramMap = new HashMap<>();
		// 상품 기본정보
		paramMap.put("productTourId", productNum);
		ProductInfo productInfo = svcProduct.selectProductInfo(paramMap);

		if (productInfo.getRegacyYn().equals("Y")) {
			HashMap<String, Object> paramSerial = new HashMap<>();
			// paramMap.clear();
			paramSerial.put("productSerial", productInfo.getProductSerial());
			ProductInfo lastProductInfo = svcProduct.selectProductInfo(paramSerial);

			String url = "/manage/product/" + typeCode + "/modify/" + lastProductInfo.getProductTourId();

			Map<String, Object> attributes = new HashMap<>();
			Enumeration<?> params = request.getParameterNames();

			while (params.hasMoreElements()) {
				String name = (String) params.nextElement();
				attributes.put(name, request.getParameter(name));

				// params = params + name+ "=" + request.getParameter(name);
			}

			re.addAllAttributes(attributes);

			return new ModelAndView(new RedirectView(url));
		}

		mav.addObject("productInfo", productInfo);
		MenuUser currMenu = menuManageService.selectOneMenuUserById(productInfo.getProductMenuId());

		if (productInfo.getPolicyInventory() != null) {
			mav.addObject("policyInventory", productInfo.getPolicyInventory().equals("1"));
		} else {
			mav.addObject("policyInventory", false);
		}

		// ProductTemplate productTemplate =
		// service.selectProductTemplateById(paramMap);
		// mav.addObject("productTemplate",productTemplate);

		// 상품 이미지
		if (productInfo.getProductImages() != null) {
			String[] images = productInfo.getProductImages().split(",");
			if (images.length > 0) {
				paramMap.put("images", images);
				List<ProductTourImages> productImageList = svcProduct.selectProductInfoImageList(paramMap);
				mav.addObject("productImageList", productImageList);

				for (ProductTourImages item : productImageList) {
					if (productInfo.getProductThumbnail().contains(item.getUploadFilename())) {
						productInfo.setProductThumbnailId(item.getImageId());
						break;
					}
				}
			}
		}

		// 메뉴 정보 불러오기
		String manageUrl = "/" + typeCode;

		paramMap.put("menuUrl", manageUrl);
		// paramMap.put("menuType","product");
		MenuUser menu = svcMenu.selectOneMenuUser(paramMap);
		// paramMap.clear();

		mav.addObject("menu", menu);
		mav.addObject("menuId", productInfo.getProductMenuId());
		mav.addObject("menuName", menu.getMenuName());

		String menuType = menu.getMenuType();
		String menuSubType = menu.getMenuSubType();

		int menu_id = menu.getMenuId().intValue();
		paramMap.put("useYn", "Y");
		paramMap.put("menuId", menu.getMenuId());
		paramMap.put("menuType", menu.getMenuType());
		// 메뉴정보
		ArrayList<MenuUser> menuList = menuManageService.selectListMenuUser(paramMap);
		if (menuList.size() > 0) {
			mav.addObject("listUrl", "/" + menuList.get(0).getMenuType() + "/" + typeCode);
		}
		// menuCategory 불러오기
		ArrayList<MenuUser> menuCategory = svcMenu.selectListMenuUserSubListById(menu_id, "product");
		paramMap.put("menuCategory", menuCategory);
		mav.addObject("menuCategory", menuCategory);

		// ArrayList<ProductCategory> categoryList =
		// svcProduct.selectListProductCategoryByMenuId( menu_id );
		// mav.addObject("categoryList",categoryList);

		/* 상위가 product가 아닌데 하위메뉴가 product인경우만. */
		if (!menuType.equals("product") && menuCategory.size() > 0) {
			if (menuCategory.size() > 1 && currMenu != null) {
				menuSubType = currMenu.getMenuSubType();
			} else {
				menuSubType = menuCategory.get(0).getMenuSubType();
			}
		}

		// typeCode
		paramMap.put("typeCode", typeCode);

		// 판매가격설정용
		String optionGroupCode = svcProduct.selectProductGroupCode(paramMap);
		mav.addObject("optionGroupCode", optionGroupCode);
		// 판매가격설정용 - 기본가격
		List<String> addBasicOptionId = new ArrayList<>();
		paramMap.put("option_type", menuSubType);

		/* 등록된 가겨정정보 조회, 미등록시 기본가격조회 처리 */

		List<ProductPriceOption.FixPriceList> oneProductFixPriceOption = svcProduct
				.selectOneProductFixPriceOption(paramMap);
		if (oneProductFixPriceOption.size() > 0) {
			mav.addObject("fixPriceOptionList", oneProductFixPriceOption);
		} else {
			paramMap.put("option_type", menuSubType);

			List<ProductPriceOption.DayList> fixPriceOptionList = svcProduct.selectPriceOptionList(paramMap);
			mav.addObject("fixPriceOptionList", fixPriceOptionList);
		}

		String priceSelectValue = "fix";
		/** 등록된 가격들의 부모 기본가격id 목록과 가격타입 **/
		List<ProductPriceOption> oneProductPriceOptionList = svcProduct.selectOneProductPriceOptionList(paramMap);
		if (oneProductPriceOptionList.size() > 0) {
			priceSelectValue = "fix";
			for (ProductPriceOption oneProductPriceOption : oneProductPriceOptionList) {
				addBasicOptionId.add(String.valueOf(oneProductPriceOption.getPriceOptionId()));
			}
		}

		List<ProductPriceOption> oneProductDayPriceOptionList = svcProduct.selectOneProductDayPriceOptionList(paramMap);
		if (oneProductDayPriceOptionList.size() > 0) {
			priceSelectValue = "calender";
			for (ProductPriceOption oneProductDayPriceOption : oneProductDayPriceOptionList) {
				addBasicOptionId.add(String.valueOf(oneProductDayPriceOption.getPriceOptionId()));
			}
		}

		if (addBasicOptionId.size() > 0) {
			mav.addObject("addBasicOptionId", addBasicOptionId);
		}

		mav.addObject("priceSelectValue", priceSelectValue);
		/** 등록된 가격들의 부모 기본가격id 목록과 가격타입 **/

		// 세부일정용
		List<ProductDetailSchedule> detailScheduleList = svcProduct.selectProductDetailScheduleList(paramMap);

		mav.addObject("dayBtnInfoList", svcProduct.selectProductDetailScheduleBtnList(paramMap));

		for (ProductDetailSchedule productDetailSchedule : detailScheduleList) {
			HashMap<String, Object> Param = new HashMap<>();
			Param.put("tsId", productDetailSchedule.getTourspotId());

			productDetailSchedule.setImageSpotList(svcPlace.selectListPlaceSpotImage(Param));
			productDetailSchedule
					.setImageNumList(svcProduct.selectProductDetailScheduleImageList(productDetailSchedule));
		}
		// 상품연계정보
		HashMap<String, Object> Param = new HashMap<>();
		Param.put("productSerial", productInfo.getProductSerial());
		List<ProductTourLink> ProductTourLinkList = svcProduct.selectListProductTourLink(Param);
		mav.addObject("productTourLinkList", ProductTourLinkList);

		/* 재고정책부분 */
		if (productInfo.getPolicyInventory() != null) {
			logger.debug("" + productInfo.getPolicyInventory().equals("1"));
			mav.addObject("policyInventory", productInfo.getPolicyInventory().equals("1"));
		} else {
			mav.addObject("policyInventory", false);
		}

		// String returnUrl = tourTemplateUrl+"/"+ menuType
		// +"/"+menuSubType+"/"+"product-modify";
		String returnUrl = tourTemplateUrl + "/product/" + "product-form";

		if (menuSubType.equals("package")) {
			returnUrl = tourTemplateUrl + "/product/" + "product-form";
		} else if (menuSubType.equals("rentcar")) {
			mav.addObject("rentCarOptionList", svcProduct.selectRentCarOptionList());
			returnUrl = tourTemplateUrl + "/product/" + "product-form";
		} else if (menuSubType.equals("stay")) {
			// 객실 옵션 정보용
			// product_tour_detail 테이블에서 detail_category가 'stayOption'인 것들을 가져온다.
			HashMap<String, Object> golfParam = new HashMap<>();
			golfParam.put("productTourId", productNum);
			golfParam.put("detailCategory", "stayOption");
			List<ProductDetailSchedule> productDetailList = svcProduct.selectProductDetailList(golfParam);
			mav.addObject("productStayDetailList", productDetailList);
			for (ProductDetailSchedule productDetailSchedule : productDetailList) {
				productDetailSchedule
						.setImageNumList(svcProduct.selectProductDetailScheduleImageList(productDetailSchedule));
			}
			returnUrl = tourTemplateUrl + "/" + menuType + "/" + "product-form";
		} else if (menuSubType.equals("golf")) {
			// 골프 옵션 정보용
			// product_tour_detail 테이블에서 detail_category가 'golfOption'인 것들을 가져온다.
			HashMap<String, Object> golfParam = new HashMap<>();
			golfParam.put("productTourId", productNum);
			golfParam.put("detailCategory", "golfOption");
			List<ProductDetailSchedule> productDetailList = svcProduct.selectProductDetailList(golfParam);
			mav.addObject("productGolfDetailList", productDetailList);
			for (ProductDetailSchedule productDetailSchedule : productDetailList) {
				productDetailSchedule
						.setImageNumList(svcProduct.selectProductDetailScheduleImageList(productDetailSchedule));
			}
		} else if (menuSubType.equals("rentcar")) {
			mav.addObject("rentCarOptionList", svcProduct.selectRentCarOptionList());
		} else if (menuSubType.equals("stay")) {
			// 객실 옵션 정보용
			// product_tour_detail 테이블에서 detail_category가 'stayOption'인 것들을 가져온다.
			HashMap<String, Object> golfParam = new HashMap<>();
			golfParam.put("productTourId", productNum);
			golfParam.put("detailCategory", "stayOption");
			List<ProductDetailSchedule> productDetailList = svcProduct.selectProductDetailList(golfParam);
			mav.addObject("productStayDetailList", productDetailList);
			for (ProductDetailSchedule productDetailSchedule : productDetailList) {
				productDetailSchedule
						.setImageNumList(svcProduct.selectProductDetailScheduleImageList(productDetailSchedule));
			}
		} else if (menuSubType.equals("single")) {
		}

		mav.addObject("productInfo", productInfo);
		mav.addObject("menuName", menu.getMenuName());
		mav.addObject("menuId", menu_id);
		mav.addObject("optionGroupCode", optionGroupCode);

		mav.addObject("detailScheduleList", detailScheduleList);
		mav.addObject("p", paramMap);
		mav.addObject("productId", productNum);
		mav.addObject("typeCode", typeCode);
		mav.addObject("productType", menuSubType);
		mav.addObject("mode", "U");
		mav.addObject("REST_API_KEY", REST_API_KEY);

		mav.setViewName(returnUrl);
		return mav;
	}

	/* ################################### ##################################### */

	// 메뉴 카테고리
	@GetMapping("/getProductCategory")
	@ResponseBody
	public HashMap<String, Object> getProductCategoryById(
			// @RequestParam("categoryId") int categoryId,
			@RequestParam HashMap<String, Object> param,
			@ModelAttribute ProductCategory data) {
		HashMap<String, Object> resultMap = new HashMap<>();
		try {
			ArrayList<ProductCategory> categories = svcProduct.selectListProductCategory(param);
			// ArrayList<ProductCategory> categories =
			// service.getProductCategoriesById(categoryId);
			resultMap.put("result", "success");
			resultMap.put("categories", categories);
			resultMap.put("message", "처리 성공");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();

		}

		return resultMap;
	}

	@PostMapping("/category-save")
	@ResponseBody
	public HashMap<String, Object> category_save_ajax(
			@RequestParam HashMap<String, Object> paramMap,
			@ModelAttribute ProductCategory pc) {
		HashMap<String, Object> resultMap = new HashMap<String, Object>();

		try {

			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			if (_user instanceof LoginUser) {
				LoginUser user = (LoginUser) _user;
				pc.setCreateId(user.getUserEmail());
				svcProduct.saveProductCommonCategory(pc);

				resultMap.put("result", "success");
				resultMap.put("message", "처리가 완료 되었습니다.");
			} else {
				resultMap.put("result", "fail");
				resultMap.put("message", "로그인 문제가 발생되었습니다.");
			}
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@PostMapping("/category-order-save")
	@ResponseBody
	public HashMap<String, Object> category_order_save(HttpServletRequest request) {
		HashMap<String, Object> retMap = new HashMap<String, Object>();

		try {
			// ArrayList<MenuUser> listMenu = new ArrayList<MenuUser>();

			String total = request.getParameter("total");

			String _productMenuId = request.getParameter("productMenuId");

			Integer productMenuId = Integer.parseInt(_productMenuId);

			for (int i = 0; i < Integer.parseInt(total); i++) {

				String menuId = request.getParameter("order[" + i + "][menuId]");
				String menuSort = request.getParameter("order[" + i + "][menuSort]");
				String menuUpperId = request.getParameter("order[" + i + "][menuUpperId]");

				if (menuId != null) {
					Integer _menuId = Integer.parseInt(menuId);
					Integer _menuSort = Integer.parseInt(menuSort);

					ProductCategory _pc = new ProductCategory()
							.addProductCategoryId(_menuId)
							.addSortOrder(_menuSort)
							.addProductMenuId(productMenuId);

					svcProduct.updateProductCommonCategory(_pc);
				}
			}

			retMap.put("result", "success");
			retMap.put("message", "처리 성공 하였습니다.");

		} catch (Exception e) {
			retMap.put("result", "ERROR");
			retMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getCause().getMessage());
		}

		return retMap;
	}

	@PostMapping("/category-del")
	@ResponseBody
	public HashMap<String, Object> category_del(@ModelAttribute ProductCategory pc) {
		HashMap<String, Object> retMap = new HashMap<String, Object>();

		try {
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			if (_user instanceof LoginUser) {
				LoginUser user = (LoginUser) _user;
				pc.setDeleteId(user.getUserEmail());
				svcProduct.deleteProductCommonCategory(pc);

				retMap.put("result", "success");
				retMap.put("message", "처리가 완료 되었습니다.");
			} else {
				retMap.put("result", "fail");
				retMap.put("message", "로그인 문제가 발생되었습니다.");
			}
		} catch (Exception e) {
			retMap.put("result", "ERROR");
			retMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getCause().getMessage());
		}

		return retMap;
	}

	@PostMapping("/category-resotre")
	@ResponseBody
	public HashMap<String, Object> category_resotre(@ModelAttribute ProductCategory pc) {
		HashMap<String, Object> retMap = new HashMap<String, Object>();

		try {
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			if (_user instanceof LoginUser) {
				LoginUser user = (LoginUser) _user;
				pc.setLastUpdateId(user.getUserEmail());
				svcProduct.resotreProductCommonCategory(pc);

				retMap.put("result", "success");
				retMap.put("message", "처리가 완료 되었습니다.");
			} else {
				retMap.put("result", "fail");
				retMap.put("message", "로그인 문제가 발생되었습니다.");
			}

		} catch (Exception e) {
			retMap.put("result", "ERROR");
			retMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getCause().getMessage());
		}

		return retMap;
	}

	// 템플릿 저장 기능
	@PostMapping("/addTemplate")
	@ResponseBody
	public void ImgSaveTest(ProductTemplate productTemplate) {
		LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

		// 나머지 정보들 등록 기능

		// html 저장
		productTemplate.setCreateId(user.getUserEmail());
		svcProduct.insertProductTemplate(productTemplate);
		svcProduct.htmlImageConverter(productTemplate, user.getUserEmail());
	}

	@GetMapping("/template-list")
	@ResponseBody
	public HashMap<String, Object> templateList() {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			HashMap<String, Object> paramMap = new HashMap<>();
			paramMap.put("contentType", "template");

			List<ProductTemplate> templateList = svcProduct.selectProductTemplateList(paramMap);

			resultMap.put("templateList", templateList);

			resultMap.put("result", "success");
			resultMap.put("message", "처리 성공");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@GetMapping("/getSampleTemplate")
	@ResponseBody
	public HashMap<String, Object> selectTemplate(@RequestParam int templateId) {
		logger.debug("templateId: " + templateId);
		HashMap<String, Object> resultMap = new HashMap<>();
		try {

			ProductTemplate template = svcProduct.getSampleTemplateById(templateId);
			String templateInfo = template.getTemplateHtml();

			logger.debug("templateInfo: " + templateInfo);

			resultMap.put("template", templateInfo);
			resultMap.put("result", "success");
			resultMap.put("message", "처리 성공");

		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();

		}

		return resultMap;
	}

	@PostMapping("/remove-template")
	@ResponseBody
	public HashMap<String, Object> removeTemplate(@RequestParam HashMap<String, Object> paramMap) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			svcProduct.deleteTemplate(paramMap);

			resultMap.put("result", "success");
			resultMap.put("message", "삭제되었습니다");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@PutMapping("/delete-product-images")
	@ResponseBody
	public HashMap<String, Object> deleteProductImage(ProductTourImages productTourImages) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			productTourImages.setLastUpdateId(loginUser.getUserEmail());
			productTourImages.setDeleteId(loginUser.getUserEmail());
			svcProduct.updateProductTourImages(productTourImages);

			resultMap.put("result", "success");
			resultMap.put("message", "삭제되었습니다.");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
			resultMap.put("message", e.getMessage());
		}

		return resultMap;
	}

	@GetMapping("/load-price-option")
	@ResponseBody
	public HashMap<String, Object> loadPriceOption(@RequestParam HashMap<String, Object> paramMap) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			// 요구 파라미터 : 상품종류
			List<ProductPriceOption.DayList> basicPriceOptionList = svcProduct.selectPriceOptionList(paramMap);

			resultMap.put("basicPriceOptionList", basicPriceOptionList);
			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@GetMapping("/load-modify-price-option")
	@ResponseBody
	public HashMap<String, Object> loadModifyPriceOption(@RequestParam HashMap<String, Object> paramMap) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			List<ProductPriceOption> addedPriceSetList = svcProduct.selectAddedPriceSetList(paramMap);
			List<ProductPriceOption> uniqueList = new ArrayList<>(addedPriceSetList.stream()
					.collect(
							Collectors.toMap(ProductPriceOption::getPriceOptionId, Function.identity(), (o1, o2) -> o1))
					.values());
			paramMap.put("priceOptionId", uniqueList);
			for (ProductPriceOption productPriceOption : addedPriceSetList) {
				if (productPriceOption.getPriceSetType().equals("periodSamePrice")) {
					paramMap.put("priceSetType", "periodSamePrice");
					break;
				}
			}
			List<ProductPriceOption> addedBasicPriceList = svcProduct.selectAddedBasicPriceList(paramMap);

			Optional<ProductPriceOption> startDate = addedPriceSetList.stream()
					.min(Comparator.comparing(ProductPriceOption::getPriceSetDate));
			Optional<ProductPriceOption> endDate = addedPriceSetList.stream()
					.max(Comparator.comparing(ProductPriceOption::getPriceSetDate));

			resultMap.put("addedBasicPriceList", addedBasicPriceList);
			resultMap.put("addedPriceSetList", addedPriceSetList);
			resultMap.put("startDate", startDate);
			resultMap.put("endDate", endDate);

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@PostMapping("/load-added-price-option-list")
	@ResponseBody
	public HashMap<String, Object> loadAddedPriceOptionList(@RequestBody ProductPriceOption productPriceOption) {
		HashMap<String, Object> resultMap = new HashMap<>();

		List<ProductPriceOption> AddedPriceOptionList = new ArrayList<>();
		try {
			if (productPriceOption.getOptionGroupCode() != null) {
				AddedPriceOptionList = svcProduct.selectAddedPriceSetMinimumList(productPriceOption);
			}

			resultMap.put("AddedPriceOptionList", AddedPriceOptionList);
			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@PostMapping("/add-product-schedule")
	@ResponseBody
	public HashMap<String, Object> addProductSchedule(@RequestBody ProductPriceOption productPriceOption) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			HashMap<String, Object> paramMap = svcProduct.insertProductSchedule(loginUser, productPriceOption);

			resultMap.put("basicPriceIdList", paramMap.get("basicPriceIdList"));
			resultMap.put("priceIdList", paramMap.get("priceIdList"));
			resultMap.put("result", "success");
			resultMap.put("message", "저장되었습니다");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	// 판매항목 추가
	@PostMapping("/add-basic-option")
	@ResponseBody
	public HashMap<String, Object> addBasicOption(@RequestBody ProductPriceOption.BasicPriceList basicPriceList) {
		HashMap<String, Object> resultMap = new HashMap<>();

		LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		basicPriceList.setCreateId(loginUser.getUserEmail());

		try {
			svcProduct.insertPriceBasicOption(basicPriceList);

			resultMap.put("priceOptionId", basicPriceList.getPriceOptionId());
			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@PostMapping("/remove-price-set")
	@ResponseBody
	public HashMap<String, Object> removePriceSet(@RequestBody HashMap<String, Object> paramMap) {
		HashMap<String, Object> resultMap = new HashMap<>();

		LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

		try {
			paramMap.put("createId", loginUser.getUserEmail());
			svcProduct.deletePriceSetService(paramMap);
			List<ProductPriceOption> deletePriceOptionList = svcProduct.selectAddedPriceSetList(paramMap);

			resultMap.put("deletePriceOptionList", deletePriceOptionList);
			resultMap.put("result", "success");
			resultMap.put("message", "삭제되었습니다.");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@GetMapping("/product-list")
	@ResponseBody
	public HashMap<String, Object> productList(
			@RequestParam HashMap<String, Object> paramMap,
			@RequestParam(value = "start", defaultValue = "0") int start,
			@RequestParam(value = "length", defaultValue = "10") int length,
			String arrExceptList,
			String limitMenuUrl) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			// 메뉴 정보 불러오기
			if (paramMap.containsKey("typeCode")) {
				String manageUrl = "/" + paramMap.get("typeCode");
				MenuUser menu = svcMenu.selectOneMenuUserByUrl(manageUrl);
				int menu_id = menu.getMenuId().intValue();
				paramMap.put("menuId", menu_id);
			}

			if (!paramMap.containsKey("orderYn"))
				paramMap.put("orderYn", "Y");
			if (!paramMap.containsKey("productStatus"))
				paramMap.put("productStatus", "ALL");

			System.out.println(arrExceptList);

			if (limitMenuUrl != null) {
				paramMap.put("menuUrl", limitMenuUrl);

			}
			if (arrExceptList != null) {
				String[] list = arrExceptList.split(",");

				paramMap.put("exceptList", list);
			}

			paramMap.put("regacyYn", "N");
			paramMap.put("deleteYn", "N");

			if (length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
			}

			int productListCount = svcProduct.selectCountProduct(paramMap);
			List<ProductInfo> productList = svcProduct.selectListProduct(paramMap);

			resultMap.put("recordsTotal", productList);
			resultMap.put("recordsFiltered", productListCount);
			resultMap.put("data", productList);

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
			resultMap.put("message", e.getMessage());
		}

		return resultMap;
	}

	@PostMapping("/change-product-order")
	@ResponseBody
	public HashMap<String, Object> changeProductOrder(@RequestBody List<ProductInfo> productInfoList) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			for (ProductInfo productInfo : productInfoList) {
				productInfo.setCreateId(loginUser.getUserEmail());
				svcProduct.updateProductOrder(productInfo);
			}

			resultMap.put("result", "success");
			resultMap.put("message", "저장되었습니다.");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
			resultMap.put("message", e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	/*
	 * ####################################### 정보 관리
	 * ####################################################
	 */
	@GetMapping("/category/list")
	public ModelAndView productCategoryListPage() {
		ModelAndView mav = new ModelAndView();
		mav.setViewName("/manage/sub/info/productCategory/product-category-list");

		// HashMap<String, Object> paramMap = new HashMap<>();
		// paramMap.put("upperCode", "product, rentCarOption");
		// List<CodeInfo> productCodeList = service.selectOptionCodeList(paramMap);
		// mav.addObject("productCodeList", productCodeList);

		return mav;
	}

	@PostMapping("/change/sales-date")
	@ResponseBody
	public HashMap<String, Object> changeSalesDate(
			@RequestParam(value = "productTourId[]", required = false) String[] productTourIds,
			@RequestParam(value = "salesStartDate", required = false) String salesStartDate,
			@RequestParam(value = "salesEndDate", required = false) String salesEndDate) {
		HashMap<String, Object> resultMap = new HashMap<>();
		HashMap<String, Object> paramMap = new HashMap<>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			paramMap.put("productTourId", productTourIds); // 배열을 직접 맵에 넣음

			// 빈 문자열이나 "null" 문자열인 경우 null로 설정
			if (salesStartDate == null || salesStartDate.isEmpty() || "null".equals(salesStartDate)) {
				paramMap.put("salesStartDate", null);
			} else {
				paramMap.put("salesStartDate", salesStartDate);
			}

			if (salesEndDate == null || salesEndDate.isEmpty() || "null".equals(salesEndDate)) {
				paramMap.put("salesEndDate", null);
			} else {
				paramMap.put("salesEndDate", salesEndDate);
			}

			paramMap.put("lastUpdateId", loginUser.getUserEmail());
			svcProduct.updateProductSalesDate(paramMap);

			resultMap.put("result", "success");
			resultMap.put("message", "저장되었습니다.");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
			resultMap.put("message", e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@GetMapping("/option/list")
	public ModelAndView productOptionListPage() {
		ModelAndView mav = new ModelAndView();
		mav.setViewName("/manage/sub/info/productOption/product-option-list");

		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("upperCode", "product, rentCarOption");
		List<CodeInfo> productCodeList = svcProduct.selectOptionCodeList(paramMap);
		mav.addObject("productCodeList", productCodeList);

		return mav;
	}

	@GetMapping("/product-option-list")
	@ResponseBody
	public HashMap<String, Object> productOptionList(
			// @RequestParam HashMap<String,Object> paramMap
			@Param(value = "optionType") String optionType) {
		HashMap<String, Object> resultMap = new HashMap<>();
		HashMap<String, Object> paramMap = new HashMap<String, Object>();
		try {
			if (!optionType.equals("all"))
				paramMap.put("optionType", optionType);

			ArrayList<ProductPriceOption> productOptionList = svcProduct.selectProductOptionList(paramMap);
			int productOptionListCount = svcProduct.selectProductOptionListCount();

			resultMap.put("recordsTotal", productOptionListCount);
			resultMap.put("recordsFiltered", productOptionListCount);
			resultMap.put("data", productOptionList);

			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
			resultMap.put("message", e.getMessage());
		}

		return resultMap;
	}

	@GetMapping("/product-option-item")
	@ResponseBody
	public HashMap<String, Object> productOptionItem(@RequestParam HashMap<String, Object> paramMap) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			ProductPriceOption productOptionItem = svcProduct.selectProductOptionItem(paramMap);

			resultMap.put("productOptionItem", productOptionItem);

			resultMap.put("result", "success");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
			resultMap.put("message", e.getMessage());
		}

		return resultMap;
	}

	@PostMapping("/add-product-option")
	@ResponseBody
	public HashMap<String, Object> addProductOption(ProductPriceOption productPriceOption) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			productPriceOption.setCreateId(loginUser.getUserEmail());
			svcProduct.insertProductOption(productPriceOption);

			resultMap.put("result", "success");
			resultMap.put("message", "저장되었습니다.");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
			resultMap.put("message", e.getMessage());
		}

		return resultMap;
	}

	@PostMapping("/modify-product-option")
	@ResponseBody
	public HashMap<String, Object> modifyProductOption(ProductPriceOption productPriceOption) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			productPriceOption.setCreateId(loginUser.getUserEmail());

			svcProduct.updateProductOption(productPriceOption);

			resultMap.put("result", "success");
			resultMap.put("message", "저장되었습니다.");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
			resultMap.put("message", e.getMessage());
		}

		return resultMap;
	}

	@PostMapping("/delete-product-option")
	@ResponseBody
	public HashMap<String, Object> deleteProductOption(ProductPriceOption productPriceOption) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			productPriceOption.setCreateId(loginUser.getUserEmail());

			svcProduct.deleteProductOption(productPriceOption);

			resultMap.put("result", "success");
			resultMap.put("message", "삭제되었습니다.");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
			resultMap.put("message", e.getMessage());
		}

		return resultMap;
	}

	@PostMapping("/change-product-option-order")
	@ResponseBody
	public HashMap<String, Object> changeProductOptionOrder(
			@RequestBody List<ProductPriceOption.BasicPriceList> basicPriceList) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			for (ProductPriceOption.BasicPriceList basicPrice : basicPriceList) {
				basicPrice.setCreateId(loginUser.getUserEmail());
				svcProduct.updateBasicProductOptionOrder(basicPrice);
			}

			resultMap.put("result", "success");
			resultMap.put("message", "저장되었습니다.");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
			resultMap.put("message", e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@PostMapping("/ck-image-upload")
	@ResponseBody
	public HashMap<String, Object> ckImageUpload(MultipartRequest request,
			@RequestParam(value = "service_type", defaultValue = "product") String serviceType) {
		HashMap<String, Object> resultMap = new HashMap<>();
		LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		try {
			MultipartFile multipartFile = null;
			if (request.getFile("upload") != null) {
				multipartFile = request.getFile("upload");
				if (multipartFile.getSize() > 0) {
					if (multipartFile.getSize() > maxFileSize) {
						throw new Exception(String.valueOf(maxFileSize / 1024 / 1024) + "MB 이하의 파일만 첨부 가능합니다.");
					}

					String uploadName = UUID.randomUUID().toString();

					File file = new File(fileUploadPath + addPath);
					if (!file.exists())
						file.mkdirs();

					multipartFile.transferTo(new File(fileUploadPath + addPath + uploadName));
					logger.debug("File Uploaded : " + multipartFile.getOriginalFilename());

					ProductDetailScheduleImage productDetailScheduleImage = new ProductDetailScheduleImage();
					productDetailScheduleImage.setDetailId(0);
					productDetailScheduleImage.setUploadPath(fileUploadPath);
					productDetailScheduleImage.setUploadFilename(uploadName);
					productDetailScheduleImage.setOriginFilename(multipartFile.getOriginalFilename());
					productDetailScheduleImage.setFileSize((int) multipartFile.getSize());
					productDetailScheduleImage.setFileMimetype(multipartFile.getContentType());
					if (multipartFile.getOriginalFilename().contains(".")) {
						productDetailScheduleImage.setFileExtension(multipartFile.getOriginalFilename()
								.substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
					}
					productDetailScheduleImage.setCreateId(user.getUserEmail());

					svcProduct.insertDetailScheduleAttachFile(productDetailScheduleImage);

					resultMap.put("result", "success");
					resultMap.put("url", "/upload/" + addPath + uploadName);
					resultMap.put("file_id", productDetailScheduleImage.getDetailImageId());

				}
			} else {
				throw new Exception("첨부된 파일이 없습니다.");
			}
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}

	@PostMapping("/upload-tour-detail-schedule-image")
	@ResponseBody
	public HashMap<String, Object> uploadTourDetailScheduleImage(@RequestParam("file") List<MultipartFile> attachFile) {
		HashMap<String, Object> returnMap = new HashMap<>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			if (attachFile != null) {
				List<MultipartFile> multipartFiles = attachFile;
				if (attachFile.size() > 0) {
					HashMap<String, Object> paramMap = new HashMap<>();
					// 저장 디렉토리 생성 확인
					File file = new File(fileUploadPath);
					if (!file.exists())
						file.mkdirs();
					// 업로드 된 파일들 복사
					for (MultipartFile multipartFile : multipartFiles) {
						if (!multipartFile.getContentType().contains("jpg") &&
								!multipartFile.getContentType().contains("jpeg") &&
								!multipartFile.getContentType().contains("png")) {
							throw new Exception("jpg, jpeg, png 파일을 첨부해주세요.");
						}

						if (multipartFile.getSize() > maxFileSize) {
							throw new Exception((maxFileSize) + "MB 이하의 파일만 첨부 가능합니다.");
						}

						String uploadName = UUID.randomUUID().toString();
						multipartFile.transferTo(new File(fileUploadPath + uploadName));
						logger.debug("User Question File Uploaded : " + multipartFile.getOriginalFilename());

						ProductDetailScheduleImage productDetailScheduleImage = new ProductDetailScheduleImage();
						productDetailScheduleImage.setDetailId(0);
						productDetailScheduleImage.setUploadPath(fileUploadPath);
						productDetailScheduleImage.setUploadFilename(uploadName);
						productDetailScheduleImage.setOriginFilename(multipartFile.getOriginalFilename());
						productDetailScheduleImage.setFileSize((int) multipartFile.getSize());
						productDetailScheduleImage.setFileMimetype(multipartFile.getContentType());
						if (multipartFile.getOriginalFilename().contains(".")) {
							productDetailScheduleImage.setFileExtension(multipartFile.getOriginalFilename()
									.substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
						}
						productDetailScheduleImage.setCreateId(loginUser.getUserEmail());

						svcProduct.insertDetailScheduleAttachFile(productDetailScheduleImage);
						returnMap.put("imageNum", productDetailScheduleImage.getDetailImageId());
						returnMap.put("uploadFilename", productDetailScheduleImage.getUploadFilename());
					}
				}
			}

			returnMap.put("result", "success");
			returnMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			returnMap.put("result", "fail");
			returnMap.put("message", e.getMessage());
			logger.error(e.getMessage());
			e.printStackTrace();
		}
		return returnMap;
	}

	@PostMapping("/upload-template-image")
	@ResponseBody
	public HashMap<String, Object> uploadEditorImage(
			@RequestParam(value = "editor-image-upload", required = false) MultipartFile[] imageFiles,
			@RequestParam(value = "editor-bgImage-upload", required = false) MultipartFile[] bgImageFiles) {
		HashMap<String, Object> resultMap = new HashMap<>();
		LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		try {
			ArrayList<ProductTemplateFile> uploadImages = new ArrayList<>();
			if (imageFiles != null && imageFiles.length > 0) {
				File file = new File(fileUploadPath + addPath);
				if (!file.exists())
					file.mkdirs();

				// 업로드 된 이미지 파일들 복사
				for (MultipartFile multipartFile : imageFiles) {

					if (multipartFile.getSize() > maxFileSize) {
						throw new Exception((maxFileSize / 1024) + "MB 이하의 파일만 첨부 가능합니다.");
					}

					String uploadName = UUID.randomUUID().toString();
					multipartFile.transferTo(new File(fileUploadPath + addPath + uploadName));

					ProductTemplateFile templateFile = new ProductTemplateFile();
					templateFile.setUploadPath(fileUploadPath + addPath);
					templateFile.setUploadFilename(uploadName);
					templateFile.setOriginFilename(multipartFile.getOriginalFilename());
					templateFile.setFileSize((int) multipartFile.getSize());
					templateFile.setFileMimetype(multipartFile.getContentType());
					if (multipartFile.getOriginalFilename().contains(".")) {
						templateFile.setFileExtension(multipartFile.getOriginalFilename()
								.substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
					}
					templateFile.setCreateId(user.getUserEmail());

					svcProduct.uploadEditorImage(templateFile);
					uploadImages.add(templateFile);

				}
			}
			if (bgImageFiles != null && bgImageFiles.length > 0) {
				File file = new File(fileUploadPath + addPath);
				if (!file.exists())
					file.mkdirs();

				// 업로드 된 이미지 파일들 복사
				for (MultipartFile multipartFile : bgImageFiles) {

					if (multipartFile.getSize() > maxFileSize) {
						throw new Exception((maxFileSize / 1024) + "MB 이하의 파일만 첨부 가능합니다.");
					}

					String uploadName = UUID.randomUUID().toString();
					multipartFile.transferTo(new File(fileUploadPath + addPath + uploadName));

					ProductTemplateFile templateFile = new ProductTemplateFile();
					templateFile.setUploadPath(fileUploadPath + addPath);
					templateFile.setUploadFilename(uploadName);
					templateFile.setOriginFilename(multipartFile.getOriginalFilename());
					templateFile.setFileSize((int) multipartFile.getSize());
					templateFile.setFileMimetype(multipartFile.getContentType());
					if (multipartFile.getOriginalFilename().contains(".")) {
						templateFile.setFileExtension(multipartFile.getOriginalFilename()
								.substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
					}
					templateFile.setCreateId(user.getUserEmail());

					svcProduct.uploadEditorImage(templateFile);
					uploadImages.add(templateFile);

				}
			}
			resultMap.put("result", "success");
			resultMap.put("list", uploadImages);

		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}

	@PostMapping("/upload-product-images")
	@ResponseBody
	public HashMap<String, Object> uploadProductImages(MultipartRequest request) {
		HashMap<String, Object> resultMap = new HashMap<>();
		LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		logger.debug("request file:" + request.getFiles("upload"));
		try {
			if (request.getFile("upload") != null) {
				List<MultipartFile> multipartFiles = request.getFiles("upload");
				ArrayList<ProductTourImages> productSlideImages = new ArrayList<>();
				if (!multipartFiles.isEmpty()) {
					// 저장 디렉토리 생성 확인
					File file = new File(fileUploadPath + addPath);
					if (!file.exists())
						file.mkdirs();

					// 업로드 된 이미지 파일들 복사
					for (MultipartFile multipartFile : multipartFiles) {
						if (multipartFile.getSize() > maxFileSize) {
							throw new Exception(String.valueOf(maxFileSize / 1024 / 1024) + "MB 이하의 파일만 첨부 가능합니다.");
						}
						/*
						 * if (!multipartFile.getContentType().contains("jpg") &&
						 * !multipartFile.getContentType().contains("jpeg") &&
						 * !multipartFile.getContentType().contains("png")) {
						 * throw new Exception("jpg, jpeg, png 파일을 첨부해주세요.");
						 * }
						 */

						String uploadName = UUID.randomUUID().toString();
						multipartFile.transferTo(new File(fileUploadPath + addPath + uploadName));
						logger.debug("Product Image File Uploaded : " + multipartFile.getOriginalFilename());

						ProductTourImages productImage = new ProductTourImages();
						productImage.setServiceType("");
						productImage.setUploadPath(fileUploadPath + addPath);
						productImage.setUploadFilename(uploadName);
						productImage.setOriginFilename(multipartFile.getOriginalFilename());
						productImage.setFileSize(multipartFile.getSize());
						productImage.setFileMimetype(multipartFile.getContentType());
						if (multipartFile.getOriginalFilename().contains(".")) {
							productImage.setFileExtension(multipartFile.getOriginalFilename()
									.substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1));
						}
						productImage.setCreateId(user.getUserEmail());

						svcProduct.writeProductSlideImages(productImage);
						productSlideImages.add(productImage);
					}
				}
				resultMap.put("result", "success");
				resultMap.put("list", productSlideImages);
			} else {
				throw new Exception("첨부된 파일이 없습니다.");
			}
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}

		return resultMap;
	}

	@DeleteMapping("/removeEditorImage")
	@ResponseBody
	public HashMap<String, Object> deleteProductImages(
			@RequestParam(value = "file_id", defaultValue = "0") int fileID,
			@RequestParam(value = "template_id", defaultValue = "0") int templateId) {
		HashMap<String, Object> resultMap = new HashMap<>();
		LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		try {
			logger.debug("DELETE FILE... ACCESS USER : " + user.getUserEmail());

			HashMap<String, Object> paramMap = new HashMap<>();
			paramMap.put("templateId", templateId);
			paramMap.put("fileId", fileID);

			// ProductTourImages image = service.getProductImagesByKey(paramMap);
			ProductTemplateFile templateFile = svcProduct.getProductTemplateFileImageByKey(paramMap);
			File file = new File(templateFile.getUploadPath() + templateFile.getUploadFilename() + addPath);
			if (file.exists()) {
				logger.debug("파일을 삭제했습니다. Delete File Path : " + file.getPath() + file.getName());
				file.delete();
			} else {
				logger.debug("파일이 존재하지 않습니다.");
			}

			svcProduct.deleteProductImageByKey(paramMap);

			resultMap.put("result", "success");
			resultMap.put("message", "삭제됐습니다.");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}
		return resultMap;
	}

	@DeleteMapping
	@ResponseBody
	public HashMap<String, Object> deleteEditorImage(
			@RequestParam(value = "file_id", defaultValue = "0") int fileId,
			@RequestParam(value = "template_id", defaultValue = "0") int templateId) {
		HashMap<String, Object> resultMap = new HashMap<>();
		LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		try {
			logger.debug("DELETE FILE... ACCESS USER : " + user.getUserEmail());

			HashMap<String, Object> paramMap = new HashMap<>();
			paramMap.put("templateId", templateId);
			paramMap.put("fileId", fileId);

			// ProductTemplateFile template = service.getProductImagesByKey(paramMap);
			ProductTemplateFile template = svcProduct.getTemplateEditorImageByKey(paramMap);
			logger.debug("file-path: " + template.getUploadPath() + template.getUploadFilename() + addPath);
			File file = new File(template.getUploadPath() + template.getUploadFilename() + addPath);
			if (file.exists()) {
				logger.debug("파일을 삭제했습니다. Delete File Path : " + file.getPath() + file.getName());
				file.delete();
			} else {
				logger.debug("파일이 존재하지 않습니다.");
			}

			// service.deleteProductImageByKey(paramMap);
			svcProduct.deleteTemplateEditorImageByKey(paramMap);

			resultMap.put("result", "success");
			resultMap.put("message", "삭제됐습니다.");
		} catch (Exception e) {
			logger.error(e.getMessage());
			resultMap.put("result", "error");
			resultMap.put("error", e.getMessage());
		}
		return resultMap;
	}

	@PostMapping("/saveProduct")
	@ResponseBody
	public HashMap<String, Object> saveProductInfo(@RequestBody ProductJsonWrapper productJsonWrapper) {
		// 임시저장
		HashMap<String, Object> resultMap = new HashMap<>();
		HashMap<String, Object> paramMap = new HashMap<>();
		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			// JSON wrapper 클래스로부터 binding
			ProductInfo basicInfo = productJsonWrapper.getProductInfo();
			ProductTemplate templateInfo = productJsonWrapper.getProductTemplate();

			// product_menu_id -> menu ID 삽입
			// String url = "/product/domestic";
			// MenuUser menu = service.selectManageMenuByUrl(url);

			String productSerial = "";
			Integer productTourId = 0;
			Integer productThumbnailId = 0;

			if (basicInfo != null) {
				productThumbnailId = basicInfo.getProductThumbnailId();

				basicInfo.setCreateId(loginUser.getUserEmail());
				// basicInfo.setProductMenuId(Long.valueOf(menu.getMenuId()).intValue());
				logger.debug("images: " + basicInfo.getImages());
				if (basicInfo.getImages() != null && basicInfo.getImages().length > 0) {
					/*
					 * paramMap.put("images", basicInfo.getImages());
					 * List<ProductTourImages> productImageList =
					 * svcProduct.selectProductInfoImageList(paramMap);
					 * 
					 * for (ProductTourImages item : productImageList) {
					 * if( productInfo.getProductThumbnail().contains( item.getUploadFilename() ) )
					 * {
					 * productInfo.setProductThumbnailId( item.getImageId() );
					 * break;
					 * }
					 * }
					 */
					String productImages = "";
					for (String image : basicInfo.getImages()) {
						if (image != null) {
							if (productImages.equals("")) {
								productImages = image;
							} else {
								productImages += "," + image;
							}
						}
					}
					basicInfo.setProductImages(productImages);
				}

				// info
				if (basicInfo.getProductTourId() == 0) {
					svcProduct.insertProductInfoWithProductSerial(basicInfo); // upsert with serial updates
					productTourId = basicInfo.getProductTourId();
					paramMap.put("productTourId", productTourId);
					paramMap.put("imageIds", basicInfo.getImages());

					svcProduct.connectProductImage(paramMap);

					ProductInfo paramProductInfo = svcProduct.selectProductInfo(paramMap);
					resultMap.put("productCode", paramProductInfo.getProductCode());
				} else {
					svcProduct.updateProductInfo(basicInfo);
					if (basicInfo.getProductStatus().equals("S")) {
						productTourId = svcProduct.updateAndCopyProductInfo(basicInfo);
					} else {
						productTourId = basicInfo.getProductTourId();
					}
				}
				paramMap.put("productTourId", productTourId);
				basicInfo = svcProduct.selectProductInfo(paramMap);
				basicInfo.setProductThumbnailId(productThumbnailId);

				if (productThumbnailId != null && productThumbnailId != 0) {
					makeProductThumbnail(basicInfo);
				}

				productSerial = basicInfo.getProductSerial();
				productTourId = basicInfo.getProductTourId();

				resultMap.put("productSerial", productSerial);
				resultMap.put("productTourId", productTourId);
				resultMap.put("productMenuId", basicInfo.getProductMenuId());
				resultMap.put("result", "success");
				resultMap.put("status", "success");
			}
			if (templateInfo != null) {
				logger.debug("templateInfo: " + templateInfo.getProductTourId());
				templateInfo.setCreateId(loginUser.getUserEmail());

				// template

				if (templateInfo.getProductTourId() != 0) {
					ProductInfo productInfo = new ProductInfo();
					productInfo.setCreateId(loginUser.getUserEmail());
					productInfo.setProductDescription(templateInfo.getTemplateHtml());
					productInfo.setProductDescriptionType(templateInfo.getTemplateType());
					productInfo.setProductTourId(templateInfo.getProductTourId());
					productInfo.setProductStatus(templateInfo.getProductStatus());
					productInfo.setProductProgramMustKnow(templateInfo.getProductProgramMustKnow());

					svcProduct.updateProductDescription(productInfo);
					productTourId = productInfo.getProductTourId();

					// 상품 복사
					if (templateInfo.getProductStatus().equals("S")) {
						productTourId = svcProduct.updateAndCopyProductInfo(productInfo);
					}
				} else {
					svcProduct.insertProductTemplate(templateInfo);
					productTourId = templateInfo.getProductTourId();
				}

				paramMap.put("productTourId", productTourId);
				basicInfo = svcProduct.selectProductInfo(paramMap);

				productSerial = basicInfo.getProductSerial();
				productTourId = basicInfo.getProductTourId();

				resultMap.put("productSerial", productSerial);
				resultMap.put("productTourId", productTourId);
				resultMap.put("result", "success");
				resultMap.put("status", "success");
			}
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@PostMapping("/add-product-price-info")
	@ResponseBody
	public HashMap<String, Object> addProductPriceInfo(@RequestBody ProductPostJson productPostJson) {
		HashMap<String, Object> resultMap = new HashMap<>();
		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			int productTourId = svcProduct.updateBasicPriceOptionUseYnService(loginUser, productPostJson);
			Map<String, Object> priceInfoMap = svcProduct.addedPriceListService(productTourId);

			resultMap.put("productTourId", productTourId);
			resultMap.put("addedpriceOptionList", priceInfoMap.get("priceOptionList"));
			resultMap.put("updOptionGroupCode", productPostJson.getUpdOptionGroupCode());
			resultMap.put("result", "success");
			resultMap.put("message", "저장되었습니다");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@PostMapping("/modifyProduct")
	@ResponseBody
	public HashMap<String, Object> modifyProductInfo(@RequestBody ProductJsonWrapper productJsonWrapper) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			// JSON wrapper 클래스로부터 binding
			ProductInfo basicInfo = productJsonWrapper.getProductInfo();
			ProductTemplate templateInfo = productJsonWrapper.getProductTemplate();

			// product_menu_id -> menu ID 삽입
			// String url = "/product/domestic";
			// MenuUser menu = service.selectManageMenuByUrl(url);

			int productTourId = 0;

			if (basicInfo != null) {
				basicInfo.setCreateId(loginUser.getUserEmail());
				if (basicInfo.getImages() != null && basicInfo.getImages().length > 0) {
					String productImages = "";
					logger.debug("images: " + basicInfo.getImages());
					for (String image : basicInfo.getImages()) {
						if (image != null) {
							if (productImages.equals("")) {
								productImages = image;
							} else {
								productImages += "," + image;
							}
						}
					}
					logger.debug("images: " + productImages);

					basicInfo.setProductImages(productImages);
				}
				// info
				svcProduct.updateProductInfo(basicInfo);
				productTourId = svcProduct.updateAndCopyProductInfo(basicInfo);
				resultMap.put("productTourId", productTourId);
				resultMap.put("result", "success");
				resultMap.put("message", "저장되었습니다.");
			}
			if (templateInfo != null) {
				// templateInfo.setCreateId(loginUser.getUserEmail());
				// TODO 메뉴 기능 완성시 menu NULL 현상 처리
				// templateInfo.setProductTourId(Long.valueOf(menu.getMenuId()).intValue());

				// template
				// service.updateProductTemplate(templateInfo);

				// 상품의 description 업데이트
				ProductInfo productInfo = new ProductInfo();
				productInfo.setCreateId(loginUser.getUserEmail());
				productInfo.setProductDescription(templateInfo.getTemplateHtml());
				productInfo.setProductDescriptionType(templateInfo.getTemplateType());
				productInfo.setProductTourId(templateInfo.getProductTourId());
				svcProduct.updateProductDescription(productInfo);
				// 상품 복사
				productTourId = svcProduct.updateAndCopyProductInfo(productInfo);

				resultMap.put("productTourId", productTourId);
				resultMap.put("result", "success");
				resultMap.put("message", "저장되었습니다.");
			}
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}
		return resultMap;

	}

	@PostMapping("/registProduct")
	@ResponseBody
	public HashMap<String, Object> writeProductInfo(@ModelAttribute ProductInfo productInfo, HttpSession session) {
		// 등록하기 - 임시저장X
		HashMap<String, Object> resultMap = new HashMap<>();
		LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		try {
			productInfo.setCreateId(user.getUserEmail());

			// product_menu_id -> menu ID 삽입
			String url = "/" + productInfo.getUrl();
			MenuUser menu = svcMenu.selectOneMenuUserByUrl(url);
			// TODO 아래 "menu" is null 오류 남
			// productInfo.setProductMenuId(Long.valueOf(menu.getMenuId()).intValue());
			if (productInfo.getImages() != null && productInfo.getImages().length > 0) {
				String productImages = "";
				for (String image : productInfo.getImages()) {
					if (image != null) {
						if (productImages.equals("")) {
							productImages = image;
						} else {
							productImages += "," + image;
						}
					}
				}
				productInfo.setProductImages(productImages);
			}
			// save info
			// service.insertProductInfo(productInfo);
			// service.updateAndCopyProductInfo(productInfo);
			svcProduct.updateProductInfo(productInfo);

			session.removeAttribute("tempBasicInfo");
			session.removeAttribute("tempTemplateInfo");
			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
			resultMap.put("status", "success");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}
		return resultMap;
	}

	@PostMapping("/removeProduct")
	@ResponseBody
	public HashMap<String, Object> removeProductInfo(@ModelAttribute ProductInfo productInfo, HttpSession session) {
		// 등록하기 - 임시저장X
		HashMap<String, Object> resultMap = new HashMap<>();
		LoginUser user = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		try {
			productInfo.setCreateId(user.getUserEmail());
			svcProduct.deleteProductInfo(productInfo);

			resultMap.put("result", "success");
			resultMap.put("message", "취소되었습니다.");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}
		return resultMap;
	}

	@PostMapping("/add-product-detail-schedule")
	@ResponseBody
	public HashMap<String, Object> addProductDetailSchedule(@RequestBody ProductPostJson productPostJson) {
		HashMap<String, Object> resultMap = new HashMap<>();
		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			int productTourId = svcProduct.addProductDetailScheduleService(loginUser, productPostJson);

			resultMap.put("productTourId", productTourId);
			resultMap.put("result", "success");
			resultMap.put("message", "저장되었습니다");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	// 상품복사
	@PostMapping("/copy-product")
	@ResponseBody
	public HashMap<String, Object> copyProduct(
			@RequestParam HashMap<String, Object> paramMap) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			svcProduct.copyProductService(paramMap);

			resultMap.put("result", "success");
			resultMap.put("message", "복사되었습니다");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@PostMapping("/change-use-yn")
	@ResponseBody
	public HashMap<String, Object> changeProductUseYn(@RequestParam HashMap<String, Object> paramMap) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			paramMap.put("createId", loginUser.getUserEmail());
			int updateCount = svcProduct.updateProductUseYn(paramMap);

			resultMap.put("updateCount", updateCount);
			resultMap.put("result", "success");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@PostMapping("/save-rentcar-info")
	@ResponseBody
	public HashMap<String, Object> saveRentCarInfo(@RequestBody ProductInfo productInfo) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			productInfo.setCreateId(loginUser.getUserEmail());

			int productTourId = svcProduct.updateProductRentCarInfoService(productInfo);

			resultMap.put("productTourId", productTourId);
			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@PostMapping("/update-rentcar-info")
	@ResponseBody
	public HashMap<String, Object> updateRentCarInfo(@RequestBody ProductInfo productInfo) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			productInfo.setCreateId(loginUser.getUserEmail());

			int productTourId = svcProduct.updateProductRentCarInfoService(productInfo);

			resultMap.put("productTourId", productTourId);
			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@GetMapping("/ajax-resv-possible-date-list")
	@ResponseBody
	public HashMap<String, Object> resvPossibleInfo(
			Reservation reservation) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {

			ProductInfo product = new ProductInfo();
			product.setPickPeopleCount(1);
			product.setStartDate(reservation.getStartDate());
			product.setEndDate(reservation.getEndDate());
			product.setProductSerial(reservation.getProductSerial());

			resultMap.put("productPossibleDateList", svcProduct.getCalendarPossibleDateList(product));
			resultMap.put("result", "success");
			resultMap.put("message", "처리되었습니다.");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			e.printStackTrace();
		}

		return resultMap;
	}

	@PostMapping("/product-price-option-list")
	@ResponseBody
	public HashMap<String, Object> productPriceOption_ajax_list(
			@RequestParam(value = "travelDate", defaultValue = "null") String travelDate,
			@RequestParam(value = "productTourId", defaultValue = "null") Integer productTourId,
			@RequestParam(value = "deleteYn", defaultValue = "N") String deleteYn) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			HashMap<String, Object> paramMap = new HashMap<>();

			if (travelDate == null || productTourId == null) {
				resultMap.put("result", "fail");
				resultMap.put("message", "중요 파라미터 누락 입니다.");
			} else {
				paramMap.put("travelDate", travelDate);
				paramMap.put("productTourId", productTourId);
				paramMap.put("deleteYn", deleteYn);

				ArrayList<ProductPriceOption> list = svcProduct.selectListPriceOption(paramMap);

				resultMap.put("data", list);
				resultMap.put("result", "success");
				resultMap.put("message", "처리되었습니다.");
			}
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			e.printStackTrace();
		}

		return resultMap;
	}

	@PostMapping("/form-product-price-option-list")
	@ResponseBody
	public HashMap<String, Object> formProductPriceOption_ajax_list(
			@RequestParam(value = "travelDate", defaultValue = "null") String travelDate,
			@RequestParam(value = "productTourId", defaultValue = "null") Integer productTourId,
			@RequestParam(value = "productSerial", defaultValue = "null") String productSerial,
			@RequestParam(value = "startDate", defaultValue = "null") String startDate,
			@RequestParam(value = "endDate", defaultValue = "null") String endDate,
			@RequestParam(value = "deleteYn", defaultValue = "") String deleteYn,
			@RequestParam(value = "fisrtLoadType", defaultValue = "no") String fisrtLoadType,
			@RequestParam(value = "isGroup", defaultValue = "no") String isGroup) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			HashMap<String, Object> paramMap = new HashMap<>();

			if (travelDate == null || productTourId == null) {
				resultMap.put("result", "fail");
				resultMap.put("message", "중요 파라미터 누락 입니다.");
			} else {
				HashMap<String, Object> productParam = new HashMap<>();
				productParam.put("productTourId", productTourId);
				ProductInfo productInfo = svcProduct.selectProductInfo(productParam);
				paramMap.put("travelDate", travelDate);
				paramMap.put("productTourId", productTourId);
				paramMap.put("orderBy", "desc");

				if (deleteYn != null && !deleteYn.isEmpty()) {
					paramMap.put("deleteYn", deleteYn);
				}
				ArrayList<ProductPriceOption> list = svcProduct.selectListPriceOption(paramMap);

				HashMap<String, Object> rsvParam = new HashMap<>();
				if (list.size() > 0) {
					rsvParam.put("startDate", startDate);
					rsvParam.put("endDate", endDate);
					rsvParam.put("productSerial", productSerial);
					ArrayList<Reservation> reservationList = reservationUtil.selectListCalcReservation(rsvParam);

					if (!"first".equals(fisrtLoadType)) {
						ArrayList<ProductPriceOption> priceList = new ArrayList();

						for (ProductPriceOption item : list) {
							for (int i = 0; i < reservationList.size(); i++) {
								ProductPriceOption itemCopy = new ProductPriceOption();
								Reservation rsvItem = reservationList.get(i);
								if (rsvItem.getIsRestDate() == 1) {
									continue;
								}
								// 무제한 판매아닐때
								if (productInfo.getPolicyInventory().equals("1")) {
									// 특가경우
									if (item.getOptionOneCode().equals(rsvItem.getSpecialOptionOneCode())) {
										if ("N".equals(isGroup)) {
											if ("remain".equals(rsvItem.getSpecialRsvPossible())
													&& rsvItem.getTotalOrderCount() < rsvItem.getMaxCapacity()) {
												priceList.add(setSpecialPriceList(itemCopy, rsvItem));
											}
										} else {
											if (rsvItem.getSpecialCapacity() != null) {
												priceList.add(setSpecialPriceList(itemCopy, rsvItem));
											}
										}
									}
									// 정가경우
									if (item.getOptionOneCode().equals(rsvItem.getOptionOneCode())
											&& !"remain".equals(rsvItem.getSpecialRsvPossible())) {
										if ("N".equals(isGroup)) {
											if ("remain".equals(rsvItem.getRsvPossible())
													&& rsvItem.getTotalOrderCount() < rsvItem.getMaxCapacity()) {
												priceList.add(setNormalPriceList(itemCopy, rsvItem));
											}
										} else {
											if (rsvItem.getMaxCapacity() != null) {
												priceList.add(setNormalPriceList(itemCopy, rsvItem));
											}
										}
									}
								} else { // 무제한 판매일때
											// 특가경우
									if (item.getOptionOneCode().equals(rsvItem.getSpecialOptionOneCode())) {
										if (rsvItem.getSpecialCapacity() != null) {
											priceList.add(setSpecialPriceList(itemCopy, rsvItem));
										}
									} else if (item.getOptionOneCode().equals(rsvItem.getOptionOneCode())
											&& rsvItem.getSpecialOptionOneCode() == null) {
										priceList.add(setNormalPriceList(itemCopy, rsvItem));
									}
								}

							}
						}
						Collections.sort(priceList, Comparator.comparing(ProductPriceOption::getDate));
						resultMap.put("data", priceList);
					} else {
						ArrayList<Reservation> reservationListResult = new ArrayList<>();
						ArrayList<Reservation> reservationList2 = reservationUtil.selectListCalcReservation(rsvParam);

						for (Reservation reservation : reservationList2) {
							if (reservation.getIsRestDate() == 0) {
								reservationListResult.add(reservation);
							}
						}
						resultMap.put("data", reservationListResult);
					}
				}

				resultMap.put("productInfo", productInfo);
				resultMap.put("result", "success");
				resultMap.put("message", "처리되었습니다.");
			}
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			e.printStackTrace();
		}

		return resultMap;
	}

	private ProductPriceOption setNormalPriceList(ProductPriceOption itemCopy, Reservation rsvItem) {
		itemCopy.setDate(rsvItem.getDate());
		itemCopy.setTravelDate(rsvItem.getTravelDate());
		itemCopy.setPriceOptionId(rsvItem.getPriceOptionId());
		itemCopy.setProductPrice(rsvItem.getProductPriceParam());
		itemCopy.setOptionName(rsvItem.getOptionName());
		itemCopy.setOptionOneCode(rsvItem.getOptionOneCode());
		itemCopy.setConsecuriveDiscountAmount(
				rsvItem.getConsecuriveDiscountAmount() == null ? 0 : (long) rsvItem.getConsecuriveDiscountAmount());
		itemCopy.setExtraPersonDefualtCharge(
				rsvItem.getExtraPersonDefualtCharge() == null ? 0 : (long) rsvItem.getExtraPersonDefualtCharge());
		itemCopy.setExtraPersonConsecuriveCharge(rsvItem.getExtraPersonConsecuriveCharge() == null ? 0
				: (long) rsvItem.getExtraPersonConsecuriveCharge());

		return itemCopy;
	}

	private ProductPriceOption setSpecialPriceList(ProductPriceOption itemCopy, Reservation rsvItem) {
		itemCopy.setDate(rsvItem.getDate());
		itemCopy.setTravelDate(rsvItem.getTravelDate());
		itemCopy.setPriceOptionId(rsvItem.getSpecialOptionId());
		itemCopy.setProductPrice(rsvItem.getSpecialPrice());
		itemCopy.setOptionName(rsvItem.getSpecialOptionName());
		itemCopy.setOptionOneCode(rsvItem.getSpecialOptionOneCode());
		itemCopy.setConsecuriveDiscountAmount((long) rsvItem.getSpecialConsecuriveDiscountAmount());
		itemCopy.setExtraPersonDefualtCharge((long) rsvItem.getSpecialExtraPersonDefualtCharge());
		itemCopy.setExtraPersonConsecuriveCharge((long) rsvItem.getSpecialExtraPersonConsecuriveCharge());

		return itemCopy;
	}

	/*
	 * ################################################ProductInventory#############
	 * ###################################
	 */
	@GetMapping("/inventory")
	public ModelAndView ProductInventoryList(
			HttpServletRequest request,
			ProductInventory bc,
			@RequestParam(value = "page", defaultValue = "1") int page,
			@RequestParam(value = "pageSize", defaultValue = "5") int pageSize,
			@Param(value = "boardId") String boardId,
			@Param(value = "boardUrl") String boardUrl,
			@Param(value = "titleLike") String titleLike,
			@Param(value = "contentLike") String contentLike) {
		ModelAndView mav = new ModelAndView();

		try {
			HashMap<String, Object> paramMap = new HashMap<>();
			//
			// List<SortData> listSort = getListOrder(request);
			// paramMap.put("listSort", listSort);
			//
			// paramMap.put("boardId", boardId);
			// paramMap.put("boardUrl", boardUrl);
			// paramMap.put("titleLike", titleLike);
			// paramMap.put("contentLike", contentLike);
			//
			// int totalCount = boardService.selectCountBoardContents(paramMap);
			//
			// PagingDTO paging = new PagingDTO(totalCount, page, 0, pageSize);
			// paramMap.put("itemStartPosition", paging.getItemStartPosition());
			// paramMap.put("pagePerSize", paging.getPagePerSize());
			//
			// mav.addObject("p", paramMap);
			// mav.addObject("boardList", boardService.selectListBoardContents(paramMap));
			// mav.addObject("paging", paging);

			paramMap.put("menuUrl", "/shop");
			paramMap.put("orderYn", "Y");

			mav.addObject("listProduct", svcProduct.selectListProduct(paramMap));

		} catch (Exception e) {
			mav.addObject("boardList", null);
			logger.error(e.getMessage());
		}

		mav.setViewName("/manage/sub/productInventory/list");
		return mav;
	}

	@GetMapping("/inventory/form")
	public ModelAndView ProductInventoryForm(
			HttpServletRequest request,
			ProductInventory bc,
			@RequestParam(value = "id") Integer id) {
		ModelAndView mav = new ModelAndView();

		// HashMap<String, Object> paramMap = new HashMap<>();
		//
		// paramMap.put("id", id);
		// paramMap.put("boardId", id);
		//
		// mav.addObject("p", paramMap);
		// mav.addObject("content", boardService.selectOneBoardContents(paramMap));
		//
		// mav.addObject("attachList",
		// boardService.selectListBoardAttachFile(paramMap));

		mav.setViewName("/manage/sub/productInventory/form");
		return mav;
	}

	@PostMapping("/inventory-list")
	@ResponseBody
	public HashMap<String, Object> ProductInventory_list_ajax(
			HttpServletRequest request,
			ProductInventory bc,
			@RequestParam(value = "start", defaultValue = "0") int start,
			@RequestParam(value = "length", defaultValue = "10") int length,
			@Param(value = "productSerial") String productSerial) {
		HashMap<String, Object> retrunMap = new HashMap<>();

		try {
			HashMap<String, Object> paramMap = new HashMap<>();

			// List<SortData> listSort = getListOrder(request);
			// paramMap.put("listSort", listSort);

			if (length >= 0) {
				paramMap.put("itemStartPosition", start);
				paramMap.put("pagePerSize", length);
			}

			paramMap.put("productSerial", productSerial);

			int totalCount = svcProduct.selectCountProductInventory(paramMap);

			retrunMap.put("recordsTotal", totalCount);
			retrunMap.put("recordsFiltered", totalCount);
			retrunMap.put("data", svcProduct.selectListProductInventory(paramMap));

			retrunMap.put("summary", svcProduct.selectSummaryProductInventory(paramMap));

			retrunMap.put("result", "success");
			retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

		return retrunMap;
	}

	@PostMapping("/inventory-save")
	@ResponseBody
	public HashMap<String, Object> ProductInventory_save_ajax(
			@RequestParam(value = "mode", defaultValue = "I") String mode,
			ProductInventory bc,
			// Multipart
			HttpServletRequest request) {
		HashMap<String, Object> retrunMap = new HashMap<>();

		try {
			Object _user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			if (_user instanceof LoginUser) {
				LoginUser user = (LoginUser) _user;
				HashMap<String, Object> paramMap = new HashMap<>();

				if (mode.equals("I")) {
					bc.setCreateId(user.getUserEmail());
					svcProduct.insertProductInventory(bc);
				} else {
					bc.setLastUpdateId(user.getUserEmail());
					svcProduct.updateProductInventory(bc);
				}
				List<MultipartFile> multipartFiles = null;

				retrunMap.put("result", "success");
				retrunMap.put("message", "처리가 완료 되었습니다.");
			} else {
				retrunMap.put("result", "fail");
				retrunMap.put("message", "로그인 문제가 발생되었습니다.");
			}
		} catch (Exception e) {
			retrunMap.put("result", "error");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
			retrunMap.put("info", e.getMessage());
			logger.error(e.getCause().getMessage());
		}
		return retrunMap;
	}

	@PostMapping("/inventory-delete")
	@ResponseBody
	public HashMap<String, Object> board_delete_ajax(
			ProductInventory bc) {
		HashMap<String, Object> retrunMap = new HashMap<>();

		try {
			svcProduct.deleteProductInventory(bc);

			retrunMap.put("result", "success");
			retrunMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			retrunMap.put("result", "fail");
			retrunMap.put("message", "처리중 문제가 발생했습니다.");
		}

		return retrunMap;
	}

	@PostMapping("/save-link-product")
	@ResponseBody
	public HashMap<String, Object> saveLinkProduct(@RequestBody ProductPostJson productPostJson) {
		HashMap<String, Object> resultMap = new HashMap<>();
		try {
			LoginUser loginUser = (LoginUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

			svcProduct.saveProductTourLink(loginUser, productPostJson);

			// resultMap.put("productTourId",productTourId);
			resultMap.put("result", "success");
			resultMap.put("message", "저장되었습니다");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	private void setSessionProductId(HttpSession session, int productID) {
		if (session.getAttribute("productId") == null) {
			session.setAttribute("productId", productID);
		}
	}

	private void makeProductThumbnail(ProductInfo basicInfo) throws IOException {
		HashMap<String, Object> paramMap = new HashMap<>();

		paramMap.put("imageId", basicInfo.getProductThumbnailId());

		ProductTourImages img = svcProduct.getProductImageById(paramMap);

		File fileImage = new File(fileUploadPath + addPath + img.getUploadFilename());

		File fileThumbnail = new File(
				fileUploadPath + addPath + basicInfo.getProductSerial() + "." + img.getFileExtension());

		Files.copy(fileImage.toPath(), fileThumbnail.toPath(), StandardCopyOption.REPLACE_EXISTING);
	}

	/*
	 * ################################### ProductTourRestPeriod
	 * #####################################
	 */
	@GetMapping("/period-list")
	@ResponseBody
	public HashMap<String, Object> getPeriodList(
			@RequestParam HashMap<String, Object> paramMap,
			@RequestParam(value = "start", defaultValue = "0") int start,
			@RequestParam(value = "length", defaultValue = "10") int length,
			@RequestParam(value = "listDates[]", required = false) String[] listDates) {
		HashMap<String, Object> resultMap = new HashMap<>();

		try {
			// 메뉴 정보 불러오기
			if (paramMap.containsKey("typeCode")) {
				String manageUrl = "/" + paramMap.get("typeCode");
				MenuUser menu = svcMenu.selectOneMenuUserByUrl(manageUrl);
				int menu_id = menu.getMenuId().intValue();
				paramMap.put("menuId", menu_id);
			}

			paramMap.put("startDate", listDates[0]);
			paramMap.put("endDate", listDates[listDates.length - 1]);
			paramMap.put("listDates", listDates);

			logger.debug("paramMap: " + paramMap);

			// retMap.put("data", svcProduct.selectListProductRestPeriod(paramMap));
			resultMap.put("data", svcProduct.selectListProductWithRestPeriod(paramMap));
			resultMap.put("result", "success");
			resultMap.put("message", "처리가 완료 되었습니다.");
		} catch (Exception e) {
			resultMap.put("result", "fail");
			resultMap.put("message", "처리중 문제가 발생했습니다.");
			logger.error(e.getMessage());
		}

		return resultMap;
	}

	@PostMapping("/period-save")
	@ResponseBody
	public HashMap<String, Object> savePeriod(
			// @RequestBody ProductRestPeriod productRestPeriod,
			@RequestParam(value = "productSerial[]", required = false) String[] productSerials,
			@RequestParam(value = "restStartDate", required = false) String restStartDate,
			@RequestParam(value = "restEndDate", required = false) String restEndDate) {
		HashMap<String, Object> resultMap = new HashMap<>();
		try {
			LoginUser loginUser = getBaseLoginUser();

			logger.debug("productSerials: " + productSerials);
			logger.debug("restStartDate: " + restStartDate);
			logger.debug("restEndDate: " + restEndDate);

			for (String productSerial : productSerials) {
				ProductRestPeriod productRestPeriod = new ProductRestPeriod();
				productRestPeriod.setProductSerial(productSerial);
				productRestPeriod.setProductRestStartDate(restStartDate);
				productRestPeriod.setProductRestEndDate(restEndDate);
				svcProduct.saveProductRestPeriod(loginUser, productRestPeriod);
			}

			resultMap.put("result", "success");
			resultMap.put("message", "저장되었습니다");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

	@PostMapping("/period-delete")
	@ResponseBody
	public HashMap<String, Object> deletePeriod(
			// @RequestBody ProductRestPeriod productRestPeriod,
			@RequestParam("productSerial") String productSerial,
			@RequestParam("id") Integer id) {
		HashMap<String, Object> resultMap = new HashMap<>();
		try {
			ProductRestPeriod productRestPeriod = new ProductRestPeriod();
			productRestPeriod.setProductSerial(productSerial);
			productRestPeriod.setId(id);

			svcProduct.deleteProductRestPeriod(productRestPeriod);

			resultMap.put("result", "success");
			resultMap.put("message", "삭제되었습니다");
		} catch (Exception e) {
			resultMap.put("result", "error");
			resultMap.put("message", "처리중 에러 발생하였습니다.");
			logger.info("------");
			logger.debug(e.getMessage());
			e.printStackTrace();
		}

		return resultMap;
	}

}
